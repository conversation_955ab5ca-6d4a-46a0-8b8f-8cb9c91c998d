{"GlobalPropertiesHash": "lsRnSu3YPLDYnlWhPMghr23621ii01tGcfX8nSSdH9E=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["H/XQx2Q2U1d//pYcU36C3emt7mDuRPS7WyheanCM670=", "oGYLuGvJwJvSE7On7KfVrkTW3Nl9wqFfUkhrnk7ue4I=", "QHzIQUBO3oEOaiA+AZb/CJ8bJcx1qo+y+w5lgJSk9Ik=", "J3zCGI1kpGlh7GNvXBCTvOCQ1MTnygXxuNX28ej5GOg=", "/zeLbTQ0LwuKw3YCe8J9x0I25SgZFhIDu5LBmA4dGOY=", "D/nZ6rcw+iyuu2KZ64qnadZe4MS8tkLMqvh+xxDXZxs=", "FOBr4VzOjPp8JpEFJ6F/gwpTz1pp2UZcbNYU9aOguDw=", "andbQ7nUWBQvca9BZtjLEa01U7Z/sQnbYZe3g77VPAk=", "Ea0yeM1ppFtqUqWEbDKDPwTRtD6FCburAAAmyGL6RbM=", "fMU7Gaep5yxxRun/VM9tk4q0WWH5G+POo0YlhTjlSlE=", "BnuZrj5CqvI5qlfbUpjquWd4tUvMyJKWq7SYnA7a16g=", "BGlOFjmW0EfIKvPHSwlG6jjWgYBGKufeNe3LPpgcR54=", "hskmXFyf5SQ2etoY82S3A6cBtDjL8dJAfojQXhZ3uaE=", "CwhTnQU4Ysefnimt1NPRimIZCTOhqS1KZKNl74+Q7ug=", "XBkdZv900NS7Z33Jw5pcQNRNmksJauRHsIRFx9rKv90=", "YlhtUr7npFeiWg1hB4keLMqe0YlHfvrcH5HDavRaKvA=", "EQxq/aj/qmwpswkwFSTcJpdatKzXCvU4J4flbCG+UYY=", "cQKJ8Eb+aNz/Pwc/TBXtXsSqExDWj+Spg69WBJh44cM=", "8BlI4PaVSuGU2O4I40UHeV77hYn1PD5aHiogfEkFxG4=", "3Sm3lfbK9FvZsUKQCzhabrEA1liYEKuLTkO6A9MZ1rU=", "GS1VvDsK9gUzNhC6FtvwTndjbCBKTnNpvLUy0zOGkFg=", "6HEXg+72ZdHb0yi/EI6zTXtX3aSKjA0bbXooIrya3lI=", "ANBy51fH0rKx7+nVMNtIakRGKghQesmCgKa171bhMJw=", "JrjYWIt4xV44sZ7eKAGYQi8Cd0Z+L7Sj5lTQqy5LUaA=", "vj8ReRMPowHvOzxtnTJxbSeLy6nDz3c5rMndYSyEW0I=", "m1NqBlCovRlShSyslsqu876sKMJzIQGgV4OeC4buBZM=", "PLizmI7cGmM1Yn/B1MK2n+7Ac0XTb5JjFfkVYKHHWAU=", "l6VnsO74jzaAQZGCx0Zuwz/hqB3soeaXCXzjSEydJ7U=", "cmFB7pogLgE7C0ZxFrhYsYWXH8KHZdS94kBwKSwh+OU=", "ytNYxa9xucRUHlnOJdQXM5Dwg35ooEuHv1lQElJ/KyY=", "vMLljGdDdBh/oMtyGy2EFBzyoJ0lJSmg886JGykQZx4=", "VbMnlU+y1avEXjtFv39ZHvHCqvYP7zNOY5WlfuPdkWc=", "eDXtXg74mYPNFs5FVYkHEpUxkgAXj7GlWZxiZ3wfcio=", "6+RrFLL43TMzz264gx0KG1hlkxNr4vXGuXkuRCCvxYk=", "OnJHG5zJ4fJgOG0bKrHgyA6g5Z9En9Se6bGjJgPR7PE=", "eevAUacSsS5MLADeOAHyiAHJz9yAD5dqNdgpaffsxeE=", "j6Zx5+ukIWvELjITkZTNB2KENAaaF0DoGTWHdVQtxMg=", "/tCu2mexEYuRnxhkf4PeN/QvZ32peWCygvxH6FLopzc=", "ne90ZzdWGL2r6OYqOiuycXUPBXITMWDhWwgqNqthEbA=", "ELvfqcR368jZH6V98we5T36YWF8hRSAJNlvHn4fUCjs=", "rJe347cDHtCjco4Op9JmYqS7EO4rq7BYLERa1p6bOyw=", "XI5gWpDH3P/wpI6HNM9fwZRAvkBiZ3ULPLpImD7nEfQ=", "nX4IqHONZn3h+IT9w0LLgguK1yRzfJpaMMP5M8yhA3Y=", "a9R6Lk0XSWZv1i958Ee5eKF9iopWti9ouGuh7u75Br8=", "uKt32mb+nP3auJlefWgrKgmfLaNkQG3ZplX8NqycKQs=", "wH/tuDJ/Zbok/go8Xa744t7iIQK/fTNbTmVQtKL3Jyg=", "X/XF38zVZAvW8cGCYzAMpny35ZmSnF5YXLoHf9wAmIQ=", "ZP/zZs1u/Jd+b4EtCcLIIHAvGaRccsLwS+FxszR3yeA=", "5WGjm1X6NI+5cD5UCg078z1znxp9iUp3Ou7vg/HJQLA=", "h0iYRtPKffH2bc8vk2DlN4DiczjlHyEjIRHkCbM6Ywk=", "1lQkcFR+JDG/bepbeY92iMx4XtxGbpbUvh+e5GiOy7Y=", "cjYvxy9ozYNrphSBG1HW0U8Mo1kpnJ1dAV7zOzmCV0c=", "ldbsOiYo1A+gPJgVzKJ6hReUwEHe4UqeVZR39WWsC6Y=", "4G9Lu02B+nL/2nZRarmLm/OCrKiMrsjs+r0uB6bECUc=", "8rsYOJkUl3jWtj8O1yujX+k5c9NpvljwKCGtxMZbNl8=", "QSqAOWUqMOE/mFQZ0oLoXf8lfQbIdqCvbKHKpM38aNc=", "TzlffWlQO7AVxpjxR5ZfDBDowxmdt5KjcBWe1AwAP98=", "Kdz0JMuWvtcJ27Vl3z5I8uJ2LbmJKoOeBZWEdjWNHeo=", "sYWfZAdMdkzzuwwDro9SbAto7TjEftN3t7M6PfyYefQ=", "mESlUK2PzF7efBP9Q+sU0R5neAMyPjPM7mnkczwJJSs=", "LSzneo9/PkmnO76cyIbR3XjQ3gkyav/HdkhK2aDh34w=", "4v6JgWvNtv/TEuKb5N/ao10C1Rx5ZmGp6JwOHvP1N4c=", "B1GATXFYWOG3LXwkarqVu6sxVAmrfETtPe4migvXqiA=", "Eb/YQa1ZPvWzNuzPD4T/30eT2Lcsq4Y1TF12wWrx/R8=", "kZa9stJi5FQUPplCTQvRG7onRR54BUhdaEKsztoewDQ=", "z8ZRiSqHQcte+Qu4WzY0qS/PLmK7XU0+T8Cywfr8By4=", "DsuBv6O2cAhWa4Kj7jEVs7DSh/PdGS8UA2wPZrOeCWU=", "rLXIuSmVIznt0OC46NWL/wVh7sUB1BZKOcxJGOMf6pc=", "9cn7h1Zml8IOSJSJqm5ZBCxn/9sftjjG42liP5AViws=", "+Lh1+idlXYHo24hs7gWVJnueC5O1mhYcqXgpVTFyNBA=", "iwmj7g+jxVnI2L9f2V37Z78jzxWvsydE75M75R46XbI=", "Hh0JvlxslqgFkbtlxGWGA1MCqazSj99BYpXVBNMqvmI=", "SeBe35g6S2TDnr/sWcbFk88W8WPq0e2ICXpSzeCXhys=", "6xtIpprkTNfIdtydUvVorLqoTsXXK3pEU2n1IBrgu1Q=", "aWNTCkRYtBXPTxekDZDDuf6meiCEq+Nf8tEKKnKQ4rI=", "vgh3ofz5QWAfeqM6VXbR4UYQgJoMaDyZuh3QyfLc6fI=", "2oMFaWsO9/5zHZpLG05p6x7l5I0wUlja1/1H/JwQRnA=", "WYcQzK0L5PDPotcO9lTZvuLbp5NaaHkbqyh6oDqmVyY=", "kZ3wxqL5YIucc9rWBbafuZQYtV4k4JmFgr3vnjYfVPA=", "RqnBcjwkb9wpKODBvddKReB8ps5M33bboXYhJEBednA=", "GbrqxRFFnCbswO6RxtpesIVWWZPPkbt5KNlPBXFj6L4=", "cvn+2EgM+Jdj1ZCd7VjJF7AgBnERgik3e7s4HhMMzOM=", "Q1Tn2rGspsKBE3DFc1edbe+6nSkHnxplP4sK8O8pIJg=", "RmqJiqPPp2VLPL4we0zHBaJtg4Msba4sPEi6uJu2uMQ=", "dVeED8eUhfr4UUyoo0hQDXcnqRmS3enAsBjN8ACUnAQ=", "ZRtOF8YyvpQvzrEurHtz69R5lpvXctvll9ds/x8KaMo=", "KLimIwh1gJS/siWlJlPqmY4p/xgTGA3ELRN2m1M8ONE=", "+VLQ7jSGMclYzb8yHMBWwb1yiO+gmIJdB8eaaPeElZQ=", "I9Vn5HLVT/B7fVuZ1HLusqnssxmW6DS5/tYxp/8lJJo=", "KfpGoWujka22OY6WK7mprQhUL8zwX4JY21OXVbkyS40=", "uAMeqC32gncUDbm79eHz85TvoBGcL5xMwZGXxjbAXAE=", "luvwH1J3/B9u1p6GZNp9JlHkJvcS4L2oHr4XdlWdMoM=", "Ppy53LJupGAqOFWashyhw3a8CjG99T0A5Ez1BSH6fto=", "ydYNEt45ERRBAbpStAOy+3UdyCCbLfCx9zUUEu0ANKc=", "K3CXvCzvrSpwuW2ecp3fr0rkYPLiKe6d0+sdcBBI/yE=", "fu5/tQMo+vEhqAmT7tPKnMxXq6S/s2g8LG6C2R5BiDQ=", "aaZTwXOl8386K9nY8RtmKSndcIC98xKEaXzxHeA0Hgs=", "u6kyAFXXRmtwga5Rdy5BSLcXyKnZHi4gr2AsypQ6qr4=", "Sa5CFulBUL/mXBn+6pEAu5gCupXnPD1eh6XJEB6OXjk=", "pWzXWrzj7/8SMC8fvnzoSdXYyDp1+9UbbrlcFRuQrdA=", "Fa5EozA6OIp/c+yoMkR1uWggp9wf4koD38U7WQ4V1jo=", "9HPJZdlfwfPME+3bC0EDsQHO8lny8++VCWZVFoehG4M=", "2nO5sctUaZswkgl95NkHFgd0juDnvwKOyPqS0PyxuEI=", "iq1FOtupaUU6w3XnUSk2sLj8GnvPkAkgb/i2CHvcqE0=", "tIESpMBgAnL3IzCqoG1VHysIqOHCFaFhwbEUixzVwiA=", "PRNO5cjMswNx8/ePZiH2wGqfdLpU5jaffcVOgU2H4g4=", "UD0Y2kqg0+fHyIo2/+E86X2UMScEycxsOux+I4f8hNo=", "0u/vqu8bOd1FerCqggNexTT3/pywwBT9Z5C3nzLLJhs=", "V2HM4x4DL082IZAegVAaxD7UExjfXlRh3B1ldcRXp8w=", "+ZhKdNQ4QSgeZZFGZ0UegGT1hJBS585Zo3BsjbV/98c=", "Xt9KqbrP7ewrfuy+lpBTpnYhd5H/bg8J4KTNXHitoWs=", "mKEppRQxIxvpOdlnx1Hi3QltZgnVXEfZy3ZZ/N1MzYA=", "bPgWKzQjMDyo9D/avBEd2Hmu2KA7cW33sXuvFtXHoS4=", "U6CHl+PdAlzxbpi1qzPQfkVnwx2D+etS+qnIeNfK8dY=", "TcGOuG4HEmjCrcGdarRM/EYuqpkdkgwAhhhHzVA4l7Q=", "+rZxR3hfXuXsMPzcbM+BNRK/L1BLwTTUFuquTPS5lhc=", "1YqBoEbbYCqwk7u0R75pftf90erfQn7oJWoafPAuLZw=", "Hb9RgFKHIj61YxEMSs0VBzz4CJDrEl6msAHD4I8WMts=", "IZVZB/EDahmJHW2LMZhN50Sl2QY2UM5n8rmq1CT8I3w=", "6FB7G1oHKF4AydhecNX79oQw7yFGFHBMo2A3v+dRtFU=", "kJ0Y3p2KTDGrvvIebzYPCbcbgQcGnl/XxgTHxuzzvaQ=", "3u6dOg+D5kuI//GsoGmV5TcxfJRf1atZaqmFbby7ejI=", "S0oMDPe51bSEuY7rXGzTqp719JWBuJdU20QrOA62ckM=", "s5eP4V5e0xjdmoMOmOAQjQ8NjS97xs5vQSQ04BiEwfQ=", "p7NHJOpDf0UDZ7Lh9QPUFjb3Y3/4bMEpHzj9bmp2sIs=", "+K5OipcBCK3lDoioellwIwYnL2UkjbfPkR7GZIdrBY4=", "Y2RQHSu/yFkz6VZ813nsleZOhcbh5PF2cR5G7XSATCA=", "22WCtpD1r0nQU7BVyI2rLvgjCgnDu2lKXUTklUHEUPs=", "mejGz2If0LO4cy8UfE7SykWXU0+pzAG2KxHyPz0QMSw=", "fV8JGjpmQOXLdfW/50WJGXCfrhEsXGAPoy0XA75bqGU="], "CachedAssets": {"H/XQx2Q2U1d//pYcU36C3emt7mDuRPS7WyheanCM670=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\css\\app.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8rbvw3on5j", "Integrity": "vGbjgqEqv4y3q5OB8W2R9LthkuF8mQfHFeNdKSReSmU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 4151, "LastWriteTime": "2025-05-26T09:27:14.9187101+00:00"}, "oGYLuGvJwJvSE7On7KfVrkTW3Nl9wqFfUkhrnk7ue4I=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\favicon.png", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-05-26T09:27:15.0876102+00:00"}, "QHzIQUBO3oEOaiA+AZb/CJ8bJcx1qo+y+w5lgJSk9Ik=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\icon-192.png", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "icon-192#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f9uvjujlxy", "Integrity": "DbpQaq68ZSb5IoPosBErM1QWBfsbTxpJqhU0REi6wP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\icon-192.png", "FileLength": 2626, "LastWriteTime": "2025-05-26T09:27:15.0906053+00:00"}, "J3zCGI1kpGlh7GNvXBCTvOCQ1MTnygXxuNX28ej5GOg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\index.html", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ycv507p0zh", "Integrity": "/sXxy7CSYGKJhpyx8tb9Jvc+xVOXVVqqjbmLgsyt7BY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 2908, "LastWriteTime": "2025-05-26T20:53:42.0606931+00:00"}, "/zeLbTQ0LwuKw3YCe8J9x0I25SgZFhIDu5LBmA4dGOY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\js\\advanced-features.js", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "js/advanced-features#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "oc05c2u9aq", "Integrity": "+Hpdvus+/fXI7BmLOmI+VCb8XM5O5+F08ANt/SbRdBI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\advanced-features.js", "FileLength": 10212, "LastWriteTime": "2025-05-26T20:53:14.6457214+00:00"}, "D/nZ6rcw+iyuu2KZ64qnadZe4MS8tkLMqvh+xxDXZxs=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-05-26T09:27:15.1395742+00:00"}, "FOBr4VzOjPp8JpEFJ6F/gwpTz1pp2UZcbNYU9aOguDw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-05-26T09:27:15.1745528+00:00"}, "andbQ7nUWBQvca9BZtjLEa01U7Z/sQnbYZe3g77VPAk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-05-26T09:27:15.1805507+00:00"}, "Ea0yeM1ppFtqUqWEbDKDPwTRtD6FCburAAAmyGL6RbM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-05-26T09:27:15.2275208+00:00"}, "fMU7Gaep5yxxRun/VM9tk4q0WWH5G+POo0YlhTjlSlE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-05-26T09:27:15.2345163+00:00"}, "BnuZrj5CqvI5qlfbUpjquWd4tUvMyJKWq7SYnA7a16g=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-05-26T09:27:15.2684958+00:00"}, "BGlOFjmW0EfIKvPHSwlG6jjWgYBGKufeNe3LPpgcR54=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-05-26T09:27:15.2774898+00:00"}, "hskmXFyf5SQ2etoY82S3A6cBtDjL8dJAfojQXhZ3uaE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-05-26T09:27:15.2874835+00:00"}, "CwhTnQU4Ysefnimt1NPRimIZCTOhqS1KZKNl74+Q7ug=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-05-26T09:27:15.2924813+00:00"}, "XBkdZv900NS7Z33Jw5pcQNRNmksJauRHsIRFx9rKv90=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-05-26T09:27:15.328458+00:00"}, "YlhtUr7npFeiWg1hB4keLMqe0YlHfvrcH5HDavRaKvA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-05-26T09:27:15.3324574+00:00"}, "EQxq/aj/qmwpswkwFSTcJpdatKzXCvU4J4flbCG+UYY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-05-26T09:27:15.3384529+00:00"}, "cQKJ8Eb+aNz/Pwc/TBXtXsSqExDWj+Spg69WBJh44cM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-05-26T09:27:15.3524438+00:00"}, "8BlI4PaVSuGU2O4I40UHeV77hYn1PD5aHiogfEkFxG4=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-05-26T09:27:15.3664357+00:00"}, "3Sm3lfbK9FvZsUKQCzhabrEA1liYEKuLTkO6A9MZ1rU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-05-26T09:27:15.3684342+00:00"}, "GS1VvDsK9gUzNhC6FtvwTndjbCBKTnNpvLUy0zOGkFg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-05-26T09:27:15.3884224+00:00"}, "6HEXg+72ZdHb0yi/EI6zTXtX3aSKjA0bbXooIrya3lI=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-05-26T09:27:15.4032432+00:00"}, "ANBy51fH0rKx7+nVMNtIakRGKghQesmCgKa171bhMJw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-05-26T09:27:15.4272286+00:00"}, "JrjYWIt4xV44sZ7eKAGYQi8Cd0Z+L7Sj5lTQqy5LUaA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-05-26T09:27:15.4502154+00:00"}, "vj8ReRMPowHvOzxtnTJxbSeLy6nDz3c5rMndYSyEW0I=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-05-26T09:27:14.0331926+00:00"}, "m1NqBlCovRlShSyslsqu876sKMJzIQGgV4OeC4buBZM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-05-26T09:27:14.0731682+00:00"}, "PLizmI7cGmM1Yn/B1MK2n+7Ac0XTb5JjFfkVYKHHWAU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-05-26T09:27:14.1251366+00:00"}, "l6VnsO74jzaAQZGCx0Zuwz/hqB3soeaXCXzjSEydJ7U=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-05-26T09:27:14.1311361+00:00"}, "cmFB7pogLgE7C0ZxFrhYsYWXH8KHZdS94kBwKSwh+OU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-05-26T09:27:14.1571184+00:00"}, "ytNYxa9xucRUHlnOJdQXM5Dwg35ooEuHv1lQElJ/KyY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-05-26T09:27:14.1811033+00:00"}, "vMLljGdDdBh/oMtyGy2EFBzyoJ0lJSmg886JGykQZx4=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-05-26T09:27:14.2390661+00:00"}, "VbMnlU+y1avEXjtFv39ZHvHCqvYP7zNOY5WlfuPdkWc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-05-26T09:27:14.2610529+00:00"}, "eDXtXg74mYPNFs5FVYkHEpUxkgAXj7GlWZxiZ3wfcio=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-05-26T09:27:14.307025+00:00"}, "6+RrFLL43TMzz264gx0KG1hlkxNr4vXGuXkuRCCvxYk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-05-26T09:27:14.3300123+00:00"}, "OnJHG5zJ4fJgOG0bKrHgyA6g5Z9En9Se6bGjJgPR7PE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-05-26T09:27:14.4039657+00:00"}, "eevAUacSsS5MLADeOAHyiAHJz9yAD5dqNdgpaffsxeE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-05-26T09:27:14.4179571+00:00"}, "j6Zx5+ukIWvELjITkZTNB2KENAaaF0DoGTWHdVQtxMg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-05-26T09:27:14.5248915+00:00"}, "/tCu2mexEYuRnxhkf4PeN/QvZ32peWCygvxH6FLopzc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bg73c5uikn", "Integrity": "hZfAgLdvcdGTSRkNhp+wj6k6NYLF10gI3HJ/+8VuTsc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-05-26T09:27:14.5448794+00:00"}, "ne90ZzdWGL2r6OYqOiuycXUPBXITMWDhWwgqNqthEbA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "car65sm25x", "Integrity": "pMTvsocIdDApkEOvqYT+WdkER2X8lS8BJ5RmM2r8VFE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-05-26T09:27:14.6498149+00:00"}, "ELvfqcR368jZH6V98we5T36YWF8hRSAJNlvHn4fUCjs=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-05-26T09:27:14.6748001+00:00"}, "rJe347cDHtCjco4Op9JmYqS7EO4rq7BYLERa1p6bOyw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "noe7lm1h3u", "Integrity": "F+N1nZF7PPElx20OPw2ArM+geeMjFOjhqE2UGvKq5Z4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-05-26T09:27:14.7397588+00:00"}, "XI5gWpDH3P/wpI6HNM9fwZRAvkBiZ3ULPLpImD7nEfQ=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c887lhynek", "Integrity": "lOFlNZipK9b8YQjSUaT9W+aGsBX5VNjtUpSMVzlAUWY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-05-26T09:27:14.7567487+00:00"}, "nX4IqHONZn3h+IT9w0LLgguK1yRzfJpaMMP5M8yhA3Y=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "775ggt5h4o", "Integrity": "rU3UgVZERjyLgvKLop+jxVTKoG5DF175dZIBIIke8hk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-05-26T09:27:14.7867306+00:00"}, "a9R6Lk0XSWZv1i958Ee5eKF9iopWti9ouGuh7u75Br8=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-05-26T09:27:14.8037805+00:00"}, "uKt32mb+nP3auJlefWgrKgmfLaNkQG3ZplX8NqycKQs=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ml3ve6jes3", "Integrity": "+nkqzBF5WGFREP2Q6NkC4YHWSc0CJx1YPvM4AUAlqP8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-05-26T09:27:14.8397583+00:00"}, "wH/tuDJ/Zbok/go8Xa744t7iIQK/fTNbTmVQtKL3Jyg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ux6g6pshvr", "Integrity": "IlHiXpSA3P8IEDZfwS/LAqtCDBoddGhIUQ80p4HNqVs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-05-26T09:27:14.8557495+00:00"}, "X/XF38zVZAvW8cGCYzAMpny35ZmSnF5YXLoHf9wAmIQ=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vsszhq6w1o", "Integrity": "ccraV2vh7deEd0D0JXqKivuGm4pgIhOsniGiWw/FTkQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-05-26T09:27:14.8847309+00:00"}, "ZP/zZs1u/Jd+b4EtCcLIIHAvGaRccsLwS+FxszR3yeA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-05-26T09:27:14.8907271+00:00"}, "5WGjm1X6NI+5cD5UCg078z1znxp9iUp3Ou7vg/HJQLA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6v1eliq898", "Integrity": "8l2GZYo3DU01livOSapIHhLp7gqK8LwRJ5QOayWO5qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-05-26T09:27:14.915712+00:00"}, "h0iYRtPKffH2bc8vk2DlN4DiczjlHyEjIRHkCbM6Ywk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\sample-data\\weather.json", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "sample-data/weather#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iag0ou56lh", "Integrity": "enKgCMkYmCpfEcmg6Annbmc40VZ/A6aYYSQjZfVn2cU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\sample-data\\weather.json", "FileLength": 453, "LastWriteTime": "2025-05-26T09:27:14.9556894+00:00"}}, "CachedCopyCandidates": {}}