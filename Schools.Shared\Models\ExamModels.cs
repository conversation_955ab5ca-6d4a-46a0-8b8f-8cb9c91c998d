using System.ComponentModel.DataAnnotations;

namespace Schools.Shared.Models
{
    // Exam
    public class Exam : BaseEntity
    {
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public int SubjectId { get; set; }

        public int? ClassId { get; set; }

        public int? SectionId { get; set; }

        public string? Description { get; set; }

        [Required]
        public DateTime StartDateTime { get; set; }

        [Required]
        public DateTime EndDateTime { get; set; }

        [Required]
        public int Duration { get; set; } // in minutes

        [Required]
        public int TotalMarks { get; set; }

        [Required]
        public ExamStatus Status { get; set; } = ExamStatus.Draft;

        public string? Instructions { get; set; }

        public bool IsRandomized { get; set; } = false;

        public string? CreatedByTeacher { get; set; }

        public string? ApprovedBy { get; set; }

        public DateTime? ApprovedAt { get; set; }

        // Navigation properties
        public virtual Subject Subject { get; set; } = null!;
        public virtual Class? Class { get; set; }
        public virtual Section? Section { get; set; }
        public virtual ICollection<ExamQuestion> ExamQuestions { get; set; } = new List<ExamQuestion>();
        public virtual ICollection<StudentExamResult> StudentExamResults { get; set; } = new List<StudentExamResult>();
    }

    // Exam Question
    public class ExamQuestion : BaseEntity
    {
        [Required]
        public int ExamId { get; set; }

        [Required]
        [StringLength(1000)]
        public string QuestionText { get; set; } = string.Empty;

        [Required]
        public int QuestionType { get; set; } // 1=Multiple Choice, 2=True/False, 3=Short Answer, 4=Essay

        [Required]
        public int Marks { get; set; }

        public int OrderIndex { get; set; }

        public string? ImageUrl { get; set; }

        // Navigation properties
        public virtual Exam Exam { get; set; } = null!;
        public virtual ICollection<ExamQuestionOption> ExamQuestionOptions { get; set; } = new List<ExamQuestionOption>();
        public virtual ICollection<StudentExamAnswer> StudentExamAnswers { get; set; } = new List<StudentExamAnswer>();
    }

    // Exam Question Option (for multiple choice questions)
    public class ExamQuestionOption : BaseEntity
    {
        [Required]
        public int ExamQuestionId { get; set; }

        [Required]
        [StringLength(500)]
        public string OptionText { get; set; } = string.Empty;

        public bool IsCorrect { get; set; } = false;

        public int OrderIndex { get; set; }

        // Navigation properties
        public virtual ExamQuestion ExamQuestion { get; set; } = null!;
    }

    // Student Exam Result
    public class StudentExamResult : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int ExamId { get; set; }

        public DateTime? StartedAt { get; set; }

        public DateTime? SubmittedAt { get; set; }

        public int? ObtainedMarks { get; set; }

        public decimal? Percentage { get; set; }

        public string? Grade { get; set; }

        public bool IsCompleted { get; set; } = false;

        public string? Notes { get; set; }

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual Exam Exam { get; set; } = null!;
        public virtual ICollection<StudentExamAnswer> StudentExamAnswers { get; set; } = new List<StudentExamAnswer>();
    }

    // Student Exam Answer
    public class StudentExamAnswer : BaseEntity
    {
        [Required]
        public int StudentExamResultId { get; set; }

        [Required]
        public int ExamQuestionId { get; set; }

        public string? AnswerText { get; set; }

        public int? SelectedOptionId { get; set; }

        public int? MarksObtained { get; set; }

        public bool? IsCorrect { get; set; }

        public string? TeacherFeedback { get; set; }

        // Navigation properties
        public virtual StudentExamResult StudentExamResult { get; set; } = null!;
        public virtual ExamQuestion ExamQuestion { get; set; } = null!;
    }

    // Assignment
    public class Assignment : BaseEntity
    {
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public int SubjectId { get; set; }

        public int? ClassId { get; set; }

        public int? SectionId { get; set; }

        [Required]
        public string Description { get; set; } = string.Empty;

        [Required]
        public DateTime DueDate { get; set; }

        [Required]
        public int TotalMarks { get; set; }

        public string? Instructions { get; set; }

        public string? AttachmentUrl { get; set; }

        public string? CreatedByTeacher { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual Subject Subject { get; set; } = null!;
        public virtual Class? Class { get; set; }
        public virtual Section? Section { get; set; }
        public virtual ICollection<StudentAssignmentSubmission> StudentAssignmentSubmissions { get; set; } = new List<StudentAssignmentSubmission>();
    }

    // Student Assignment Submission
    public class StudentAssignmentSubmission : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int AssignmentId { get; set; }

        public string? SubmissionText { get; set; }

        public string? AttachmentUrl { get; set; }

        public DateTime? SubmittedAt { get; set; }

        public int? MarksObtained { get; set; }

        public string? Grade { get; set; }

        public string? TeacherFeedback { get; set; }

        public bool IsLate { get; set; } = false;

        public bool IsGraded { get; set; } = false;

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual Assignment Assignment { get; set; } = null!;
    }
}
