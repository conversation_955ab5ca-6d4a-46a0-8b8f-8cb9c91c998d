using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Schools.Shared.Models
{
    // Electronic Exam Model
    public class Exam : BaseEntity
    {
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        public string? Description { get; set; }

        [Required]
        public int SubjectId { get; set; }

        [Required]
        public int ClassId { get; set; }

        [Required]
        public int AcademicYearId { get; set; }

        [Required]
        public string TeacherId { get; set; } = string.Empty;

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        [Required]
        public int DurationMinutes { get; set; }

        [Required]
        public int TotalMarks { get; set; }

        [Required]
        public int PassingMarks { get; set; }

        public bool IsActive { get; set; } = true;

        public bool AllowRetake { get; set; } = false;

        public int MaxAttempts { get; set; } = 1;

        public bool ShowResultsImmediately { get; set; } = true;

        public bool RandomizeQuestions { get; set; } = false;

        public bool RandomizeOptions { get; set; } = false;

        public ExamStatus Status { get; set; } = ExamStatus.Draft;

        public string? Instructions { get; set; }

        public bool IsPublished { get; set; } = false;

        public DateTime? PublishedDate { get; set; }

        // Navigation properties
        public virtual Subject Subject { get; set; } = null!;
        public virtual Class Class { get; set; } = null!;
        public virtual AcademicYear AcademicYear { get; set; } = null!;
        public virtual ApplicationUser Teacher { get; set; } = null!;
        public virtual ICollection<ExamQuestion> Questions { get; set; } = new List<ExamQuestion>();
        public virtual ICollection<ExamAttempt> ExamAttempts { get; set; } = new List<ExamAttempt>();
    }

    // Exam Attempt Model
    public class ExamAttempt : BaseEntity
    {
        [Required]
        public int ExamId { get; set; }

        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public DateTime StartTime { get; set; }

        public DateTime? EndTime { get; set; }

        [Required]
        public int DurationMinutes { get; set; }

        public int RemainingMinutes { get; set; }

        [Required]
        public AttemptStatus Status { get; set; } = AttemptStatus.InProgress;

        [Required]
        public int AttemptNumber { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? Score { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? Percentage { get; set; }

        public string? Grade { get; set; }

        public bool IsPassed { get; set; } = false;

        public bool IsSubmitted { get; set; } = false;

        public DateTime? SubmittedAt { get; set; }

        public string? IpAddress { get; set; }

        public string? UserAgent { get; set; }

        public string? Notes { get; set; }

        // Navigation properties
        public virtual Exam Exam { get; set; } = null!;
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual ICollection<StudentAnswer> StudentAnswers { get; set; } = new List<StudentAnswer>();
    }

    // Student Answer Model
    public class StudentAnswer : BaseEntity
    {
        [Required]
        public int AttemptId { get; set; }

        [Required]
        public int QuestionId { get; set; }

        public string? AnswerText { get; set; }

        public int? SelectedOptionId { get; set; }

        public string? SelectedOptionIds { get; set; } // JSON array for multiple choice

        public bool IsCorrect { get; set; } = false;

        [Column(TypeName = "decimal(5,2)")]
        public decimal MarksObtained { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal TotalMarks { get; set; } = 0;

        public DateTime AnsweredAt { get; set; }

        public bool IsMarked { get; set; } = false;

        public string? TeacherFeedback { get; set; }

        public string? MarkedBy { get; set; }

        public DateTime? MarkedAt { get; set; }

        // Navigation properties
        public virtual ExamAttempt Attempt { get; set; } = null!;
        public virtual ExamQuestion Question { get; set; } = null!;
    }

    // Exam Question
    public class ExamQuestion : BaseEntity
    {
        [Required]
        public int ExamId { get; set; }

        [Required]
        [StringLength(1000)]
        public string QuestionText { get; set; } = string.Empty;

        [Required]
        public int QuestionType { get; set; } // 1=Multiple Choice, 2=True/False, 3=Short Answer, 4=Essay

        [Required]
        public int Marks { get; set; }

        public int OrderIndex { get; set; }

        public string? ImageUrl { get; set; }

        // Navigation properties
        public virtual Exam Exam { get; set; } = null!;
        public virtual ICollection<QuestionOption> Options { get; set; } = new List<QuestionOption>();
    }

    // Question Option (for multiple choice questions)
    public class QuestionOption : BaseEntity
    {
        [Required]
        public int QuestionId { get; set; }

        [Required]
        [StringLength(500)]
        public string OptionText { get; set; } = string.Empty;

        public bool IsCorrect { get; set; } = false;

        public int OrderIndex { get; set; }

        // Navigation properties
        public virtual ExamQuestion Question { get; set; } = null!;
    }

    // Student Exam Result
    public class StudentExamResult : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int ExamId { get; set; }

        public DateTime? StartedAt { get; set; }

        public DateTime? SubmittedAt { get; set; }

        public int? ObtainedMarks { get; set; }

        public decimal? Percentage { get; set; }

        public string? Grade { get; set; }

        public bool IsCompleted { get; set; } = false;

        public string? Notes { get; set; }

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual Exam Exam { get; set; } = null!;
        public virtual ICollection<StudentExamAnswer> StudentExamAnswers { get; set; } = new List<StudentExamAnswer>();
    }

    // Student Exam Answer
    public class StudentExamAnswer : BaseEntity
    {
        [Required]
        public int StudentExamResultId { get; set; }

        [Required]
        public int ExamQuestionId { get; set; }

        public string? AnswerText { get; set; }

        public int? SelectedOptionId { get; set; }

        public int? MarksObtained { get; set; }

        public bool? IsCorrect { get; set; }

        public string? TeacherFeedback { get; set; }

        // Navigation properties
        public virtual StudentExamResult StudentExamResult { get; set; } = null!;
        public virtual ExamQuestion ExamQuestion { get; set; } = null!;
    }

    // Assignment
    public class Assignment : BaseEntity
    {
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public int SubjectId { get; set; }

        public int? ClassId { get; set; }

        public int? SectionId { get; set; }

        [Required]
        public string Description { get; set; } = string.Empty;

        [Required]
        public DateTime DueDate { get; set; }

        [Required]
        public int TotalMarks { get; set; }

        public string? Instructions { get; set; }

        public string? AttachmentUrl { get; set; }

        public string? CreatedByTeacher { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual Subject Subject { get; set; } = null!;
        public virtual Class? Class { get; set; }
        public virtual Section? Section { get; set; }
        public virtual ICollection<StudentAssignmentSubmission> StudentAssignmentSubmissions { get; set; } = new List<StudentAssignmentSubmission>();
    }

    // Student Assignment Submission
    public class StudentAssignmentSubmission : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int AssignmentId { get; set; }

        public string? SubmissionText { get; set; }

        public string? AttachmentUrl { get; set; }

        public DateTime? SubmittedAt { get; set; }

        public int? MarksObtained { get; set; }

        public string? Grade { get; set; }

        public string? TeacherFeedback { get; set; }

        public bool IsLate { get; set; } = false;

        public bool IsGraded { get; set; } = false;

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual Assignment Assignment { get; set; } = null!;
    }

    // Enums
    public enum ExamStatus
    {
        Draft = 1,
        Published = 2,
        InProgress = 3,
        Completed = 4,
        Cancelled = 5,
        Archived = 6
    }

    public enum QuestionType
    {
        MultipleChoice = 1,
        TrueFalse = 2,
        ShortAnswer = 3,
        Essay = 4,
        FillInTheBlank = 5,
        Matching = 6,
        Ordering = 7,
        Numerical = 8
    }

    public enum AttemptStatus
    {
        InProgress = 1,
        Completed = 2,
        Submitted = 3,
        TimeExpired = 4,
        Cancelled = 5,
        UnderReview = 6,
        Graded = 7
    }

    public enum DifficultyLevel
    {
        Easy = 1,
        Medium = 2,
        Hard = 3,
        Expert = 4
    }
}
