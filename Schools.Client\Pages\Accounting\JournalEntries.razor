@page "/accounting/journal-entries"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Accountant,Admin")]

<PageTitle>القيود اليومية - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-book text-info me-2"></i>
                        القيود اليومية
                    </h2>
                    <p class="text-muted mb-0">إدارة وتتبع جميع القيود المحاسبية اليومية</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-info" @onclick="CreateNewEntry">
                        <i class="fas fa-plus me-2"></i>
                        قيد جديد
                    </button>
                    <button class="btn btn-outline-primary" @onclick="RefreshData">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث
                    </button>
                    <button class="btn btn-outline-success" @onclick="ExportEntries">
                        <i class="fas fa-download me-2"></i>
                        تصدير
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل القيود اليومية...</p>
        </div>
    }
    else
    {
        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" @bind="fromDate" @bind:after="FilterEntries" />
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" @bind="toDate" @bind:after="FilterEntries" />
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الحالة</label>
                                <select @bind="selectedStatus" @bind:after="FilterEntries" class="form-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="@JournalEntryStatus.Draft">مسودة</option>
                                    <option value="@JournalEntryStatus.Posted">مرحل</option>
                                    <option value="@JournalEntryStatus.Cancelled">ملغي</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" placeholder="رقم القيد أو الوصف..."
                                           @bind="searchTerm" @bind:after="FilterEntries" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm bg-info text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@filteredEntries.Count</h4>
                        <p class="mb-0">إجمالي القيود</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm bg-primary text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@GetTotalDebit().ToString("C")</h4>
                        <p class="mb-0">إجمالي المدين</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm bg-success text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@GetTotalCredit().ToString("C")</h4>
                        <p class="mb-0">إجمالي الدائن</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm bg-warning text-dark">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@GetUnpostedCount()</h4>
                        <p class="mb-0">قيود غير مرحلة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Journal Entries Table -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2 text-info"></i>
                            قائمة القيود اليومية
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (filteredEntries?.Any() == true)
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>رقم القيد</th>
                                            <th>التاريخ</th>
                                            <th>الوصف</th>
                                            <th>المرجع</th>
                                            <th>المدين</th>
                                            <th>الدائن</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var entry in filteredEntries.Skip((currentPage - 1) * pageSize).Take(pageSize))
                                        {
                                            <tr>
                                                <td>
                                                    <strong class="text-info">@entry.EntryNumber</strong>
                                                </td>
                                                <td>@entry.EntryDate.ToString("dd/MM/yyyy")</td>
                                                <td>
                                                    <div>
                                                        <strong>@TruncateText(entry.Description, 50)</strong>
                                                        @if (!string.IsNullOrEmpty(entry.Reference))
                                                        {
                                                            <br>
                                                            <small class="text-muted">مرجع: @entry.Reference</small>
                                                        }
                                                    </div>
                                                </td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(entry.Reference))
                                                    {
                                                        <span class="badge bg-secondary">@entry.Reference</span>
                                                    }
                                                </td>
                                                <td>
                                                    <strong class="text-primary">@entry.TotalDebit.ToString("C")</strong>
                                                </td>
                                                <td>
                                                    <strong class="text-success">@entry.TotalCredit.ToString("C")</strong>
                                                </td>
                                                <td>
                                                    <span class="badge @GetStatusBadge(entry.Status)">
                                                        @GetStatusText(entry.Status)
                                                    </span>
                                                    @if (entry.TotalDebit != entry.TotalCredit)
                                                    {
                                                        <br>
                                                        <small class="text-danger">
                                                            <i class="fas fa-exclamation-triangle"></i>
                                                            غير متوازن
                                                        </small>
                                                    }
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewEntry(entry.Id)" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        @if (entry.Status == JournalEntryStatus.Draft)
                                                        {
                                                            <button class="btn btn-sm btn-outline-secondary" @onclick="() => EditEntry(entry.Id)" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            @if (entry.TotalDebit == entry.TotalCredit && entry.TotalDebit > 0)
                                                            {
                                                                <button class="btn btn-sm btn-outline-success" @onclick="() => PostEntry(entry.Id)" title="ترحيل">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                            }
                                                        }
                                                        <button class="btn btn-sm btn-outline-warning" @onclick="() => PrintEntry(entry.Id)" title="طباعة">
                                                            <i class="fas fa-print"></i>
                                                        </button>
                                                        @if (entry.Status != JournalEntryStatus.Posted)
                                                        {
                                                            <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteEntry(entry.Id)" title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        }
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            @if (totalPages > 1)
                            {
                                <nav class="mt-4">
                                    <ul class="pagination justify-content-center">
                                        <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                            <button class="page-link" @onclick="() => ChangePage(currentPage - 1)">السابق</button>
                                        </li>
                                        @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                                        {
                                            <li class="page-item @(i == currentPage ? "active" : "")">
                                                <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                                            </li>
                                        }
                                        <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                                            <button class="page-link" @onclick="() => ChangePage(currentPage + 1)">التالي</button>
                                        </li>
                                    </ul>
                                </nav>
                            }
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-book fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد قيود يومية</h5>
                                <p class="text-muted">ابدأ بإنشاء قيد يومي جديد</p>
                                <button class="btn btn-info" @onclick="CreateNewEntry">
                                    <i class="fas fa-plus me-2"></i>
                                    إنشاء قيد جديد
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private List<JournalEntryDto> allEntries = new();
    private List<JournalEntryDto> filteredEntries = new();

    private bool isLoading = true;
    private DateTime fromDate = DateTime.Now.AddMonths(-1);
    private DateTime toDate = DateTime.Now;
    private string selectedStatus = "";
    private string searchTerm = "";

    private int currentPage = 1;
    private int pageSize = 10;
    private int totalPages = 1;

    protected override async Task OnInitializedAsync()
    {
        await LoadEntries();
    }

    private async Task LoadEntries()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            allEntries = await ApiService.GetJournalEntriesAsync() ?? new List<JournalEntryDto>();
            FilterEntries();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void FilterEntries()
    {
        filteredEntries = allEntries.Where(e =>
            e.EntryDate >= fromDate &&
            e.EntryDate <= toDate &&
            (string.IsNullOrEmpty(selectedStatus) || e.Status.ToString() == selectedStatus) &&
            (string.IsNullOrEmpty(searchTerm) ||
             e.EntryNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
             e.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
             (!string.IsNullOrEmpty(e.Reference) && e.Reference.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)))
        ).OrderByDescending(e => e.EntryDate).ToList();

        UpdatePagination();
        StateHasChanged();
    }

    private void UpdatePagination()
    {
        totalPages = (int)Math.Ceiling((double)filteredEntries.Count / pageSize);
        if (currentPage > totalPages && totalPages > 0)
        {
            currentPage = totalPages;
        }
        else if (currentPage < 1)
        {
            currentPage = 1;
        }
    }

    private void ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            StateHasChanged();
        }
    }

    private async Task RefreshData()
    {
        await LoadEntries();
    }

    private void CreateNewEntry()
    {
        Navigation.NavigateTo("/accounting/journal-entries/new");
    }

    private void ViewEntry(int id)
    {
        Navigation.NavigateTo($"/accounting/journal-entries/{id}");
    }

    private void EditEntry(int id)
    {
        Navigation.NavigateTo($"/accounting/journal-entries/{id}/edit");
    }

    private async Task PostEntry(int id)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من ترحيل هذا القيد؟ لن يمكن التراجع عن هذا الإجراء."))
        {
            try
            {
                var success = await ApiService.PostJournalEntryAsync(id);
                if (success)
                {
                    await LoadEntries();
                    await JSRuntime.InvokeVoidAsync("alert", "تم ترحيل القيد بنجاح");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في ترحيل القيد: {ex.Message}");
            }
        }
    }

    private async Task DeleteEntry(int id)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا القيد؟"))
        {
            try
            {
                var success = await ApiService.DeleteJournalEntryAsync(id);
                if (success)
                {
                    await LoadEntries();
                    await JSRuntime.InvokeVoidAsync("alert", "تم حذف القيد بنجاح");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف القيد: {ex.Message}");
            }
        }
    }

    private async Task PrintEntry(int id)
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة الطباعة ستكون متاحة قريباً");
    }

    private async Task ExportEntries()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة التصدير ستكون متاحة قريباً");
    }

    private decimal GetTotalDebit()
    {
        return filteredEntries.Sum(e => e.TotalDebit);
    }

    private decimal GetTotalCredit()
    {
        return filteredEntries.Sum(e => e.TotalCredit);
    }

    private int GetUnpostedCount()
    {
        return filteredEntries.Count(e => e.Status == JournalEntryStatus.Draft);
    }

    private string TruncateText(string text, int maxLength)
    {
        if (string.IsNullOrEmpty(text) || text.Length <= maxLength)
            return text;

        return text.Substring(0, maxLength) + "...";
    }

    private string GetStatusBadge(JournalEntryStatus status)
    {
        return status switch
        {
            JournalEntryStatus.Draft => "bg-secondary",
            JournalEntryStatus.Posted => "bg-success",
            JournalEntryStatus.Cancelled => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetStatusText(JournalEntryStatus status)
    {
        return status switch
        {
            JournalEntryStatus.Draft => "مسودة",
            JournalEntryStatus.Posted => "مرحل",
            JournalEntryStatus.Cancelled => "ملغي",
            _ => "غير محدد"
        };
    }
}
