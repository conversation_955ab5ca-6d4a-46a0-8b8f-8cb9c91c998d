using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;

namespace Schools.API.Services
{
    public interface INotificationService
    {
        Task SendNotificationAsync(string userId, string title, string message, string type = "info");
        Task SendNotificationToRoleAsync(string role, string title, string message, string type = "info");
        Task SendNotificationToClassAsync(int classId, string title, string message, string type = "info");
        Task SendExamReminderAsync(int examId);
        Task SendFeeReminderAsync(string studentId);
        Task SendAttendanceNotificationAsync(string studentId, DateTime date, string status);
        Task SendGradeNotificationAsync(string studentId, int subjectId, decimal grade);
        Task<List<NotificationDto>> GetUserNotificationsAsync(string userId, int page = 1, int pageSize = 20);
        Task MarkNotificationAsReadAsync(int notificationId, string userId);
        Task MarkAllNotificationsAsReadAsync(string userId);
        Task<int> GetUnreadNotificationCountAsync(string userId);
    }

    public class NotificationService : INotificationService
    {
        private readonly SchoolsDbContext _context;
        private readonly IHubContext<NotificationHub> _hubContext;
        private readonly ILogger<NotificationService> _logger;

        public NotificationService(
            SchoolsDbContext context,
            IHubContext<NotificationHub> hubContext,
            ILogger<NotificationService> logger)
        {
            _context = context;
            _hubContext = hubContext;
            _logger = logger;
        }

        public async Task SendNotificationAsync(string userId, string title, string message, string type = "info")
        {
            try
            {
                // Save to database
                var notification = new Notification
                {
                    UserId = userId,
                    Title = title,
                    Message = message,
                    Type = type,
                    IsRead = false,
                    CreatedAt = DateTime.UtcNow
                };

                _context.Notifications.Add(notification);
                await _context.SaveChangesAsync();

                // Send real-time notification
                await _hubContext.Clients.User(userId).SendAsync("ReceiveNotification", new
                {
                    id = notification.Id,
                    title = notification.Title,
                    message = notification.Message,
                    type = notification.Type,
                    createdAt = notification.CreatedAt,
                    isRead = notification.IsRead
                });

                _logger.LogInformation("Notification sent to user {UserId}: {Title}", userId, title);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notification to user {UserId}", userId);
            }
        }

        public async Task SendNotificationToRoleAsync(string role, string title, string message, string type = "info")
        {
            try
            {
                var users = await _context.UserRoles
                    .Include(ur => ur.Role)
                    .Where(ur => ur.Role.Name == role)
                    .Select(ur => ur.UserId)
                    .ToListAsync();

                foreach (var userId in users)
                {
                    await SendNotificationAsync(userId, title, message, type);
                }

                _logger.LogInformation("Notification sent to role {Role}: {Title}", role, title);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notification to role {Role}", role);
            }
        }

        public async Task SendNotificationToClassAsync(int classId, string title, string message, string type = "info")
        {
            try
            {
                var studentIds = await _context.StudentEnrollments
                    .Where(e => e.ClassId == classId && e.IsActive)
                    .Select(e => e.StudentId)
                    .ToListAsync();

                foreach (var studentId in studentIds)
                {
                    await SendNotificationAsync(studentId, title, message, type);
                }

                _logger.LogInformation("Notification sent to class {ClassId}: {Title}", classId, title);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notification to class {ClassId}", classId);
            }
        }

        public async Task SendExamReminderAsync(int examId)
        {
            try
            {
                var exam = await _context.Exams
                    .Include(e => e.Subject)
                    .Include(e => e.Class)
                    .FirstOrDefaultAsync(e => e.Id == examId);

                if (exam == null) return;

                var title = "تذكير بالامتحان";
                var message = $"امتحان {exam.Subject.Name} للصف {exam.Class.Name} سيبدأ في {exam.StartDate:yyyy-MM-dd HH:mm}";

                await SendNotificationToClassAsync(exam.ClassId, title, message, "warning");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending exam reminder for exam {ExamId}", examId);
            }
        }

        public async Task SendFeeReminderAsync(string studentId)
        {
            try
            {
                var student = await _context.Users.FindAsync(studentId);
                if (student == null) return;

                var outstandingFees = await _context.StudentFees
                    .Where(sf => sf.StudentId == studentId && sf.Status == FeeStatus.Pending)
                    .SumAsync(sf => sf.TotalAmount - sf.PaidAmount);

                if (outstandingFees > 0)
                {
                    var title = "تذكير بالرسوم المستحقة";
                    var message = $"لديك رسوم مستحقة بقيمة {outstandingFees:C}. يرجى المراجعة لسداد الرسوم.";

                    await SendNotificationAsync(studentId, title, message, "warning");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending fee reminder for student {StudentId}", studentId);
            }
        }

        public async Task SendAttendanceNotificationAsync(string studentId, DateTime date, string status)
        {
            try
            {
                var title = status == "Present" ? "تم تسجيل الحضور" : "تم تسجيل الغياب";
                var message = $"تم تسجيل {(status == "Present" ? "حضورك" : "غيابك")} بتاريخ {date:yyyy-MM-dd}";
                var type = status == "Present" ? "success" : "warning";

                await SendNotificationAsync(studentId, title, message, type);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending attendance notification for student {StudentId}", studentId);
            }
        }

        public async Task SendGradeNotificationAsync(string studentId, int subjectId, decimal grade)
        {
            try
            {
                var subject = await _context.Subjects.FindAsync(subjectId);
                if (subject == null) return;

                var title = "درجة جديدة";
                var message = $"تم إضافة درجة جديدة في مادة {subject.Name}: {grade}%";
                var type = grade >= 60 ? "success" : "warning";

                await SendNotificationAsync(studentId, title, message, type);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending grade notification for student {StudentId}", studentId);
            }
        }

        public async Task<List<NotificationDto>> GetUserNotificationsAsync(string userId, int page = 1, int pageSize = 20)
        {
            try
            {
                var notifications = await _context.Notifications
                    .Where(n => n.UserId == userId)
                    .OrderByDescending(n => n.CreatedAt)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(n => new NotificationDto
                    {
                        Id = n.Id,
                        Title = n.Title,
                        Message = n.Message,
                        Type = n.Type,
                        IsRead = n.IsRead,
                        CreatedAt = n.CreatedAt
                    })
                    .ToListAsync();

                return notifications;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notifications for user {UserId}", userId);
                return new List<NotificationDto>();
            }
        }

        public async Task MarkNotificationAsReadAsync(int notificationId, string userId)
        {
            try
            {
                var notification = await _context.Notifications
                    .FirstOrDefaultAsync(n => n.Id == notificationId && n.UserId == userId);

                if (notification != null)
                {
                    notification.IsRead = true;
                    notification.ReadAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking notification {NotificationId} as read", notificationId);
            }
        }

        public async Task MarkAllNotificationsAsReadAsync(string userId)
        {
            try
            {
                var notifications = await _context.Notifications
                    .Where(n => n.UserId == userId && !n.IsRead)
                    .ToListAsync();

                foreach (var notification in notifications)
                {
                    notification.IsRead = true;
                    notification.ReadAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking all notifications as read for user {UserId}", userId);
            }
        }

        public async Task<int> GetUnreadNotificationCountAsync(string userId)
        {
            try
            {
                return await _context.Notifications
                    .CountAsync(n => n.UserId == userId && !n.IsRead);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unread notification count for user {UserId}", userId);
                return 0;
            }
        }
    }

    // SignalR Hub for real-time notifications
    public class NotificationHub : Hub
    {
        private readonly ILogger<NotificationHub> _logger;

        public NotificationHub(ILogger<NotificationHub> logger)
        {
            _logger = logger;
        }

        public override async Task OnConnectedAsync()
        {
            var userId = Context.UserIdentifier;
            if (!string.IsNullOrEmpty(userId))
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, $"User_{userId}");
                _logger.LogInformation("User {UserId} connected to notification hub", userId);
            }
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            var userId = Context.UserIdentifier;
            if (!string.IsNullOrEmpty(userId))
            {
                await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"User_{userId}");
                _logger.LogInformation("User {UserId} disconnected from notification hub", userId);
            }
            await base.OnDisconnectedAsync(exception);
        }

        public async Task JoinUserGroup(string userId)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"User_{userId}");
        }

        public async Task LeaveUserGroup(string userId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"User_{userId}");
        }
    }

    // Notification model
    public class Notification : BaseEntity
    {
        public string UserId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Type { get; set; } = "info"; // info, success, warning, error
        public bool IsRead { get; set; } = false;
        public DateTime? ReadAt { get; set; }

        // Navigation properties
        public virtual ApplicationUser User { get; set; } = null!;
    }

    // Notification DTO
    public class NotificationDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public bool IsRead { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? ReadAt { get; set; }
    }
}
