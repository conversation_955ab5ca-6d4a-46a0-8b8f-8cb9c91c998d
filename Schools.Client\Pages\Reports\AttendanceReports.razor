@page "/reports/attendance"
@using Schools.Shared.DTOs
@using Schools.Client.Services
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-check me-2"></i>
                        تقارير الحضور والغياب
                    </h4>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <label class="form-label">الصف</label>
                            <select class="form-select" @bind="selectedClassId" @bind:after="LoadAttendanceData">
                                <option value="">جميع الصفوف</option>
                                @if (classes != null)
                                {
                                    @foreach (var cls in classes)
                                    {
                                        <option value="@cls.Id">@cls.Name</option>
                                    }
                                }
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" @bind="startDate" @bind:after="LoadAttendanceData" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" @bind="endDate" @bind:after="LoadAttendanceData" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">حالة الحضور</label>
                            <select class="form-select" @bind="selectedStatus" @bind:after="LoadAttendanceData">
                                <option value="">جميع الحالات</option>
                                <option value="Present">حاضر</option>
                                <option value="Absent">غائب</option>
                                <option value="Late">متأخر</option>
                                <option value="Excused">معذور</option>
                            </select>
                        </div>
                    </div>

                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-success" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else if (statistics != null)
                    {
                        <!-- Main Statistics -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-users fa-2x mb-2"></i>
                                        <h4>@statistics.TotalRecords</h4>
                                        <p class="mb-0">إجمالي السجلات</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                                        <h4>@statistics.PresentCount</h4>
                                        <p class="mb-0">حاضر</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-times-circle fa-2x mb-2"></i>
                                        <h4>@statistics.AbsentCount</h4>
                                        <p class="mb-0">غائب</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-clock fa-2x mb-2"></i>
                                        <h4>@statistics.LateCount</h4>
                                        <p class="mb-0">متأخر</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Attendance Rate -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">معدل الحضور العام</h5>
                                    </div>
                                    <div class="card-body text-center">
                                        <div class="progress mb-3" style="height: 30px;">
                                            <div class="progress-bar bg-success" style="width: @statistics.AttendanceRate%">
                                                <strong>@statistics.AttendanceRate.ToString("F1")%</strong>
                                            </div>
                                        </div>
                                        <h3 class="text-success">@statistics.AttendanceRate.ToString("F1")%</h3>
                                        <p class="text-muted">معدل الحضور الإجمالي</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">توزيع حالات الحضور</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-6 mb-3">
                                                <div class="border rounded p-2">
                                                    <h5 class="text-success">@GetPercentage(statistics.PresentCount, statistics.TotalRecords).ToString("F1")%</h5>
                                                    <small class="text-muted">حاضر</small>
                                                </div>
                                            </div>
                                            <div class="col-6 mb-3">
                                                <div class="border rounded p-2">
                                                    <h5 class="text-danger">@GetPercentage(statistics.AbsentCount, statistics.TotalRecords).ToString("F1")%</h5>
                                                    <small class="text-muted">غائب</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="border rounded p-2">
                                                    <h5 class="text-warning">@GetPercentage(statistics.LateCount, statistics.TotalRecords).ToString("F1")%</h5>
                                                    <small class="text-muted">متأخر</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="border rounded p-2">
                                                    <h5 class="text-info">@GetPercentage(statistics.ExcusedCount, statistics.TotalRecords).ToString("F1")%</h5>
                                                    <small class="text-muted">معذور</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Daily Statistics -->
                        @if (statistics.DailyStatistics?.Any() == true)
                        {
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">الإحصائيات اليومية</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-striped table-hover">
                                                    <thead class="table-dark">
                                                        <tr>
                                                            <th>التاريخ</th>
                                                            <th>إجمالي الطلاب</th>
                                                            <th>حاضر</th>
                                                            <th>غائب</th>
                                                            <th>متأخر</th>
                                                            <th>معذور</th>
                                                            <th>معدل الحضور</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach (var daily in statistics.DailyStatistics.OrderByDescending(d => d.Date))
                                                        {
                                                            <tr>
                                                                <td>
                                                                    <strong>@daily.Date.ToString("yyyy-MM-dd")</strong>
                                                                    <br>
                                                                    <small class="text-muted">@daily.Date.ToString("dddd", new System.Globalization.CultureInfo("ar-SA"))</small>
                                                                </td>
                                                                <td>
                                                                    <span class="badge bg-primary">@daily.TotalStudents</span>
                                                                </td>
                                                                <td>
                                                                    <span class="badge bg-success">@daily.PresentCount</span>
                                                                </td>
                                                                <td>
                                                                    <span class="badge bg-danger">@daily.AbsentCount</span>
                                                                </td>
                                                                <td>
                                                                    <span class="badge bg-warning">@daily.LateCount</span>
                                                                </td>
                                                                <td>
                                                                    <span class="badge bg-info">@daily.ExcusedCount</span>
                                                                </td>
                                                                <td>
                                                                    <div class="progress" style="height: 20px;">
                                                                        <div class="progress-bar bg-success"
                                                                             style="width: @GetDailyAttendanceRate(daily)%">
                                                                            @GetDailyAttendanceRate(daily).ToString("F1")%
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        <!-- Detailed Attendance Records -->
                        @if (attendanceRecords?.Any() == true)
                        {
                            <div class="row">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">سجلات الحضور التفصيلية</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-striped table-hover">
                                                    <thead class="table-dark">
                                                        <tr>
                                                            <th>الطالب</th>
                                                            <th>الصف</th>
                                                            <th>التاريخ</th>
                                                            <th>الحالة</th>
                                                            <th>وقت الوصول</th>
                                                            <th>ملاحظات</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach (var record in attendanceRecords.OrderByDescending(r => r.Date))
                                                        {
                                                            <tr class="@GetRowClass(record.Status)">
                                                                <td>
                                                                    <strong>@record.StudentName</strong>
                                                                    <br>
                                                                    <small class="text-muted">@record.StudentEmail</small>
                                                                </td>
                                                                <td>
                                                                    <span class="badge bg-info">@record.ClassName</span>
                                                                </td>
                                                                <td>@record.Date.ToString("yyyy-MM-dd")</td>
                                                                <td>
                                                                    <span class="badge @GetStatusClass(record.Status)">
                                                                        @GetStatusText(record.Status)
                                                                    </span>
                                                                </td>
                                                                <td>
                                                                    @if (!string.IsNullOrEmpty(record.ArrivalTime))
                                                                    {
                                                                        <span>@record.ArrivalTime</span>
                                                                    }
                                                                    else
                                                                    {
                                                                        <span class="text-muted">-</span>
                                                                    }
                                                                </td>
                                                                <td>
                                                                    @if (!string.IsNullOrEmpty(record.Notes))
                                                                    {
                                                                        <small>@record.Notes</small>
                                                                    }
                                                                    else
                                                                    {
                                                                        <span class="text-muted">-</span>
                                                                    }
                                                                </td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-user-check fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد بيانات حضور</h5>
                            <p class="text-muted">لم يتم العثور على سجلات حضور للمعايير المحددة</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private List<ClassDto>? classes;
    private List<AttendanceDto>? attendanceRecords;
    private AttendanceStatisticsDto? statistics;

    private bool isLoading = true;

    // Filters
    private int? selectedClassId;
    private DateTime startDate = DateTime.Today.AddDays(-30);
    private DateTime endDate = DateTime.Today;
    private string selectedStatus = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadInitialData();
        await LoadAttendanceData();
    }

    private async Task LoadInitialData()
    {
        try
        {
            classes = (await ApiService.GetClassesAsync()).ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
    }

    private async Task LoadAttendanceData()
    {
        try
        {
            isLoading = true;

            var attendanceTask = ApiService.GetAttendanceAsync(
                selectedClassId,
                null,
                null,
                1,
                1000);

            var statisticsTask = ApiService.GetAttendanceStatisticsAsync(
                selectedClassId,
                startDate,
                endDate);

            await Task.WhenAll(attendanceTask, statisticsTask);

            var allAttendance = await attendanceTask;

            // Filter by date range and status
            attendanceRecords = allAttendance
                .Where(a => a.Date >= startDate && a.Date <= endDate)
                .Where(a => string.IsNullOrEmpty(selectedStatus) || a.Status == selectedStatus)
                .ToList();

            statistics = await statisticsTask;
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل بيانات الحضور: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private double GetPercentage(int count, int total)
    {
        return total > 0 ? (double)count / total * 100 : 0;
    }

    private double GetDailyAttendanceRate(DailyAttendanceStatDto daily)
    {
        return daily.TotalStudents > 0 ? (double)daily.PresentCount / daily.TotalStudents * 100 : 0;
    }

    private string GetRowClass(string status)
    {
        return status switch
        {
            "Present" => "table-success",
            "Absent" => "table-danger",
            "Late" => "table-warning",
            "Excused" => "table-info",
            _ => ""
        };
    }

    private string GetStatusClass(string status)
    {
        return status switch
        {
            "Present" => "bg-success",
            "Absent" => "bg-danger",
            "Late" => "bg-warning",
            "Excused" => "bg-info",
            _ => "bg-secondary"
        };
    }

    private string GetStatusText(string status)
    {
        return status switch
        {
            "Present" => "حاضر",
            "Absent" => "غائب",
            "Late" => "متأخر",
            "Excused" => "معذور",
            _ => status
        };
    }
}
