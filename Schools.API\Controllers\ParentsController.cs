using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;
using System.Security.Claims;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ParentsController : ControllerBase
    {
        private readonly SchoolsDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<ParentsController> _logger;

        public ParentsController(
            SchoolsDbContext context,
            UserManager<ApplicationUser> userManager,
            ILogger<ParentsController> logger)
        {
            _context = context;
            _userManager = userManager;
            _logger = logger;
        }

        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<IEnumerable<ParentDto>>> GetParents(
            [FromQuery] string? search = null,
            [FromQuery] bool? isActive = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                // Mock data for now - replace with actual database query
                var parents = new List<ParentDto>
                {
                    new()
                    {
                        Id = "parent1",
                        FirstName = "أحمد",
                        LastName = "محمد",
                        Email = "<EMAIL>",
                        PhoneNumber = "+966501234567",
                        NationalId = "1234567890",
                        IsActive = true,
                        CreatedAt = DateTime.Now.AddMonths(-6),
                        TotalChildren = 2,
                        Occupation = "مهندس",
                        WorkPlace = "شركة التقنية المتقدمة",
                        Children = new List<ParentChildDto>
                        {
                            new() { StudentId = "student1", StudentName = "عبدالله أحمد", StudentNumber = "S001", ClassName = "الصف الثالث أ", Relationship = "Father", AverageGrade = 87.5, AttendanceRate = 95.2 },
                            new() { StudentId = "student2", StudentName = "فاطمة أحمد", StudentNumber = "S002", ClassName = "الصف الأول ب", Relationship = "Father", AverageGrade = 92.1, AttendanceRate = 98.5 }
                        }
                    },
                    new()
                    {
                        Id = "parent2",
                        FirstName = "فاطمة",
                        LastName = "علي",
                        Email = "<EMAIL>",
                        PhoneNumber = "+966507654321",
                        NationalId = "0987654321",
                        IsActive = true,
                        CreatedAt = DateTime.Now.AddMonths(-4),
                        TotalChildren = 1,
                        Occupation = "طبيبة",
                        WorkPlace = "مستشفى الملك فهد",
                        Children = new List<ParentChildDto>
                        {
                            new() { StudentId = "student3", StudentName = "محمد علي", StudentNumber = "S003", ClassName = "الصف الثاني أ", Relationship = "Mother", AverageGrade = 89.3, AttendanceRate = 96.8 }
                        }
                    },
                    new()
                    {
                        Id = "parent3",
                        FirstName = "خالد",
                        LastName = "سالم",
                        Email = "<EMAIL>",
                        PhoneNumber = "+966509876543",
                        NationalId = "1122334455",
                        IsActive = true,
                        CreatedAt = DateTime.Now.AddMonths(-8),
                        TotalChildren = 3,
                        Occupation = "معلم",
                        WorkPlace = "وزارة التعليم",
                        Children = new List<ParentChildDto>
                        {
                            new() { StudentId = "student4", StudentName = "نورا خالد", StudentNumber = "S004", ClassName = "الصف الرابع أ", Relationship = "Father", AverageGrade = 91.7, AttendanceRate = 97.3 },
                            new() { StudentId = "student5", StudentName = "سارة خالد", StudentNumber = "S005", ClassName = "الصف الثاني ب", Relationship = "Father", AverageGrade = 88.9, AttendanceRate = 94.1 },
                            new() { StudentId = "student6", StudentName = "يوسف خالد", StudentNumber = "S006", ClassName = "الصف الأول أ", Relationship = "Father", AverageGrade = 85.4, AttendanceRate = 92.7 }
                        }
                    }
                };

                // Apply filters
                if (!string.IsNullOrEmpty(search))
                {
                    parents = parents.Where(p =>
                        p.FirstName.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                        p.LastName.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                        p.Email.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                        (p.PhoneNumber?.Contains(search) ?? false) ||
                        (p.NationalId?.Contains(search) ?? false)
                    ).ToList();
                }

                if (isActive.HasValue)
                {
                    parents = parents.Where(p => p.IsActive == isActive.Value).ToList();
                }

                // Apply pagination
                var totalCount = parents.Count;
                var pagedParents = parents
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                Response.Headers.Add("X-Total-Count", totalCount.ToString());
                Response.Headers.Add("X-Page", page.ToString());
                Response.Headers.Add("X-Page-Size", pageSize.ToString());

                return Ok(pagedParents);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving parents");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ParentDto>> GetParent(string id)
        {
            try
            {
                // Check if user is admin or the parent themselves
                var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var isAdmin = User.IsInRole("Admin");

                if (!isAdmin && currentUserId != id)
                {
                    return Forbid("You can only access your own information");
                }

                // Mock data - replace with actual database query
                var parent = new ParentDto
                {
                    Id = id,
                    FirstName = "أحمد",
                    LastName = "محمد",
                    Email = "<EMAIL>",
                    PhoneNumber = "+966501234567",
                    NationalId = "1234567890",
                    DateOfBirth = new DateTime(1985, 5, 15),
                    Address = "الرياض، حي النرجس، شارع الملك فهد",
                    IsActive = true,
                    CreatedAt = DateTime.Now.AddMonths(-6),
                    TotalChildren = 2,
                    EmergencyContact = "+966502345678",
                    Occupation = "مهندس برمجيات",
                    WorkPlace = "شركة التقنية المتقدمة",
                    WorkPhone = "+966112345678",
                    Children = new List<ParentChildDto>
                    {
                        new()
                        {
                            StudentId = "student1",
                            StudentName = "عبدالله أحمد محمد",
                            StudentNumber = "S001",
                            ClassName = "الصف الثالث أ",
                            Relationship = "Father",
                            AverageGrade = 87.5,
                            AttendanceRate = 95.2,
                            PendingAssignments = 2
                        },
                        new()
                        {
                            StudentId = "student2",
                            StudentName = "فاطمة أحمد محمد",
                            StudentNumber = "S002",
                            ClassName = "الصف الأول ب",
                            Relationship = "Father",
                            AverageGrade = 92.1,
                            AttendanceRate = 98.5,
                            PendingAssignments = 1
                        }
                    }
                };

                return Ok(parent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving parent {ParentId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ParentDto>> CreateParent(CreateParentDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Check if email already exists
                var existingUser = await _userManager.FindByEmailAsync(createDto.Email);
                if (existingUser != null)
                {
                    return BadRequest("Email already exists");
                }

                // Mock creation - replace with actual user creation
                var newParent = new ParentDto
                {
                    Id = Guid.NewGuid().ToString(),
                    FirstName = createDto.FirstName,
                    LastName = createDto.LastName,
                    Email = createDto.Email,
                    PhoneNumber = createDto.PhoneNumber,
                    NationalId = createDto.NationalId,
                    DateOfBirth = createDto.DateOfBirth,
                    Address = createDto.Address,
                    EmergencyContact = createDto.EmergencyContact,
                    Occupation = createDto.Occupation,
                    WorkPlace = createDto.WorkPlace,
                    WorkPhone = createDto.WorkPhone,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    TotalChildren = 0,
                    Children = new List<ParentChildDto>()
                };

                return CreatedAtAction(nameof(GetParent), new { id = newParent.Id }, newParent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating parent");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateParent(string id, UpdateParentDto updateDto)
        {
            try
            {
                // Check if user is admin or the parent themselves
                var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var isAdmin = User.IsInRole("Admin");

                if (!isAdmin && currentUserId != id)
                {
                    return Forbid("You can only update your own information");
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Mock update - replace with actual database update
                return Ok(new { message = "Parent updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating parent {ParentId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteParent(string id)
        {
            try
            {
                // Mock deletion - replace with actual database deletion
                return Ok(new { message = "Parent deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting parent {ParentId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}/children")]
        public async Task<ActionResult<IEnumerable<ParentChildDto>>> GetParentChildren(string id)
        {
            try
            {
                // Check if user is admin or the parent themselves
                var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var isAdmin = User.IsInRole("Admin");

                if (!isAdmin && currentUserId != id)
                {
                    return Forbid("You can only access your own children information");
                }

                // Mock data - replace with actual database query
                var children = new List<ParentChildDto>
                {
                    new()
                    {
                        StudentId = "student1",
                        StudentName = "عبدالله أحمد محمد",
                        StudentNumber = "S001",
                        ClassName = "الصف الثالث أ",
                        Relationship = "Father",
                        IsActive = true,
                        AverageGrade = 87.5,
                        AttendanceRate = 95.2,
                        PendingAssignments = 2
                    },
                    new()
                    {
                        StudentId = "student2",
                        StudentName = "فاطمة أحمد محمد",
                        StudentNumber = "S002",
                        ClassName = "الصف الأول ب",
                        Relationship = "Father",
                        IsActive = true,
                        AverageGrade = 92.1,
                        AttendanceRate = 98.5,
                        PendingAssignments = 1
                    }
                };

                return Ok(children);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving children for parent {ParentId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("{parentId}/children/{studentId}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ParentStudentDto>> LinkParentStudent(string parentId, string studentId, LinkParentStudentDto linkDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (linkDto.ParentId != parentId || linkDto.StudentId != studentId)
                {
                    return BadRequest("Parent ID and Student ID in URL must match the request body");
                }

                // Mock linking - replace with actual database operation
                var parentStudent = new ParentStudentDto
                {
                    Id = new Random().Next(1000, 9999),
                    ParentId = parentId,
                    StudentId = studentId,
                    Relationship = linkDto.Relationship,
                    IsActive = true
                };

                return CreatedAtAction(nameof(GetParentStudentRelationship),
                    new { parentId, studentId }, parentStudent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error linking parent {ParentId} to student {StudentId}", parentId, studentId);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{parentId}/children/{studentId}")]
        public async Task<ActionResult<ParentStudentDto>> GetParentStudentRelationship(string parentId, string studentId)
        {
            try
            {
                // Check if user is admin or the parent themselves
                var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var isAdmin = User.IsInRole("Admin");

                if (!isAdmin && currentUserId != parentId)
                {
                    return Forbid("You can only access your own relationships");
                }

                // Mock data - replace with actual database query
                var relationship = new ParentStudentDto
                {
                    Id = 1,
                    ParentId = parentId,
                    StudentId = studentId,
                    Relationship = "Father",
                    IsActive = true,
                    Parent = new ParentDto
                    {
                        Id = parentId,
                        FirstName = "أحمد",
                        LastName = "محمد",
                        Email = "<EMAIL>"
                    },
                    Student = new StudentSummaryDto
                    {
                        Id = studentId,
                        FirstName = "عبدالله",
                        LastName = "أحمد",
                        StudentNumber = "S001",
                        ClassName = "الصف الثالث أ"
                    }
                };

                return Ok(relationship);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving relationship between parent {ParentId} and student {StudentId}", parentId, studentId);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("{parentId}/children/{studentId}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UnlinkParentStudent(string parentId, string studentId)
        {
            try
            {
                // Mock unlinking - replace with actual database operation
                return Ok(new { message = "Parent-student relationship removed successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unlinking parent {ParentId} from student {StudentId}", parentId, studentId);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}/dashboard")]
        public async Task<ActionResult<ParentDashboardDto>> GetParentDashboard(string id)
        {
            try
            {
                // Check if user is admin or the parent themselves
                var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var isAdmin = User.IsInRole("Admin");

                if (!isAdmin && currentUserId != id)
                {
                    return Forbid("You can only access your own dashboard");
                }

                // Mock dashboard data - replace with actual database queries
                var dashboard = new ParentDashboardDto
                {
                    Parent = new ParentDto
                    {
                        Id = id,
                        FirstName = "أحمد",
                        LastName = "محمد",
                        Email = "<EMAIL>",
                        TotalChildren = 2
                    },
                    Children = new List<ChildPerformanceDto>
                    {
                        new()
                        {
                            StudentId = "student1",
                            StudentName = "عبدالله أحمد محمد",
                            StudentNumber = "S001",
                            ClassName = "الصف الثالث أ",
                            AverageGrade = 87.5,
                            AttendanceRate = 95.2,
                            PendingAssignments = 2,
                            TotalAssignments = 15,
                            PerformanceTrend = "improving",
                            RecentGrades = new List<RecentGradeDto>
                            {
                                new() { SubjectName = "الرياضيات", Score = 85, MaxScore = 100, Percentage = 85, Grade = "B+", Date = DateTime.Now.AddDays(-3), ExamType = "Quiz" },
                                new() { SubjectName = "العلوم", Score = 92, MaxScore = 100, Percentage = 92, Grade = "A-", Date = DateTime.Now.AddDays(-5), ExamType = "Assignment" }
                            },
                            RecentAttendance = new List<AttendanceRecordDto>
                            {
                                new() { Date = DateTime.Today, IsPresent = true, CheckInTime = new TimeSpan(7, 30, 0) },
                                new() { Date = DateTime.Today.AddDays(-1), IsPresent = true, CheckInTime = new TimeSpan(7, 25, 0) },
                                new() { Date = DateTime.Today.AddDays(-2), IsPresent = false, Reason = "مرض" }
                            }
                        }
                    },
                    RecentNotifications = new List<NotificationDto>
                    {
                        new() { Id = 1, Title = "درجة جديدة", Message = "تم رصد درجة جديدة في مادة الرياضيات", Type = "grade", CreatedAt = DateTime.Now.AddHours(-2), IsRead = false, StudentName = "عبدالله" },
                        new() { Id = 2, Title = "واجب منزلي", Message = "يوجد واجب منزلي جديد في مادة العلوم", Type = "assignment", CreatedAt = DateTime.Now.AddHours(-5), IsRead = true, StudentName = "عبدالله" }
                    },
                    UpcomingEvents = new List<UpcomingEventDto>
                    {
                        new() { Id = 1, Title = "اجتماع أولياء الأمور", Description = "اجتماع دوري مع أولياء الأمور", StartDate = DateTime.Now.AddDays(3), Location = "القاعة الكبرى", Category = "meeting" },
                        new() { Id = 2, Title = "معرض العلوم", Description = "معرض مشاريع الطلاب العلمية", StartDate = DateTime.Now.AddDays(7), Location = "مختبر العلوم", Category = "academic" }
                    },
                    Statistics = new ParentStatisticsDto
                    {
                        TotalChildren = 2,
                        OverallAverageGrade = 89.8,
                        OverallAttendanceRate = 96.8,
                        TotalPendingAssignments = 3,
                        UnreadNotifications = 1,
                        UpcomingEvents = 2
                    }
                };

                return Ok(dashboard);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving dashboard for parent {ParentId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}/children/{studentId}/grades")]
        public async Task<ActionResult<IEnumerable<RecentGradeDto>>> GetChildGrades(
            string id,
            string studentId,
            [FromQuery] string? subject = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                // Check if user is admin or the parent themselves
                var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var isAdmin = User.IsInRole("Admin");

                if (!isAdmin && currentUserId != id)
                {
                    return Forbid("You can only access your own children's grades");
                }

                // Mock grades data - replace with actual database query
                var grades = new List<RecentGradeDto>
                {
                    new() { Id = 1, SubjectName = "الرياضيات", Score = 85, MaxScore = 100, Percentage = 85, Grade = "B+", Date = DateTime.Now.AddDays(-3), ExamType = "Quiz" },
                    new() { Id = 2, SubjectName = "العلوم", Score = 92, MaxScore = 100, Percentage = 92, Grade = "A-", Date = DateTime.Now.AddDays(-5), ExamType = "Assignment" },
                    new() { Id = 3, SubjectName = "اللغة العربية", Score = 88, MaxScore = 100, Percentage = 88, Grade = "B+", Date = DateTime.Now.AddDays(-7), ExamType = "Test" },
                    new() { Id = 4, SubjectName = "الإنجليزية", Score = 90, MaxScore = 100, Percentage = 90, Grade = "A-", Date = DateTime.Now.AddDays(-10), ExamType = "Quiz" },
                    new() { Id = 5, SubjectName = "التاريخ", Score = 87, MaxScore = 100, Percentage = 87, Grade = "B+", Date = DateTime.Now.AddDays(-12), ExamType = "Assignment" }
                };

                // Apply filters
                if (!string.IsNullOrEmpty(subject))
                {
                    grades = grades.Where(g => g.SubjectName.Contains(subject, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                if (startDate.HasValue)
                {
                    grades = grades.Where(g => g.Date >= startDate.Value).ToList();
                }

                if (endDate.HasValue)
                {
                    grades = grades.Where(g => g.Date <= endDate.Value).ToList();
                }

                return Ok(grades.OrderByDescending(g => g.Date));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving grades for student {StudentId} of parent {ParentId}", studentId, id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}/children/{studentId}/attendance")]
        public async Task<ActionResult<IEnumerable<AttendanceRecordDto>>> GetChildAttendance(
            string id,
            string studentId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                // Check if user is admin or the parent themselves
                var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var isAdmin = User.IsInRole("Admin");

                if (!isAdmin && currentUserId != id)
                {
                    return Forbid("You can only access your own children's attendance");
                }

                // Mock attendance data - replace with actual database query
                var attendance = new List<AttendanceRecordDto>();
                var currentDate = DateTime.Today;

                for (int i = 0; i < 30; i++)
                {
                    var date = currentDate.AddDays(-i);
                    if (date.DayOfWeek != DayOfWeek.Friday && date.DayOfWeek != DayOfWeek.Saturday)
                    {
                        var isPresent = new Random().Next(1, 101) > 5; // 95% attendance rate
                        attendance.Add(new AttendanceRecordDto
                        {
                            Date = date,
                            IsPresent = isPresent,
                            Reason = isPresent ? null : "غياب بعذر",
                            CheckInTime = isPresent ? new TimeSpan(7, 30 + new Random().Next(-10, 10), 0) : null,
                            CheckOutTime = isPresent ? new TimeSpan(14, 30 + new Random().Next(-10, 10), 0) : null
                        });
                    }
                }

                // Apply filters
                if (startDate.HasValue)
                {
                    attendance = attendance.Where(a => a.Date >= startDate.Value).ToList();
                }

                if (endDate.HasValue)
                {
                    attendance = attendance.Where(a => a.Date <= endDate.Value).ToList();
                }

                return Ok(attendance.OrderByDescending(a => a.Date));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving attendance for student {StudentId} of parent {ParentId}", studentId, id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}/notifications")]
        public async Task<ActionResult<IEnumerable<NotificationDto>>> GetParentNotifications(
            string id,
            [FromQuery] bool? isRead = null,
            [FromQuery] string? type = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                // Check if user is admin or the parent themselves
                var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var isAdmin = User.IsInRole("Admin");

                if (!isAdmin && currentUserId != id)
                {
                    return Forbid("You can only access your own notifications");
                }

                // Mock notifications data - replace with actual database query
                var notifications = new List<NotificationDto>
                {
                    new() { Id = 1, Title = "درجة جديدة", Message = "تم رصد درجة جديدة في مادة الرياضيات", Type = "grade", CreatedAt = DateTime.Now.AddHours(-2), IsRead = false, StudentName = "عبدالله" },
                    new() { Id = 2, Title = "واجب منزلي", Message = "يوجد واجب منزلي جديد في مادة العلوم", Type = "assignment", CreatedAt = DateTime.Now.AddHours(-5), IsRead = true, StudentName = "عبدالله" },
                    new() { Id = 3, Title = "غياب", Message = "تم تسجيل غياب لطفلك اليوم", Type = "attendance", CreatedAt = DateTime.Now.AddDays(-1), IsRead = true, StudentName = "فاطمة" },
                    new() { Id = 4, Title = "فعالية مدرسية", Message = "دعوة لحضور معرض العلوم", Type = "event", CreatedAt = DateTime.Now.AddDays(-2), IsRead = false, StudentName = null },
                    new() { Id = 5, Title = "اجتماع أولياء الأمور", Message = "اجتماع دوري مع أولياء الأمور الأسبوع القادم", Type = "meeting", CreatedAt = DateTime.Now.AddDays(-3), IsRead = true, StudentName = null }
                };

                // Apply filters
                if (isRead.HasValue)
                {
                    notifications = notifications.Where(n => n.IsRead == isRead.Value).ToList();
                }

                if (!string.IsNullOrEmpty(type))
                {
                    notifications = notifications.Where(n => n.Type.Equals(type, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                // Apply pagination
                var totalCount = notifications.Count;
                var pagedNotifications = notifications
                    .OrderByDescending(n => n.CreatedAt)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                Response.Headers.Add("X-Total-Count", totalCount.ToString());
                Response.Headers.Add("X-Page", page.ToString());
                Response.Headers.Add("X-Page-Size", pageSize.ToString());

                return Ok(pagedNotifications);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving notifications for parent {ParentId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("{id}/notifications/{notificationId}/read")]
        public async Task<IActionResult> MarkNotificationAsRead(string id, int notificationId)
        {
            try
            {
                // Check if user is admin or the parent themselves
                var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var isAdmin = User.IsInRole("Admin");

                if (!isAdmin && currentUserId != id)
                {
                    return Forbid("You can only mark your own notifications as read");
                }

                // Mock operation - replace with actual database update
                return Ok(new { message = "Notification marked as read" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking notification {NotificationId} as read for parent {ParentId}", notificationId, id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("statistics")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<object>> GetParentsStatistics()
        {
            try
            {
                // Mock statistics - replace with actual database queries
                var statistics = new
                {
                    TotalParents = 156,
                    ActiveParents = 148,
                    InactiveParents = 8,
                    ParentsWithMultipleChildren = 45,
                    AverageChildrenPerParent = 1.8,
                    ParentsByRelationship = new Dictionary<string, int>
                    {
                        { "Father", 89 },
                        { "Mother", 67 },
                        { "Guardian", 12 }
                    },
                    RecentRegistrations = new
                    {
                        ThisMonth = 8,
                        LastMonth = 12,
                        ThisYear = 89
                    },
                    EngagementMetrics = new
                    {
                        LoginRate = 78.5,
                        NotificationReadRate = 85.2,
                        EventAttendanceRate = 67.3
                    }
                };

                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving parents statistics");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
