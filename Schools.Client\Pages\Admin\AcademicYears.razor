@page "/admin/academic-years"
@using Schools.Shared.Models
@using Schools.Shared.DTOs
@using Schools.Client.Services
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@attribute [Authorize(Roles = "Admin")]

<PageTitle>إدارة الأعوام الدراسية</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        إدارة الأعوام الدراسية
                    </h4>
                    <button class="btn btn-light" @onclick="ShowAddModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة عام دراسي
                    </button>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else if (academicYears?.Any() == true)
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>اسم العام الدراسي</th>
                                        <th>تاريخ البداية</th>
                                        <th>تاريخ النهاية</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var year in academicYears)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@year.Name</strong>
                                                @if (year.IsActive)
                                                {
                                                    <span class="badge bg-success ms-2">نشط</span>
                                                }
                                            </td>
                                            <td>@year.StartDate.ToString("dd/MM/yyyy")</td>
                                            <td>@year.EndDate.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                @if (year.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-primary" @onclick="() => EditYear(year)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    @if (!year.IsActive)
                                                    {
                                                        <button class="btn btn-sm btn-outline-success" @onclick="() => ActivateYear(year.Id)">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    }
                                                    <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteYear(year.Id)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد أعوام دراسية</h5>
                            <p class="text-muted">ابدأ بإضافة العام الدراسي الأول</p>
                            <button class="btn btn-primary" @onclick="ShowAddModal">
                                <i class="fas fa-plus me-2"></i>
                                إضافة عام دراسي
                            </button>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Modal -->
@if (showModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        @if (editingYear == null)
                        {
                            <text>إضافة عام دراسي جديد</text>
                        }
                        else
                        {
                            <text>تعديل العام الدراسي</text>
                        }
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseModal"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="yearModel" OnValidSubmit="SaveYear">
                        <DataAnnotationsValidator />
                        <div class="mb-3">
                            <label class="form-label">اسم العام الدراسي</label>
                            <InputText class="form-control" @bind-Value="yearModel.Name" placeholder="مثال: 2024-2025" />
                            <ValidationMessage For="() => yearModel.Name" class="text-danger" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">تاريخ البداية</label>
                            <InputDate class="form-control" @bind-Value="yearModel.StartDate" />
                            <ValidationMessage For="() => yearModel.StartDate" class="text-danger" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">تاريخ النهاية</label>
                            <InputDate class="form-control" @bind-Value="yearModel.EndDate" />
                            <ValidationMessage For="() => yearModel.EndDate" class="text-danger" />
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" @onclick="CloseModal">إلغاء</button>
                            <button type="submit" class="btn btn-primary" disabled="@isSaving">
                                @if (isSaving)
                                {
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                }
                                حفظ
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<AcademicYear>? academicYears;
    private bool isLoading = true;
    private bool showModal = false;
    private bool isSaving = false;
    private AcademicYear? editingYear;
    private CreateAcademicYearDto yearModel = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadAcademicYears();
    }

    private async Task LoadAcademicYears()
    {
        try
        {
            isLoading = true;
            var academicYearDtos = await ApiService.GetAcademicYearsAsync();
            academicYears = academicYearDtos.Select(dto => new AcademicYear
            {
                Id = dto.Id,
                Name = dto.Name,
                StartDate = dto.StartDate,
                EndDate = dto.EndDate,
                IsActive = dto.IsActive
            }).ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void ShowAddModal()
    {
        editingYear = null;
        yearModel = new CreateAcademicYearDto();
        showModal = true;
    }

    private void EditYear(AcademicYear year)
    {
        editingYear = year;
        yearModel = new CreateAcademicYearDto
        {
            Name = year.Name,
            StartDate = year.StartDate,
            EndDate = year.EndDate
        };
        showModal = true;
    }

    private void CloseModal()
    {
        showModal = false;
        editingYear = null;
        yearModel = new();
    }

    private async Task SaveYear()
    {
        try
        {
            isSaving = true;

            if (editingYear == null)
            {
                await ApiService.CreateAcademicYearAsync(yearModel);
                await JSRuntime.InvokeVoidAsync("alert", "تم إضافة العام الدراسي بنجاح");
            }
            else
            {
                await ApiService.UpdateAcademicYearAsync(editingYear.Id, yearModel);
                await JSRuntime.InvokeVoidAsync("alert", "تم تحديث العام الدراسي بنجاح");
            }

            CloseModal();
            await LoadAcademicYears();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ البيانات: {ex.Message}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task ActivateYear(int yearId)
    {
        try
        {
            await ApiService.ActivateAcademicYearAsync(yearId);
            await JSRuntime.InvokeVoidAsync("alert", "تم تفعيل العام الدراسي بنجاح");
            await LoadAcademicYears();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تفعيل العام الدراسي: {ex.Message}");
        }
    }

    private async Task DeleteYear(int yearId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا العام الدراسي؟"))
        {
            try
            {
                await ApiService.DeleteAcademicYearAsync(yearId);
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف العام الدراسي بنجاح");
                await LoadAcademicYears();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف العام الدراسي: {ex.Message}");
            }
        }
    }
}
