@page "/student/exam/{ExamId:int}"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Student")]

<PageTitle>أداء الامتحان - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل الامتحان...</p>
        </div>
    }
    else if (exam == null)
    {
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h5 class="text-muted">الامتحان غير متاح</h5>
            <p class="text-muted">لم يتم العثور على الامتحان أو أنه غير متاح حالياً</p>
            <a href="/student/exams" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للامتحانات
            </a>
        </div>
    }
    else if (currentAttempt == null && !canStartExam)
    {
        <!-- Exam Information -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card border-0 shadow-lg">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات الامتحان
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-12">
                                <h5 class="text-primary">@exam.Title</h5>
                                <p class="text-muted">@exam.Description</p>
                            </div>

                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-book text-primary me-3"></i>
                                    <div>
                                        <strong>المادة:</strong>
                                        <span class="ms-2">@exam.SubjectName</span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-clock text-info me-3"></i>
                                    <div>
                                        <strong>المدة:</strong>
                                        <span class="ms-2">@exam.DurationMinutes دقيقة</span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-star text-warning me-3"></i>
                                    <div>
                                        <strong>إجمالي الدرجات:</strong>
                                        <span class="ms-2">@exam.TotalMarks درجة</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-question-circle text-success me-3"></i>
                                    <div>
                                        <strong>عدد الأسئلة:</strong>
                                        <span class="ms-2">@exam.TotalQuestions سؤال</span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-check-circle text-success me-3"></i>
                                    <div>
                                        <strong>درجة النجاح:</strong>
                                        <span class="ms-2">@exam.PassingMarks درجة</span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-redo text-secondary me-3"></i>
                                    <div>
                                        <strong>المحاولات المتاحة:</strong>
                                        <span class="ms-2">@(exam.MaxAttempts - attemptsUsed) من @exam.MaxAttempts</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if (!string.IsNullOrEmpty(examUnavailableReason))
                        {
                            <div class="alert alert-warning mt-4">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                @examUnavailableReason
                            </div>
                        }
                        else
                        {
                            <div class="alert alert-info mt-4">
                                <h6 class="alert-heading">تعليمات مهمة:</h6>
                                <ul class="mb-0">
                                    <li>تأكد من اتصالك بالإنترنت قبل البدء</li>
                                    <li>لا يمكنك العودة للسؤال السابق بعد الانتقال للتالي</li>
                                    <li>سيتم حفظ إجاباتك تلقائياً</li>
                                    <li>سيتم إنهاء الامتحان تلقائياً عند انتهاء الوقت</li>
                                </ul>
                            </div>
                        }
                    </div>
                    <div class="card-footer text-center">
                        @if (canStartExam)
                        {
                            <button class="btn btn-success btn-lg" @onclick="StartExam">
                                <i class="fas fa-play me-2"></i>
                                بدء الامتحان
                            </button>
                        }
                        else
                        {
                            <button class="btn btn-secondary btn-lg" disabled>
                                <i class="fas fa-lock me-2"></i>
                                غير متاح حالياً
                            </button>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
    else if (currentAttempt != null && currentAttempt.Status == AttemptStatus.InProgress)
    {
        <!-- Exam Interface -->
        <div class="row">
            <!-- Timer and Progress -->
            <div class="col-12">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <h5 class="mb-0">@exam.Title</h5>
                                <small class="text-muted">السؤال @(currentQuestionIndex + 1) من @exam.TotalQuestions</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="progress" style="height: 10px;">
                                    <div class="progress-bar bg-success" style="width: @(((double)(currentQuestionIndex + 1) / exam.TotalQuestions) * 100)%"></div>
                                </div>
                                <small class="text-muted">@(((double)(currentQuestionIndex + 1) / exam.TotalQuestions) * 100).ToString("F0")% مكتمل</small>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="timer-display">
                                    <i class="fas fa-clock text-warning me-2"></i>
                                    <span class="timer-text" id="timer">@FormatTime(remainingMinutes)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Question -->
            <div class="col-12">
                @if (currentQuestion != null)
                {
                    <div class="card border-0 shadow-lg">
                        <div class="card-header bg-light">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h6 class="mb-0">
                                        <span class="badge bg-primary me-2">السؤال @(currentQuestionIndex + 1)</span>
                                        <span class="badge bg-info">@currentQuestion.Marks درجة</span>
                                    </h6>
                                </div>
                                <div class="col-auto">
                                    <span class="badge @(currentQuestion.IsRequired ? "bg-danger" : "bg-secondary")">
                                        @(currentQuestion.IsRequired ? "إجباري" : "اختياري")
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="question-content mb-4">
                                <h5 class="question-text">@currentQuestion.QuestionText</h5>

                                @if (!string.IsNullOrEmpty(currentQuestion.ImageUrl))
                                {
                                    <div class="question-image mt-3">
                                        <img src="@currentQuestion.ImageUrl" class="img-fluid rounded" alt="صورة السؤال" />
                                    </div>
                                }
                            </div>

                            <!-- Answer Options -->
                            <div class="answer-options">
                                @if (currentQuestion.Type == QuestionType.MultipleChoice)
                                {
                                    <div class="row g-3">
                                        @foreach (var option in currentQuestion.Options)
                                        {
                                            <div class="col-md-6">
                                                <div class="form-check option-card">
                                                    <input class="form-check-input" type="radio"
                                                           name="<EMAIL>"
                                                           id="<EMAIL>"
                                                           value="@option.Id"
                                                           @onchange="@((e) => SelectOption(option.Id))"
                                                           checked="@(selectedAnswers.ContainsKey(currentQuestion.Id) && selectedAnswers[currentQuestion.Id].SelectedOptionId == option.Id)" />
                                                    <label class="form-check-label w-100" for="<EMAIL>">
                                                        <div class="option-content">
                                                            @option.OptionText
                                                            @if (!string.IsNullOrEmpty(option.ImageUrl))
                                                            {
                                                                <img src="@option.ImageUrl" class="option-image mt-2" alt="صورة الخيار" />
                                                            }
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                }
                                else if (currentQuestion.Type == QuestionType.TrueFalse)
                                {
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="form-check option-card">
                                                <input class="form-check-input" type="radio"
                                                       name="<EMAIL>"
                                                       id="<EMAIL>"
                                                       value="true"
                                                       @onchange="@((e) => SelectTextAnswer("صحيح"))"
                                                       checked="@(selectedAnswers.ContainsKey(currentQuestion.Id) && selectedAnswers[currentQuestion.Id].AnswerText == "صحيح")" />
                                                <label class="form-check-label w-100" for="<EMAIL>">
                                                    <div class="option-content text-center">
                                                        <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                                                        <div>صحيح</div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check option-card">
                                                <input class="form-check-input" type="radio"
                                                       name="<EMAIL>"
                                                       id="<EMAIL>"
                                                       value="false"
                                                       @onchange="@((e) => SelectTextAnswer("خطأ"))"
                                                       checked="@(selectedAnswers.ContainsKey(currentQuestion.Id) && selectedAnswers[currentQuestion.Id].AnswerText == "خطأ")" />
                                                <label class="form-check-label w-100" for="<EMAIL>">
                                                    <div class="option-content text-center">
                                                        <i class="fas fa-times-circle text-danger fa-2x mb-2"></i>
                                                        <div>خطأ</div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                }
                                else if (currentQuestion.Type == QuestionType.ShortAnswer || currentQuestion.Type == QuestionType.Essay)
                                {
                                    <div class="form-group">
                                        <textarea class="form-control"
                                                  rows="@(currentQuestion.Type == QuestionType.Essay ? 8 : 3)"
                                                  placeholder="اكتب إجابتك هنا..."
                                                  @bind="textAnswer"
                                                  @bind:after="SaveTextAnswer"></textarea>
                                    </div>
                                }
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="row align-items-center">
                                <div class="col">
                                    @if (currentQuestionIndex > 0)
                                    {
                                        <button class="btn btn-outline-secondary" @onclick="PreviousQuestion">
                                            <i class="fas fa-arrow-right me-2"></i>
                                            السؤال السابق
                                        </button>
                                    }
                                </div>
                                <div class="col-auto">
                                    @if (currentQuestionIndex < exam.TotalQuestions - 1)
                                    {
                                        <button class="btn btn-primary" @onclick="NextQuestion">
                                            السؤال التالي
                                            <i class="fas fa-arrow-left ms-2"></i>
                                        </button>
                                    }
                                    else
                                    {
                                        <button class="btn btn-success" @onclick="() => SubmitExam()">
                                            <i class="fas fa-check me-2"></i>
                                            إنهاء الامتحان
                                        </button>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
    else if (examResult != null)
    {
        <!-- Exam Results -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card border-0 shadow-lg">
                    <div class="card-header @(examResult.IsPassed ? "bg-success" : "bg-danger") text-white text-center">
                        <h4 class="mb-0">
                            <i class="fas @(examResult.IsPassed ? "fa-check-circle" : "fa-times-circle") me-2"></i>
                            @(examResult.IsPassed ? "مبروك! لقد نجحت" : "للأسف، لم تنجح")
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <div class="result-score">
                                <h2 class="@(examResult.IsPassed ? "text-success" : "text-danger")">
                                    @examResult.MarksObtained / @examResult.TotalMarks
                                </h2>
                                <p class="text-muted">@examResult.Percentage.ToString("F1")%</p>
                                <span class="badge @(examResult.IsPassed ? "bg-success" : "bg-danger") fs-6">
                                    @examResult.Grade
                                </span>
                            </div>
                        </div>

                        <div class="row g-3 mb-4">
                            <div class="col-md-3 text-center">
                                <div class="stat-item">
                                    <h5 class="text-primary">@examResult.CorrectAnswers</h5>
                                    <small class="text-muted">إجابات صحيحة</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="stat-item">
                                    <h5 class="text-danger">@(examResult.TotalQuestions - examResult.CorrectAnswers)</h5>
                                    <small class="text-muted">إجابات خاطئة</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="stat-item">
                                    <h5 class="text-info">@examResult.DurationTaken دقيقة</h5>
                                    <small class="text-muted">الوقت المستغرق</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="stat-item">
                                    <h5 class="text-warning">@examResult.AttemptId</h5>
                                    <small class="text-muted">رقم المحاولة</small>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <a href="/student/exams" class="btn btn-primary me-2">
                                <i class="fas fa-list me-2"></i>
                                العودة للامتحانات
                            </a>
                            @if (exam.AllowRetake && attemptsUsed < exam.MaxAttempts)
                            {
                                <button class="btn btn-outline-primary" @onclick="RetakeExam">
                                    <i class="fas fa-redo me-2"></i>
                                    إعادة المحاولة
                                </button>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .timer-display {
        font-size: 1.2rem;
        font-weight: bold;
    }

    .timer-text {
        color: #dc3545;
    }

    .option-card {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .option-card:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }

    .option-card .form-check-input:checked ~ .form-check-label .option-content {
        background-color: #e3f2fd;
        border-color: #007bff;
    }

    .option-content {
        padding: 10px;
        border-radius: 6px;
        transition: all 0.3s ease;
    }

    .option-image {
        max-width: 100%;
        max-height: 150px;
        border-radius: 4px;
    }

    .question-image img {
        max-height: 300px;
        border: 1px solid #dee2e6;
    }

    .result-score h2 {
        font-size: 3rem;
        font-weight: bold;
    }

    .stat-item {
        padding: 15px;
        border-radius: 8px;
        background-color: #f8f9fa;
    }

    .question-text {
        line-height: 1.6;
        margin-bottom: 20px;
    }
</style>

@code {
    [Parameter] public int ExamId { get; set; }

    private ExamDto? exam;
    private ExamAttemptDto? currentAttempt;
    private ExamResultDto? examResult;
    private List<ExamQuestionDto>? questions;
    private ExamQuestionDto? currentQuestion;
    private int currentQuestionIndex = 0;
    private int remainingMinutes = 0;
    private int attemptsUsed = 0;
    private bool isLoading = true;
    private bool canStartExam = false;
    private string examUnavailableReason = "";
    private string textAnswer = "";

    private Dictionary<int, SubmitAnswerDto> selectedAnswers = new();
    private Timer? examTimer;

    protected override async Task OnInitializedAsync()
    {
        await LoadExamData();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && currentAttempt?.Status == AttemptStatus.InProgress)
        {
            await StartTimer();
        }
    }

    private async Task LoadExamData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            exam = await ApiService.GetExamAsync(ExamId);
            if (exam == null)
            {
                return;
            }

            // Check if student can take the exam
            var examStatus = await ApiService.GetStudentExamStatusAsync(ExamId);
            canStartExam = examStatus.CanTakeExam;
            attemptsUsed = examStatus.AttemptsUsed;
            examUnavailableReason = examStatus.UnavailableReason ?? "";

            // Check for existing attempt
            currentAttempt = await ApiService.GetCurrentExamAttemptAsync(ExamId);

            if (currentAttempt?.Status == AttemptStatus.InProgress)
            {
                questions = exam.Questions;
                if (questions?.Any() == true)
                {
                    currentQuestion = questions[currentQuestionIndex];
                    remainingMinutes = currentAttempt.RemainingMinutes;

                    // Load existing answers
                    foreach (var answer in currentAttempt.Answers)
                    {
                        selectedAnswers[answer.QuestionId] = new SubmitAnswerDto
                        {
                            AttemptId = currentAttempt.Id,
                            QuestionId = answer.QuestionId,
                            AnswerText = answer.AnswerText,
                            SelectedOptionId = answer.SelectedOptionId,
                            SelectedOptionIds = answer.SelectedOptionIds
                        };
                    }
                }
            }
            else if (currentAttempt?.Status == AttemptStatus.Completed || currentAttempt?.Status == AttemptStatus.Submitted)
            {
                examResult = await ApiService.GetExamResultAsync(currentAttempt.Id);
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الامتحان: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task StartExam()
    {
        try
        {
            var startDto = new StartExamDto { ExamId = ExamId };
            currentAttempt = await ApiService.StartExamAsync(startDto);

            if (currentAttempt != null)
            {
                questions = exam?.Questions;
                if (questions?.Any() == true)
                {
                    currentQuestion = questions[currentQuestionIndex];
                    remainingMinutes = currentAttempt.DurationMinutes;
                    await StartTimer();
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في بدء الامتحان: {ex.Message}");
        }

        StateHasChanged();
    }

    private async Task StartTimer()
    {
        examTimer?.Dispose();
        examTimer = new Timer(async _ =>
        {
            remainingMinutes--;
            await InvokeAsync(() =>
            {
                StateHasChanged();
                if (remainingMinutes <= 0)
                {
                    _ = Task.Run(async () => await TimeExpired());
                }
            });
        }, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    private async Task TimeExpired()
    {
        examTimer?.Dispose();
        await SubmitExam(true);
    }

    private void SelectOption(int optionId)
    {
        if (currentQuestion == null) return;

        selectedAnswers[currentQuestion.Id] = new SubmitAnswerDto
        {
            AttemptId = currentAttempt!.Id,
            QuestionId = currentQuestion.Id,
            SelectedOptionId = optionId
        };

        // Auto-save answer
        _ = Task.Run(async () => await SaveAnswer());
    }

    private void SelectTextAnswer(string answer)
    {
        if (currentQuestion == null) return;

        textAnswer = answer;
        selectedAnswers[currentQuestion.Id] = new SubmitAnswerDto
        {
            AttemptId = currentAttempt!.Id,
            QuestionId = currentQuestion.Id,
            AnswerText = answer
        };

        // Auto-save answer
        _ = Task.Run(async () => await SaveAnswer());
    }

    private void SaveTextAnswer()
    {
        SelectTextAnswer(textAnswer);
    }

    private async Task SaveAnswer()
    {
        if (currentQuestion == null || !selectedAnswers.ContainsKey(currentQuestion.Id)) return;

        try
        {
            await ApiService.SaveExamAnswerAsync(selectedAnswers[currentQuestion.Id]);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving answer: {ex.Message}");
        }
    }

    private void NextQuestion()
    {
        if (currentQuestionIndex < (questions?.Count ?? 0) - 1)
        {
            currentQuestionIndex++;
            currentQuestion = questions![currentQuestionIndex];

            // Load existing answer for this question
            if (selectedAnswers.ContainsKey(currentQuestion.Id))
            {
                textAnswer = selectedAnswers[currentQuestion.Id].AnswerText ?? "";
            }
            else
            {
                textAnswer = "";
            }
        }
    }

    private void PreviousQuestion()
    {
        if (currentQuestionIndex > 0)
        {
            currentQuestionIndex--;
            currentQuestion = questions![currentQuestionIndex];

            // Load existing answer for this question
            if (selectedAnswers.ContainsKey(currentQuestion.Id))
            {
                textAnswer = selectedAnswers[currentQuestion.Id].AnswerText ?? "";
            }
            else
            {
                textAnswer = "";
            }
        }
    }

    private async Task SubmitExam(bool timeExpired = false)
    {
        if (currentAttempt == null) return;

        var confirmMessage = timeExpired
            ? "انتهى وقت الامتحان. سيتم إرسال إجاباتك تلقائياً."
            : "هل أنت متأكد من إنهاء الامتحان؟ لن تتمكن من تعديل إجاباتك بعد ذلك.";

        if (timeExpired || await JSRuntime.InvokeAsync<bool>("confirm", confirmMessage))
        {
            try
            {
                examTimer?.Dispose();

                var submitDto = new SubmitExamDto
                {
                    AttemptId = currentAttempt.Id,
                    Answers = selectedAnswers.Values.ToList()
                };

                examResult = await ApiService.SubmitExamAsync(submitDto);
                currentAttempt = null;

                if (!timeExpired)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "تم إرسال الامتحان بنجاح!");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في إرسال الامتحان: {ex.Message}");
            }

            StateHasChanged();
        }
    }

    private async Task RetakeExam()
    {
        examResult = null;
        currentAttempt = null;
        selectedAnswers.Clear();
        currentQuestionIndex = 0;
        await LoadExamData();
    }

    private string FormatTime(int minutes)
    {
        var hours = minutes / 60;
        var mins = minutes % 60;
        return $"{hours:D2}:{mins:D2}";
    }

    public void Dispose()
    {
        examTimer?.Dispose();
    }
}
