@page "/parent/dashboard"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Parent")]

<PageTitle>لوحة تحكم ولي الأمر - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white border-0 shadow-lg">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-1">
                                <i class="fas fa-home me-2"></i>
                                مرحباً، @(parentData?.Parent?.FirstName ?? "ولي الأمر")
                            </h2>
                            <p class="mb-0 opacity-75">
                                <i class="fas fa-calendar me-2"></i>
                                @DateTime.Now.ToString("dddd، dd MMMM yyyy", new System.Globalization.CultureInfo("ar-SA"))
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="d-flex justify-content-end align-items-center">
                                <div class="me-3">
                                    <small class="d-block opacity-75">عدد الأبناء</small>
                                    <h4 class="mb-0">@(parentData?.Statistics?.TotalChildren ?? 0)</h4>
                                </div>
                                <div class="avatar-lg bg-white bg-opacity-20 rounded-circle d-flex align-items-center justify-content-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="avatar-md bg-success text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                        <i class="fas fa-star fa-lg"></i>
                    </div>
                    <h4 class="text-success mb-1">@(parentData?.Statistics?.OverallAverageGrade.ToString("F1") ?? "0.0")</h4>
                    <p class="text-muted mb-0">المعدل العام للأبناء</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="avatar-md bg-info text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                        <i class="fas fa-calendar-check fa-lg"></i>
                    </div>
                    <h4 class="text-info mb-1">@(parentData?.Statistics?.OverallAttendanceRate.ToString("F1") ?? "0.0")%</h4>
                    <p class="text-muted mb-0">معدل الحضور العام</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="avatar-md bg-warning text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                        <i class="fas fa-tasks fa-lg"></i>
                    </div>
                    <h4 class="text-warning mb-1">@(parentData?.Statistics?.TotalPendingAssignments ?? 0)</h4>
                    <p class="text-muted mb-0">الواجبات المعلقة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="avatar-md bg-danger text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                        <i class="fas fa-bell fa-lg"></i>
                    </div>
                    <h4 class="text-danger mb-1">@(parentData?.Statistics?.UnreadNotifications ?? 0)</h4>
                    <p class="text-muted mb-0">إشعارات غير مقروءة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Children Performance Section -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">
                                <i class="fas fa-child me-2 text-primary"></i>
                                أداء الأبناء
                            </h5>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-outline-primary btn-sm" @onclick="RefreshData">
                                <i class="fas fa-sync-alt me-2"></i>
                                تحديث البيانات
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2 text-muted">جاري تحميل بيانات الأبناء...</p>
                        </div>
                    }
                    else if (parentData?.Children?.Any() == true)
                    {
                        <div class="row g-4">
                            @foreach (var child in parentData.Children)
                            {
                                <div class="col-lg-6">
                                    <div class="card border h-100 child-card" @onclick="() => ViewChildDetails(child.StudentId)">
                                        <div class="card-body">
                                            <div class="row align-items-center mb-3">
                                                <div class="col-auto">
                                                    <div class="avatar-lg bg-primary text-white rounded-circle d-flex align-items-center justify-content-center">
                                                        @if (!string.IsNullOrEmpty(child.ProfilePicture))
                                                        {
                                                            <img src="@child.ProfilePicture" class="rounded-circle" style="width: 60px; height: 60px; object-fit: cover;" alt="@child.StudentName" />
                                                        }
                                                        else
                                                        {
                                                            <span class="fs-4">@child.StudentName.Substring(0, 1)</span>
                                                        }
                                                    </div>
                                                </div>
                                                <div class="col">
                                                    <h6 class="mb-1">@child.StudentName</h6>
                                                    <p class="text-muted mb-0">
                                                        <i class="fas fa-id-card me-1"></i>
                                                        @child.StudentNumber
                                                    </p>
                                                    <p class="text-muted mb-0">
                                                        <i class="fas fa-school me-1"></i>
                                                        @child.ClassName
                                                    </p>
                                                </div>
                                                <div class="col-auto">
                                                    <div class="performance-indicator @GetPerformanceClass(child.PerformanceTrend)">
                                                        <i class="fas @GetPerformanceIcon(child.PerformanceTrend)"></i>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Performance Metrics -->
                                            <div class="row g-3 mb-3">
                                                <div class="col-4">
                                                    <div class="text-center">
                                                        <div class="progress mb-2" style="height: 8px;">
                                                            <div class="progress-bar bg-success" style="width: @(child.AverageGrade)%"></div>
                                                        </div>
                                                        <small class="text-muted">المعدل</small>
                                                        <div class="fw-bold text-success">@child.AverageGrade.ToString("F1")</div>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="text-center">
                                                        <div class="progress mb-2" style="height: 8px;">
                                                            <div class="progress-bar bg-info" style="width: @(child.AttendanceRate)%"></div>
                                                        </div>
                                                        <small class="text-muted">الحضور</small>
                                                        <div class="fw-bold text-info">@child.AttendanceRate.ToString("F1")%</div>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="text-center">
                                                        <div class="progress mb-2" style="height: 8px;">
                                                            <div class="progress-bar bg-warning" style="width: @(GetAssignmentProgress(child))%"></div>
                                                        </div>
                                                        <small class="text-muted">الواجبات</small>
                                                        <div class="fw-bold text-warning">@child.PendingAssignments/@child.TotalAssignments</div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Recent Activity -->
                                            <div class="border-top pt-3">
                                                <h6 class="text-muted mb-2">
                                                    <i class="fas fa-clock me-1"></i>
                                                    النشاط الأخير
                                                </h6>
                                                @if (child.RecentGrades?.Any() == true)
                                                {
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <small class="text-muted">@child.RecentGrades.First().SubjectName</small>
                                                        <span class="badge bg-success">@child.RecentGrades.First().Score/@child.RecentGrades.First().MaxScore</span>
                                                    </div>
                                                }
                                                @if (child.RecentAttendance?.Any() == true)
                                                {
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <small class="text-muted">آخر حضور</small>
                                                        <span class="badge @(child.RecentAttendance.First().IsPresent ? "bg-success" : "bg-danger")">
                                                            @(child.RecentAttendance.First().IsPresent ? "حاضر" : "غائب")
                                                        </span>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-child fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد بيانات أطفال</h5>
                            <p class="text-muted">لم يتم ربط أي طلاب بحسابك بعد</p>
                            <button class="btn btn-primary" @onclick="ContactAdmin">
                                <i class="fas fa-phone me-2"></i>
                                تواصل مع الإدارة
                            </button>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications and Events Section -->
    <div class="row mt-4">
        <!-- Recent Notifications -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="mb-0">
                                <i class="fas fa-bell me-2 text-warning"></i>
                                الإشعارات الأخيرة
                            </h6>
                        </div>
                        <div class="col-auto">
                            <a href="/parent/notifications" class="btn btn-outline-warning btn-sm">
                                عرض الكل
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if (parentData?.RecentNotifications?.Any() == true)
                    {
                        <div class="list-group list-group-flush">
                            @foreach (var notification in parentData.RecentNotifications.Take(5))
                            {
                                <div class="list-group-item border-0 px-0 @(!notification.IsRead ? "bg-light" : "")">
                                    <div class="d-flex align-items-start">
                                        <div class="avatar-sm bg-warning text-white rounded-circle me-3 d-flex align-items-center justify-content-center flex-shrink-0">
                                            <i class="fas fa-bell fa-sm"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1 @(!notification.IsRead ? "fw-bold" : "")">@notification.Title</h6>
                                            <p class="mb-1 text-muted small">@notification.Message</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <i class="fas fa-clock me-1"></i>
                                                    @notification.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                                                </small>
                                                @if (!string.IsNullOrEmpty(notification.StudentName))
                                                {
                                                    <span class="badge bg-primary">@notification.StudentName</span>
                                                }
                                            </div>
                                        </div>
                                        @if (!notification.IsRead)
                                        {
                                            <div class="badge bg-warning rounded-pill ms-2">جديد</div>
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">لا توجد إشعارات جديدة</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Upcoming Events -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="mb-0">
                                <i class="fas fa-calendar me-2 text-info"></i>
                                الفعاليات القادمة
                            </h6>
                        </div>
                        <div class="col-auto">
                            <a href="/parent/events" class="btn btn-outline-info btn-sm">
                                عرض الكل
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if (parentData?.UpcomingEvents?.Any() == true)
                    {
                        <div class="list-group list-group-flush">
                            @foreach (var eventItem in parentData.UpcomingEvents.Take(5))
                            {
                                <div class="list-group-item border-0 px-0">
                                    <div class="d-flex align-items-start">
                                        <div class="avatar-sm bg-info text-white rounded-circle me-3 d-flex align-items-center justify-content-center flex-shrink-0">
                                            <i class="fas fa-calendar fa-sm"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">@eventItem.Title</h6>
                                            <p class="mb-1 text-muted small">@eventItem.Description</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <i class="fas fa-clock me-1"></i>
                                                    @eventItem.StartDate.ToString("dd/MM/yyyy")
                                                </small>
                                                <span class="badge bg-info">@eventItem.Category</span>
                                            </div>
                                            @if (!string.IsNullOrEmpty(eventItem.Location))
                                            {
                                                <small class="text-muted">
                                                    <i class="fas fa-map-marker-alt me-1"></i>
                                                    @eventItem.Location
                                                </small>
                                            }
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">لا توجد فعاليات قادمة</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2 text-primary"></i>
                        الإجراءات السريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="/parent/children" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-child fa-2x mb-2"></i>
                                <span>عرض الأبناء</span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="/parent/grades" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-star fa-2x mb-2"></i>
                                <span>الدرجات</span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="/parent/attendance" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-calendar-check fa-2x mb-2"></i>
                                <span>الحضور والغياب</span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="/parent/fees" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                                <span>الرسوم المالية</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .child-card {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .child-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
    }

    .performance-indicator {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }

    .performance-improving {
        background-color: #28a745;
    }

    .performance-declining {
        background-color: #dc3545;
    }

    .performance-stable {
        background-color: #6c757d;
    }

    .avatar-sm {
        width: 32px;
        height: 32px;
    }

    .avatar-md {
        width: 48px;
        height: 48px;
    }

    .avatar-lg {
        width: 64px;
        height: 64px;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }
</style>

@code {
    private ParentDashboardDto? parentData;
    private bool isLoading = true;
    private string? currentParentId;

    protected override async Task OnInitializedAsync()
    {
        await LoadParentDashboard();
    }

    private async Task LoadParentDashboard()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // Get current parent ID from authentication context
            currentParentId = await GetCurrentParentId();

            if (!string.IsNullOrEmpty(currentParentId))
            {
                parentData = await ApiService.GetParentDashboardAsync(currentParentId);
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading parent dashboard: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في تحميل البيانات. يرجى المحاولة مرة أخرى.");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task<string?> GetCurrentParentId()
    {
        try
        {
            // This would typically come from the authentication context
            // For now, we'll use a placeholder method
            return await ApiService.GetCurrentUserIdAsync();
        }
        catch
        {
            return null;
        }
    }

    private async Task RefreshData()
    {
        await LoadParentDashboard();
        await JSRuntime.InvokeVoidAsync("alert", "تم تحديث البيانات بنجاح");
    }

    private void ViewChildDetails(string studentId)
    {
        Navigation.NavigateTo($"/parent/child/{studentId}");
    }

    private async Task ContactAdmin()
    {
        await JSRuntime.InvokeVoidAsync("alert", "يرجى التواصل مع إدارة المدرسة لربط حساب الطلاب بحسابك");
    }

    private string GetPerformanceClass(string trend)
    {
        return trend.ToLower() switch
        {
            "improving" => "performance-improving",
            "declining" => "performance-declining",
            _ => "performance-stable"
        };
    }

    private string GetPerformanceIcon(string trend)
    {
        return trend.ToLower() switch
        {
            "improving" => "fa-arrow-up",
            "declining" => "fa-arrow-down",
            _ => "fa-minus"
        };
    }

    private double GetAssignmentProgress(ChildPerformanceDto child)
    {
        if (child.TotalAssignments == 0) return 0;
        var completed = child.TotalAssignments - child.PendingAssignments;
        return (double)completed / child.TotalAssignments * 100;
    }
}
