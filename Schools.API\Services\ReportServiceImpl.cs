using Microsoft.EntityFrameworkCore;
using Schools.API.Data;
using Schools.Shared.DTOs;
using System.Text;

namespace Schools.API.Services;

public class ReportServiceImpl : IReportService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<ReportServiceImpl> _logger;

    public ReportServiceImpl(ApplicationDbContext context, ILogger<ReportServiceImpl> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<TeacherPerformanceReportDto> GetTeacherPerformanceReportAsync(
        int? teacherId = null, 
        DateTime? startDate = null, 
        DateTime? endDate = null)
    {
        try
        {
            var teachersQuery = _context.Users
                .Where(u => u.TeacherSubjects.Any() && u.IsActive);

            if (teacherId.HasValue)
                teachersQuery = teachersQuery.Where(t => t.Id == teacherId.Value.ToString());

            var teachers = await teachersQuery
                .Include(t => t.TeacherSubjects)
                .ThenInclude(ts => ts.Subject)
                .ToListAsync();

            var teacherDetails = new List<TeacherPerformanceDetailDto>();

            foreach (var teacher in teachers)
            {
                var subjectIds = teacher.TeacherSubjects.Select(ts => ts.SubjectId).ToList();
                
                var gradesQuery = _context.Grades
                    .Where(g => subjectIds.Contains(g.SubjectId));

                if (startDate.HasValue)
                    gradesQuery = gradesQuery.Where(g => g.Date >= startDate.Value);

                if (endDate.HasValue)
                    gradesQuery = gradesQuery.Where(g => g.Date <= endDate.Value);

                var grades = await gradesQuery.ToListAsync();
                var averageGrade = grades.Any() ? grades.Average(g => (double)g.Score) : 0;

                teacherDetails.Add(new TeacherPerformanceDetailDto
                {
                    TeacherId = int.Parse(teacher.Id),
                    TeacherName = $"{teacher.FirstName} {teacher.LastName}",
                    SubjectsCount = teacher.TeacherSubjects.Count,
                    StudentsCount = grades.Select(g => g.StudentId).Distinct().Count(),
                    AverageGrade = averageGrade,
                    TotalGrades = grades.Count
                });
            }

            return new TeacherPerformanceReportDto
            {
                TotalTeachers = teacherDetails.Count,
                OverallAverageGrade = teacherDetails.Any() ? teacherDetails.Average(t => t.AverageGrade) : 0,
                TeacherDetails = teacherDetails
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating teacher performance report");
            throw;
        }
    }

    public async Task<string> ExportReportAsync(string reportType, string format, Dictionary<string, object>? parameters = null)
    {
        try
        {
            var csv = new StringBuilder();
            
            switch (reportType.ToLower())
            {
                case "students":
                    return await ExportStudentsReportAsync(format);
                case "attendance":
                    return await ExportAttendanceReportAsync(format);
                case "grades":
                    return await ExportGradesReportAsync(format);
                case "financial":
                    return await ExportFinancialReportAsync(format);
                default:
                    csv.AppendLine($"Report Type: {reportType}");
                    csv.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                    csv.AppendLine("No data available");
                    break;
            }
            
            return csv.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting report {ReportType}", reportType);
            throw;
        }
    }

    private async Task<string> ExportStudentsReportAsync(string format)
    {
        var students = await _context.Users
            .Where(u => u.StudentEnrollments.Any() && u.IsActive)
            .Include(u => u.StudentEnrollments)
            .ThenInclude(se => se.Class)
            .Take(100) // Limit for performance
            .ToListAsync();

        if (format.ToLower() == "csv")
        {
            var csv = new StringBuilder();
            csv.AppendLine("ID,Name,Email,Class,CreatedDate");
            
            foreach (var student in students)
            {
                var enrollment = student.StudentEnrollments.FirstOrDefault(se => se.IsActive);
                csv.AppendLine($"{student.Id},{student.FirstName} {student.LastName},{student.Email},{enrollment?.Class?.Name ?? "غير محدد"},{student.CreatedAt:yyyy-MM-dd}");
            }
            
            return csv.ToString();
        }

        return System.Text.Json.JsonSerializer.Serialize(students.Select(s => new
        {
            Id = s.Id,
            Name = $"{s.FirstName} {s.LastName}",
            Email = s.Email,
            Class = s.StudentEnrollments.FirstOrDefault(se => se.IsActive)?.Class?.Name ?? "غير محدد",
            CreatedDate = s.CreatedAt.ToString("yyyy-MM-dd")
        }));
    }

    private async Task<string> ExportAttendanceReportAsync(string format)
    {
        var attendance = await _context.AttendanceRecords
            .Include(a => a.Student)
            .OrderByDescending(a => a.Date)
            .Take(100) // Limit for performance
            .ToListAsync();

        if (format.ToLower() == "csv")
        {
            var csv = new StringBuilder();
            csv.AppendLine("Date,StudentName,IsPresent,Reason");
            
            foreach (var record in attendance)
            {
                csv.AppendLine($"{record.Date:yyyy-MM-dd},{record.Student.FirstName} {record.Student.LastName},{record.IsPresent},{record.Notes ?? ""}");
            }
            
            return csv.ToString();
        }

        return System.Text.Json.JsonSerializer.Serialize(attendance.Select(a => new
        {
            Date = a.Date.ToString("yyyy-MM-dd"),
            StudentName = $"{a.Student.FirstName} {a.Student.LastName}",
            IsPresent = a.IsPresent,
            Reason = a.Notes ?? ""
        }));
    }

    private async Task<string> ExportGradesReportAsync(string format)
    {
        var grades = await _context.Grades
            .Include(g => g.Student)
            .Include(g => g.Subject)
            .OrderByDescending(g => g.Date)
            .Take(100) // Limit for performance
            .ToListAsync();

        if (format.ToLower() == "csv")
        {
            var csv = new StringBuilder();
            csv.AppendLine("Date,StudentName,Subject,Grade,Comments");
            
            foreach (var grade in grades)
            {
                csv.AppendLine($"{grade.Date:yyyy-MM-dd},{grade.Student.FirstName} {grade.Student.LastName},{grade.Subject.Name},{grade.Score},{grade.Comments ?? ""}");
            }
            
            return csv.ToString();
        }

        return System.Text.Json.JsonSerializer.Serialize(grades.Select(g => new
        {
            Date = g.Date.ToString("yyyy-MM-dd"),
            StudentName = $"{g.Student.FirstName} {g.Student.LastName}",
            Subject = g.Subject.Name,
            Grade = g.Score.ToString(),
            Comments = g.Comments ?? ""
        }));
    }

    private async Task<string> ExportFinancialReportAsync(string format)
    {
        var receipts = await _context.ReceiptVouchers
            .OrderByDescending(r => r.Date)
            .Take(50)
            .ToListAsync();

        var payments = await _context.PaymentVouchers
            .OrderByDescending(p => p.Date)
            .Take(50)
            .ToListAsync();

        if (format.ToLower() == "csv")
        {
            var csv = new StringBuilder();
            csv.AppendLine("Date,Type,Category,Amount,Description");
            
            foreach (var receipt in receipts)
            {
                csv.AppendLine($"{receipt.Date:yyyy-MM-dd},Income,{receipt.Category},{receipt.Amount},{receipt.Description}");
            }
            
            foreach (var payment in payments)
            {
                csv.AppendLine($"{payment.Date:yyyy-MM-dd},Expense,{payment.Category},{payment.Amount},{payment.Description}");
            }
            
            return csv.ToString();
        }

        var data = receipts.Select(r => new
        {
            Date = r.Date.ToString("yyyy-MM-dd"),
            Type = "Income",
            Category = r.Category,
            Amount = r.Amount,
            Description = r.Description
        }).Concat(payments.Select(p => new
        {
            Date = p.Date.ToString("yyyy-MM-dd"),
            Type = "Expense",
            Category = p.Category,
            Amount = p.Amount,
            Description = p.Description
        }));

        return System.Text.Json.JsonSerializer.Serialize(data);
    }

    // Implement other interface methods by delegating to the main ReportService
    public async Task<StudentPerformanceReportDto> GetStudentPerformanceReportAsync(string? studentId = null, int? classId = null, DateTime? startDate = null, DateTime? endDate = null)
    {
        // This would be implemented similar to the main service
        throw new NotImplementedException("Use main ReportService implementation");
    }

    public async Task<AttendanceReportDto> GetAttendanceReportAsync(string? studentId = null, int? classId = null, DateTime? startDate = null, DateTime? endDate = null)
    {
        // This would be implemented similar to the main service
        throw new NotImplementedException("Use main ReportService implementation");
    }

    public async Task<FinancialReportDto> GetFinancialReportAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        // This would be implemented similar to the main service
        throw new NotImplementedException("Use main ReportService implementation");
    }

    public async Task<ClassSummaryReportDto> GetClassSummaryReportAsync(int? classId = null)
    {
        // This would be implemented similar to the main service
        throw new NotImplementedException("Use main ReportService implementation");
    }
}
