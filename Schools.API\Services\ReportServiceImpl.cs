using Microsoft.EntityFrameworkCore;
using Schools.API.Data;
using Schools.Shared.DTOs;
using System.Text;

namespace Schools.API.Services;

public class ReportServiceImpl : IReportService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<ReportServiceImpl> _logger;

    public ReportServiceImpl(ApplicationDbContext context, ILogger<ReportServiceImpl> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<TeacherPerformanceReportDto> GetTeacherPerformanceReportAsync(
        int? teacherId = null,
        DateTime? startDate = null,
        DateTime? endDate = null)
    {
        try
        {
            var teachersQuery = _context.Users
                .Where(u => u.TeacherSubjects.Any() && u.IsActive);

            if (teacherId.HasValue)
                teachersQuery = teachersQuery.Where(t => t.Id == teacherId.Value.ToString());

            var teachers = await teachersQuery
                .Include(t => t.TeacherSubjects)
                .ThenInclude(ts => ts.Subject)
                .ToListAsync();

            var teacherDetails = new List<TeacherPerformanceDetailDto>();

            foreach (var teacher in teachers)
            {
                var subjectIds = teacher.TeacherSubjects.Select(ts => ts.SubjectId).ToList();

                var gradesQuery = _context.Grades
                    .Where(g => subjectIds.Contains(g.SubjectId));

                if (startDate.HasValue)
                    gradesQuery = gradesQuery.Where(g => g.Date >= startDate.Value);

                if (endDate.HasValue)
                    gradesQuery = gradesQuery.Where(g => g.Date <= endDate.Value);

                var grades = await gradesQuery.ToListAsync();
                var averageGrade = grades.Any() ? grades.Average(g => (double)g.Score) : 0;

                teacherDetails.Add(new TeacherPerformanceDetailDto
                {
                    TeacherId = int.Parse(teacher.Id),
                    TeacherName = $"{teacher.FirstName} {teacher.LastName}",
                    SubjectsCount = teacher.TeacherSubjects.Count,
                    StudentsCount = grades.Select(g => g.StudentId).Distinct().Count(),
                    AverageGrade = averageGrade,
                    TotalGrades = grades.Count
                });
            }

            return new TeacherPerformanceReportDto
            {
                TotalTeachers = teacherDetails.Count,
                OverallAverageGrade = teacherDetails.Any() ? teacherDetails.Average(t => t.AverageGrade) : 0,
                TeacherDetails = teacherDetails
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating teacher performance report");
            throw;
        }
    }

    public async Task<string> ExportReportAsync(string reportType, string format, Dictionary<string, object>? parameters = null)
    {
        try
        {
            var csv = new StringBuilder();

            switch (reportType.ToLower())
            {
                case "students":
                    return await ExportStudentsReportAsync(format);
                case "attendance":
                    return await ExportAttendanceReportAsync(format);
                case "grades":
                    return await ExportGradesReportAsync(format);
                case "financial":
                    return await ExportFinancialReportAsync(format);
                default:
                    csv.AppendLine($"Report Type: {reportType}");
                    csv.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                    csv.AppendLine("No data available");
                    break;
            }

            return csv.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting report {ReportType}", reportType);
            throw;
        }
    }

    private async Task<string> ExportStudentsReportAsync(string format)
    {
        var students = await _context.Users
            .Where(u => u.StudentEnrollments.Any() && u.IsActive)
            .Include(u => u.StudentEnrollments)
            .ThenInclude(se => se.Class)
            .Take(100) // Limit for performance
            .ToListAsync();

        if (format.ToLower() == "csv")
        {
            var csv = new StringBuilder();
            csv.AppendLine("ID,Name,Email,Class,CreatedDate");

            foreach (var student in students)
            {
                var enrollment = student.StudentEnrollments.FirstOrDefault(se => se.IsActive);
                csv.AppendLine($"{student.Id},{student.FirstName} {student.LastName},{student.Email},{enrollment?.Class?.Name ?? "غير محدد"},{student.CreatedAt:yyyy-MM-dd}");
            }

            return csv.ToString();
        }

        return System.Text.Json.JsonSerializer.Serialize(students.Select(s => new
        {
            Id = s.Id,
            Name = $"{s.FirstName} {s.LastName}",
            Email = s.Email,
            Class = s.StudentEnrollments.FirstOrDefault(se => se.IsActive)?.Class?.Name ?? "غير محدد",
            CreatedDate = s.CreatedAt.ToString("yyyy-MM-dd")
        }));
    }

    private async Task<string> ExportAttendanceReportAsync(string format)
    {
        var attendance = await _context.AttendanceRecords
            .Include(a => a.Student)
            .OrderByDescending(a => a.Date)
            .Take(100) // Limit for performance
            .ToListAsync();

        if (format.ToLower() == "csv")
        {
            var csv = new StringBuilder();
            csv.AppendLine("Date,StudentName,IsPresent,Reason");

            foreach (var record in attendance)
            {
                csv.AppendLine($"{record.Date:yyyy-MM-dd},{record.Student.FirstName} {record.Student.LastName},{record.IsPresent},{record.Notes ?? ""}");
            }

            return csv.ToString();
        }

        return System.Text.Json.JsonSerializer.Serialize(attendance.Select(a => new
        {
            Date = a.Date.ToString("yyyy-MM-dd"),
            StudentName = $"{a.Student.FirstName} {a.Student.LastName}",
            IsPresent = a.IsPresent,
            Reason = a.Notes ?? ""
        }));
    }

    private async Task<string> ExportGradesReportAsync(string format)
    {
        var grades = await _context.Grades
            .Include(g => g.Student)
            .Include(g => g.Subject)
            .OrderByDescending(g => g.Date)
            .Take(100) // Limit for performance
            .ToListAsync();

        if (format.ToLower() == "csv")
        {
            var csv = new StringBuilder();
            csv.AppendLine("Date,StudentName,Subject,Grade,Comments");

            foreach (var grade in grades)
            {
                csv.AppendLine($"{grade.Date:yyyy-MM-dd},{grade.Student.FirstName} {grade.Student.LastName},{grade.Subject.Name},{grade.Score},{grade.Comments ?? ""}");
            }

            return csv.ToString();
        }

        return System.Text.Json.JsonSerializer.Serialize(grades.Select(g => new
        {
            Date = g.Date.ToString("yyyy-MM-dd"),
            StudentName = $"{g.Student.FirstName} {g.Student.LastName}",
            Subject = g.Subject.Name,
            Grade = g.Score.ToString(),
            Comments = g.Comments ?? ""
        }));
    }

    private async Task<string> ExportFinancialReportAsync(string format)
    {
        var receipts = await _context.ReceiptVouchers
            .OrderByDescending(r => r.Date)
            .Take(50)
            .ToListAsync();

        var payments = await _context.PaymentVouchers
            .OrderByDescending(p => p.Date)
            .Take(50)
            .ToListAsync();

        if (format.ToLower() == "csv")
        {
            var csv = new StringBuilder();
            csv.AppendLine("Date,Type,Category,Amount,Description");

            foreach (var receipt in receipts)
            {
                csv.AppendLine($"{receipt.Date:yyyy-MM-dd},Income,{receipt.Category},{receipt.Amount},{receipt.Description}");
            }

            foreach (var payment in payments)
            {
                csv.AppendLine($"{payment.Date:yyyy-MM-dd},Expense,{payment.Category},{payment.Amount},{payment.Description}");
            }

            return csv.ToString();
        }

        var data = receipts.Select(r => new
        {
            Date = r.Date.ToString("yyyy-MM-dd"),
            Type = "Income",
            Category = r.Category,
            Amount = r.Amount,
            Description = r.Description
        }).Concat(payments.Select(p => new
        {
            Date = p.Date.ToString("yyyy-MM-dd"),
            Type = "Expense",
            Category = p.Category,
            Amount = p.Amount,
            Description = p.Description
        }));

        return System.Text.Json.JsonSerializer.Serialize(data);
    }

    public async Task<StudentPerformanceReportDto> GetStudentPerformanceReportAsync(string? studentId = null, int? classId = null, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            // Get students query
            var studentsQuery = _context.Users
                .Where(u => u.StudentEnrollments.Any() && u.IsActive);

            if (!string.IsNullOrEmpty(studentId))
                studentsQuery = studentsQuery.Where(s => s.Id == studentId);

            if (classId.HasValue)
                studentsQuery = studentsQuery.Where(s => s.StudentEnrollments.Any(se => se.ClassId == classId.Value));

            // Get grades query
            var gradesQuery = _context.Grades.AsQueryable();

            if (startDate.HasValue)
                gradesQuery = gradesQuery.Where(g => g.Date >= startDate.Value);

            if (endDate.HasValue)
                gradesQuery = gradesQuery.Where(g => g.Date <= endDate.Value);

            // Calculate statistics
            var totalStudents = await studentsQuery.CountAsync();
            var grades = await gradesQuery.ToListAsync();

            var averageGrade = grades.Any() ? grades.Average(g => (double)g.Score) : 0;
            var highestGrade = grades.Any() ? grades.Max(g => (double)g.Score) : 0;
            var lowestGrade = grades.Any() ? grades.Min(g => (double)g.Score) : 0;

            // Calculate grade distribution
            var gradeDistribution = new Dictionary<string, int>();
            foreach (var grade in grades)
            {
                var letterGrade = GetLetterGrade(grade.Score);
                gradeDistribution[letterGrade] = gradeDistribution.GetValueOrDefault(letterGrade, 0) + 1;
            }

            // Calculate subject performance
            var subjectPerformance = await gradesQuery
                .Include(g => g.Subject)
                .GroupBy(g => g.Subject.Name)
                .Select(g => new { Subject = g.Key, Average = g.Average(x => (double)x.Score) })
                .ToDictionaryAsync(x => x.Subject, x => x.Average);

            return new StudentPerformanceReportDto
            {
                TotalStudents = totalStudents,
                AverageGrade = averageGrade,
                HighestGrade = highestGrade,
                LowestGrade = lowestGrade,
                GradeDistribution = gradeDistribution,
                SubjectPerformance = subjectPerformance
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating student performance report");
            throw;
        }
    }

    public async Task<AttendanceReportDto> GetAttendanceReportAsync(string? studentId = null, int? classId = null, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            startDate ??= DateTime.Now.AddDays(-30);
            endDate ??= DateTime.Now;

            // Get attendance records
            var attendanceQuery = _context.AttendanceRecords
                .Where(a => a.Date >= startDate.Value && a.Date <= endDate.Value);

            if (!string.IsNullOrEmpty(studentId))
                attendanceQuery = attendanceQuery.Where(a => a.StudentId == studentId);

            if (classId.HasValue)
                attendanceQuery = attendanceQuery.Where(a => a.Student.StudentEnrollments.Any(se => se.ClassId == classId.Value));

            var attendanceRecords = await attendanceQuery
                .Include(a => a.Student)
                .ToListAsync();

            var totalStudents = await _context.Users
                .Where(u => u.StudentEnrollments.Any() && u.IsActive)
                .CountAsync();

            var totalSchoolDays = CalculateSchoolDays(startDate.Value, endDate.Value);
            var totalPossibleAttendance = totalStudents * totalSchoolDays;
            var totalPresent = attendanceRecords.Count(a => a.IsPresent);
            var overallAttendanceRate = totalPossibleAttendance > 0 ? (double)totalPresent / totalPossibleAttendance * 100 : 0;

            // Get student attendance details
            var studentAttendance = attendanceRecords
                .GroupBy(a => a.StudentId)
                .Select(g => new StudentAttendanceReportDto
                {
                    StudentId = g.Key,
                    StudentName = g.First().Student.FirstName + " " + g.First().Student.LastName,
                    StudentNumber = g.First().Student.UserName ?? "",
                    TotalPresent = g.Count(a => a.IsPresent),
                    TotalAbsent = g.Count(a => !a.IsPresent),
                    TotalLate = g.Count(a => a.IsLate),
                    AttendanceRate = g.Any() ? (double)g.Count(a => a.IsPresent) / g.Count() * 100 : 0
                })
                .ToList();

            return new AttendanceReportDto
            {
                ClassId = classId ?? 0,
                ClassName = classId.HasValue ? await GetClassNameAsync(classId.Value) : "جميع الصفوف",
                GradeName = "",
                FromDate = startDate.Value,
                ToDate = endDate.Value,
                TotalStudents = totalStudents,
                TotalSchoolDays = totalSchoolDays,
                OverallAttendanceRate = overallAttendanceRate,
                StudentAttendance = studentAttendance
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating attendance report");
            throw;
        }
    }

    public async Task<FinancialReportDto> GetFinancialReportAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            startDate ??= DateTime.UtcNow.AddMonths(-1);
            endDate ??= DateTime.UtcNow;

            // Get receipt vouchers (income)
            var receiptVouchers = await _context.ReceiptVouchers
                .Where(rv => rv.Date >= startDate.Value && rv.Date <= endDate.Value)
                .ToListAsync();

            // Get payment vouchers (expenses)
            var paymentVouchers = await _context.PaymentVouchers
                .Where(pv => pv.Date >= startDate.Value && pv.Date <= endDate.Value)
                .ToListAsync();

            var totalIncome = receiptVouchers.Sum(rv => rv.Amount);
            var totalExpenses = paymentVouchers.Sum(pv => pv.Amount);
            var netIncome = totalIncome - totalExpenses;

            // Group by category
            var incomeByCategory = receiptVouchers
                .GroupBy(rv => rv.Category)
                .Select(g => new CategoryAmountDto
                {
                    Category = g.Key,
                    Amount = g.Sum(rv => rv.Amount)
                })
                .ToList();

            var expensesByCategory = paymentVouchers
                .GroupBy(pv => pv.Category)
                .Select(g => new CategoryAmountDto
                {
                    Category = g.Key,
                    Amount = g.Sum(pv => pv.Amount)
                })
                .ToList();

            return new FinancialReportDto
            {
                FromDate = startDate.Value,
                ToDate = endDate.Value,
                TotalIncome = totalIncome,
                TotalExpenses = totalExpenses,
                NetIncome = netIncome,
                StudentFeesCollected = receiptVouchers.Where(rv => rv.Category == "رسوم دراسية").Sum(rv => rv.Amount),
                TotalReceiptVouchers = receiptVouchers.Count,
                TotalPaymentVouchers = paymentVouchers.Count,
                IncomeByCategory = incomeByCategory,
                ExpensesByCategory = expensesByCategory
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating financial report");
            throw;
        }
    }

    public async Task<ClassSummaryReportDto> GetClassSummaryReportAsync(int? classId = null)
    {
        try
        {
            var classesQuery = _context.Classes.AsQueryable();

            if (classId.HasValue)
                classesQuery = classesQuery.Where(c => c.Id == classId.Value);

            var classes = await classesQuery
                .Include(c => c.StudentEnrollments)
                .ThenInclude(se => se.Student)
                .ToListAsync();

            var classDetails = new List<ClassSummaryDetailDto>();

            foreach (var cls in classes)
            {
                var activeStudents = cls.StudentEnrollments.Where(se => se.IsActive).ToList();
                var studentIds = activeStudents.Select(se => se.StudentId).ToList();

                // Get grades for this class
                var grades = await _context.Grades
                    .Where(g => studentIds.Contains(g.StudentId))
                    .ToListAsync();

                // Get attendance for this class
                var attendance = await _context.AttendanceRecords
                    .Where(a => studentIds.Contains(a.StudentId))
                    .ToListAsync();

                var averageGrade = grades.Any() ? grades.Average(g => (double)g.Score) : 0;
                var attendanceRate = attendance.Any() ? (double)attendance.Count(a => a.IsPresent) / attendance.Count * 100 : 0;

                classDetails.Add(new ClassSummaryDetailDto
                {
                    ClassId = cls.Id,
                    ClassName = cls.Name,
                    TotalStudents = activeStudents.Count,
                    AverageGrade = averageGrade,
                    AttendanceRate = attendanceRate,
                    SubjectCount = await _context.Subjects.CountAsync()
                });
            }

            return new ClassSummaryReportDto
            {
                TotalClasses = classDetails.Count,
                TotalStudents = classDetails.Sum(c => c.TotalStudents),
                OverallAverageGrade = classDetails.Any() ? classDetails.Average(c => c.AverageGrade) : 0,
                OverallAttendanceRate = classDetails.Any() ? classDetails.Average(c => c.AttendanceRate) : 0,
                ClassDetails = classDetails
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating class summary report");
            throw;
        }
    }

    // Helper methods
    private string GetLetterGrade(decimal score)
    {
        return score switch
        {
            >= 95 => "A+",
            >= 90 => "A",
            >= 85 => "B+",
            >= 80 => "B",
            >= 75 => "C+",
            >= 70 => "C",
            >= 65 => "D+",
            >= 60 => "D",
            _ => "F"
        };
    }

    private async Task<string> GetClassNameAsync(int classId)
    {
        var className = await _context.Classes
            .Where(c => c.Id == classId)
            .Select(c => c.Name)
            .FirstOrDefaultAsync();

        return className ?? "غير محدد";
    }

    private int CalculateSchoolDays(DateTime startDate, DateTime endDate)
    {
        int schoolDays = 0;
        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            // Exclude weekends (Friday and Saturday in many Arab countries)
            if (date.DayOfWeek != DayOfWeek.Friday && date.DayOfWeek != DayOfWeek.Saturday)
            {
                schoolDays++;
            }
        }
        return schoolDays;
    }

    private async Task<string> ExportTeachersReportAsync(string format)
    {
        var teachers = await _context.Users
            .Where(u => u.TeacherSubjects.Any() && u.IsActive)
            .Include(u => u.TeacherSubjects)
            .ThenInclude(ts => ts.Subject)
            .Take(100) // Limit for performance
            .ToListAsync();

        if (format.ToLower() == "csv")
        {
            var csv = new StringBuilder();
            csv.AppendLine("ID,Name,Email,Department,Subjects,Experience,HireDate");

            foreach (var teacher in teachers)
            {
                var subjects = string.Join("; ", teacher.TeacherSubjects.Select(ts => ts.Subject.Name));
                csv.AppendLine($"{teacher.Id},{teacher.FirstName} {teacher.LastName},{teacher.Email},التعليم,{subjects},5,{teacher.CreatedAt:yyyy-MM-dd}");
            }

            return csv.ToString();
        }

        return System.Text.Json.JsonSerializer.Serialize(teachers.Select(t => new
        {
            Id = t.Id,
            Name = $"{t.FirstName} {t.LastName}",
            Email = t.Email,
            Department = "التعليم",
            Subjects = t.TeacherSubjects.Select(ts => ts.Subject.Name).ToList(),
            Experience = 5,
            HireDate = t.CreatedAt.ToString("yyyy-MM-dd")
        }));
    }

    private async Task<string> ExportClassesReportAsync(string format)
    {
        var classes = await _context.Classes
            .Include(c => c.StudentEnrollments)
            .Include(c => c.Grade)
            .Take(100) // Limit for performance
            .ToListAsync();

        if (format.ToLower() == "csv")
        {
            var csv = new StringBuilder();
            csv.AppendLine("ID,Name,Grade,TotalStudents,Capacity,AcademicYear");

            foreach (var cls in classes)
            {
                var totalStudents = cls.StudentEnrollments.Count(se => se.IsActive);
                csv.AppendLine($"{cls.Id},{cls.Name},{cls.Grade?.Name ?? "غير محدد"},{totalStudents},{cls.Capacity},{cls.AcademicYear}");
            }

            return csv.ToString();
        }

        return System.Text.Json.JsonSerializer.Serialize(classes.Select(c => new
        {
            Id = c.Id,
            Name = c.Name,
            Grade = c.Grade?.Name ?? "غير محدد",
            TotalStudents = c.StudentEnrollments.Count(se => se.IsActive),
            Capacity = c.Capacity,
            AcademicYear = c.AcademicYear
        }));
    }
}
