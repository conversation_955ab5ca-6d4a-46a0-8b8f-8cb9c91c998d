@page "/admin/grades-scores"
@using Schools.Shared.Models
@using Schools.Shared.DTOs
@using Schools.Client.Services
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Admin,Teacher")]

<PageTitle>إدارة الدرجات</PageTitle>

<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-warning text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="text-white mb-1">
                                <i class="fas fa-star me-2"></i>
                                إدارة الدرجات والتقييم
                            </h2>
                            <p class="text-white-75 mb-0">إدخال ومتابعة درجات الطلاب في جميع المواد</p>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-3x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@excellentCount</h4>
                            <p class="mb-0">ممتاز (90+)</p>
                        </div>
                        <i class="fas fa-trophy fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@goodCount</h4>
                            <p class="mb-0">جيد جداً (80-89)</p>
                        </div>
                        <i class="fas fa-medal fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@passCount</h4>
                            <p class="mb-0">مقبول (60-79)</p>
                        </div>
                        <i class="fas fa-thumbs-up fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@failCount</h4>
                            <p class="mb-0">راسب (أقل من 60)</p>
                        </div>
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label class="form-label">الصف</label>
                            <select class="form-select" @bind="selectedClass" @bind:after="OnClassChanged">
                                <option value="">جميع الصفوف</option>
                                @foreach (var classItem in availableClasses)
                                {
                                    <option value="@classItem.Id">@classItem.Name</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">المادة</label>
                            <select class="form-select" @bind="selectedSubject" @bind:after="OnSubjectChanged">
                                <option value="">جميع المواد</option>
                                @foreach (var subject in availableSubjects)
                                {
                                    <option value="@subject.Id">@subject.Name</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">نوع التقييم</label>
                            <select class="form-select" @bind="selectedExamType" @bind:after="OnExamTypeChanged">
                                <option value="">جميع الأنواع</option>
                                <option value="Quiz">اختبار قصير</option>
                                <option value="Midterm">اختبار نصف الفصل</option>
                                <option value="Final">اختبار نهائي</option>
                                <option value="Assignment">واجب</option>
                                <option value="Project">مشروع</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">الفصل الدراسي</label>
                            <select class="form-select" @bind="selectedSemester" @bind:after="OnSemesterChanged">
                                <option value="">جميع الفصول</option>
                                <option value="1">الفصل الأول</option>
                                <option value="2">الفصل الثاني</option>
                                <option value="3">الفصل الثالث</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" placeholder="البحث عن طالب..." @bind="searchTerm" @oninput="OnSearchChanged">
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <button class="btn btn-success me-2" @onclick="ShowAddGradeModal">
                                <i class="fas fa-plus me-2"></i>
                                إضافة درجة جديدة
                            </button>
                            <button class="btn btn-primary" @onclick="BulkGradeEntry">
                                <i class="fas fa-edit me-2"></i>
                                إدخال جماعي
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Grades Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        سجل الدرجات
                    </h5>
                    <div class="btn-group">
                        <button class="btn btn-outline-success btn-sm" @onclick="ExportToExcel">
                            <i class="fas fa-file-excel me-2"></i>
                            تصدير Excel
                        </button>
                        <button class="btn btn-outline-primary btn-sm" @onclick="GenerateReport">
                            <i class="fas fa-chart-bar me-2"></i>
                            تقرير تفصيلي
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-4">
                            <div class="spinner-border text-warning" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else if (filteredGrades?.Any() == true)
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الطالب</th>
                                        <th>رقم الطالب</th>
                                        <th>الصف</th>
                                        <th>المادة</th>
                                        <th>نوع التقييم</th>
                                        <th>الدرجة</th>
                                        <th>النسبة المئوية</th>
                                        <th>التقدير</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var grade in filteredGrades.Take(20))
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-@GetGradeColor(grade.Percentage) rounded-circle d-flex align-items-center justify-content-center me-3">
                                                        <i class="fas @GetGradeIcon(grade.Percentage) text-white"></i>
                                                    </div>
                                                    <div>
                                                        <strong>@grade.StudentName</strong>
                                                        <br><small class="text-muted">@grade.StudentEmail</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>@grade.StudentNumber</td>
                                            <td>@grade.ClassName</td>
                                            <td>
                                                <span class="badge bg-primary">@grade.SubjectName</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-@GetExamTypeColor(grade.ExamType)">
                                                    @GetExamTypeName(grade.ExamType)
                                                </span>
                                            </td>
                                            <td>
                                                <strong class="text-@GetGradeColor(grade.Percentage)">
                                                    @grade.Score / @grade.MaxScore
                                                </strong>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="progress me-2" style="width: 60px; height: 8px;">
                                                        <div class="progress-bar bg-@GetGradeColor(grade.Percentage)"
                                                             style="width: @grade.Percentage%"></div>
                                                    </div>
                                                    <span class="text-@GetGradeColor(grade.Percentage)">@grade.Percentage%</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-@GetGradeColor(grade.Percentage)">
                                                    @GetGradeLetter(grade.Percentage)
                                                </span>
                                            </td>
                                            <td>@grade.Date.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-primary" @onclick="() => EditGrade(grade.Id)" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" @onclick="() => ViewGradeDetails(grade.Id)" title="تفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteGrade(grade.Id)" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        @if (filteredGrades.Count() > 20)
                        {
                            <div class="text-center mt-3">
                                <p class="text-muted">عرض 20 من أصل @filteredGrades.Count() درجة</p>
                                <button class="btn btn-outline-warning" @onclick="LoadMoreGrades">
                                    تحميل المزيد
                                </button>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-star fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد درجات</h5>
                            <p class="text-muted">لم يتم العثور على درجات بالفلاتر المحددة</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .avatar-sm {
        width: 35px;
        height: 35px;
    }

    .progress {
        background-color: #e9ecef;
    }
</style>

@code {
    private List<LocalGradeDto> grades = new();
    private List<LocalGradeDto> filteredGrades = new();
    private List<ClassDto> availableClasses = new();
    private List<SubjectDto> availableSubjects = new();
    private bool isLoading = true;

    private string selectedClass = string.Empty;
    private string selectedSubject = string.Empty;
    private string selectedExamType = string.Empty;
    private string selectedSemester = string.Empty;
    private string searchTerm = string.Empty;

    private int excellentCount => grades.Count(g => g.Percentage >= 90);
    private int goodCount => grades.Count(g => g.Percentage >= 80 && g.Percentage < 90);
    private int passCount => grades.Count(g => g.Percentage >= 60 && g.Percentage < 80);
    private int failCount => grades.Count(g => g.Percentage < 60);

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            // Load grades from API - fallback to mock data for now
            grades = GenerateMockGrades();

            // Load classes and subjects from API
            var apiClasses = await ApiService.GetClassesAsync();
            availableClasses = apiClasses.Select(c => new ClassDto
            {
                Id = c.Id,
                Name = c.Name
            }).ToList();

            var apiSubjects = await ApiService.GetSubjectsAsync();
            availableSubjects = apiSubjects.ToList();

            FilterGrades();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
            // Fallback to mock data if API fails
            grades = GenerateMockGrades();
            availableClasses = GenerateMockClasses();
            availableSubjects = GenerateMockSubjects();
            FilterGrades();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private List<LocalGradeDto> GenerateMockGrades()
    {
        var mockGrades = new List<LocalGradeDto>();
        var random = new Random();
        var examTypes = new[] { "Quiz", "Midterm", "Final", "Assignment", "Project" };
        var subjects = new[] { "الرياضيات", "العلوم", "اللغة العربية", "اللغة الإنجليزية", "التاريخ" };

        for (int i = 1; i <= 200; i++)
        {
            var maxScore = random.Next(20, 101);
            var score = random.Next(10, maxScore + 1);
            var percentage = (int)((double)score / maxScore * 100);

            mockGrades.Add(new LocalGradeDto
            {
                Id = i,
                StudentId = (i % 50 + 1).ToString(),
                StudentName = $"الطالب {i % 50 + 1}",
                StudentNumber = $"STU{(i % 50 + 1):D4}",
                StudentEmail = $"student{i % 50 + 1}@school.com",
                ClassName = $"الصف {(i % 6) + 1} أ",
                SubjectName = subjects[random.Next(subjects.Length)],
                ExamType = examTypes[random.Next(examTypes.Length)],
                Score = score,
                MaxScore = maxScore,
                Percentage = percentage,
                Date = DateTime.Now.AddDays(-random.Next(1, 90)),
                Semester = random.Next(1, 4).ToString()
            });
        }

        return mockGrades;
    }

    private List<ClassDto> GenerateMockClasses()
    {
        return new List<ClassDto>
        {
            new ClassDto { Id = 1, Name = "الصف الأول أ" },
            new ClassDto { Id = 2, Name = "الصف الثاني أ" },
            new ClassDto { Id = 3, Name = "الصف الثالث أ" },
            new ClassDto { Id = 4, Name = "الصف الرابع أ" },
            new ClassDto { Id = 5, Name = "الصف الخامس أ" },
            new ClassDto { Id = 6, Name = "الصف السادس أ" }
        };
    }

    private List<SubjectDto> GenerateMockSubjects()
    {
        return new List<SubjectDto>
        {
            new SubjectDto { Id = 1, Name = "الرياضيات" },
            new SubjectDto { Id = 2, Name = "العلوم" },
            new SubjectDto { Id = 3, Name = "اللغة العربية" },
            new SubjectDto { Id = 4, Name = "اللغة الإنجليزية" },
            new SubjectDto { Id = 5, Name = "التاريخ" }
        };
    }

    private void OnClassChanged()
    {
        FilterGrades();
    }

    private void OnSubjectChanged()
    {
        FilterGrades();
    }

    private void OnExamTypeChanged()
    {
        FilterGrades();
    }

    private void OnSemesterChanged()
    {
        FilterGrades();
    }

    private async Task OnSearchChanged(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? string.Empty;
        FilterGrades();
    }

    private void FilterGrades()
    {
        var query = grades.AsEnumerable();

        if (!string.IsNullOrEmpty(selectedExamType))
        {
            query = query.Where(g => g.ExamType == selectedExamType);
        }

        if (!string.IsNullOrEmpty(selectedSemester))
        {
            query = query.Where(g => g.Semester == selectedSemester);
        }

        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(g =>
                g.StudentName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                g.StudentNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                g.SubjectName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
        }

        filteredGrades = query.OrderByDescending(g => g.Date).ToList();
        StateHasChanged();
    }

    private async Task ShowAddGradeModal()
    {
        Navigation.NavigateTo("/admin/grades-scores/add");
    }

    private async Task BulkGradeEntry()
    {
        Navigation.NavigateTo("/admin/grades-scores/bulk");
    }

    private async Task EditGrade(int gradeId)
    {
        Navigation.NavigateTo($"/admin/grades-scores/{gradeId}/edit");
    }

    private async Task ViewGradeDetails(int gradeId)
    {
        Navigation.NavigateTo($"/admin/grades-scores/{gradeId}");
    }

    private async Task DeleteGrade(int gradeId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذه الدرجة؟"))
        {
            try
            {
                var success = await ApiService.DeleteStudentGradeAsync(gradeId);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "تم حذف الدرجة بنجاح");
                    await LoadData();
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "فشل في حذف الدرجة");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف الدرجة: {ex.Message}");
            }
        }
    }

    private async Task LoadMoreGrades()
    {
        await JSRuntime.InvokeVoidAsync("alert", "تم تحميل المزيد من الدرجات");
    }

    private async Task ExportToExcel()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم تصدير البيانات إلى Excel");
    }

    private async Task GenerateReport()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم إنشاء تقرير تفصيلي");
    }

    private string GetGradeColor(int percentage)
    {
        return percentage switch
        {
            >= 90 => "success",
            >= 80 => "primary",
            >= 60 => "warning",
            _ => "danger"
        };
    }

    private string GetGradeIcon(int percentage)
    {
        return percentage switch
        {
            >= 90 => "fa-trophy",
            >= 80 => "fa-medal",
            >= 60 => "fa-thumbs-up",
            _ => "fa-exclamation-triangle"
        };
    }

    private string GetGradeLetter(int percentage)
    {
        return percentage switch
        {
            >= 90 => "ممتاز",
            >= 80 => "جيد جداً",
            >= 70 => "جيد",
            >= 60 => "مقبول",
            _ => "راسب"
        };
    }

    private string GetExamTypeName(string examType)
    {
        return examType switch
        {
            "Quiz" => "اختبار قصير",
            "Midterm" => "نصف الفصل",
            "Final" => "نهائي",
            "Assignment" => "واجب",
            "Project" => "مشروع",
            _ => examType
        };
    }

    private string GetExamTypeColor(string examType)
    {
        return examType switch
        {
            "Quiz" => "info",
            "Midterm" => "warning",
            "Final" => "danger",
            "Assignment" => "success",
            "Project" => "primary",
            _ => "secondary"
        };
    }

    // Local GradeDto for display purposes
    public class LocalGradeDto
    {
        public int Id { get; set; }
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public string StudentEmail { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
        public string SubjectName { get; set; } = string.Empty;
        public string ExamType { get; set; } = string.Empty;
        public int Score { get; set; }
        public int MaxScore { get; set; }
        public int Percentage { get; set; }
        public DateTime Date { get; set; }
        public string Semester { get; set; } = string.Empty;
    }
}
