{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["vBQj9fDP6Uy30mUfuN6129zDvlyAXciEwS7S6X8dW0I=", "5fyn2zhvNgfxOAS7uqzcZMJG0XI1TSK1Ndj8IuWve2I=", "I096DMO1sNnuMC4/rS44UU2Wb3pnroMSmBr6nEIiF7Q=", "DaWXYSLq+6XqVNtanCz4e66sr95QdIVjguY0hTBLtiw=", "gE6fT5uFWx+LhwfVf8NJDzbf5ygKCBFbucrj4C5S5No=", "1NJC7Qe8NZgvpKoqyFpiw/m1hXEmkUAwtoqXmCVMMyg=", "cCZ2XZXOVFj+gojzOOgwuhSt4BWHELhgApTv2xsvI1M=", "1REiR6HFyL/ZKmuGd3zwbBBbhYnH51FNz+KSvq0grZo=", "JzlPNfviJCNiPUIL6QcrQ16LMKPy1clx3wSY+kk1m0Q=", "LYvVU0Zq83KR3vKBCRDHzVc+XUv2TTZShlH/9HmUg70=", "7l3Txt9EEH5yKz3DgrthZpKBeHqhYbJczU4SIELcxuY=", "eFvfB/0liL4bE7nHFIZQdUZB9idz7OgOcJwEiSvFqCw=", "pRCSsUnQYdhYSgdSGnMg1hc24RiFzIn/cKL0w786S4k=", "g1ELnEWZVLwU7L7ZsE+8yPlQWvIRWgYf+N5jXJQmzeM=", "nIpGpPBx2nXj4MDqmhrny/d0WERsiMc37Mmrbt06j/c=", "2MupjTbXTOw3I1XCkNN4ulsJKteQarW5YxxvzGfwlDM=", "zitRP7ZC+QsrS7pt6xYIfVXdMAhaG84w8sUMqvOgUJo=", "0gL4EbIjZj8Pz/biBfDXzL0TPuueMJEBhNC91AJByDI=", "Z0plM31Vc6L+Xe9DsO4ag+9+V9ZecWnUYgpOGBFJU2o=", "eYSQexJa3Oxde+fWx3cUP5I6scBABcwfYcwMzB9STcw=", "OzRijkHkmwQgavqnNEBs3p+m7BHQxXog7eHcFavG4jM=", "FVAyhb05sOH0nuVah81bK3pRVafvq7/Tp3w/P4ztsKo=", "iSJeltujU2qefNZ8fgp1BZvg4Kt899FI0FeQI5HGlck=", "cr1oyR/N3xBWuJNlKU3TZ/z0K1CP1vZKbAmuWKwTXQM=", "KVoXEGuqFuB4LlSHwiWfpSiA1FwCkE1L2VfrLYgEQDc=", "hbX7ED+25ASNv2gD6JHBHmfrqkvunNyZGENdz8DKcA4=", "joAKZZylFM4luNctgv3jRG8cH3TTEQL2qOblm/aJXJg=", "VIL/Jfi80GC7shzn5pazJeVZ1qQS/dLOBfaspQgJ2pk=", "SKGyhXHatcSRcp+bCLunTCs4oS2AiW30lJIgbCjRanw=", "qUN+wMxnpnNiPrWWE/HcWpJ3CLScRwPQNy61prA4a64=", "cQm3Rr7uX4K1ghi0Gag/eXtylgRIc1N81h2vTu0vQTQ=", "uaj605W3W4hg8TVt6coLQ4zdjYzLlGc1a6F0hJuticU=", "Nr3UmYRl4YPIlKoC4O7hHRMB9/qiw/qFHEQE0GtygoQ=", "xPWj7MhU/VjgwCQEDX3OMZTGno/0HDc0hdHfktIbD34=", "RCaafleUpf1pmRQCmQlmlNEJfqNsTR3Fj38HaHPudhw=", "c9V5qh/rEvA/tl+Pbph9T9LJFl3W44wb0QaPCEOS6xI=", "3URepPptXKgZFpWrpQYM+DW3IOfnjY0X9ONQUC6DXKk=", "a7x/PsDGAmXWwRQT4FxHUshhxF3nDpWvC9fWvookowY=", "1gGe1d7PgFrE0yK5Hl2Gq5s8Q/OnjHuPmhexpqPdScE=", "2bELORlK6RLKlwoYCO5KhKN1FyoLNFU/k4cP7SHjfEc=", "ntAx2NtrmVjgWK701Z4iU6daAHF4UsC3AXl/Ju/y90g=", "x6sbyiREJyAyhGZaQ1JYCIqiv+h1tX3DVnlzNxKhx2U=", "0IuJ2p9EIJ73cTZFaonBx2AUuiHiLksocZa7s+60ew4=", "ufhC6nVYO7cpLHMSGMbWIFfpgLsooBWPONNbfru/tpE=", "zVRwCvEhU3HtgaooZ96vBh1+UoPJPuytor+mkp376PU=", "IvV6HOnicqfAIry6u5VHPXSAx7yiljS1GT8oIp9L8fI=", "xVdX7+s6gEWkvB1sQ8XKl1hRnKps7xSvG9Q4BZSrn6A=", "J5ZXcjSt0U7W26SLH6dQ586bs8nfkySA9iRbTU+JlCE=", "XcrxYgmnirE6sDW5iohvIPMgslg5LYiOLtx3N1B7tlE=", "11ZxnRr95qGPrLTipdRDPfwWEZcHRUwrK0Eq8WpygWU=", "GwwVu9NFyKfgDDtAy0cEhvs6X4adzdXAmqElSIMZqM8=", "JTX3gKs5JDXNKFD6kzIl5AtrljoL9Fjr/ZzQejyZBPw=", "VoOBhHCvsluedS7gc9dISkiZLhDJ8wQYoh5h/QgD8Vw=", "S8npSQNAGVP7z0uBzBVsGm3Aj6Keh23U9mkz0v8c3qQ=", "oS6MOtXcw1T60wvyWCn8HO5cFVd6r3h0mB82ZSli0H0=", "ecwX4NjAiADgsBKRfQSnQbauUsmmXVLlfC5frjwsCFA=", "/WsSTzLe22G4WXGqlH2kFzHCnihFIRE2c1xuSJncScY=", "PMZz6BD7szysFCHNI8pLRurauuz+q3J02drKlea05lk=", "m8xV/B1J2uOBPhQb+bI5weaRqRL9uwjmQb6jKoLqr6g=", "kEvAHqJSvWpXqDsknuDBio5l75bpOtg1cB8ERJlfZ6Q=", "o+d6xQKq3nw7inkuiQmTovytTQzY2QCbAMowIEDd53A=", "U+rJ0FhtlviypiXaew4PMecuvWFbO1UUMss5MW/JDy4=", "Zpa0aFoBH41hQiHlE+FKylFxHTbkdXXU3V3Wg5F3JrA=", "yPHKldWW6b5AqfT1BT9MA6vlgxj6h9TH1hFF9IqZyW8=", "zyOfnw8Il+MKyZWE4j3oqdEPgWRgiYCZdTquFYYBE80=", "8ne+ruvGDeZt4F+PlVYpbjG3q4BDVlFbatGaPY1uS4g=", "fgY4yRyaIAePWFa5yJPSIlOktQH/gZN6umARqy/LRTk=", "mCvMTP4OWT8We9UcDalZMXXkkewHfv/JhApUxeFL03g=", "ecXuI4NqDO/U2E3zlHzwwdFd5LkcDitxQgZUqYtHyfY=", "JO68lljrLRMiw/EwBd7i5LDK9q3CunIJdv6tjf1xenU=", "L/GPTQ0YYnB4Mvgc8RM4ScjmmPHppGpi6QINSZFkuBU=", "ALM3PRcan/I4qMvSvShfcQjF2gCuubBy5M5SsZIAzpI=", "os2O6dOriTwcwlABe0C7ka7MgUPYZgWkMUxJ4IM+k5c=", "6rP8HJTx99TCQv8Z+RJm/O0nz7defCQR89K2mecKogo=", "62n7pHgMHDhc/lU7yjZA7208oxD1dnMA6wNzzY9ppKc=", "/hC6cj2rF1+b7IRjcTv6GV/EWj56DRQH0e36WtJ2ewU=", "ZvimTFS2rqQto/Fjs3dPIA/Fdy3V7jKceIsKC751500=", "ZaHXZ0z70zOfgUeZU2FzaoERcPgi9i7QqYIsY/owfIg=", "Hctr5GpNclLFWO5i8yA4+aXxmHLaLvksLebIJZZZXAc=", "QO2aYTO0LYEamPo6z2CTq0YV0nLbQyoOVOA5Gy/gMzs=", "j/mV5skimVvMK4NCpC8zdiBCYwQlgDvwZ9k6f4ZadNM=", "ZUie4A7+w0P8omoX8WVOr/EGoij4cXFT0V5ks8AgGZk=", "2WUzY0FSCubJN5SLLvGLL1YUI8TvusP9uB6n98/zCV8=", "km525HWMNmMHEijs85H8dW0eok+9pNto9HHCBC/5LHU=", "w3lk2RFiGakSOoPo7Kg19dpF0c8q7VyLO2BGaRY6Njk=", "zmfSyRPelqA/BlvNiNw/4paAuhCDQlBR66GcqKOOWqk=", "EvfnAStzfVV7NUHBzDKsgjdzxM7vxDpjSQtU82zVpLg=", "Zz/Ozd/SbBTABz/oBIBtdRkasq0N03iul6btxcJGXVU=", "y9LZRCMn3/+ihFM8WjI23GWQ6G9XU0hLK8SU9JXk098=", "iKFjU+Mq3rMzYCybBQrDWviS2Uv+TPaekuh6+NwVtAM=", "5Jomnl/uPQOXFq45h4zcmBIPxOoe9B0+Mq2qv5yt2yQ=", "lPB+PYHM3du7HMwQm97iFpxODNvHuONpCRw6uf995Qs=", "gjC/Artv8e4rUO1X+UVeL73ABW6ytYivwjIgt0Lfwks=", "NP4ndIEcUMLqO/YJGTnBu9QuyNx0yzKyUUjxD+jTr7E=", "UQ3s2mWY8mWZW9T6jKziVO86KX6uci7fwSXehDMd1Uk=", "Sk4VAP4Y0R7Hqn5Ec5w/7+3cWDm3ayT/oa9ORJ9HptA=", "1RdiXJcrIUrsagVKuCGZuogAhBSOhEFU4i7tY6D3W6c=", "ccz87Rw9SFZjnDx3p2Y5xRbqes1QnsKbGq6wmXh5TGA=", "G0t3zLXzY+E3ZkpBUpu69hZzUx8LjqMidDyJeYugguI=", "NK4I2xFXWTumDgzYy42tZd27c23qhmg4JcH6wiWe1tc=", "zoVOqSh8YnPoefXjqqNfMjl3XzEeHeEm7AxhDCbi+a0=", "5FuA+hiI8Uwl/OAcTra6ucQVItMM/7Zhnibppq3QXtA=", "sI1IhLYyEs4cYU5TyUNGEHOz7XjfPOXVqxtWfN77FVQ=", "SodN0MntqSPtLjxLSp0K8TRXk96gwQxfsBxS/BG7vIc=", "nXveDtcz0t9q0bniTUFNBCIXewL1QnpJ+3u3Ueyu+ec=", "JOcMgnBhp34uo6bKKw8q4Y1eryBPQ4nD+PA78BhAAvk=", "m5P9GOd4bGYFkOV5gJkOKO8eXNCDIpJ9150Dva0Tod8=", "odYwrLCy6vgQmRaR2n0sRAHOk6SWsDrBTVPFzGG9z0Q=", "/d78IgGIZM95zp9snA3d7RFzIwz4vrL7St7k7EnaAlE=", "Aa7SGkIpogq5DSg42eOvij38etiPiu65RsyCifVNYOc=", "LT0jDVYN1XbeJviwwFzS5U72D8KsUQOU4JiyrRYHrIc=", "yh91T4Fic0MW5En3yFuOQMQiO7o/yq1+EzShnYNHWtc=", "YNZYbhoMGHIrub2UwzuNe7sacBIIrW6jwYg/Gg5rk4Y=", "85ndlgvWC1E2Y2blxxo+qDL/WjPoPOrL/vpZcC2ItMs=", "/k8ziHpN7DLUSEkeu1QPnJxjkVrfxX1PFdL3EuQ82J0=", "IuZ44AgWTla4Zkgf5PEfkxudWJEUIYMCa4Z/WyQSB3s=", "sboGazIVx+rhAeGtDvWs9w7ACrFPgZFU1qQqc8hczeM=", "BTGIj8RB1l5UfowduTPpAWMTv44YAdCEs9JpBgsdp3A=", "XbuwlrHmxgehXGXMDyG5QUD1c5uMjV2maWbT4pga/gg=", "45qw7XsS51V88r9m51Uif6Mft505X/M5psoGOCkGwmE=", "ytDVhx3XJLMZeBeQ04R7GTwBq38gm1raV8/lsg3RTew=", "U8d5ghY7aUGNsD9nEx8HAXb8SaQnCF7b70N541wCUQQ=", "Dt7KFCFUXxNJs+aZ/mHZb8CNvwbad+UFNqzCCbfKdEM=", "YO9KxxzBdfaFegHa+VWc1q3gCVCxx+hDcpIHchr6psc=", "sz2YYPmSXJaNCG0+7WUbJKoOugIJPYmtHhqZs5vZTdY=", "I+ahNhoZH8WejC4fcBfKzijtrOBbg6+ecdtc66nCUHY=", "wXIfypgM82DPWTSx9b8in2OtvM0lRq7nL2XpraxihB0=", "AfMHsGUFzaI5meDN3bDGX9+gc//IIst3OmEDnv4dSbU=", "/kr7hTpgyOrJqyaNfVJA1Nze+K9O/mKY7Wvd3vK6oPQ=", "LprW1vVXf4L8F//q8A0t1cLNYq5coA/QLbuSr2taQ5I=", "7U6OC8VgyRht8klfXmwfPbggpMnKM68n7fwYeCnjijU=", "V9Yow1b7OxX7JG+GMNhaQObAsBuvjbwaKS6EE7Wj2Cs=", "2BoIPgloP9rIEtLxKw0YFI63yaYeDjetlwLEJz4H3X0=", "3ecdQXC+obaVDoyqnavK2PJVMXyS/Qj4TP4L4BGER18=", "BfSy0vIUYYws1X5DY5tIBS7s0nmpCxbt0loZ8fqXxkc=", "TDpS4oYrWJVF8LDgcMRnNQUfX/5i4cpRF/lB0YUpe/M=", "xxcIpvjNXeTydG36E0y2pq6bMulZ/9eYvfgBVnsD68s=", "JLNc/CgRF9wAesPTMjGi+lutXLLYshsuPUPikDPi0fM=", "JwLuhfPK7nIFDKuiV2CgB9IoGp3keyhuk7Kh+mel4xc=", "lWEsZ20c+vRlfA8wHX2wxJiI7/Ve4ebNQEi3sMPrWyg=", "jegOnc0t4Em826RaJgKkDpeK9CcEafRfcoJ5/1bh+54=", "zfF6oera4+8LomlOpflhc5J3/PqY2m2SJee71XeoawI=", "qKGddrJNtsAlRRkZc7nHWU2WqyU/9FKPSr/6dm55Qkc=", "lChexNZJXe+FSYu9fgNaN9MosRHnv0rIiCel1oiGZYc=", "BHV2BrZC8kcRVDtMV6XRDaNmB7oK8aJJfzyETIqZBMI=", "vdWiTMwlEVgQtmieMUIAU2ryhD76gBmMtLhLsue6Mjc=", "RFva2Yo+rjzu/7zBKeQ5BTq/mNpDIn0u+fyBVcuV0qs=", "3+T28hcrSAnXFrQgolLqH4jj9tAjm1ODZNX9mQCohlE=", "wstgUAtmPsNCbUdLF53L0uSiSVvoAdOjyWSv2NFwSQU=", "corRAa4opPkqwI9uIGmBicnSaGKxrbiYqT1VgtubqTc=", "LY0VsvvKn/f/4ezZfpbuLq4NIfx/8hjFr3mmL5QbFsE=", "T44E569wW3K3MkDa+CJUzO451Wi8a4QMGuuJGdeH+Ak=", "wRyAhloYAnNzhdo91medG9KlXHLUzgguGlkk39S38FM=", "ySE9A9gIybxqg/K7v37D+JdJEbUQBOLI+0QoZ3PIBVA=", "DfIVWiB7pIZdVDj0g8KD1bEoy/97IB3bA+crLy1nGsU=", "UAqf8OdE23OBdwikp6tEQ8a9FizriEHd+BtKRcLJH3I=", "e3TAAKFdSW07qXjvIwXN8Rq6ybUo2I8uI3XobNkfHeQ=", "cEMXJc7o84tiKCodAMZRzMwlegIWVudZCvUKyNfpruY=", "URqM5rCz4l/Hgyw080rogjJQyVv8BD7/O+q4Fg029u0=", "asO3MlRX0dqhr75p0NJO+pf1ngFFPJKSJUgFVKjPPRc=", "FYnac2suDPUtgKYOWvvNcvedKv4+zx0n91oOHGq7WuI=", "vHtvrjzZrsSjKbEg7yzwx5sjFkw6P7bBldfkxWecloI=", "VGlyCW83WkTy4O2rWFHJTxpBJrKwB2sRGkI1elVKZLU=", "9tqRnzZvXBeMgauOQnvXPfhky1nkqA9KCO4O8XFEThk=", "32cYACOveoeYXoSJqaqrL4u35bZPOnLHkaQp/Zh7e5c=", "LPTOGJbZsTm8S/Xir8+x+WmM0JluQOegOZGbNspV1rA=", "I98iP23tasnuXt86TXtf/Ox/lqwA6ZnuES5uotc+TRQ=", "9fX5+bNUUwLFio0xIV2x3z4bZ3Hz+UTyhEwRgEXfB4E=", "Oko0lGnlbMYqGjH2/Ge+JUo+exGP9IEqa8x3Ts0YJZI=", "wy4nLSa4SOOLKRzIGU4WWNBgeNaftXgy+fkKZpZ4jhU=", "v5mEMa79+5Dw3V1hflN1wokTUWNqqB4VV2WEKnde7Zk=", "kTBnaa/RB5hW2vIV8SilvsSeHJFujjl2BeuYopFqHaY=", "8JQWVS50mZYqVTx8KMHeuM9kd49e71/qbDkglQkG0ZU=", "DwdyGsP5pQH5bX/aT2bNtsPi+h0e7e5Jtb4F0pFhNGk=", "i0hSDHLSuhVIQMYH0WeM/k6Sar3c8yE0b/pDhjq+7xM=", "taS0ZO/guNgu08C7cegWOZtnJWRzPM4X/JkIWmxyqLg=", "dZCvKuMf/6KJWViYMsbI9KDjHx2TRXIpp/DFok1Zj8s=", "h4i1BaGIX8jIFgaURdRjGqZsWbqvPpW5EvPKfwFi4YA=", "UU3OU8spuUfiGbX7nJUrEgyrlCQGYeUNMofGsHqK1/g=", "YZeWOQav66PQtfZu/rAK+C1/mdsSmRVEMfGJGYuxHbE=", "S7PenJQxfqlYf3BCPLkiYG1AcbTqF2BtZv9lblHfJNc=", "6rGndtgRo7c8QEXT97AcE/sFYEdhYVDyXfCClVNEEjA=", "FFSuxJ3k41oYAJeM4Oyz6yYUB19RK6tPR4JEFEpA5ho=", "XXk9bvLXuBlts6QhFyPQaBH6f4sVs3YyS/XGGGbhgHA=", "jRW2uf+z2YJgZvERGMS90R4xi1KCK7yoEWLceIfr0cw=", "1TzTWJXqXTsYf/IuJEla7kQtqVz9/tURto2PrTj/8VA=", "rGgCzLeXpmPxfGicBp7lGNX6lKdzxI8i8TCSs5FyHkc=", "5vpzojn4WfgfTdaKNEILQEVooOwi/f/o3q/iLyjhjWE=", "m2Nrrw//rB8rB6dXdol/eiXqz0CTydOfPIn7OURt3as=", "JMWyt1NbQWBgeKhyLZOym/OFUy5dFrpdatGLzca3jjY=", "VgjoWm2YDkNZLxLz7vWOdrtQNb9ejuEngureFhPDei0=", "qOZ9DtH9sG8IHvA2VxmBTWA41zS9iGqTwUZCioEI4ZQ=", "sNrfbXVNgQy2k+lWKuCGFqrx53PGXsbVO+2w5+oj7Dc=", "8DeHMgp1OD8eUIic5j8oDatmJtlQiJSP+IB4emgmeJg=", "WmLf3UCAj7bCGu4ir4sMdFyOXXpxuC82VGAgQSDRBZ4=", "LNRNglYDqS8Drz0ymNfQ82F5poLqWxfJ2Bl1DI3lcbo=", "9dMPdgU4rLhAHtne9NasBvdd1Hl3AearsvrUoqtFU+4=", "tCpY+qcTyt2raJ8EKHEU/qbzdP2L91WrslxxjZ4M3Cw=", "2cliBUlAp3PfvQLRyelAbvWXJQIgIcyE/T/0PtYaefM=", "ZgZPNOCQY5/L7KzpOBfC+09X6RJA76rRxkuedbxvkgw=", "JfP+9BxR+6NdIxzvesWQyex9y98ZaBoEER4hc737wrE=", "4oDTbkTVHk7RO4BhVGRRw9YYRhjT8MoYmWsQkhg33wQ=", "/RfryhDhoIb2e3u8+Anrw0jXaQLLDMOEvYWGdqGiu3c=", "TE/N05Pjln2QfDSV9JK4cIS77J25KZitIflHUfYE758=", "EZL3fx40SdFufrcU3ZC0wq4tuwC2lMDBjFXy1dYmj9M=", "kKH8vRXXMKyHogWLXnd0ZO4t5MYSZ2uvejx45EmfOcg=", "nNgIZtqH7ONz8W7HnBE9sQwx8ZpFlRngoUjykVXGde8=", "KzAUvx83ZcnZiXDz148Drczrkz9IMjiSHxg6j7a8Z4E=", "zcR5zAtp12PmY8iR+J4O0jbZ1gaP/tf+F380L8zjBwI=", "nsPv44oY4Coa3UxoZokwrRx5+S+q5cHObD4CKXdLAis=", "29X2Y40z+cC1+C3JKVs2hfhc/OseohZ15CSAuPTFa9A=", "9xW6zxZkhM72UrLqw5opHuA1EdhKn/WtwqAZI8QlLTg=", "mQLsoe/B6FK4dPKhTS87w+pKyUBY2CWbZpxqNhvmpOE=", "MlPc0h/fesaK/lJl3xQgN4YEU2s2bslgGD1N80p0mlE=", "2S4oYNV6+0Bs3bZBVKaF9ZRJOKhWahn7Hjf6ggU2jmw=", "LeHwVe0OyOtsJix5O0WPL8nKt2pPZV30Ha3GioMDTtw=", "5hVPpP3wULn7WsMIv7RE7boOzORgOvv5o/sCnVOhGEo=", "MdiGiOHnrERSFe2nAzvlISvfsugGonOhfVq34pEQtZc=", "pmuSQhOJT0e/2V8nQN3zQTf/tgdIg38af1jzZIF4OmU=", "TAWH/m3+pVfZkEHUaVn9cBulpNeV+sRRhmJB8zGCQWE=", "anrHhfsn6lXpDowWodRDLrOTNbFoEktbxISmsTZp8Wc=", "+4LW+as85V5vdqpTpPbFR0AXHQQyML74I03vQSnjUyQ=", "Re5igt0lTZ0WlqaRUx8y0kN4VwefxrRc5xCHtVLHspU=", "6ay7Jkc3JcudscC6CHP/F/PnQq9UF+V7TH+9TTPHkcY=", "BeBhw1B/fA4ynFKyOnPosaCv7lz+Gj8V+SlBF4yZMAk=", "O6R1SkEfTvn6qZw3XpDfdgn/14y4Ss5Y07rGu80xob0=", "dlsm5ey6TOo/wlpAYmWUtfDPvV1hkVCdB/4K2ejBzsw=", "itW5U7A8lOMVIIXL08dcCcbQ2gsY9k6LrZVq46m1o8A=", "oqDvvgw3ZZLORzEAgSRwel8sybf3yiuE04IKPvd50i8=", "qn0cImkpZomv2bVH0WwqXlQrIZtxS037NabkAy92kMA=", "sPzuyWbsWatYDYDNnRtUoVF6IlDbM6URSNPY56zqTl8=", "O37LOxu6NF+AA4l0uiArbqxUoYb1AHewyrVl/Y23yLI=", "l50v6Jdqui/5yIawkfeKBQcoIOi5hkM8s9hJeWcEA9M=", "B09KEUu9J1UZeTnERk9rZi8xwB+RxO4PwdujalzeO40=", "Nx7o2xGFWVZLuTsAwcv0ZK7+fGuFKy09VVXHfRagrXw=", "Aje4/7uuebiJApnHvpneup+CAoSCCrDm7vAZFLeab4k=", "5WMQNTgIx1/oMSs46KEWjzkXFRmNNcP0WCgaRnX6lEY=", "+zJMWwZmQ9uR1ZHHxZtHSCKtQ+oCn3oPTsQKKb2AQ+8=", "mnwkPwCvQJPfDqLTJ6gCTCElhUxksLbsNnjYMLV6l14=", "jf7Fd6U7NrZ9/Zf2CZLRNWMmPFiFfIex6h+yjbIZ/l0=", "I/5ZxBMf8RhMm7rGKrWymdoop7cJui3p394rFWHF2zU=", "J70RHa5tBpGIgLeVVubDbFlzL7lLrlSymnI4u2jfoT4=", "OmRRqeAmVCtO/PpUk3zZ3+AuQJiD6JaMZBdIgjcEtGg=", "Ilj3FjGr/IZY2DZ4DNWsOXVPz+5eaaBQUdk3Oth78O0=", "iwnXiXq7hfREjIA4l6r4iYATi9iKFfVwM9zLfLAmwCI=", "SjQ7eH1Phb5YZw84YNReazOG/k1vsKA6dSno7AlgYu8=", "SSHZBFmWIiQesOeNZJDp3AykmPBtVDOm/YhVPHv6Be8=", "MnjLuw/GyQE6MBvHeQXW8sbuNOWQxdplDGgZ8ksQILY=", "+0/GDPKcgTj8ObR7htfLMslaw1CimP292PC6TP54TV8=", "l8gxdJod/JGiuu0anXxwvnx6vJbUCZkzW2s3w9TECqA=", "l3rBOmie0Fwt6c/zwP6lhYB2dNgjeY3dxrGDYQAeJ/Q=", "UI9/rzEUa9/vg8Z6EwU7clBC4Wg1joVg/7CjSLyEvj4=", "ldwF9BTvokdOtm5zY9dpuUWCiZ6+dXP6NqD5ZwwJQJ0=", "1cyi50z0OsJ/HzdDj94iVS5qx0MmcXcmbcmygT+X1yA=", "otHDRMiwUCXBdA4H1P67+9qdPHIMd6FAdrGIoMJLFNE=", "gGCz1w1Qb19d6D07Vbl3ZxPZQ/aBlpweAeLxaMRwuKM=", "QSTFDv52tjITrFk1Vvq6kw+lPc+xdazsI+edbc9ntlc=", "hJUPJwxKIrYY1/9zxAWEVBYqSxAjJ6gEH/aP/QFKjg4=", "0QoVAJdGbShCs6Wv2uaSOxUAXyVarcL3JtdcLVo3kwM=", "4VdRYT4yeM6pMa9daimHO6+bUuYRNYOIGpqnxdRyBHk=", "0uUNSXc4bHqAtcjWTzu1bBTvVfdI8Or0x2GvWFoIpA0=", "VlFbFPVqh3FjeLMqT+yXGyd40tHgHStupF/eiPnvTxo=", "a0amJVyGpRhiOe9VwdbvxHli6Srzm2ILixn29M17w/A=", "wOQ5j6CaSz9LPTmnExaq0qq5dDGVfAKeo8pXWwhHaRM=", "ZV580w1aWN95e+9IoWVXt+olpqFOhna8gAyzx3gjuDg=", "Kj98AHRPuXO1TPVOMp0wKKzLAK23ZMYENBUZwPXvNok=", "C2vn5SQC09PCtkWdhZJ92c7BCH4I5rPs3hSSUDiwce4=", "H5YPfzDWQzhsZ6a6KLuBgLELuXukzW2QQ7FUwi5fCsY=", "wum/kSGj46RBCpBZUS52soqC/geeddP+zOwM6DGk7XM=", "7Ba0dDOdLHYsCbt2PlB8ruXwwOKEkQ1p3yZk2ANPdLY=", "a+mEYLp4O6yneGkn+I1HGqBmWrNBdkeC2YOXa+iSjkA=", "Tcvcf0O/htgIYD7fVm5+pbjkuIkJ4Z8YKas3oi6o5fY=", "m1++IIEbW+5YgtY8BJCCqr6oHSWTsW80JX9iiz42pfE=", "+sm/o2etL7+eRrBNd67IxAbN+0Ayv3X12lydYhBjzmg=", "16b+rNdGaGxDvtrxRz0r6ZYO/9POEXK4womHOnN1xLs=", "GQ8DOgXcRlvDK/wfLClUlN7MdIJkLQFxQa/4tE/jw2E=", "EVrMefIrxMDE7wdDPMpw1nrHNAhDY/u07WeuAUnuiyE=", "3WQ1qVRv6p+RKUtKBeLsjTwPJL6/gsoKbGjyYp5Fg/8=", "3f5B1AqjV2+GbEafVR97GjsAXQCHxB2WKIGK0cLSGHw=", "YU61nVzxiYgSnMSZLfVA0oSCkDD8PKs1pam6CWdfPJQ=", "XuVTYl1SsmgKoiSg1mLAN2qVPPrhRtl3D6+My+1TAIU="], "CachedAssets": {"vBQj9fDP6Uy30mUfuN6129zDvlyAXciEwS7S6X8dW0I=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\3znw05pd5j-8rbvw3on5j.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint=8rbvw3on5j}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\css\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ri8oomj080", "Integrity": "3V6n6GUWrXibPBz7fhqi3nKUDtOkIOblRDVtLs9wCh4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\css\\app.css", "FileLength": 2031, "LastWriteTime": "2025-05-27T22:01:13.7993595+00:00"}, "5fyn2zhvNgfxOAS7uqzcZMJG0XI1TSK1Ndj8IuWve2I=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\0g4dpgfbmy-ycv507p0zh.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint=ycv507p0zh}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w7jbrql77e", "Integrity": "DOlPMCwnUkRFFFNyDmlgyU9pb5Z+3FhG/ny5DQi2wpY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\index.html", "FileLength": 1184, "LastWriteTime": "2025-05-27T22:01:13.7383987+00:00"}, "I096DMO1sNnuMC4/rS44UU2Wb3pnroMSmBr6nEIiF7Q=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\kvtfxmh6pg-oc05c2u9aq.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "js/advanced-features#[.{fingerprint=oc05c2u9aq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\js\\advanced-features.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z13wei941m", "Integrity": "iUJ5RcpaRpehzOChJnyYZfSS8sUM3PE0EnrPFGr1J+g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\js\\advanced-features.js", "FileLength": 3122, "LastWriteTime": "2025-05-27T22:01:13.7443957+00:00"}, "DaWXYSLq+6XqVNtanCz4e66sr95QdIVjguY0hTBLtiw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\apbg2frf8u-bqjiyaj88i.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-05-27T22:01:13.755388+00:00"}, "gE6fT5uFWx+LhwfVf8NJDzbf5ygKCBFbucrj4C5S5No=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\676pdy3xu2-c2jlpeoesf.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-05-27T22:01:13.7473917+00:00"}, "1NJC7Qe8NZgvpKoqyFpiw/m1hXEmkUAwtoqXmCVMMyg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\39zuxfvx2t-erw9l3u2r3.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-05-27T22:01:13.7483908+00:00"}, "cCZ2XZXOVFj+gojzOOgwuhSt4BWHELhgApTv2xsvI1M=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\guhz648dfx-aexeepp0ev.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-05-27T22:01:13.7503899+00:00"}, "1REiR6HFyL/ZKmuGd3zwbBBbhYnH51FNz+KSvq0grZo=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\03xxxpvuuz-d7shbmvgxk.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-05-27T22:01:13.7513895+00:00"}, "JzlPNfviJCNiPUIL6QcrQ16LMKPy1clx3wSY+kk1m0Q=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\i41r3tsar4-ausgxo2sd3.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-05-27T22:01:13.7613831+00:00"}, "LYvVU0Zq83KR3vKBCRDHzVc+XUv2TTZShlH/9HmUg70=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\i6lo54s51n-k8d9w2qqmf.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-05-27T22:01:13.7623825+00:00"}, "7l3Txt9EEH5yKz3DgrthZpKBeHqhYbJczU4SIELcxuY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\qb7n85dym6-cosvhxvwiu.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-05-27T22:01:13.7623825+00:00"}, "eFvfB/0liL4bE7nHFIZQdUZB9idz7OgOcJwEiSvFqCw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\vbgw1juty0-ub07r2b239.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-05-27T22:01:13.7633817+00:00"}, "pRCSsUnQYdhYSgdSGnMg1hc24RiFzIn/cKL0w786S4k=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\ihv52x5deq-fvhpjtyr6v.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-05-27T22:01:13.7663799+00:00"}, "g1ELnEWZVLwU7L7ZsE+8yPlQWvIRWgYf+N5jXJQmzeM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\xivtvhk866-b7pk76d08c.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-05-27T22:01:13.7683786+00:00"}, "nIpGpPBx2nXj4MDqmhrny/d0WERsiMc37Mmrbt06j/c=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\1p3xshjrgi-fsbi9cje9m.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-05-27T22:01:13.7633817+00:00"}, "2MupjTbXTOw3I1XCkNN4ulsJKteQarW5YxxvzGfwlDM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\qirwr63m8e-rzd6atqjts.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-05-27T22:01:13.7663799+00:00"}, "zitRP7ZC+QsrS7pt6xYIfVXdMAhaG84w8sUMqvOgUJo=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\x5kri2rrwx-ee0r1s7dh0.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-05-27T22:01:13.7723776+00:00"}, "0gL4EbIjZj8Pz/biBfDXzL0TPuueMJEBhNC91AJByDI=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\t147kmly3g-dxx9fxp4il.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-05-27T22:01:13.7733771+00:00"}, "Z0plM31Vc6L+Xe9DsO4ag+9+V9ZecWnUYgpOGBFJU2o=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\a8vhlhrpel-jd9uben2k1.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-05-27T22:01:13.7773731+00:00"}, "eYSQexJa3Oxde+fWx3cUP5I6scBABcwfYcwMzB9STcw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\am79qs6ie7-khv3u5hwcm.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-05-27T22:01:13.7793716+00:00"}, "OzRijkHkmwQgavqnNEBs3p+m7BHQxXog7eHcFavG4jM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\859ey37hp5-r4e9w2rdcm.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-05-27T22:01:13.7833695+00:00"}, "FVAyhb05sOH0nuVah81bK3pRVafvq7/Tp3w/P4ztsKo=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\w59s09y059-lcd1t2u6c8.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-05-27T22:01:13.784369+00:00"}, "iSJeltujU2qefNZ8fgp1BZvg4Kt899FI0FeQI5HGlck=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\u7girbjqzc-c2oey78nd0.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-05-27T22:01:13.7793716+00:00"}, "cr1oyR/N3xBWuJNlKU3TZ/z0K1CP1vZKbAmuWKwTXQM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\jviw60dhmp-tdbxkamptv.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-05-27T22:01:13.7803714+00:00"}, "KVoXEGuqFuB4LlSHwiWfpSiA1FwCkE1L2VfrLYgEQDc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\vzp7yaywun-j5mq2jizvt.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-05-27T22:01:13.784369+00:00"}, "hbX7ED+25ASNv2gD6JHBHmfrqkvunNyZGENdz8DKcA4=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\nndpijkz2i-06098lyss8.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-05-27T22:01:13.7923643+00:00"}, "joAKZZylFM4luNctgv3jRG8cH3TTEQL2qOblm/aJXJg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\zzogolbk2q-nvvlpmu67g.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-05-27T22:01:13.7943625+00:00"}, "VIL/Jfi80GC7shzn5pazJeVZ1qQS/dLOBfaspQgJ2pk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\uku6jk2qkz-s35ty4nyc5.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-05-27T22:01:13.7993595+00:00"}, "SKGyhXHatcSRcp+bCLunTCs4oS2AiW30lJIgbCjRanw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\5l3hnk7cwj-pj5nd1wqec.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-05-27T22:01:13.8143512+00:00"}, "qUN+wMxnpnNiPrWWE/HcWpJ3CLScRwPQNy61prA4a64=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\2cyqeja7ij-46ein0sx1k.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-05-27T22:01:13.8583241+00:00"}, "cQm3Rr7uX4K1ghi0Gag/eXtylgRIc1N81h2vTu0vQTQ=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\dmv5t1350d-v0zj4ognzu.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-05-27T22:01:13.8473298+00:00"}, "uaj605W3W4hg8TVt6coLQ4zdjYzLlGc1a6F0hJuticU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\bwzai814lx-37tfw0ft22.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-05-27T22:01:13.8673177+00:00"}, "Nr3UmYRl4YPIlKoC4O7hHRMB9/qiw/qFHEQE0GtygoQ=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\16j0q4bgic-hrwsygsryq.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-05-27T22:01:13.8923028+00:00"}, "xPWj7MhU/VjgwCQEDX3OMZTGno/0HDc0hdHfktIbD34=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\7r69k1xezh-pk9g2wxc8p.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-05-27T22:01:13.8953003+00:00"}, "RCaafleUpf1pmRQCmQlmlNEJfqNsTR3Fj38HaHPudhw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\w6lty0gveb-ft3s53vfgj.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-05-27T22:01:13.9062942+00:00"}, "c9V5qh/rEvA/tl+Pbph9T9LJFl3W44wb0QaPCEOS6xI=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\164orc3m5u-bg73c5uikn.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=bg73c5uikn}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w1q1cbwuwx", "Integrity": "3K2EIErjxBmN+WXJGyxdbjj+ZqHgoRbY/O2W5+5A9Do=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44357, "LastWriteTime": "2025-05-27T22:01:13.9122913+00:00"}, "3URepPptXKgZFpWrpQYM+DW3IOfnjY0X9ONQUC6DXKk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\4xaoy75vdz-car65sm25x.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=car65sm25x}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w16565go8p", "Integrity": "HFiq7vN+gaGaqvmj5jYlE/cMaafbeSRqSFAWRiJl3PY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92054, "LastWriteTime": "2025-05-27T22:01:14.0651954+00:00"}, "a7x/PsDGAmXWwRQT4FxHUshhxF3nDpWvC9fWvookowY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\melpxu4v7k-493y06b0oq.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-05-27T22:01:14.184017+00:00"}, "1gGe1d7PgFrE0yK5Hl2Gq5s8Q/OnjHuPmhexpqPdScE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\2g4ykkhh2t-noe7lm1h3u.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=noe7lm1h3u}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3q1knmrm1i", "Integrity": "RfuABJdArtrE8RtQkmKPHM9z6nH4Wp/GRPBvGv+GLM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86959, "LastWriteTime": "2025-05-27T22:01:14.1930115+00:00"}, "2bELORlK6RLKlwoYCO5KhKN1FyoLNFU/k4cP7SHjfEc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\wl0wsp5n79-c887lhynek.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=c887lhynek}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b6vu4azqsj", "Integrity": "U5UwS5vqbypQY/QYywnYVIKMhuO2bYO8uLl8MCJBhTo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28855, "LastWriteTime": "2025-05-27T22:01:14.1960094+00:00"}, "ntAx2NtrmVjgWK701Z4iU6daAHF4UsC3AXl/Ju/y90g=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\5lhf60ub84-775ggt5h4o.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=775ggt5h4o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "68ddsj5wz2", "Integrity": "NpxfK8ZN5TxpsvscoqJ4Tato63ha1JuWJ1R5jdF+oKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64127, "LastWriteTime": "2025-05-27T22:01:14.2060039+00:00"}, "x6sbyiREJyAyhGZaQ1JYCIqiv+h1tX3DVnlzNxKhx2U=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\zkn5zttttb-jj8uyg4cgr.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-05-27T22:01:14.2080023+00:00"}, "0IuJ2p9EIJ73cTZFaonBx2AUuiHiLksocZa7s+60ew4=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\w6b6mcxc87-ml3ve6jes3.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=ml3ve6jes3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xogecqln5i", "Integrity": "AZDzoaBJ+4MGSrUpUNqPchK5zCeYSoRMBmXyGdn6ew0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56671, "LastWriteTime": "2025-05-27T22:01:14.2129988+00:00"}, "ufhC6nVYO7cpLHMSGMbWIFfpgLsooBWPONNbfru/tpE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\fr4wh0manu-ux6g6pshvr.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=ux6g6pshvr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jxx8r0i0jj", "Integrity": "OYfHoSAU9mLSOdl9M7o/ui0owNdLQhs544RbflMx4PE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29571, "LastWriteTime": "2025-05-27T22:01:14.2159976+00:00"}, "zVRwCvEhU3HtgaooZ96vBh1+UoPJPuytor+mkp376PU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\xy0hy0nhpx-vsszhq6w1o.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=vsszhq6w1o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gd713ndk34", "Integrity": "lEpWpxEvFPAJ0i57o+RSZfgxAV7OE/IxnPDO7H9hYDU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64429, "LastWriteTime": "2025-05-27T22:01:14.2349861+00:00"}, "IvV6HOnicqfAIry6u5VHPXSAx7yiljS1GT8oIp9L8fI=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\dzxnviy8gt-63fj8s7r0e.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-05-27T22:01:14.2999463+00:00"}, "xVdX7+s6gEWkvB1sQ8XKl1hRnKps7xSvG9Q4BZSrn6A=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\g3d3umg3qp-6v1eliq898.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=6v1eliq898}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7dds806hme", "Integrity": "q9qLfZtylQVlPO0tBUV8+OhaUqvVpy1XiXCEU/ZzXlk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55850, "LastWriteTime": "2025-05-27T22:01:13.8183478+00:00"}, "J5ZXcjSt0U7W26SLH6dQ586bs8nfkySA9iRbTU+JlCE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\r57f2fyds4-iag0ou56lh.gz", "SourceId": "Schools.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "sample-data/weather#[.{fingerprint=iag0ou56lh}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\sample-data\\weather.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4cbitfuojg", "Integrity": "HD3vAUwurZXW96vdgG5RVLhMjmVeSgBs4iKLSw2+Uwk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\wwwroot\\sample-data\\weather.json", "FileLength": 153, "LastWriteTime": "2025-05-27T22:01:13.8183478+00:00"}, "XcrxYgmnirE6sDW5iohvIPMgslg5LYiOLtx3N1B7tlE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\9wdu8pq31z-md9yvkcqlf.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.webassembly.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1uijd3xue", "Integrity": "aODHHN99ZO6xBdRfQYKCKDEdMXpBUI2D7x+JOLJt8MQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "FileLength": 18128, "LastWriteTime": "2025-05-27T22:01:13.8213475+00:00"}, "11ZxnRr95qGPrLTipdRDPfwWEZcHRUwrK0Eq8WpygWU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\i3xz8jzs77-zrbizd1hk1.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "Schools.Client#[.{fingerprint=zrbizd1hk1}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Schools.Client.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h7tr297tpo", "Integrity": "Y305zYhgWaGQSwSmQQRfea9dvN1Cts3Rjs53ohgyM10=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Schools.Client.styles.css", "FileLength": 1399, "LastWriteTime": "2025-05-27T22:01:13.8233447+00:00"}, "GwwVu9NFyKfgDDtAy0cEhvs6X4adzdXAmqElSIMZqM8=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\del5hs79zg-zrbizd1hk1.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "Schools.Client#[.{fingerprint=zrbizd1hk1}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Schools.Client.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h7tr297tpo", "Integrity": "Y305zYhgWaGQSwSmQQRfea9dvN1Cts3Rjs53ohgyM10=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Schools.Client.bundle.scp.css", "FileLength": 1399, "LastWriteTime": "2025-05-27T22:01:13.8233447+00:00"}, "JTX3gKs5JDXNKFD6kzIl5AtrljoL9Fjr/ZzQejyZBPw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\0tgdws4xyu-n7we7nbch1.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Authorization#[.{fingerprint=n7we7nbch1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8dwgdjh07i", "Integrity": "JI99V42ZlXuwUlbWd+jAz2TH6hn9u9Zom/xBNU61YAs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "FileLength": 18064, "LastWriteTime": "2025-05-27T22:01:13.8223458+00:00"}, "VoOBhHCvsluedS7gc9dISkiZLhDJ8wQYoh5h/QgD8Vw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\nco65bn28z-waskkzx0nn.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components#[.{fingerprint=waskkzx0nn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f2hdu0b03q", "Integrity": "WdpED5NvE8u4TVMJRLZEqbZ9OeJ1B1hs3uZ6xkZGG/Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "FileLength": 135097, "LastWriteTime": "2025-05-27T22:01:13.8303404+00:00"}, "S8npSQNAGVP7z0uBzBVsGm3Aj6Keh23U9mkz0v8c3qQ=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\ndxraozopi-cm4k89q76m.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Authorization#[.{fingerprint=cm4k89q76m}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n45nxfy520", "Integrity": "tzZn0fky7KYnqUL+y6vqvDTs+kHRyCmcQ0h1Nioeptw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Authorization.wasm", "FileLength": 10079, "LastWriteTime": "2025-05-27T22:01:13.8313396+00:00"}, "oS6MOtXcw1T60wvyWCn8HO5cFVd6r3h0mB82ZSli0H0=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\050mos6n6m-nkegowssc4.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Forms#[.{fingerprint=nkegowssc4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uqkby6mtqg", "Integrity": "xhfCvmFEl6wFv9H64CwSD3FBehgIZj3LTWYfIKiXFmo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "FileLength": 16706, "LastWriteTime": "2025-05-27T22:01:13.8323389+00:00"}, "ecwX4NjAiADgsBKRfQSnQbauUsmmXVLlfC5frjwsCFA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\giy04kmgmv-x0bkxyyfna.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Web#[.{fingerprint=x0bkxyyfna}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dzeql5eh3h", "Integrity": "WVfOK9v6tL5pNlVyHBYkTMGmOJM0HFDSoxQzcevtOH8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "FileLength": 72577, "LastWriteTime": "2025-05-27T22:01:13.8283419+00:00"}, "/WsSTzLe22G4WXGqlH2kFzHCnihFIRE2c1xuSJncScY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\uh9e75bgae-h6ioronled.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly#[.{fingerprint=h6ioronled}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ijwhpv5p2j", "Integrity": "Ahx7fiRa4p+0d8du7KStExXiCwrIGXHAzS5EPF7DSXg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "FileLength": 67490, "LastWriteTime": "2025-05-27T22:01:13.8323389+00:00"}, "PMZz6BD7szysFCHNI8pLRurauuz+q3J02drKlea05lk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\r54zppvsxu-byb028jwzs.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Connections.Abstractions#[.{fingerprint=byb028jwzs}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Connections.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qp8x58jsdu", "Integrity": "Xh9P4LEn6Pl6+RwlzrPWpn/iXpQzazXk5kAtzDgiZsE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Connections.Abstractions.wasm", "FileLength": 13644, "LastWriteTime": "2025-05-27T22:01:13.8333387+00:00"}, "m8xV/B1J2uOBPhQb+bI5weaRqRL9uwjmQb6jKoLqr6g=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\21fyz6ud1h-kfu5pdpt31.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Cryptography.Internal#[.{fingerprint=kfu5pdpt31}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Cryptography.Internal.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "57twt0n4oz", "Integrity": "0QiGn6u1/S5uCOWUJrR95YS1NtENI0Q3wG9QyghusN4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Cryptography.Internal.wasm", "FileLength": 18128, "LastWriteTime": "2025-05-27T22:01:13.8353377+00:00"}, "kEvAHqJSvWpXqDsknuDBio5l75bpOtg1cB8ERJlfZ6Q=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\fbwichwjek-rt7h24abjh.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Cryptography.KeyDerivation#[.{fingerprint=rt7h24abjh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Cryptography.KeyDerivation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n28w7auka8", "Integrity": "IWCgFT8RBRrKh0JhMEspe0EgDaQ2HmScrWqTsbeLYZQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Cryptography.KeyDerivation.wasm", "FileLength": 6479, "LastWriteTime": "2025-05-27T22:01:13.8333387+00:00"}, "o+d6xQKq3nw7inkuiQmTovytTQzY2QCbAMowIEDd53A=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\zffam2fbkc-kwvr5jo1fy.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Http.Connections.Client#[.{fingerprint=kwvr5jo1fy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Http.Connections.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rvbuy8hjv1", "Integrity": "p+kTl3jnliJTKkiIsAdeaDHIYmycH7GrVmaw1HMqkV8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Http.Connections.Client.wasm", "FileLength": 42625, "LastWriteTime": "2025-05-27T22:01:13.8353377+00:00"}, "U+rJ0FhtlviypiXaew4PMecuvWFbO1UUMss5MW/JDy4=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\4jmkbc1los-w14ri3w66r.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Http.Connections.Common#[.{fingerprint=w14ri3w66r}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Http.Connections.Common.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4turvvkvql", "Integrity": "uC8NyQGv2sUV7FQa0G46IkzAh2qNRJFSBCxS2mWEbFU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Http.Connections.Common.wasm", "FileLength": 8343, "LastWriteTime": "2025-05-27T22:01:13.8373368+00:00"}, "Zpa0aFoBH41hQiHlE+FKylFxHTbkdXXU3V3Wg5F3JrA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\ay9vqd2qcd-hkaih17v66.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Identity.EntityFrameworkCore#[.{fingerprint=hkaih17v66}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Identity.EntityFrameworkCore.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ktvta2xd98", "Integrity": "WCjeBfiithxBeUsTe0aZNuqx0SFeSayLxy3Ha7yUl6c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Identity.EntityFrameworkCore.wasm", "FileLength": 35777, "LastWriteTime": "2025-05-27T22:01:13.8403346+00:00"}, "yPHKldWW6b5AqfT1BT9MA6vlgxj6h9TH1hFF9IqZyW8=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\mwbspmwivt-kwdybu2ptu.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Metadata#[.{fingerprint=kwdybu2ptu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eh6yckw5ar", "Integrity": "rg4AIoDCz/OFkrpPnqcPaXqTreA82tA9YzOQ84MJhGs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "FileLength": 2421, "LastWriteTime": "2025-05-27T22:01:13.8403346+00:00"}, "zyOfnw8Il+MKyZWE4j3oqdEPgWRgiYCZdTquFYYBE80=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\mfb4as3t6t-c5inbbiber.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.SignalR.Client#[.{fingerprint=c5inbbiber}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.SignalR.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a06z3llbpw", "Integrity": "1o8YjJ/6XP+a3RHj6kRDRCM9ogVFxkgHzr5sJy3FaOg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.SignalR.Client.wasm", "FileLength": 4405, "LastWriteTime": "2025-05-27T22:01:13.841334+00:00"}, "8ne+ruvGDeZt4F+PlVYpbjG3q4BDVlFbatGaPY1uS4g=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\5bhvjud5p4-s7j9342lak.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.SignalR.Client.Core#[.{fingerprint=s7j9342lak}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.SignalR.Client.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y4fqb2rqng", "Integrity": "3s29SPzWQIrw1zstlx/t1sDVZUpTJhFAwCv4sVtn8q4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.SignalR.Client.Core.wasm", "FileLength": 74762, "LastWriteTime": "2025-05-27T22:01:13.8453311+00:00"}, "fgY4yRyaIAePWFa5yJPSIlOktQH/gZN6umARqy/LRTk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\r6xkx635e3-mgiueequaf.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.SignalR.Common#[.{fingerprint=mgiueequaf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.SignalR.Common.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vla21zffte", "Integrity": "zaAj8F4irmj3yJUmK9TKmCSr6I2CONXrUju52o9Yk04=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.SignalR.Common.wasm", "FileLength": 15043, "LastWriteTime": "2025-05-27T22:01:13.8573244+00:00"}, "mCvMTP4OWT8We9UcDalZMXXkkewHfv/JhApUxeFL03g=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\4sh4mdghgg-gj3ldi925b.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.SignalR.Protocols.Json#[.{fingerprint=gj3ldi925b}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.SignalR.Protocols.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3e1c6wns0x", "Integrity": "C0hktRIVDoggPHwuMF4IarkBJlmYNpi0YUfZamBCetE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.SignalR.Protocols.Json.wasm", "FileLength": 13013, "LastWriteTime": "2025-05-27T22:01:13.8383362+00:00"}, "ecXuI4NqDO/U2E3zlHzwwdFd5LkcDitxQgZUqYtHyfY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\ckkq7hpkxv-xtx4wr9lug.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.EntityFrameworkCore#[.{fingerprint=xtx4wr9lug}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.EntityFrameworkCore.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jjziu0vpqw", "Integrity": "b5K/wdJBx7JJacLREYupHHOkKk+yxj3tLRYpfrgHUJQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.EntityFrameworkCore.wasm", "FileLength": 872110, "LastWriteTime": "2025-05-27T22:01:14.0621975+00:00"}, "JO68lljrLRMiw/EwBd7i5LDK9q3CunIJdv6tjf1xenU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\fnri61ghlw-qbxiezgttl.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.EntityFrameworkCore.Abstractions#[.{fingerprint=qbxiezgttl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.EntityFrameworkCore.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mk97qxkxzb", "Integrity": "ytXuAWKaqh/J8RzLt8axmf5PT9LJQapsnKA9bn/G/aU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.EntityFrameworkCore.Abstractions.wasm", "FileLength": 11037, "LastWriteTime": "2025-05-27T22:01:14.1250544+00:00"}, "L/GPTQ0YYnB4Mvgc8RM4ScjmmPHppGpi6QINSZFkuBU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\rp6sm5c2a6-89et6ikpnv.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.EntityFrameworkCore.Relational#[.{fingerprint=89et6ikpnv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.EntityFrameworkCore.Relational.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qh5yz7d5pj", "Integrity": "siCsmL8IMn2cAa3yrPE7N2/UCKAPcLZioC9AU+eKe9s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.EntityFrameworkCore.Relational.wasm", "FileLength": 717807, "LastWriteTime": "2025-05-27T22:01:13.9162869+00:00"}, "ALM3PRcan/I4qMvSvShfcQjF2gCuubBy5M5SsZIAzpI=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\hd6dw8d9if-zj3btj1hyj.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Caching.Abstractions#[.{fingerprint=zj3btj1hyj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Caching.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e8pp7rz01e", "Integrity": "AtECkJ5bVCi1gZD/Mu4NzqJZ4uwe9L9rya3VDV6FNiw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Caching.Abstractions.wasm", "FileLength": 11428, "LastWriteTime": "2025-05-27T22:01:13.7413962+00:00"}, "os2O6dOriTwcwlABe0C7ka7MgUPYZgWkMUxJ4IM+k5c=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\o5gpds2g3i-q76eld6ixt.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Caching.Memory#[.{fingerprint=q76eld6ixt}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Caching.Memory.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21oh999o9s", "Integrity": "Gm/2YD7D1bi0II3xh007rsYGzCMzZNwMPUvC+U0duHw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Caching.Memory.wasm", "FileLength": 17651, "LastWriteTime": "2025-05-27T22:01:13.7963616+00:00"}, "6rP8HJTx99TCQv8Z+RJm/O0nz7defCQR89K2mecKogo=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\wh5bj73q2s-h2p7847z2f.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration#[.{fingerprint=h2p7847z2f}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ilz0o36izl", "Integrity": "yme24RHQ7uWRTehrxyTqFEi5r6cH4FuL2u0lW225EUQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "FileLength": 15910, "LastWriteTime": "2025-05-27T22:01:13.7973608+00:00"}, "62n7pHgMHDhc/lU7yjZA7208oxD1dnMA6wNzzY9ppKc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\r3ym1e7jdi-qfj5950lp1.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Abstractions#[.{fingerprint=qfj5950lp1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6dbvgyu8g0", "Integrity": "tvx/HKSybKWoM7ZRE40oaXi34QnDChk2ZuVa2BnMbWg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "FileLength": 8473, "LastWriteTime": "2025-05-27T22:01:13.7983602+00:00"}, "/hC6cj2rF1+b7IRjcTv6GV/EWj56DRQH0e36WtJ2ewU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\15z47g5p7t-v8ihxrft1m.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Binder#[.{fingerprint=v8ihxrft1m}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ozkyunwgcm", "Integrity": "PMeXg9t7mG7j0tzztsd8PfT3Ruz/4aSnYfOTrl1cErs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "FileLength": 14898, "LastWriteTime": "2025-05-27T22:01:13.7993595+00:00"}, "ZvimTFS2rqQto/Fjs3dPIA/Fdy3V7jKceIsKC751500=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\lqatqb0xgm-jgemvlpy71.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.FileExtensions#[.{fingerprint=jgemvlpy71}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u8f3b62sm1", "Integrity": "Eq1sepPAvGGWcfZ/FXW0VbJCrqbWgm0czZk3y2FVeBo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "FileLength": 8401, "LastWriteTime": "2025-05-27T22:01:13.8063557+00:00"}, "ZaHXZ0z70zOfgUeZU2FzaoERcPgi9i7QqYIsY/owfIg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\nb55vgtpa8-nn2igycfnm.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Json#[.{fingerprint=nn2igycfnm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lu0ylxyefe", "Integrity": "M85PMWJVkGaxzop7jtuwLkNrDlNriFZ5sCnY5dMUg+E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "FileLength": 8204, "LastWriteTime": "2025-05-27T22:01:13.8073552+00:00"}, "Hctr5GpNclLFWO5i8yA4+aXxmHLaLvksLebIJZZZXAc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\zbgxdaljqh-0t6hu68uix.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection#[.{fingerprint=0t6hu68uix}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hfjvtuq4tp", "Integrity": "HYnu/85SaS9qmKpfsX2Od5Hcl5ouZEVl+gWFLtoHzr0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "FileLength": 36323, "LastWriteTime": "2025-05-27T22:01:13.8093536+00:00"}, "QO2aYTO0LYEamPo6z2CTq0YV0nLbQyoOVOA5Gy/gMzs=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\43larm3m39-mxyhp8w354.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions#[.{fingerprint=mxyhp8w354}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrfio62i7", "Integrity": "j8uMfI9k0OYnZmNhB35Y/CD7IveM9XS4gj/Y+rGJgi0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "FileLength": 21994, "LastWriteTime": "2025-05-27T22:01:13.8113523+00:00"}, "j/mV5skimVvMK4NCpC8zdiBCYwQlgDvwZ9k6f4ZadNM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\gh3pn62u3e-pxy8kdgcv9.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Features#[.{fingerprint=pxy8kdgcv9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Features.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i7gdkp0ide", "Integrity": "Tqs5+0txP2w7fpFGZsAk0ZFlByk+1Tfm9IXttPjVCsA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Features.wasm", "FileLength": 7284, "LastWriteTime": "2025-05-27T22:01:13.8113523+00:00"}, "ZUie4A7+w0P8omoX8WVOr/EGoij4cXFT0V5ks8AgGZk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\y7zf65eu1a-t0xgg5v1wp.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Abstractions#[.{fingerprint=t0xgg5v1wp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0anp09lt9b", "Integrity": "6Gl/HUNwoAfvEKO09YcfAVHWuAox+8dsQ6HPGF65vug=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "FileLength": 5725, "LastWriteTime": "2025-05-27T22:01:13.8123525+00:00"}, "2WUzY0FSCubJN5SLLvGLL1YUI8TvusP9uB6n98/zCV8=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\jikpfui4d2-k8mchckl5y.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Physical#[.{fingerprint=k8mchckl5y}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mqv1rf7kv8", "Integrity": "EqIFyoS5iXLv3WBecVHWA8WpwvRHBlfb8fDXetQeuq0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "FileLength": 17356, "LastWriteTime": "2025-05-27T22:01:13.8133521+00:00"}, "km525HWMNmMHEijs85H8dW0eok+9pNto9HHCBC/5LHU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\lx6wrrnpvy-whx15sk3zg.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileSystemGlobbing#[.{fingerprint=whx15sk3zg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3mijfn4fza", "Integrity": "+w+Q4GZv/z0lG+fK+EPO0e6ZJQl1Q6UtMbu3rYzZr3U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "FileLength": 16773, "LastWriteTime": "2025-05-27T22:01:13.8153495+00:00"}, "w3lk2RFiGakSOoPo7Kg19dpF0c8q7VyLO2BGaRY6Njk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\cjrso7ux1y-pfdo9cl058.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Identity.Core#[.{fingerprint=pfdo9cl058}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Identity.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h0s50wbd3c", "Integrity": "EY2qek0uPtFbnn7g6HMMeyjKo8C9AvzblqLxhIFeCS8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Identity.Core.wasm", "FileLength": 62341, "LastWriteTime": "2025-05-27T22:01:13.8193473+00:00"}, "zmfSyRPelqA/BlvNiNw/4paAuhCDQlBR66GcqKOOWqk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\0i5goa22xn-cvqp7ikd00.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Identity.Stores#[.{fingerprint=cvqp7ikd00}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Identity.Stores.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h7jlzig8bc", "Integrity": "fd/drPps2RdqERv2S2OxJTqfIDHbz8nhEBmBWqo6EEM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Identity.Stores.wasm", "FileLength": 14304, "LastWriteTime": "2025-05-27T22:01:13.8213475+00:00"}, "EvfnAStzfVV7NUHBzDKsgjdzxM7vxDpjSQtU82zVpLg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\hojrqxebu5-r19g4upt09.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging#[.{fingerprint=r19g4upt09}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u5ztklcv83", "Integrity": "viBYvEPN09y6MBHR7WY0eY+n/FMwYjRWOeuX9/bIa7k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "FileLength": 19459, "LastWriteTime": "2025-05-27T22:01:13.8223458+00:00"}, "Zz/Ozd/SbBTABz/oBIBtdRkasq0N03iul6btxcJGXVU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\upjr6mna64-576c9qdvb8.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.Abstractions#[.{fingerprint=576c9qdvb8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ud1nfi3dnd", "Integrity": "PjLeYrKCefW/bjDTaeJ8QcTEFvRzS/EStP/hvlhlhB8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "FileLength": 25078, "LastWriteTime": "2025-05-27T22:01:13.8243439+00:00"}, "y9LZRCMn3/+ihFM8WjI23GWQ6G9XU0hLK8SU9JXk098=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\8xrut77z5u-7s85c9su09.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options#[.{fingerprint=7s85c9su09}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qp0iw31o9g", "Integrity": "HvD+ipaqGcx7roBVfCAbuF98T8L9MpGrkSgj+lwAjLU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "FileLength": 24190, "LastWriteTime": "2025-05-27T22:01:13.8263429+00:00"}, "iKFjU+Mq3rMzYCybBQrDWviS2Uv+TPaekuh6+NwVtAM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\sddjcbhqv9-cp6sp8t3ma.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Primitives#[.{fingerprint=cp6sp8t3ma}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rv7b8ki9ga", "Integrity": "Wcv+TBksTsn5uQ+oBF7ixsWAAZ3kx9bDyEnu86+Pr6o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "FileLength": 15646, "LastWriteTime": "2025-05-27T22:01:13.8273423+00:00"}, "5Jomnl/uPQOXFq45h4zcmBIPxOoe9B0+Mq2qv5yt2yQ=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\m6tv635u3c-agsjembwft.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.IdentityModel.Abstractions#[.{fingerprint=agsjembwft}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.IdentityModel.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1uvlhpmd0z", "Integrity": "lxaC1FkwojvM50Dc0+LH87O5kHBXQIL9S/ZKjSf56cA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.IdentityModel.Abstractions.wasm", "FileLength": 4021, "LastWriteTime": "2025-05-27T22:01:13.8273423+00:00"}, "lPB+PYHM3du7HMwQm97iFpxODNvHuONpCRw6uf995Qs=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\bxgda6lt7o-1xvsxedrro.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.IdentityModel.JsonWebTokens#[.{fingerprint=1xvsxedrro}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.IdentityModel.JsonWebTokens.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i3tdjfbxxw", "Integrity": "jlO97EPSQsFfCD73jTtcOVNqbXyrbYls+eM38o+stCo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.IdentityModel.JsonWebTokens.wasm", "FileLength": 55731, "LastWriteTime": "2025-05-27T22:01:13.8313396+00:00"}, "gjC/Artv8e4rUO1X+UVeL73ABW6ytYivwjIgt0Lfwks=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\qhlfrnzvgw-9s7vggh2wm.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.IdentityModel.Logging#[.{fingerprint=9s7vggh2wm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.IdentityModel.Logging.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dor163iw3g", "Integrity": "XRTV/Q7rTZIAV9QnFbh3TC0Jit1W1R/+NTC8oZYxDmw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.IdentityModel.Logging.wasm", "FileLength": 10374, "LastWriteTime": "2025-05-27T22:01:13.8323389+00:00"}, "NP4ndIEcUMLqO/YJGTnBu9QuyNx0yzKyUUjxD+jTr7E=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\mbhx9qhfbr-jumoio5ruo.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.IdentityModel.Tokens#[.{fingerprint=jumoio5ruo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.IdentityModel.Tokens.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svzaa2aerb", "Integrity": "2Hl9fiX1QsSbBliIvbTYFw7+0QXj1IyhRLuVrxjWLT4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.IdentityModel.Tokens.wasm", "FileLength": 114823, "LastWriteTime": "2025-05-27T22:01:13.8643195+00:00"}, "UQ3s2mWY8mWZW9T6jKziVO86KX6uci7fwSXehDMd1Uk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\ghbw26vubr-fzaop883yy.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop#[.{fingerprint=fzaop883yy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zd8ebmky9m", "Integrity": "CRuPyHVYybexQ0zqV+cmulhiI8pqnnDJzHGWuSn7ph0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "FileLength": 24106, "LastWriteTime": "2025-05-27T22:01:13.8663191+00:00"}, "Sk4VAP4Y0R7Hqn5Ec5w/7+3cWDm3ayT/oa9ORJ9HptA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\vb90dvhdih-v1y0duelx8.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.WebAssembly#[.{fingerprint=v1y0duelx8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9ihvfdpe3k", "Integrity": "YT0UVFPVRBpKeD7gamA5oUbq5Fq1CtUYfuF6u5gI3q0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "FileLength": 5794, "LastWriteTime": "2025-05-27T22:01:13.8673177+00:00"}, "1RdiXJcrIUrsagVKuCGZuogAhBSOhEFU4i7tY6D3W6c=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\ydnvxcljlv-vchzwfyyhz.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IdentityModel.Tokens.Jwt#[.{fingerprint=vchzwfyyhz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IdentityModel.Tokens.Jwt.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lpso7qoxuo", "Integrity": "p4loFNAYZF2orq/HNjRLtmMaE5ccGaX1dNrTopP5loo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IdentityModel.Tokens.Jwt.wasm", "FileLength": 31149, "LastWriteTime": "2025-05-27T22:01:13.8763125+00:00"}, "ccz87Rw9SFZjnDx3p2Y5xRbqes1QnsKbGq6wmXh5TGA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\xfgaw02bx8-rtyskzgzjf.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.ServerSentEvents#[.{fingerprint=rtyskzgzjf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.ServerSentEvents.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y2z8utkwi6", "Integrity": "Zuq0ggSoxPWXZZymO1RnOm57PTU7jvMoiGqeAQFS2RE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.ServerSentEvents.wasm", "FileLength": 11674, "LastWriteTime": "2025-05-27T22:01:13.8773121+00:00"}, "G0t3zLXzY+E3ZkpBUpu69hZzUx8LjqMidDyJeYugguI=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\drgad8jqki-wjcmf9ahqt.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.CSharp#[.{fingerprint=wjcmf9ahqt}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "whn2qrvdkx", "Integrity": "dLk/NeTPAMoZXQ7+kQfLn6Tqdo7TvGxUu1wV+F1Tpsc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "FileLength": 132481, "LastWriteTime": "2025-05-27T22:01:13.8853064+00:00"}, "NK4I2xFXWTumDgzYy42tZd27c23qhmg4JcH6wiWe1tc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\yjvphpcsut-ccpi3o747q.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.Core#[.{fingerprint=ccpi3o747q}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k6unbd2stq", "Integrity": "jFcHVfm9QLVWCtisUrkbLfVXBvSYsiOsz3R3gwhFuaQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "FileLength": 171163, "LastWriteTime": "2025-05-27T22:01:13.8962999+00:00"}, "zoVOqSh8YnPoefXjqqNfMjl3XzEeHeEm7AxhDCbi+a0=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\j5ssmj4mpx-pbpr2ghqk0.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic#[.{fingerprint=pbpr2ghqk0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gxddmsgt2e", "Integrity": "xI2vA0+H5JFItBQiIcI+2x3g4g17Ec7bJce0b65Hgzs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "FileLength": 2874, "LastWriteTime": "2025-05-27T22:01:13.8962999+00:00"}, "5FuA+hiI8Uwl/OAcTra6ucQVItMM/7Zhnibppq3QXtA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\8nonob0stp-dqwofb9ps2.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Primitives#[.{fingerprint=dqwofb9ps2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fnmlg52jej", "Integrity": "FXY6BsQcn2XoiSqQemKGsSvKr+iX6h4VrmD7qOD/m8o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "FileLength": 2198, "LastWriteTime": "2025-05-27T22:01:13.897299+00:00"}, "sI1IhLYyEs4cYU5TyUNGEHOz7XjfPOXVqxtWfN77FVQ=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\tv4tl8lbev-rcu343ydbj.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Registry#[.{fingerprint=rcu343ydbj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8lzj6pew6w", "Integrity": "FBvNldlj2Mv/YAElTqKc4IdUZlvAyxQ9yAPvqg8OdX4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "FileLength": 9272, "LastWriteTime": "2025-05-27T22:01:13.8583241+00:00"}, "SodN0MntqSPtLjxLSp0K8TRXk96gwQxfsBxS/BG7vIc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\ytynfhm0fe-00rm2k4rqj.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.AppContext#[.{fingerprint=00rm2k4rqj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.AppContext.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gbky2dbmvb", "Integrity": "XGFQiiA2iSU/jhMtWd8tYiWYL9t4l9Avpz36qIveoDU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.AppContext.wasm", "FileLength": 2098, "LastWriteTime": "2025-05-27T22:01:13.8583241+00:00"}, "nXveDtcz0t9q0bniTUFNBCIXewL1QnpJ+3u3Ueyu+ec=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\3tid7rmy0s-z32pttvtk4.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Buffers#[.{fingerprint=z32pttvtk4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Buffers.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w8eef0mofq", "Integrity": "oA4D9hmv1BEvfv5qORgbbE5hMkdx4uhVRpjjTWagIDY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Buffers.wasm", "FileLength": 2102, "LastWriteTime": "2025-05-27T22:01:13.8593227+00:00"}, "JOcMgnBhp34uo6bKKw8q4Y1eryBPQ4nD+PA78BhAAvk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\n1bv57xupw-rx21q3n11f.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Concurrent#[.{fingerprint=rx21q3n11f}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0i67dnlhhd", "Integrity": "qJXKIYFSSNFCA84oRN0q17gXLyqXbWgBoYqTgOVX5D0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "FileLength": 34481, "LastWriteTime": "2025-05-27T22:01:13.8613217+00:00"}, "m5P9GOd4bGYFkOV5gJkOKO8eXNCDIpJ9150Dva0Tod8=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\hrrmnusktb-d8bqqugfwk.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Immutable#[.{fingerprint=d8bqqugfwk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhyv6nchce", "Integrity": "XkLA1HoKdRMuat0Rys6Eh5T9R7ihmG+EQjQqSJ6C8DU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "FileLength": 100292, "LastWriteTime": "2025-05-27T22:01:13.8673177+00:00"}, "odYwrLCy6vgQmRaR2n0sRAHOk6SWsDrBTVPFzGG9z0Q=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\qk9qxjdggg-5ray22bvhs.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.NonGeneric#[.{fingerprint=5ray22bvhs}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bkdv7bmfz6", "Integrity": "a+kqxOHfjR9qcvERcBAsjkNFWm7dL3AZik9ZJwOH7uA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "FileLength": 14906, "LastWriteTime": "2025-05-27T22:01:13.8683172+00:00"}, "/d78IgGIZM95zp9snA3d7RFzIwz4vrL7St7k7EnaAlE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\acl4ehubmw-i9b18l2jwg.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Specialized#[.{fingerprint=i9b18l2jwg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "587qlqjbxq", "Integrity": "FXSFbYyRSkg7WMFXEL2jXHL04e5gLLZiSPTvvD8Gohs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "FileLength": 16545, "LastWriteTime": "2025-05-27T22:01:13.8693164+00:00"}, "Aa7SGkIpogq5DSg42eOvij38etiPiu65RsyCifVNYOc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\nurp10v735-r2s7n2sx3x.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections#[.{fingerprint=r2s7n2sx3x}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sc0urzraoy", "Integrity": "tAttvLK+Dkz7TUD3bHWMLlzvCmk7N6Q7pZKML89FbNM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.wasm", "FileLength": 49318, "LastWriteTime": "2025-05-27T22:01:13.8733143+00:00"}, "LT0jDVYN1XbeJviwwFzS5U72D8KsUQOU4JiyrRYHrIc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\uqn9oz4jm0-shw18la3r8.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Annotations#[.{fingerprint=shw18la3r8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9dsz96d69q", "Integrity": "XUu6ZwDlA3BaLbNMn5o8uVYlU+3CHg6oM6RouNTXcBU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "FileLength": 36237, "LastWriteTime": "2025-05-27T22:01:13.8753131+00:00"}, "yh91T4Fic0MW5En3yFuOQMQiO7o/yq1+EzShnYNHWtc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\jhjllirds1-3tfviggf6l.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.DataAnnotations#[.{fingerprint=3tfviggf6l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aiu5067y09", "Integrity": "Tg5KQ6dDGE/PUnLaUs1wulH7OmsM11j2bhd5E5LolwQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "FileLength": 2574, "LastWriteTime": "2025-05-27T22:01:13.8763125+00:00"}, "YNZYbhoMGHIrub2UwzuNe7sacBIIrW6jwYg/Gg5rk4Y=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\iqbdnc14oy-yssxj4mc04.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.EventBasedAsync#[.{fingerprint=yssxj4mc04}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4c1xewlgtm", "Integrity": "BCPI3RteE/wof6nY6ogvkzvVAiw2ZwvYhMsfWee55PA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "FileLength": 6877, "LastWriteTime": "2025-05-27T22:01:13.8773121+00:00"}, "85ndlgvWC1E2Y2blxxo+qDL/WjPoPOrL/vpZcC2ItMs=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\0p4d3tumxz-b2gyl5z8cy.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Primitives#[.{fingerprint=b2gyl5z8cy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "se3q3ak7s4", "Integrity": "PCHpBiK0R7POCERG/FQbPCgnTubMzKy9YPQDr//lDKM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "FileLength": 13564, "LastWriteTime": "2025-05-27T22:01:13.8783123+00:00"}, "/k8ziHpN7DLUSEkeu1QPnJxjkVrfxX1PFdL3EuQ82J0=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\bttnxi1mao-o2rbwek366.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.TypeConverter#[.{fingerprint=o2rbwek366}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6iufqo47s1", "Integrity": "0v0qzKjLMpP4glPJTjPU7rEQnkuVkGcSXFAmSQJXoLc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "FileLength": 124649, "LastWriteTime": "2025-05-27T22:01:13.8853064+00:00"}, "IuZ44AgWTla4Zkgf5PEfkxudWJEUIYMCa4Z/WyQSB3s=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\8e26tbo3qr-ijpzmqvdg2.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel#[.{fingerprint=ijpzmqvdg2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fh7l3ppj1z", "Integrity": "I1Aq1Dc1Oo8DYWZ/kW+O8up7wIVQVbvq0gXyTn2po78=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "FileLength": 2565, "LastWriteTime": "2025-05-27T22:01:13.8873058+00:00"}, "sboGazIVx+rhAeGtDvWs9w7ACrFPgZFU1qQqc8hczeM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\zar2m2mugl-s48boytfg6.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Configuration#[.{fingerprint=s48boytfg6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ryjlal1a0g", "Integrity": "U6bMQhPCth9g5LpZH/RZOtSLbfVBtYZih2mrdSUNHK4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Configuration.wasm", "FileLength": 3136, "LastWriteTime": "2025-05-27T22:01:13.8883052+00:00"}, "BTGIj8RB1l5UfowduTPpAWMTv44YAdCEs9JpBgsdp3A=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\zihhkectpr-0c362v5xxq.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Console#[.{fingerprint=0c362v5xxq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Console.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yd5s3b1x79", "Integrity": "RkmGbj8ZnSECLn2b7+62cOCy9mYfacZq6oXLvPaYml4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Console.wasm", "FileLength": 19991, "LastWriteTime": "2025-05-27T22:01:13.8893044+00:00"}, "XbuwlrHmxgehXGXMDyG5QUD1c5uMjV2maWbT4pga/gg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\h09cug2pbt-aiaao2jpd5.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Core#[.{fingerprint=aiaao2jpd5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jxxnlpguoa", "Integrity": "eTue0iMffna09Z1hFfpWHOmU3nmxH+OSbXU06A4IXN8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Core.wasm", "FileLength": 4597, "LastWriteTime": "2025-05-27T22:01:13.8593227+00:00"}, "45qw7XsS51V88r9m51Uif6Mft505X/M5psoGOCkGwmE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\rky58087qd-lcca6mlcc9.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.Common#[.{fingerprint=lcca6mlcc9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.Common.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bndvkguu89", "Integrity": "KXVTZ34mhkbrqfqx2oIN1qNdkTUQ2fnX5XmIZyNP3Es=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.Common.wasm", "FileLength": 378863, "LastWriteTime": "2025-05-27T22:01:13.8823085+00:00"}, "ytDVhx3XJLMZeBeQ04R7GTwBq38gm1raV8/lsg3RTew=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\uhxo5flcal-8vc4lkhdbq.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.DataSetExtensions#[.{fingerprint=8vc4lkhdbq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "epz5zlsyrp", "Integrity": "/gRySnjVGGyHAJWONxTZztnO715UQU8T7zyNQLBS62o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "FileLength": 2061, "LastWriteTime": "2025-05-27T22:01:13.8833078+00:00"}, "U8d5ghY7aUGNsD9nEx8HAXb8SaQnCF7b70N541wCUQQ=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\7pg76hn7v9-zn919kbv52.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data#[.{fingerprint=zn919kbv52}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w2qnpfbgi1", "Integrity": "yOhwSK2HejpWevbXsmbDrSNfN8CaeR96Y/Oz6P4R1/Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.wasm", "FileLength": 5066, "LastWriteTime": "2025-05-27T22:01:13.8843073+00:00"}, "Dt7KFCFUXxNJs+aZ/mHZb8CNvwbad+UFNqzCCbfKdEM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\5djcbzohk8-q6kkw3vdcg.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Contracts#[.{fingerprint=q6kkw3vdcg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "umbp5jmvjn", "Integrity": "JnjQvWWvQGA1b129nERB5Omtk3fVhw0nM2LanT3WZFY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "FileLength": 2390, "LastWriteTime": "2025-05-27T22:01:13.8843073+00:00"}, "YO9KxxzBdfaFegHa+VWc1q3gCVCxx+hDcpIHchr6psc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\sazktb3d7f-2ahnaecxp2.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Debug#[.{fingerprint=2ahnaecxp2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vxmwgbwzc1", "Integrity": "Ixg7wld6kFJzTcOto8TsCfWv04NYMU1FmuvGiSS1Srs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "FileLength": 2270, "LastWriteTime": "2025-05-27T22:01:13.8853064+00:00"}, "sz2YYPmSXJaNCG0+7WUbJKoOugIJPYmtHhqZs5vZTdY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\sz8tcuo56q-g6jmcgi72c.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.DiagnosticSource#[.{fingerprint=g6jmcgi72c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t0fcto2ywt", "Integrity": "DRTmxn991ElmuBvew4H2E17LlsakWjeu+9dHY6vadvY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "FileLength": 74367, "LastWriteTime": "2025-05-27T22:01:13.9932405+00:00"}, "I+ahNhoZH8WejC4fcBfKzijtrOBbg6+ecdtc66nCUHY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\owvswo5gme-vpthu2a2k9.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.FileVersionInfo#[.{fingerprint=vpthu2a2k9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y0g8pmg425", "Integrity": "3ivvr45tcunKnTnWkuKND4+nqKHue3CpTgQmDApWr3o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "FileLength": 5159, "LastWriteTime": "2025-05-27T22:01:14.0002358+00:00"}, "wXIfypgM82DPWTSx9b8in2OtvM0lRq7nL2XpraxihB0=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\h0v7twm0xk-agxw6p2exh.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Process#[.{fingerprint=agxw6p2exh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5cqutv7qac", "Integrity": "1ekcESQu9PtrK44dSbLgtSqBq5bCjzaSpT5vyqLqeKg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "FileLength": 16549, "LastWriteTime": "2025-05-27T22:01:14.0621975+00:00"}, "AfMHsGUFzaI5meDN3bDGX9+gc//IIst3OmEDnv4dSbU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\zjf1b60ceu-r4u6i8kfog.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.StackTrace#[.{fingerprint=r4u6i8kfog}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anfijay65x", "Integrity": "thpNCsGdaFZ7TT+mGanvAig3cWI5/oXyNsNGikJ3JoA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "FileLength": 7500, "LastWriteTime": "2025-05-27T22:01:14.1220555+00:00"}, "/kr7hTpgyOrJqyaNfVJA1Nze+K9O/mKY7Wvd3vK6oPQ=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\oafx1exwzz-zuba91cmsq.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TextWriterTraceListener#[.{fingerprint=zuba91cmsq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ph4twsscl9", "Integrity": "/Yscl2e7VRBBZ+EiKxK19ZLqJh84BBTZP6j8t4ZLIpo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "FileLength": 9522, "LastWriteTime": "2025-05-27T22:01:14.1300508+00:00"}, "LprW1vVXf4L8F//q8A0t1cLNYq5coA/QLbuSr2taQ5I=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\d9z6bimbwi-t9yjdk35pr.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tools#[.{fingerprint=t9yjdk35pr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cdvfaiztpm", "Integrity": "cHEne9SK8rrP+xPc+bzJTg/JNRqX7uF4xJTi17nZyRo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "FileLength": 2175, "LastWriteTime": "2025-05-27T22:01:14.1830186+00:00"}, "7U6OC8VgyRht8klfXmwfPbggpMnKM68n7fwYeCnjijU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\xgfpt4vul2-90w5tp9zha.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TraceSource#[.{fingerprint=90w5tp9zha}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6bulh20l6q", "Integrity": "ssUBRqgc2MJe2SyAT+YJB0zgJxH8nKu2JYj4hQnaWQ4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "FileLength": 20417, "LastWriteTime": "2025-05-27T22:01:14.184017+00:00"}, "V9Yow1b7OxX7JG+GMNhaQObAsBuvjbwaKS6EE7Wj2Cs=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\ehlqey1b1t-uno27yk9k6.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tracing#[.{fingerprint=uno27yk9k6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4rhwxvk649", "Integrity": "4H4pa1Zb+HJlXKxPb2EJNkA1Uiq3PYZUA5Q/Osa0zCg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "FileLength": 2498, "LastWriteTime": "2025-05-27T22:01:14.1850163+00:00"}, "2BoIPgloP9rIEtLxKw0YFI63yaYeDjetlwLEJz4H3X0=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\3auop7f389-nzdrnpuduj.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.Primitives#[.{fingerprint=nzdrnpuduj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bzthh40wc9", "Integrity": "+QxdqoH7R2P7/53w0wHo2JHERi5JXd0WzxFyWKSj1MU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "FileLength": 24545, "LastWriteTime": "2025-05-27T22:01:14.1860159+00:00"}, "3ecdQXC+obaVDoyqnavK2PJVMXyS/Qj4TP4L4BGER18=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\tqf0vfqams-egkesygmuw.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing#[.{fingerprint=egkesygmuw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "csfg28pnvh", "Integrity": "FJqYuxMaAQeGxiDsZAa6CzJOps6i+oNKnk36wJ4XPjw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.wasm", "FileLength": 3885, "LastWriteTime": "2025-05-27T22:01:14.1880167+00:00"}, "BfSy0vIUYYws1X5DY5tIBS7s0nmpCxbt0loZ8fqXxkc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\6d0rjn6vva-efxlzpwato.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Dynamic.Runtime#[.{fingerprint=efxlzpwato}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3f2zw98yjn", "Integrity": "/9ro6NA9eSZOycX49SCvAK8MSSq35bJD9MDw8DtPBmA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "FileLength": 2437, "LastWriteTime": "2025-05-27T22:01:13.8893044+00:00"}, "TDpS4oYrWJVF8LDgcMRnNQUfX/5i4cpRF/lB0YUpe/M=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\iof205xirs-xxh459yzyl.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Asn1#[.{fingerprint=xxh459yzyl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qg933724kw", "Integrity": "Vi6L8nHtINGY8UNUSMcbswST2adk3eeAuAVc3rnz2fw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "FileLength": 35944, "LastWriteTime": "2025-05-27T22:01:13.9932405+00:00"}, "xxcIpvjNXeTydG36E0y2pq6bMulZ/9eYvfgBVnsD68s=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\rji204vkkm-qx19rhlhxw.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Tar#[.{fingerprint=qx19rhlhxw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42lxtx6ijk", "Integrity": "JNZzTs20B2X5ESynC8UJ48206tQsbFUERJfIESuL9r8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "FileLength": 10569, "LastWriteTime": "2025-05-27T22:01:13.9952397+00:00"}, "JLNc/CgRF9wAesPTMjGi+lutXLLYshsuPUPikDPi0fM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\x29c1hmjj5-8s8o2vqz82.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Calendars#[.{fingerprint=8s8o2vqz82}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4se6s4hhex", "Integrity": "rPOuUEg5GRqoeOIFtfkDvsleWjnkoHr2U4qsZFE0ZNQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "FileLength": 2286, "LastWriteTime": "2025-05-27T22:01:13.9962392+00:00"}, "JwLuhfPK7nIFDKuiV2CgB9IoGp3keyhuk7Kh+mel4xc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\klzbdz9wji-c5jbxrm4w6.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Extensions#[.{fingerprint=c5jbxrm4w6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3wvwpdptbn", "Integrity": "jbkg3ibmnYPvOUIB7v6osTsGwOLGoKH4x8Z/p+2VyVs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "FileLength": 2166, "LastWriteTime": "2025-05-27T22:01:13.9992368+00:00"}, "lWEsZ20c+vRlfA8wHX2wxJiI7/Ve4ebNQEi3sMPrWyg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\kweiky8dyb-up27xu0uxp.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization#[.{fingerprint=up27xu0uxp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rw01wp4to1", "Integrity": "214E7tvBvXAfnuLGg0+1+rbzxXkK0CwmBnTad6M+mdo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.wasm", "FileLength": 2255, "LastWriteTime": "2025-05-27T22:01:13.9992368+00:00"}, "jegOnc0t4Em826RaJgKkDpeK9CcEafRfcoJ5/1bh+54=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\brsx8yeqm6-797644ybc0.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.Brotli#[.{fingerprint=797644ybc0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rcvdnzixr4", "Integrity": "Bd1TQf4h382vOIaPR3jM2R99rwtibKoNGvn3mJSSAyo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "FileLength": 7046, "LastWriteTime": "2025-05-27T22:01:13.7453931+00:00"}, "zfF6oera4+8LomlOpflhc5J3/PqY2m2SJee71XeoawI=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\8tyekntdod-phrc4jhttw.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.FileSystem#[.{fingerprint=phrc4jhttw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rojoeciz5t", "Integrity": "q9fyOwRKOC9QJfY4IDT4uvAYkkO2NMueygK788ACPEA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "FileLength": 1982, "LastWriteTime": "2025-05-27T22:01:13.7393987+00:00"}, "qKGddrJNtsAlRRkZc7nHWU2WqyU/9FKPSr/6dm55Qkc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\cad14qge61-odllsikviu.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.ZipFile#[.{fingerprint=odllsikviu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4zi909xrgr", "Integrity": "UFykutoRalkt42KbosoPJyMbtA9/IOJ7HwjzS4b6f4E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "FileLength": 12718, "LastWriteTime": "2025-05-27T22:01:13.7423956+00:00"}, "lChexNZJXe+FSYu9fgNaN9MosRHnv0rIiCel1oiGZYc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\1qcfl47rrc-em8v7c7339.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression#[.{fingerprint=em8v7c7339}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n65fffo5wh", "Integrity": "AW3Q5+zaBsLOSFteolDj/v6mqq6h8oWQjl8Wmd5YSCM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "FileLength": 43809, "LastWriteTime": "2025-05-27T22:01:13.7593851+00:00"}, "BHV2BrZC8kcRVDtMV6XRDaNmB7oK8aJJfzyETIqZBMI=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\9rb4w973my-9er38h0om3.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.AccessControl#[.{fingerprint=9er38h0om3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlancx7970", "Integrity": "dfWjbRhgi4ZBTrpnZOA5EZCrYXu2GYp33Xqy+XmXh8I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "FileLength": 8606, "LastWriteTime": "2025-05-27T22:01:13.7463923+00:00"}, "vdWiTMwlEVgQtmieMUIAU2ryhD76gBmMtLhLsue6Mjc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\ou30alo069-5n4il3qn29.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.DriveInfo#[.{fingerprint=5n4il3qn29}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wk08zacyg4", "Integrity": "8F/KcasSGB4S1b8gIS4/WHIGWStyS6vN2j5w8wXbJ5o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "FileLength": 6069, "LastWriteTime": "2025-05-27T22:01:13.7463923+00:00"}, "RFva2Yo+rjzu/7zBKeQ5BTq/mNpDIn0u+fyBVcuV0qs=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\s9gm69eb60-q19vnktpak.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Primitives#[.{fingerprint=q19vnktpak}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hcl20q90o2", "Integrity": "b++Raa/3GRPTMAemOfRryhk5ait728noAZNXfOGRmEU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "FileLength": 2170, "LastWriteTime": "2025-05-27T22:01:13.7473917+00:00"}, "3+T28hcrSAnXFrQgolLqH4jj9tAjm1ODZNX9mQCohlE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\9lpt4srauv-m01xezp84e.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Watcher#[.{fingerprint=m01xezp84e}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5hwco04ju3", "Integrity": "l7gqqdD1XmOvVgNd4dGG0d1+sVS7IKx9cZzNb1rSzY0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "FileLength": 8901, "LastWriteTime": "2025-05-27T22:01:13.7473917+00:00"}, "wstgUAtmPsNCbUdLF53L0uSiSVvoAdOjyWSv2NFwSQU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\y93hw0l4d1-canbiw5jz4.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem#[.{fingerprint=canbiw5jz4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w0ce466m0t", "Integrity": "oI93ROVDQlzPrdPOvfFbUVxdq/TdFmDecOOGAYgOdCI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "FileLength": 2297, "LastWriteTime": "2025-05-27T22:01:13.7483908+00:00"}, "corRAa4opPkqwI9uIGmBicnSaGKxrbiYqT1VgtubqTc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\5erodj9ti0-ovh8t119yi.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.IsolatedStorage#[.{fingerprint=ovh8t119yi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dp3olazm9p", "Integrity": "wXV7CMQlGsZOq/yI6mSMkiHDmBgheAgdwNTJty4YWUg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "FileLength": 9513, "LastWriteTime": "2025-05-27T22:01:13.7493906+00:00"}, "LY0VsvvKn/f/4ezZfpbuLq4NIfx/8hjFr3mmL5QbFsE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\2fdqtzb2ah-wjnda4frws.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.MemoryMappedFiles#[.{fingerprint=wjnda4frws}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fja97s81rw", "Integrity": "m7QlOu/McWJ9OHr3Jene9qIqqUDNYNMvZNfIwphXUM8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "FileLength": 16992, "LastWriteTime": "2025-05-27T22:01:13.7503899+00:00"}, "T44E569wW3K3MkDa+CJUzO451Wi8a4QMGuuJGdeH+Ak=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\w1peyznk6q-7ro2a7frhf.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipelines#[.{fingerprint=7ro2a7frhf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1m08d65x4z", "Integrity": "GUpUe9FOVtVgMMlZeIysiPvr6C9/hi4H7BONrTe2peI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "FileLength": 31017, "LastWriteTime": "2025-05-27T22:01:13.7953618+00:00"}, "wRyAhloYAnNzhdo91medG9KlXHLUzgguGlkk39S38FM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\8kl25hrzov-9pomna8szr.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.AccessControl#[.{fingerprint=9pomna8szr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hzdxq9coho", "Integrity": "81yCnq7/SH/1Yj/U4w5dddIB98+0RPDv4nC7GxaL2b0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "FileLength": 5646, "LastWriteTime": "2025-05-27T22:01:13.7993595+00:00"}, "ySE9A9gIybxqg/K7v37D+JdJEbUQBOLI+0QoZ3PIBVA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\uw1odfor9i-tqouqgzazg.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes#[.{fingerprint=tqouqgzazg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "whidliziz2", "Integrity": "5As+bJFS+z1W+xD0UGaM1NMZ3vSgg+Kd53lTwNALCP4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "FileLength": 11588, "LastWriteTime": "2025-05-27T22:01:13.8133521+00:00"}, "DfIVWiB7pIZdVDj0g8KD1bEoy/97IB3bA+crLy1nGsU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\989k8wnvw8-2qs4v41wjf.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.UnmanagedMemoryStream#[.{fingerprint=2qs4v41wjf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ywq0n6o836", "Integrity": "vqj7XpFXOp+EmMjVMyNYKii/uLNXql+YsWRNXhYhKkA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "FileLength": 2200, "LastWriteTime": "2025-05-27T22:01:13.8143512+00:00"}, "UAqf8OdE23OBdwikp6tEQ8a9FizriEHd+BtKRcLJH3I=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\0yuwrfk7oc-6m5851g67n.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO#[.{fingerprint=6m5851g67n}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9wkpaji8xh", "Integrity": "CcxihuD84qx5WVldbmzPcajqSR6kcdmOOvZgejVEVtQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.wasm", "FileLength": 2248, "LastWriteTime": "2025-05-27T22:01:13.8153495+00:00"}, "e3TAAKFdSW07qXjvIwXN8Rq6ybUo2I8uI3XobNkfHeQ=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\yca9qce8hy-ejydgoj0oc.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Expressions#[.{fingerprint=ejydgoj0oc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r8mdv10o6p", "Integrity": "JStxU+YJ9OCM5aZP6om3XoEHOW4TIIIuDWtdniE/mSU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "FileLength": 217767, "LastWriteTime": "2025-05-27T22:01:13.8283419+00:00"}, "cEMXJc7o84tiKCodAMZRzMwlegIWVudZCvUKyNfpruY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\lkie5t383a-is9nca195d.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Parallel#[.{fingerprint=is9nca195d}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5zr57svufs", "Integrity": "4eO+DE/5v/EilNlRyQu+tZ5AZXoWa8iW7KDNZ0EEaNQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "FileLength": 88007, "LastWriteTime": "2025-05-27T22:01:13.8333387+00:00"}, "URqM5rCz4l/Hgyw080rogjJQyVv8BD7/O+q4Fg029u0=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\uzx02s5p0a-8vjofh88qa.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Queryable#[.{fingerprint=8vjofh88qa}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trepdz8lmd", "Integrity": "CXAB0adlvHEmuWLPNK10nO0YmybxwsePqOP4BLNJPsc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "FileLength": 21320, "LastWriteTime": "2025-05-27T22:01:13.7623825+00:00"}, "asO3MlRX0dqhr75p0NJO+pf1ngFFPJKSJUgFVKjPPRc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\jvu2o5z5yd-zqotd0pp5c.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq#[.{fingerprint=zqotd0pp5c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "819zd6cnti", "Integrity": "hGK4Vn2RrOfO7zndets9CFX0H2G6b1uG19ElQM8A0N4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.wasm", "FileLength": 56610, "LastWriteTime": "2025-05-27T22:01:13.7673795+00:00"}, "FYnac2suDPUtgKYOWvvNcvedKv4+zx0n91oOHGq7WuI=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\xw7t1rn43b-mr8xev4a41.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Memory#[.{fingerprint=mr8xev4a41}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Memory.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "79rwsiw025", "Integrity": "BYoDfONCogjjcnJvbH/Vqyiq4IMElLIUjhgPfxUJayI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Memory.wasm", "FileLength": 21085, "LastWriteTime": "2025-05-27T22:01:13.769378+00:00"}, "vHtvrjzZrsSjKbEg7yzwx5sjFkw6P7bBldfkxWecloI=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\52imrxj3z9-fsk5apfw1y.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.Json#[.{fingerprint=fsk5apfw1y}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bq296k6m5o", "Integrity": "E+w9Sz1YnqRGHsmKasMWiqlbCFUsPucShOp2xqDffLw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "FileLength": 19910, "LastWriteTime": "2025-05-27T22:01:13.7723776+00:00"}, "VGlyCW83WkTy4O2rWFHJTxpBJrKwB2sRGkI1elVKZLU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\bduh5rofva-jzkfjdda15.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http#[.{fingerprint=jzkfjdda15}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4rhmvu43r9", "Integrity": "YLofC+uAE08Ux5v18oCOY8XfnXXoqRXHucCcx7i4LMs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.wasm", "FileLength": 115972, "LastWriteTime": "2025-05-27T22:01:13.7823701+00:00"}, "9tqRnzZvXBeMgauOQnvXPfhky1nkqA9KCO4O8XFEThk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\uzmw7x42yk-xweq5xme4y.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.HttpListener#[.{fingerprint=xweq5xme4y}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nf65glf5aq", "Integrity": "02OIcQJsJVJmXmgWl/FPtnYywfVMUZvtojaI4uyRiMI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "FileLength": 16311, "LastWriteTime": "2025-05-27T22:01:13.7833695+00:00"}, "32cYACOveoeYXoSJqaqrL4u35bZPOnLHkaQp/Zh7e5c=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\m8tuiswury-exa7sc8li4.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Mail#[.{fingerprint=exa7sc8li4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7tlqlc2vzl", "Integrity": "67Wbfi7k859Q3kfHJLDRK9eaOfpedVL4LGk4457ag1k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "FileLength": 42449, "LastWriteTime": "2025-05-27T22:01:13.7883678+00:00"}, "LPTOGJbZsTm8S/Xir8+x+WmM0JluQOegOZGbNspV1rA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\nr18yqw2ef-7nsh0ffju4.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NameResolution#[.{fingerprint=7nsh0ffju4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8pvcf6hv4o", "Integrity": "fEKnPcKXcU0qZPwouGLtsTvLv/6jcXO85yy4pFGqq6E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "FileLength": 5993, "LastWriteTime": "2025-05-27T22:01:13.7893661+00:00"}, "I98iP23tasnuXt86TXtf/Ox/lqwA6ZnuES5uotc+TRQ=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\4ajkgrt11i-16618q39eu.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NetworkInformation#[.{fingerprint=16618q39eu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rpfrlvl5kd", "Integrity": "TykzPm72WKIZfkZz7puqEOMR4kKfstaqaGNWb57NHgA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "FileLength": 13038, "LastWriteTime": "2025-05-27T22:01:13.7903652+00:00"}, "9fX5+bNUUwLFio0xIV2x3z4bZ3Hz+UTyhEwRgEXfB4E=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\xzakcfz7fb-73n1msm1kf.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Ping#[.{fingerprint=73n1msm1kf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k6z0lauc98", "Integrity": "HC+9MBmff+hfTts74jgAidmAySmNHbnnbuTR0lEVEAo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "FileLength": 7668, "LastWriteTime": "2025-05-27T22:01:13.7913643+00:00"}, "Oko0lGnlbMYqGjH2/Ge+JUo+exGP9IEqa8x3Ts0YJZI=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\ak6dd7cblb-cp7icbjoye.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Primitives#[.{fingerprint=cp7icbjoye}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fhq9z9nk81", "Integrity": "UyX7E103tn9fCbTI+zBosG8Iu0AmjAeBCqJWyU7Y4ZA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "FileLength": 46566, "LastWriteTime": "2025-05-27T22:01:13.7943625+00:00"}, "wy4nLSa4SOOLKRzIGU4WWNBgeNaftXgy+fkKZpZ4jhU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\u8e73lyacz-5ac51dyvls.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Quic#[.{fingerprint=5ac51dyvls}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3giiwpk9sp", "Integrity": "HS4HpXvM8x7niFWoJ77qeNuoetHNYjfJv+JGvJnZMw8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "FileLength": 11095, "LastWriteTime": "2025-05-27T22:01:13.7953618+00:00"}, "v5mEMa79+5Dw3V1hflN1wokTUWNqqB4VV2WEKnde7Zk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\iwia80zh59-74urp2owxv.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Requests#[.{fingerprint=74urp2owxv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cwdctrhh92", "Integrity": "pFUNOGqTL7naBFyqYZFpVYgZ2iC3mxfbMp7WdKf1vXQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "FileLength": 20751, "LastWriteTime": "2025-05-27T22:01:13.9542637+00:00"}, "kTBnaa/RB5hW2vIV8SilvsSeHJFujjl2BeuYopFqHaY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\r7kki233cz-q4em7fcmos.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Security#[.{fingerprint=q4em7fcmos}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bty1n81i48", "Integrity": "gXKI7nfI2h+Ppr19L8n7Sl0w0PY2md/+dPs0LHkDpg8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Security.wasm", "FileLength": 33462, "LastWriteTime": "2025-05-27T22:01:13.957264+00:00"}, "8JQWVS50mZYqVTx8KMHeuM9kd49e71/qbDkglQkG0ZU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\mmz05dagzs-60vubhwq2d.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.ServicePoint#[.{fingerprint=60vubhwq2d}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4s9kt2xgb8", "Integrity": "hANrp18roqpY8f0KsgSUHXT2vzV59O4w53V6leaVrnw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "FileLength": 2169, "LastWriteTime": "2025-05-27T22:01:13.9582632+00:00"}, "DwdyGsP5pQH5bX/aT2bNtsPi+h0e7e5Jtb4F0pFhNGk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\1mypffcdv2-6qp5a9tfvh.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Sockets#[.{fingerprint=6qp5a9tfvh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yooe6hjo0b", "Integrity": "frGWc9I8JsCoqBjykXxirsW+rpKLYFf/WU0GGOWT4DE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "FileLength": 23479, "LastWriteTime": "2025-05-27T22:01:13.9602606+00:00"}, "i0hSDHLSuhVIQMYH0WeM/k6Sar3c8yE0b/pDhjq+7xM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\xg7dme6el6-kwo41wq289.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebClient#[.{fingerprint=kwo41wq289}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "frim2dakz9", "Integrity": "hcEHpc4BsPbmCyLAGWAXeQHmP7LCB40YiWjmoVlylSg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "FileLength": 14626, "LastWriteTime": "2025-05-27T22:01:13.9632609+00:00"}, "taS0ZO/guNgu08C7cegWOZtnJWRzPM4X/JkIWmxyqLg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\d0d6m4mlay-t1y05ppbtx.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebHeaderCollection#[.{fingerprint=t1y05ppbtx}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "54ztvvfryz", "Integrity": "BWJqx/4okFJhvsdC2nRnsPsprIiwwywsuWLhmwcCQ0s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "FileLength": 10375, "LastWriteTime": "2025-05-27T22:01:13.9662587+00:00"}, "dZCvKuMf/6KJWViYMsbI9KDjHx2TRXIpp/DFok1Zj8s=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\1rk64401xt-232idirb7b.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebProxy#[.{fingerprint=232idirb7b}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d84uqjzx4r", "Integrity": "E5mQSm54bsDjWp5u8UA/InuqT2saCBU6VwBeINK/uyk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "FileLength": 5667, "LastWriteTime": "2025-05-27T22:01:13.9672572+00:00"}, "h4i1BaGIX8jIFgaURdRjGqZsWbqvPpW5EvPKfwFi4YA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\epibos07i6-1rwz2j0ytm.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.Client#[.{fingerprint=1rwz2j0ytm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7067r3xlzo", "Integrity": "J1G4APD9A9YrpZtOyG1Ra3SftJJm2Qx0MpnJ2TfGpfg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "FileLength": 17399, "LastWriteTime": "2025-05-27T22:01:13.9682557+00:00"}, "UU3OU8spuUfiGbX7nJUrEgyrlCQGYeUNMofGsHqK1/g=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\hyp2ap3z22-7ns4dakcmj.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets#[.{fingerprint=7ns4dakcmj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "89f7t4dqmn", "Integrity": "BJpgWRcAbp+xrjLu7q4WDFzFpMcfW9z+1Mgben3GwEA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "FileLength": 39056, "LastWriteTime": "2025-05-27T22:01:13.9712538+00:00"}, "YZeWOQav66PQtfZu/rAK+C1/mdsSmRVEMfGJGYuxHbE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\8elaqa2qtm-hyl81vktsx.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net#[.{fingerprint=hyl81vktsx}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "byukzbr3ri", "Integrity": "s1iZ+hIO38BqTBvBM7tJxkbd4e5RzuherNjppCzkWU0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.wasm", "FileLength": 2756, "LastWriteTime": "2025-05-27T22:01:13.9722556+00:00"}, "S7PenJQxfqlYf3BCPLkiYG1AcbTqF2BtZv9lblHfJNc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\mcnuz8rqmu-3ulov9gswu.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.Vectors#[.{fingerprint=3ulov9gswu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pqb5vl6kko", "Integrity": "sR2J0CLvti5NE3I0sJWp8mRkvo/h8sUYCQMXk6oDJ98=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "FileLength": 2265, "LastWriteTime": "2025-05-27T22:01:13.9732544+00:00"}, "6rGndtgRo7c8QEXT97AcE/sFYEdhYVDyXfCClVNEEjA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\495ad4t8bz-3xrgeyndxe.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics#[.{fingerprint=3xrgeyndxe}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6s<PERSON><PERSON><PERSON><PERSON>", "Integrity": "BhtIimBTaAkFUD+k+CIJ4UnnoGpnYHyhksIB3L3C/rY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.wasm", "FileLength": 2025, "LastWriteTime": "2025-05-27T22:01:13.9732544+00:00"}, "FFSuxJ3k41oYAJeM4Oyz6yYUB19RK6tPR4JEFEpA5ho=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\v1cm2epe4f-2onw4a3huo.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ObjectModel#[.{fingerprint=2onw4a3huo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sxvoqv12vx", "Integrity": "sH0U8F9JMXRS3qbk9bl3ccwhYBo89vdsF6lobimEwro=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "FileLength": 13597, "LastWriteTime": "2025-05-27T22:01:13.9742536+00:00"}, "XXk9bvLXuBlts6QhFyPQaBH6f4sVs3YyS/XGGGbhgHA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\lzev4w040n-viwejdyf0o.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.DataContractSerialization#[.{fingerprint=viwejdyf0o}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v0tpe5zuap", "Integrity": "0e6IPfEPIUv/S2HWclJlZOUaWE3dv6nvfppe0WbUsIs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "FileLength": 304582, "LastWriteTime": "2025-05-27T22:01:14.0651954+00:00"}, "jRW2uf+z2YJgZvERGMS90R4xi1KCK7yoEWLceIfr0cw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\5knlzqs5yr-gccfg575xd.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Uri#[.{fingerprint=gccfg575xd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "apk4l8jhbp", "Integrity": "MvmtBja82bSzObdnObl32oWRxbN4PmU44J1flF4g8Ig=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "FileLength": 42187, "LastWriteTime": "2025-05-27T22:01:14.1300508+00:00"}, "1TzTWJXqXTsYf/IuJEla7kQtqVz9/tURto2PrTj/8VA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\e2gudq44ak-hubb8bdrav.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.Linq#[.{fingerprint=hubb8bdrav}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "49q40n9g8j", "Integrity": "6KnXwcHhS/F7ingX74oTAU8I5+sMgbt7Y5U26NrIamg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "FileLength": 59684, "LastWriteTime": "2025-05-27T22:01:14.1900135+00:00"}, "rGgCzLeXpmPxfGicBp7lGNX6lKdzxI8i8TCSs5FyHkc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\hn0o5sl1z2-36oo928nq7.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml#[.{fingerprint=36oo928nq7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5219u0a8mx", "Integrity": "nevJgTU5ugwx45kJYmr3G7BEcyLdbfeb17XMGSK8EWk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "FileLength": 1069740, "LastWriteTime": "2025-05-27T22:01:14.0900752+00:00"}, "5vpzojn4WfgfTdaKNEILQEVooOwi/f/o3q/iLyjhjWE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\1ls0nftn72-ytnrm7l6ga.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.DispatchProxy#[.{fingerprint=ytnrm7l6ga}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aduvzikrr4", "Integrity": "fjbAERry1JYO91FDWwM1IdfWhm7g2AyRHUbjbft6l90=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "FileLength": 13142, "LastWriteTime": "2025-05-27T22:01:14.1850163+00:00"}, "m2Nrrw//rB8rB6dXdol/eiXqz0CTydOfPIn7OURt3as=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\e5p0sun1y0-n2z595t7zv.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.ILGeneration#[.{fingerprint=n2z595t7zv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xfa4ownoqt", "Integrity": "3RWNNkoqTXtUctZ3ku3YNWFEEhn7i8CSXdyzrgIDiWY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "FileLength": 2267, "LastWriteTime": "2025-05-27T22:01:14.1860159+00:00"}, "JMWyt1NbQWBgeKhyLZOym/OFUy5dFrpdatGLzca3jjY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\g7bqyf33am-icwj787vcb.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.Lightweight#[.{fingerprint=icwj787vcb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hf748uts2z", "Integrity": "nQ4hHq7boWMq6GidqZyVJzUVsrygcGXF/LjVdAzwfKs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "FileLength": 2224, "LastWriteTime": "2025-05-27T22:01:14.1860159+00:00"}, "VgjoWm2YDkNZLxLz7vWOdrtQNb9ejuEngureFhPDei0=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\f55ma8o14b-w74i2eyu03.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit#[.{fingerprint=w74i2eyu03}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f2dyoz0440", "Integrity": "WQW/EaQHfIOJ9JqHDzmmpfqVxWoum6C4g/Ptohu5CWw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "FileLength": 52814, "LastWriteTime": "2025-05-27T22:01:14.1910125+00:00"}, "qOZ9DtH9sG8IHvA2VxmBTWA41zS9iGqTwUZCioEI4ZQ=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\aaavqz1led-5iae6nghwr.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Extensions#[.{fingerprint=5iae6nghwr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r9anno5xjk", "Integrity": "QQVuz+kHRyv3ihrKq3AYd8Jny+Yc2OTngPSWs/tIr6U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "FileLength": 2146, "LastWriteTime": "2025-05-27T22:01:14.1920122+00:00"}, "sNrfbXVNgQy2k+lWKuCGFqrx53PGXsbVO+2w5+oj7Dc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\q13dekoav1-eu6g5dmyrp.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Metadata#[.{fingerprint=eu6g5dmyrp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6z0rgr7k1b", "Integrity": "sgmwM22TOz1EsW7J9RqeFRNIuic0wSMA3eCiHw+95IQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "FileLength": 195561, "LastWriteTime": "2025-05-27T22:01:14.204005+00:00"}, "8DeHMgp1OD8eUIic5j8oDatmJtlQiJSP+IB4emgmeJg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\sa3qpync71-8h25o84kf1.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Primitives#[.{fingerprint=8h25o84kf1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mrcrf36cet", "Integrity": "YjR7t0ASlrZqV++UXHgCpp+PFOUC22LYujJQJ9rLe+Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "FileLength": 2363, "LastWriteTime": "2025-05-27T22:01:14.2050049+00:00"}, "WmLf3UCAj7bCGu4ir4sMdFyOXXpxuC82VGAgQSDRBZ4=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\zn6hp1jd09-ncossl2nra.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.TypeExtensions#[.{fingerprint=ncossl2nra}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "renrjgtpjv", "Integrity": "Q/L8FeRlM8Q2kV5vxU0KYRwieUWU8b/DeMkjBxZz6Gw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "FileLength": 5728, "LastWriteTime": "2025-05-27T22:01:14.2050049+00:00"}, "LNRNglYDqS8Drz0ymNfQ82F5poLqWxfJ2Bl1DI3lcbo=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\9knc68tzoh-fu4huwo1gu.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection#[.{fingerprint=fu4huwo1gu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dkexmxjpzl", "Integrity": "/TF0XNR6/B5plFUZa09azq7vNsemEiVAxONcxSGv7mI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.wasm", "FileLength": 2453, "LastWriteTime": "2025-05-27T22:01:14.2060039+00:00"}, "9dMPdgU4rLhAHtne9NasBvdd1Hl3AearsvrUoqtFU+4=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\588t2n9b5j-ckle7drwww.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Reader#[.{fingerprint=ckle7drwww}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kf59tlsfkm", "Integrity": "wLRkZuFfbhZ6iMhaB+igJQCHyWiHh71HE0T/0B3ghVs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "FileLength": 2112, "LastWriteTime": "2025-05-27T22:01:14.2060039+00:00"}, "tCpY+qcTyt2raJ8EKHEU/qbzdP2L91WrslxxjZ4M3Cw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\kojxz4bezk-n9sh7wxvjn.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.ResourceManager#[.{fingerprint=n9sh7wxvjn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3u6ji2kjcg", "Integrity": "pbw34r0coSL7Tbr3MxlCbMAFPqdFXxd///rwFcZN1Os=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "FileLength": 2232, "LastWriteTime": "2025-05-27T22:01:14.2070028+00:00"}, "2cliBUlAp3PfvQLRyelAbvWXJQIgIcyE/T/0PtYaefM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\3j0wle7zbh-9kebxuk0er.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Writer#[.{fingerprint=9kebxuk0er}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w09afeatn4", "Integrity": "MJP3uYVxDtNWdi7aBeYK5bIe+OS5BsXdXp34RRXTKCY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "FileLength": 7743, "LastWriteTime": "2025-05-27T22:01:14.2080023+00:00"}, "ZgZPNOCQY5/L7KzpOBfC+09X6RJA76rRxkuedbxvkgw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\yr8ppzfnvz-j5tmcq1bfo.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.Unsafe#[.{fingerprint=j5tmcq1bfo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "16l2yr4gcm", "Integrity": "VzpTo+RGtJL7FmUkG6n74NCxlrH6GYRUk6QRN1+JXaM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "FileLength": 2120, "LastWriteTime": "2025-05-27T22:01:14.2080023+00:00"}, "JfP+9BxR+6NdIxzvesWQyex9y98ZaBoEER4hc737wrE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\hby7uy9znr-02zc66007n.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.VisualC#[.{fingerprint=02zc66007n}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gdr7lgvqz6", "Integrity": "rJTVgTFol1iMKqhcYSiwvUbigJMwqrt8Uvn8NdAgV8c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "FileLength": 3068, "LastWriteTime": "2025-05-27T22:01:14.2090017+00:00"}, "4oDTbkTVHk7RO4BhVGRRw9YYRhjT8MoYmWsQkhg33wQ=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\lgdvva5fqm-oiot8mv0g3.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Extensions#[.{fingerprint=oiot8mv0g3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vfrd19jmim", "Integrity": "1R7rQrXvE8Hkr10pcoPZRdkFtUFCY6OYuG9lJNMe194=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "FileLength": 2993, "LastWriteTime": "2025-05-27T22:01:14.2090017+00:00"}, "/RfryhDhoIb2e3u8+Anrw0jXaQLLDMOEvYWGdqGiu3c=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\1ptq02sjwy-vvyys879d4.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Handles#[.{fingerprint=vvyys879d4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ny55yyw8do", "Integrity": "b9d1is02yhlOfwSiOPD7MJh5ry//CnIW7OZCAcnQrLE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "FileLength": 2193, "LastWriteTime": "2025-05-27T22:01:13.897299+00:00"}, "TE/N05Pjln2QfDSV9JK4cIS77J25KZitIflHUfYE758=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\5r09r55aft-y6arj9x08o.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.JavaScript#[.{fingerprint=y6arj9x08o}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lqxn0cbh2v", "Integrity": "jarmqiUKR11EATbNwiegrZY5pM+d9+TNU2zVX2LF7hE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "FileLength": 31682, "LastWriteTime": "2025-05-27T22:01:13.8992978+00:00"}, "EZL3fx40SdFufrcU3ZC0wq4tuwC2lMDBjFXy1dYmj9M=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\sr2552qcxy-4gcavmlio6.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.RuntimeInformation#[.{fingerprint=4gcavmlio6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vxe4lavh0s", "Integrity": "XQ7ju0HCdfD25K9qzAm/uE5UAWp5kyQkFiZU0H1dh/g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "FileLength": 2139, "LastWriteTime": "2025-05-27T22:01:13.9952397+00:00"}, "kKH8vRXXMKyHogWLXnd0ZO4t5MYSZ2uvejx45EmfOcg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\s12pe1qsj5-zcf5xh4j89.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices#[.{fingerprint=zcf5xh4j89}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dbgyr4mbib", "Integrity": "581ClXqiUPWr97KQQ0lh9ntTx2mkun5vFuFdPYaHVVc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "FileLength": 23801, "LastWriteTime": "2025-05-27T22:01:13.9992368+00:00"}, "nNgIZtqH7ONz8W7HnBE9sQwx8ZpFlRngoUjykVXGde8=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\dw1yhgxy87-unx94uxfba.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Intrinsics#[.{fingerprint=unx94uxfba}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n0251x81jn", "Integrity": "dx4Sr4uCJgUsdKqQKbRIkKWyHe/vZjWKCN0+0igoBJo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "FileLength": 2736, "LastWriteTime": "2025-05-27T22:01:14.0312166+00:00"}, "KzAUvx83ZcnZiXDz148Drczrkz9IMjiSHxg6j7a8Z4E=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\ys3syal6kk-lio72255su.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Loader#[.{fingerprint=lio72255su}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "111l0cewzo", "Integrity": "dYh8wh7QK+EKoi/82oqhNjQ95nk7Kb8ACyxqoRb4uAk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "FileLength": 2317, "LastWriteTime": "2025-05-27T22:01:14.0830812+00:00"}, "zcR5zAtp12PmY8iR+J4O0jbZ1gaP/tf+F380L8zjBwI=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\cefa9p1d9t-drjgq7o7pq.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Numerics#[.{fingerprint=drjgq7o7pq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zs8dj8t5t5", "Integrity": "LW99X1O3TXmAM1p1CAG44lIJhkBUo95HIgKsrCFT398=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "FileLength": 53377, "LastWriteTime": "2025-05-27T22:01:14.1320508+00:00"}, "nsPv44oY4Coa3UxoZokwrRx5+S+q5cHObD4CKXdLAis=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\rypvdavjps-7hfaupc75z.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Formatters#[.{fingerprint=7hfaupc75z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6s4tktbxu3", "Integrity": "dtWvYqMnxFg15AnyzATN8x5fJMgr8Z79xcCuov9p4x0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "FileLength": 24579, "LastWriteTime": "2025-05-27T22:01:14.184017+00:00"}, "29X2Y40z+cC1+C3JKVs2hfhc/OseohZ15CSAuPTFa9A=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\a4rvyav28y-avkizgk9x1.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Json#[.{fingerprint=avkizgk9x1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7dru20eg6o", "Integrity": "9fcJiQN/IPC1NFVQJINqPScXWKxfhb6L+VJv9FBZLnM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "FileLength": 2243, "LastWriteTime": "2025-05-27T22:01:13.7963616+00:00"}, "9xW6zxZkhM72UrLqw5opHuA1EdhKn/WtwqAZI8QlLTg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\vltcjr3xoj-k309oqfoa4.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Primitives#[.{fingerprint=k309oqfoa4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6v1575zl41", "Integrity": "TlyxoZ1KrArE0NkxJPxz2fSn7TxwJDGghwwacSF7aj0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "FileLength": 5496, "LastWriteTime": "2025-05-27T22:01:13.7443957+00:00"}, "mQLsoe/B6FK4dPKhTS87w+pKyUBY2CWbZpxqNhvmpOE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\uladd78wnc-0jwn7dp9nd.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Xml#[.{fingerprint=0jwn7dp9nd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yz2xe2blvx", "Integrity": "kmyYm5klRjvUx0qoAfPJY5Ae3AkzzlWKHMURwCfy7yk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "FileLength": 2561, "LastWriteTime": "2025-05-27T22:01:13.7513895+00:00"}, "MlPc0h/fesaK/lJl3xQgN4YEU2s2bslgGD1N80p0mlE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\80ecyyv9e5-z7sglijpt0.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization#[.{fingerprint=z7sglijpt0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "udkn1zj0w7", "Integrity": "DvwWXdG13Y6p9ZFvrLSkohAszmliFA4jHSElfDmFMv4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "FileLength": 2497, "LastWriteTime": "2025-05-27T22:01:13.756387+00:00"}, "2S4oYNV6+0Bs3bZBVKaF9ZRJOKhWahn7Hjf6ggU2jmw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\6q6i1sjiw7-c61u9af934.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime#[.{fingerprint=c61u9af934}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a5pokwbxou", "Integrity": "TpmyQYKOvXIyrQcAzHQu79M4iG3seS4BEIeeD18wMm0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.wasm", "FileLength": 10722, "LastWriteTime": "2025-05-27T22:01:13.7613831+00:00"}, "LeHwVe0OyOtsJix5O0WPL8nKt2pPZV30Ha3GioMDTtw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\rx06hw9d9l-rl384mt7e7.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.AccessControl#[.{fingerprint=rl384mt7e7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bfwh06wbmi", "Integrity": "Z1a5PJL9ixZ5QeudaRoK2aoZxr12cw8sKhDyHIIhFPw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "FileLength": 17218, "LastWriteTime": "2025-05-27T22:01:13.7633817+00:00"}, "5hVPpP3wULn7WsMIv7RE7boOzORgOvv5o/sCnVOhGEo=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\0991i33qiw-845vwhbngt.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Claims#[.{fingerprint=845vwhbngt}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "650y71lryt", "Integrity": "OlAZBERUyOQhNpFlkXLN/Ljuoi1SqQmC/nFMUeecaoo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "FileLength": 16443, "LastWriteTime": "2025-05-27T22:01:13.7673795+00:00"}, "MdiGiOHnrERSFe2nAzvlISvfsugGonOhfVq34pEQtZc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\tter28qsx7-v13868m0ek.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Algorithms#[.{fingerprint=v13868m0ek}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h61zxljlmj", "Integrity": "R2reDiWUHDvBgOmxrzbblzQKuiVoNcGVyTDcA+u2Os4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "FileLength": 2715, "LastWriteTime": "2025-05-27T22:01:13.769378+00:00"}, "pmuSQhOJT0e/2V8nQN3zQTf/tgdIg38af1jzZIF4OmU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\qbnen0z1b0-uqhggvgrxh.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Cng#[.{fingerprint=uqhggvgrxh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "etho4sm9hq", "Integrity": "znvJJczXKdbYbrVUV/Ry+UNhFLMhVhQZQa3lIl25TPM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "FileLength": 2474, "LastWriteTime": "2025-05-27T22:01:13.7743756+00:00"}, "TAWH/m3+pVfZkEHUaVn9cBulpNeV+sRRhmJB8zGCQWE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\r7k5rauv84-ujtlhftd9v.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Csp#[.{fingerprint=ujtlhftd9v}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nevvo7wwfh", "Integrity": "QbtqkDn0ONiXxHp//wMMjirW4ttuYjsWvV/jOqoPfCM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "FileLength": 2335, "LastWriteTime": "2025-05-27T22:01:13.7753754+00:00"}, "anrHhfsn6lXpDowWodRDLrOTNbFoEktbxISmsTZp8Wc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\qz92ker35a-9v1z3jix27.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Encoding#[.{fingerprint=9v1z3jix27}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gub9b8e30s", "Integrity": "yLC82OQznBeCQo8Cs0HMotwNU06FKEF9urxXKSu/dnk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "FileLength": 2268, "LastWriteTime": "2025-05-27T22:01:13.7763741+00:00"}, "+4LW+as85V5vdqpTpPbFR0AXHQQyML74I03vQSnjUyQ=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\hxcr6nk5ds-zh8vki7uno.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.OpenSsl#[.{fingerprint=zh8vki7uno}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0wgykgr2d1", "Integrity": "AHK4gUkTaOLrwyznkLTIwKJHf9kwIdCJrV4Ujfq17FU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "FileLength": 2205, "LastWriteTime": "2025-05-27T22:01:13.7773731+00:00"}, "Re5igt0lTZ0WlqaRUx8y0kN4VwefxrRc5xCHtVLHspU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\vt5rzavh3u-x3mep84tc4.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Primitives#[.{fingerprint=x3mep84tc4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qidzpu8srf", "Integrity": "MqBnPBjBOg8L59TJLNgp2pffrPAC2MijlS/7wwqM1cA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "FileLength": 2330, "LastWriteTime": "2025-05-27T22:01:13.7773731+00:00"}, "6ay7Jkc3JcudscC6CHP/F/PnQq9UF+V7TH+9TTPHkcY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\151j5596w9-j8t07tanc6.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.X509Certificates#[.{fingerprint=j8t07tanc6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gfsuzdddst", "Integrity": "bFK+IieQ6EdMf2LZ+FTRNleVdeK9DbFxMGe2GfeH4OQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "FileLength": 2669, "LastWriteTime": "2025-05-27T22:01:13.7793716+00:00"}, "BeBhw1B/fA4ynFKyOnPosaCv7lz+Gj8V+SlBF4yZMAk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\d6j61snjvr-05gw4icbhi.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography#[.{fingerprint=05gw4icbhi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kp4q0pwhuu", "Integrity": "qCFJ2KKwfR+8G/usUTOK75/unU5h8OtIjyTY3PvGe00=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "FileLength": 192146, "LastWriteTime": "2025-05-27T22:01:13.7933631+00:00"}, "O6R1SkEfTvn6qZw3XpDfdgn/14y4Ss5Y07rGu80xob0=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\pp97orfgsq-m4byz8sabo.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.Windows#[.{fingerprint=m4byz8sabo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3g9uo2rj7z", "Integrity": "raTVsC71M4T3mkIUawyL+wHoqc6/NsJ3n8nwDLuTgLQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "FileLength": 11377, "LastWriteTime": "2025-05-27T22:01:13.7943625+00:00"}, "dlsm5ey6TOo/wlpAYmWUtfDPvV1hkVCdB/4K2ejBzsw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\vystqybhj2-5bg4gnsg0x.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal#[.{fingerprint=5bg4gnsg0x}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "frfnd1e7bz", "Integrity": "dd8pRYgDkGqwQJSmHpxnVsotvI7nimOaPILaWyQcbIo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "FileLength": 2160, "LastWriteTime": "2025-05-27T22:01:13.7953618+00:00"}, "itW5U7A8lOMVIIXL08dcCcbQ2gsY9k6LrZVq46m1o8A=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\wxd33v33x4-fiastri9ki.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.SecureString#[.{fingerprint=fiastri9ki}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "03w5o7tfgv", "Integrity": "VKoj5gSUlDjFY07jbCzZyn1PuImho74o8m2QelygF9w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "FileLength": 2188, "LastWriteTime": "2025-05-27T22:01:13.7953618+00:00"}, "oqDvvgw3ZZLORzEAgSRwel8sybf3yiuE04IKPvd50i8=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\1fl3gg8qee-80ju9y7l18.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security#[.{fingerprint=80ju9y7l18}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "910uig5nh5", "Integrity": "UwKb8SUGmpiDNDzsqvKoDSyuBjF1U5KaFvk6m0Ikclc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.wasm", "FileLength": 2977, "LastWriteTime": "2025-05-27T22:01:13.7963616+00:00"}, "qn0cImkpZomv2bVH0WwqXlQrIZtxS037NabkAy92kMA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\h6u46qptao-tv3f98ejk8.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceModel.Web#[.{fingerprint=tv3f98ejk8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sf69pgmb1s", "Integrity": "KF5VncW8OjGhYiD+Ca7GzpFNIrdqkLIQnulEMxadPGk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "FileLength": 2537, "LastWriteTime": "2025-05-27T22:01:13.7963616+00:00"}, "sPzuyWbsWatYDYDNnRtUoVF6IlDbM6URSNPY56zqTl8=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\z13dn8ngiq-j3o0lzzi7t.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceProcess#[.{fingerprint=j3o0lzzi7t}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jc8eymt107", "Integrity": "PVT1xtBwE4/uZLidPJYlilBRELMkVTeKHQOcfhR6Mi8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "FileLength": 2299, "LastWriteTime": "2025-05-27T22:01:13.7973608+00:00"}, "O37LOxu6NF+AA4l0uiArbqxUoYb1AHewyrVl/Y23yLI=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\pela39oqtr-hhvnal3rqz.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.CodePages#[.{fingerprint=hhvnal3rqz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m2kohm1ff4", "Integrity": "nhRDoFu+049ErA6P2TvuBvja1Z/4N5W4XrJfdPZCdeo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "FileLength": 518381, "LastWriteTime": "2025-05-27T22:01:13.9002975+00:00"}, "l50v6Jdqui/5yIawkfeKBQcoIOi5hkM8s9hJeWcEA9M=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\jg7otnaaig-esy6mc7t8y.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.Extensions#[.{fingerprint=esy6mc7t8y}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9lydlcaahu", "Integrity": "owq+c6AUh4KbWE56QntwUaQyQTrx/7vHIyZUqG8fOhE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "FileLength": 2248, "LastWriteTime": "2025-05-27T22:01:13.9002975+00:00"}, "B09KEUu9J1UZeTnERk9rZi8xwB+RxO4PwdujalzeO40=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\jhkdypp21m-so36gwcdvm.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding#[.{fingerprint=so36gwcdvm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "31v0376del", "Integrity": "F5ydBbOQYZ4a+nS8cDzIbKBGEF6/Ds9Bw4b5Bq6L7iI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "FileLength": 2228, "LastWriteTime": "2025-05-27T22:01:13.9012967+00:00"}, "Nx7o2xGFWVZLuTsAwcv0ZK7+fGuFKy09VVXHfRagrXw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\q5t4vb1tb4-g33dyw9sdz.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encodings.Web#[.{fingerprint=g33dyw9sdz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4gzqmv3rzl", "Integrity": "9Ne1CvP66MY83w0WURyUuvil5Oj0QchKMkD5Opll2U0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "FileLength": 23940, "LastWriteTime": "2025-05-27T22:01:13.902296+00:00"}, "Aje4/7uuebiJApnHvpneup+CAoSCCrDm7vAZFLeab4k=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\pxinhg3iab-o0tkiahb9x.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Json#[.{fingerprint=o0tkiahb9x}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w8xldhchtb", "Integrity": "cFO+7RIYmu/Tk5SCYZ5zDrTfWKrkXQCLajyCbsGCzlg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Json.wasm", "FileLength": 221078, "LastWriteTime": "2025-05-27T22:01:13.9252826+00:00"}, "5WMQNTgIx1/oMSs46KEWjzkXFRmNNcP0WCgaRnX6lEY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\ym94ay6s62-5x7wnnoptp.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.RegularExpressions#[.{fingerprint=5x7wnnoptp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rltf9ilvx1", "Integrity": "YXuTBWH+7p2VCQA+sken9raTdvVqwj2ALpW6+Hoj9iU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "FileLength": 156838, "LastWriteTime": "2025-05-27T22:01:14.0462074+00:00"}, "+zJMWwZmQ9uR1ZHHxZtHSCKtQ+oCn3oPTsQKKb2AQ+8=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\jerap1adoq-sans7ybw3q.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Channels#[.{fingerprint=sans7ybw3q}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0whzwkaod5", "Integrity": "sCqw7oi+jBbq8m/kfAIBBereUtBZKYuVsH2kK4AInnA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "FileLength": 20997, "LastWriteTime": "2025-05-27T22:01:14.074191+00:00"}, "mnwkPwCvQJPfDqLTJ6gCTCElhUxksLbsNnjYMLV6l14=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\rtbxxuf0s9-w9h9po1nje.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Overlapped#[.{fingerprint=w9h9po1nje}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mfk05vrk78", "Integrity": "jud6Xm+goKDplyACNaH96kLlmIBEgGakRwaC9bCOmjw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "FileLength": 2305, "LastWriteTime": "2025-05-27T22:01:14.1190572+00:00"}, "jf7Fd6U7NrZ9/Zf2CZLRNWMmPFiFfIex6h+yjbIZ/l0=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\86msqs7qqi-fhcbb6k32h.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Dataflow#[.{fingerprint=fhcbb6k32h}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fwpn5n37ph", "Integrity": "37OTuJW/m4U0HH5adciUUO+oAfMBCf1wfyhVVVrlvOM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "FileLength": 74089, "LastWriteTime": "2025-05-27T22:01:14.1820189+00:00"}, "I/5ZxBMf8RhMm7rGKrWymdoop7cJui3p394rFWHF2zU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\lc62puresx-hs7o6v5tiq.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Extensions#[.{fingerprint=hs7o6v5tiq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zufethjtgx", "Integrity": "y9hFEKzPbaVRGdzpa3fs51L2PgYL7kz69RVAjKifhXU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "FileLength": 2298, "LastWriteTime": "2025-05-27T22:01:14.1830186+00:00"}, "J70RHa5tBpGIgLeVVubDbFlzL7lLrlSymnI4u2jfoT4=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\5m3lf67z35-k8t7rlfiat.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Parallel#[.{fingerprint=k8t7rlfiat}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21dkb75re7", "Integrity": "9vX4o6tdEL585y9bu78+XkSEyTQKxBi4Wmfxd1oJQ7M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "FileLength": 21516, "LastWriteTime": "2025-05-27T22:01:14.184017+00:00"}, "OmRRqeAmVCtO/PpUk3zZ3+AuQJiD6JaMZBdIgjcEtGg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\siixtzmeq6-tbqaq378z6.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks#[.{fingerprint=tbqaq378z6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxnjaeocll", "Integrity": "IT4/N3Nj9ZXzTSvbAospKqicBDA1wiLYmlHtHHxk+ss=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "FileLength": 2551, "LastWriteTime": "2025-05-27T22:01:13.7973608+00:00"}, "Ilj3FjGr/IZY2DZ4DNWsOXVPz+5eaaBQUdk3Oth78O0=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\lf9bzj8df2-76ednmbzmi.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Thread#[.{fingerprint=76ednmbzmi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nbh9aky2fj", "Integrity": "83lDK8/Dcp1bEomcfSKxl5MYiqRcq8Ps/lQIghzQCSg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "FileLength": 2329, "LastWriteTime": "2025-05-27T22:01:13.7973608+00:00"}, "iwnXiXq7hfREjIA4l6r4iYATi9iKFfVwM9zLfLAmwCI=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\wtp9aqktx2-dggkxr83to.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.ThreadPool#[.{fingerprint=dggkxr83to}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0uvnflgmym", "Integrity": "6GPlnWI2/ooLqCBWrPQ/gJlSQtYaR+iPGrTu7GQeBwg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "FileLength": 2254, "LastWriteTime": "2025-05-27T22:01:13.7983602+00:00"}, "SjQ7eH1Phb5YZw84YNReazOG/k1vsKA6dSno7AlgYu8=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\tsx4mmvnrw-j8kpz62luj.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Timer#[.{fingerprint=j8kpz62luj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lmao32glr2", "Integrity": "M0mdtBlkm57FpDUvcD1SqumYPtnoqTi3Kru5GYqzQP8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "FileLength": 2126, "LastWriteTime": "2025-05-27T22:01:13.7983602+00:00"}, "SSHZBFmWIiQesOeNZJDp3AykmPBtVDOm/YhVPHv6Be8=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\lfrd2nfs7a-myoimpdcs6.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading#[.{fingerprint=myoimpdcs6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hlbkx72ow5", "Integrity": "SyvRr2eLoSS9OCef/k8wQfxnRiCLx+YrHXyT98Kmhc8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.wasm", "FileLength": 14939, "LastWriteTime": "2025-05-27T22:01:13.7993595+00:00"}, "MnjLuw/GyQE6MBvHeQXW8sbuNOWQxdplDGgZ8ksQILY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\wvt0xtflqn-7hdcq2g011.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.Local#[.{fingerprint=7hdcq2g011}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8zhceh2jcz", "Integrity": "BHgZMVvLdppCMzTvKrM+Lb3vgkzLmWkwnWhhTV/A2JE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "FileLength": 52485, "LastWriteTime": "2025-05-27T22:01:13.8173481+00:00"}, "+0/GDPKcgTj8ObR7htfLMslaw1CimP292PC6TP54TV8=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\vzlxudg8eb-p83alyf85n.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions#[.{fingerprint=p83alyf85n}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oqllczrnyr", "Integrity": "UMljsZuTp31qCUhx/IpRefLT8lg06LS9lORl7XYl0+I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.wasm", "FileLength": 2364, "LastWriteTime": "2025-05-27T22:01:13.8173481+00:00"}, "l8gxdJod/JGiuu0anXxwvnx6vJbUCZkzW2s3w9TECqA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\2ofmdpse1c-ygaqfhsa0b.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ValueTuple#[.{fingerprint=ygaqfhsa0b}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rp26x2c5yy", "Integrity": "waMSz5KdQrTdT9dmvxuFFii7dQqEmwfn/nJGOxzqCMg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "FileLength": 2169, "LastWriteTime": "2025-05-27T22:01:13.8183478+00:00"}, "l3rBOmie0Fwt6c/zwP6lhYB2dNgjeY3dxrGDYQAeJ/Q=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\xlqkfwzswr-btaxuyjcvs.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.HttpUtility#[.{fingerprint=btaxuyjcvs}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nnwpeck7hq", "Integrity": "k+9yeRoQNPUaO+1j1JqT9iZ8ugHFS5GI9x9Ep7iI/PA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "FileLength": 10060, "LastWriteTime": "2025-05-27T22:01:13.8193473+00:00"}, "UI9/rzEUa9/vg8Z6EwU7clBC4Wg1joVg/7CjSLyEvj4=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\w6n52935ag-xfk5e3d2ux.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web#[.{fingerprint=xfk5e3d2ux}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pb8120erd9", "Integrity": "ZcKI1nasaPQvwfogSwQt9MnxZfK+z8zzx1rlpWYgONA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.wasm", "FileLength": 2111, "LastWriteTime": "2025-05-27T22:01:13.8213475+00:00"}, "ldwF9BTvokdOtm5zY9dpuUWCiZ6+dXP6NqD5ZwwJQJ0=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\u7l75xakxn-dvpjq0fzc1.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Windows#[.{fingerprint=dvpjq0fzc1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nf65zmcbod", "Integrity": "LbXjFbzDuDz4EWalL8cnl+kNMILvcTlbTxokuexZAPw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Windows.wasm", "FileLength": 2270, "LastWriteTime": "2025-05-27T22:01:13.8223458+00:00"}, "1cyi50z0OsJ/HzdDj94iVS5qx0MmcXcmbcmygT+X1yA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\qh34dfvf9l-3g0uzfg3q5.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Linq#[.{fingerprint=3g0uzfg3q5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l7xf0yj7r1", "Integrity": "4Q12Wav7WmRi6Rjb6NrSQ8t2W8U9HcAMNxucqwioQnE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "FileLength": 2211, "LastWriteTime": "2025-05-27T22:01:13.8223458+00:00"}, "otHDRMiwUCXBdA4H1P67+9qdPHIMd6FAdrGIoMJLFNE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\h69720ziad-nmw5lay0th.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.ReaderWriter#[.{fingerprint=nmw5lay0th}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g73npthj4u", "Integrity": "BHMfSNFKBB6O7DwOnX6xKlEbf6SU7uO+uaDIVU7naLk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "FileLength": 4021, "LastWriteTime": "2025-05-27T22:01:13.8913028+00:00"}, "gGCz1w1Qb19d6D07Vbl3ZxPZQ/aBlpweAeLxaMRwuKM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\2xaiv1moxe-35x5v84a2s.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Serialization#[.{fingerprint=35x5v84a2s}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8bjadm0num", "Integrity": "vHrTH+CFn6bTfMXsNxSuyJ4kkSnHsoyuu5JRJMm1uVQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "FileLength": 2244, "LastWriteTime": "2025-05-27T22:01:13.8913028+00:00"}, "QSTFDv52tjITrFk1Vvq6kw+lPc+xdazsI+edbc9ntlc=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\jyou2936ze-dm2u9ur6u8.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XDocument#[.{fingerprint=dm2u9ur6u8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uk99701wj2", "Integrity": "vg+sRJ8H7b4njJLiDDtGa53X7LJRX5gbspbWt+0IHCA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "FileLength": 2390, "LastWriteTime": "2025-05-27T22:01:13.8923028+00:00"}, "hJUPJwxKIrYY1/9zxAWEVBYqSxAjJ6gEH/aP/QFKjg4=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\sau2cbcfh5-p77fb7ls9l.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.XDocument#[.{fingerprint=p77fb7ls9l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8mbpeu8wxy", "Integrity": "EI1hMmhf8Uh3yeo+mYaw8lzQ/TfpejDkz/+/QVn83PY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "FileLength": 2472, "LastWriteTime": "2025-05-27T22:01:13.8923028+00:00"}, "0QoVAJdGbShCs6Wv2uaSOxUAXyVarcL3JtdcLVo3kwM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\47803fu243-gkgxk99utf.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath#[.{fingerprint=gkgxk99utf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5dvbhxd7hb", "Integrity": "idivwdEftnBAe8gg6jhgIHJs82XvxyvhjcHZuy7GKKo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "FileLength": 2314, "LastWriteTime": "2025-05-27T22:01:13.8933018+00:00"}, "4VdRYT4yeM6pMa9daimHO6+bUuYRNYOIGpqnxdRyBHk=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\ngcoqfk2l0-obea1y0o4e.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlDocument#[.{fingerprint=obea1y0o4e}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z0u359qsy0", "Integrity": "w0ObXpsNkGJELGl9TI3AgPEFM3x81G4KeETL4/bFCEo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "FileLength": 2351, "LastWriteTime": "2025-05-27T22:01:13.8933018+00:00"}, "0uUNSXc4bHqAtcjWTzu1bBTvVfdI8Or0x2GvWFoIpA0=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\g7ayxejngu-69wknkil2z.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlSerializer#[.{fingerprint=69wknkil2z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ya84tiro7n", "Integrity": "CngJxBiq4MBqbu6qW3FJg3Gg6fSHVZNcY6G/hSjzYgA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "FileLength": 2851, "LastWriteTime": "2025-05-27T22:01:13.8933018+00:00"}, "VlFbFPVqh3FjeLMqT+yXGyd40tHgHStupF/eiPnvTxo=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\gdew6z5q30-ob3ozmrjnu.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml#[.{fingerprint=ob3ozmrjnu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "seg3gdhqeu", "Integrity": "1GkPQrZIRAsULitxmnn4zXS1sFj/OSqV1nMuc5+xFF8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.wasm", "FileLength": 4235, "LastWriteTime": "2025-05-27T22:01:13.8943008+00:00"}, "a0amJVyGpRhiOe9VwdbvxHli6Srzm2ILixn29M17w/A=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\amr1csf94v-n8l4zjq3i2.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System#[.{fingerprint=n8l4zjq3i2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0fk7qlkmr0", "Integrity": "HPFwWMXgZop84xvtGEdxS4Nmgl7RNcYuNa3cVnu+bZQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.wasm", "FileLength": 11848, "LastWriteTime": "2025-05-27T22:01:13.8953003+00:00"}, "wOQ5j6CaSz9LPTmnExaq0qq5dDGVfAKeo8pXWwhHaRM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\rl7nx3mh6j-pe1q66fox7.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/WindowsBase#[.{fingerprint=pe1q66fox7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\WindowsBase.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fznkwt3t1j", "Integrity": "lSWhu5HQGCy0Fsy2AKw4IleG4AA1sJEnYE/JyJtk+o4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\WindowsBase.wasm", "FileLength": 2514, "LastWriteTime": "2025-05-27T22:01:13.8962999+00:00"}, "ZV580w1aWN95e+9IoWVXt+olpqFOhna8gAyzx3gjuDg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\fsud91o0do-ypkr78tz1c.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/mscorlib#[.{fingerprint=ypkr78tz1c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\mscorlib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w0i5d72psq", "Integrity": "gAyOAO+94sjkLNY68fN4jlxlalciNzJm+I6XYS0I7Bw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\mscorlib.wasm", "FileLength": 14885, "LastWriteTime": "2025-05-27T22:01:13.897299+00:00"}, "Kj98AHRPuXO1TPVOMp0wKKzLAK23ZMYENBUZwPXvNok=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\1ek94srepp-ifx8vojh9u.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/netstandard#[.{fingerprint=ifx8vojh9u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\netstandard.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qirbek1i0e", "Integrity": "Eo4bn2RUmuUigS0hb7JarDE3/IB9CSt6TIhSKtfnD8M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\netstandard.wasm", "FileLength": 26237, "LastWriteTime": "2025-05-27T22:01:13.8992978+00:00"}, "C2vn5SQC09PCtkWdhZJ92c7BCH4I5rPs3hSSUDiwce4=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\i5hwbdci2n-gcclii65ud.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.CoreLib#[.{fingerprint=gcclii65ud}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4fvdxma37a", "Integrity": "UBmSeXdN84hiqIFvJFY//9IleOM9H3ZjuDGYgNFoGSM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "FileLength": 1533173, "LastWriteTime": "2025-05-27T22:01:14.1780219+00:00"}, "H5YPfzDWQzhsZ6a6KLuBgLELuXukzW2QQ7FUwi5fCsY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\jx4r6k03c8-krzbht210g.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet#[.{fingerprint=krzbht210g}]?.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "287x1i8tsp", "Integrity": "LnTpohuyB7qCHmU/gJEz5dlO6IH623+II5JrBSlu8b4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js", "FileLength": 12781, "LastWriteTime": "2025-05-27T22:01:14.1910125+00:00"}, "wum/kSGj46RBCpBZUS52soqC/geeddP+zOwM6DGk7XM=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\eorv2lffmw-f3yjzao5wb.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js#[.{fingerprint=f3yjzao5wb}]?.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ld7tbigvw9", "Integrity": "ZwIDTLCxA+aYBlfUhYxxExurATFmX1TeHYZtp1CM1Mw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js.map", "FileLength": 21303, "LastWriteTime": "2025-05-27T22:01:14.2639682+00:00"}, "7Ba0dDOdLHYsCbt2PlB8ruXwwOKEkQ1p3yZk2ANPdLY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\xu7kqnh2at-uhsveqo9bs.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native#[.{fingerprint=uhsveqo9bs}]!.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g3tuo91ndp", "Integrity": "zJqkY/VaNC9384QQYfnWJavcdCmHHGZYYa4dsGuL1X0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.js", "FileLength": 35022, "LastWriteTime": "2025-05-27T22:01:14.2899527+00:00"}, "a+mEYLp4O6yneGkn+I1HGqBmWrNBdkeC2YOXa+iSjkA=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\g6mp0w2hpz-swgexbmoy7.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native#[.{fingerprint=swgexbmoy7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jha0st9jt2", "Integrity": "F7UN4/a/oyI8Ws0Eh+VySD4xt1Mj5BoOd5Rz1srLy2U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.wasm", "FileLength": 1198975, "LastWriteTime": "2025-05-27T22:01:14.5887684+00:00"}, "Tcvcf0O/htgIYD7fVm5+pbjkuIkJ4Z8YKas3oi6o5fY=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\mrb8l7uvur-5nhp1wfg9b.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime#[.{fingerprint=5nhp1wfg9b}]!.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "temgmv0ap4", "Integrity": "i/ieKoSIsEfcjibSDSUao0clzABnZuwK6zycugy2KUQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js", "FileLength": 56235, "LastWriteTime": "2025-05-27T22:01:14.6107548+00:00"}, "m1++IIEbW+5YgtY8BJCCqr6oHSWTsW80JX9iiz42pfE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\di2ruzw1ep-sw8h8rjm4u.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js#[.{fingerprint=sw8h8rjm4u}]?.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bopwojh8cb", "Integrity": "+50SWGZnXo43nreZeh39dFiihsE6JvkFMoxdp7GRa3M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "FileLength": 88603, "LastWriteTime": "2025-05-27T22:01:13.9242821+00:00"}, "+sm/o2etL7+eRrBNd67IxAbN+0Ayv3X12lydYhBjzmg=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\4edmmpo23i-tjcz0u77k5.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_CJK#[.{fingerprint=tjcz0u77k5}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ffu5aujli6", "Integrity": "Sxt3k51yp5RyINpi7a/YQNnTWexafADiNQYBnBjQrYc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_CJK.dat", "FileLength": 359724, "LastWriteTime": "2025-05-27T22:01:14.0661961+00:00"}, "16b+rNdGaGxDvtrxRz0r6ZYO/9POEXK4womHOnN1xLs=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\tw0a21up03-tptq2av103.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_EFIGS#[.{fingerprint=tptq2av103}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kcp4n5gllp", "Integrity": "rLsEn/DgWXf7nRK2qegv2ARpYrcwixMOaXOoFcVmgjg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "FileLength": 220055, "LastWriteTime": "2025-05-27T22:01:14.0312166+00:00"}, "GQ8DOgXcRlvDK/wfLClUlN7MdIJkLQFxQa/4tE/jw2E=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\mw4a0tgiop-lfu7j35m59.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_no_CJK#[.{fingerprint=lfu7j35m59}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m3twp0zama", "Integrity": "UsST+ZWYFgogrz0pZB/4gGQWboPgRkurfdpi/0/ENCA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "FileLength": 347023, "LastWriteTime": "2025-05-27T22:01:14.0810805+00:00"}, "3f5B1AqjV2+GbEafVR97GjsAXQCHxB2WKIGK0cLSGHw=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\zqs1eycw5m-9fbxyyp30j.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Schools.Client#[.{fingerprint=9fbxyyp30j}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Schools.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oudh9wbvx5", "Integrity": "oAYUhyMy4NGqqdl51NdfoCCEshU05WhfWuK9XGbh4uY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Schools.Client.wasm", "FileLength": 497817, "LastWriteTime": "2025-05-27T22:40:45.8285552+00:00"}, "YU61nVzxiYgSnMSZLfVA0oSCkDD8PKs1pam6CWdfPJQ=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\17ntqbcxlo-opm2avp44a.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Schools.Client#[.{fingerprint=opm2avp44a}]!.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Schools.Client.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k5tweubq7k", "Integrity": "mJEM0V+sSRd1GtNsbZzemEW9wCdN3HkrTTO33d5MOyE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Schools.Client.pdb", "FileLength": 558745, "LastWriteTime": "2025-05-27T22:40:45.9175012+00:00"}, "XuVTYl1SsmgKoiSg1mLAN2qVPPrhRtl3D6+My+1TAIU=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\vvamxjootg-ejl1ytm3t6.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.boot.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.boot.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7nidwuxlmr", "Integrity": "o0ebKLQ1cngFXkw1yDEuOyjaMJ3THIDxKEB7mINETn8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.boot.json", "FileLength": 13959, "LastWriteTime": "2025-05-27T22:42:33.4581737+00:00"}, "3WQ1qVRv6p+RKUtKBeLsjTwPJL6/gsoKbGjyYp5Fg/8=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\dgzuuj2y6q-2q2y675jka.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Schools.Shared#[.{fingerprint=2q2y675jka}]!.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Schools.Shared.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v01btd7ew3", "Integrity": "EaXJsMbqapzN6pRlrzCyO+9uUNligWosubTx/N0/e60=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Schools.Shared.pdb", "FileLength": 41797, "LastWriteTime": "2025-05-27T22:16:29.4279447+00:00"}, "EVrMefIrxMDE7wdDPMpw1nrHNAhDY/u07WeuAUnuiyE=": {"Identity": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\qqy0pogpxg-re6xacl91d.gz", "SourceId": "Schools.Client", "SourceType": "Computed", "ContentRoot": "D:\\Shools App\\Schools4\\Schools.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Schools.Shared#[.{fingerprint=re6xacl91d}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Schools.Shared.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vca238hlzs", "Integrity": "7/NBxWIZdSZuF629A/2uohV0C0zT4+qSXt+c0ujFXRY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Shools App\\Schools4\\Schools.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Schools.Shared.wasm", "FileLength": 105454, "LastWriteTime": "2025-05-27T22:16:29.4239456+00:00"}}, "CachedCopyCandidates": {}}