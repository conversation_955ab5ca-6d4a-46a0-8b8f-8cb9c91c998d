@page "/accounting/bulk-payment"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Accountant,Admin")]

<PageTitle>التحصيل الجماعي - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-users text-success me-2"></i>
                        التحصيل الجماعي
                    </h2>
                    <p class="text-muted mb-0">تحصيل رسوم من طلاب متعددين في عملية واحدة</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-outline-secondary" @onclick="GoBack">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل بيانات الطلاب...</p>
        </div>
    }
    else
    {
        <!-- Summary -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            ملخص العملية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <h5 class="text-primary mb-1">@selectedStudents.Count</h5>
                                    <small class="text-muted">عدد الطلاب</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <h5 class="text-success mb-1">@GetTotalAmount().ToString("C")</h5>
                                    <small class="text-muted">إجمالي المبلغ</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <h5 class="text-info mb-1">@GetAverageAmount().ToString("C")</h5>
                                    <small class="text-muted">متوسط المبلغ</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <h5 class="text-warning mb-1">@GetTotalOutstanding().ToString("C")</h5>
                                    <small class="text-muted">إجمالي المستحق</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Form -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-credit-card me-2 text-success"></i>
                            بيانات الدفع الجماعي
                        </h5>
                    </div>
                    <div class="card-body">
                        <EditForm Model="bulkPayment" OnValidSubmit="ProcessBulkPayment">
                            <DataAnnotationsValidator />

                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">تاريخ الدفع <span class="text-danger">*</span></label>
                                    <InputDate @bind-Value="bulkPayment.PaymentDate" class="form-control" />
                                    <ValidationMessage For="() => bulkPayment.PaymentDate" />
                                </div>

                                <div class="col-md-4">
                                    <label class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                                    <InputSelect @bind-Value="bulkPayment.PaymentMethod" class="form-select">
                                        <option value="">اختر طريقة الدفع</option>
                                        <option value="Cash">نقدي</option>
                                        <option value="Bank">تحويل بنكي</option>
                                        <option value="Check">شيك</option>
                                        <option value="Card">بطاقة ائتمان</option>
                                    </InputSelect>
                                    <ValidationMessage For="() => bulkPayment.PaymentMethod" />
                                </div>

                                <div class="col-md-4">
                                    <label class="form-label">المرجع</label>
                                    <InputText @bind-Value="bulkPayment.Reference" class="form-control" placeholder="رقم المرجع أو الدفعة" />
                                </div>

                                <div class="col-12">
                                    <label class="form-label">ملاحظات</label>
                                    <InputTextArea @bind-Value="bulkPayment.Notes" class="form-control" rows="3" placeholder="ملاحظات عامة للدفعة الجماعية..." />
                                </div>
                            </div>
                        </EditForm>
                    </div>
                </div>
            </div>
        </div>

        <!-- Students List -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2 text-primary"></i>
                                قائمة الطلاب المحددين
                            </h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" @onclick="SelectAllAmounts">
                                    <i class="fas fa-check-double me-1"></i>
                                    تحديد الكل
                                </button>
                                <button class="btn btn-outline-secondary" @onclick="ClearAllAmounts">
                                    <i class="fas fa-times me-1"></i>
                                    إلغاء الكل
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (selectedStudents?.Any() == true)
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الطالب</th>
                                            <th>الصف/الفصل</th>
                                            <th>إجمالي الرسوم</th>
                                            <th>المدفوع</th>
                                            <th>المتبقي</th>
                                            <th>مبلغ الدفعة</th>
                                            <th>حالة الدفع</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var student in selectedStudents)
                                        {
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                            @student.StudentName.Substring(0, 1)
                                                        </div>
                                                        <div>
                                                            <strong>@student.StudentName</strong>
                                                            <br>
                                                            <small class="text-muted">@student.StudentNumber</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info">@student.GradeName</span>
                                                    <br>
                                                    <small class="text-muted">@student.ClassName</small>
                                                </td>
                                                <td>
                                                    <strong class="text-primary">@student.TotalFees.ToString("C")</strong>
                                                </td>
                                                <td>
                                                    <strong class="text-success">@student.PaidAmount.ToString("C")</strong>
                                                </td>
                                                <td>
                                                    <strong class="text-danger">@student.OutstandingAmount.ToString("C")</strong>
                                                </td>
                                                <td>
                                                    <div class="input-group input-group-sm">
                                                        <span class="input-group-text">ر.س</span>
                                                        <input type="number" class="form-control"
                                                               @bind="studentPayments[student.StudentId]"
                                                               max="@student.OutstandingAmount"
                                                               min="0" step="0.01" />
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge @GetPaymentStatusBadge(student.PaymentStatus)">
                                                        @GetPaymentStatusText(student.PaymentStatus)
                                                    </span>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                    <tfoot class="table-secondary">
                                        <tr>
                                            <th colspan="5">الإجمالي</th>
                                            <th>
                                                <strong class="text-success">@GetTotalPaymentAmount().ToString("C")</strong>
                                            </th>
                                            <th></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>

                            <!-- Action Buttons -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="d-flex justify-content-end gap-2">
                                        <button type="button" class="btn btn-outline-secondary" @onclick="GoBack">
                                            <i class="fas fa-times me-2"></i>
                                            إلغاء
                                        </button>
                                        <button type="button" class="btn btn-success" @onclick="ProcessBulkPayment" disabled="@isSaving">
                                            @if (isSaving)
                                            {
                                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                            }
                                            else
                                            {
                                                <i class="fas fa-save me-2"></i>
                                            }
                                            تسجيل الدفعات (@GetValidPaymentsCount())
                                        </button>
                                    </div>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا يوجد طلاب محددين</h5>
                                <p class="text-muted">يرجى العودة وتحديد الطلاب المراد تحصيل الرسوم منهم</p>
                                <button class="btn btn-primary" @onclick="GoBack">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    العودة لتحديد الطلاب
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] [SupplyParameterFromQuery] public string? Students { get; set; }

    private List<StudentFeeDto> selectedStudents = new();
    private Dictionary<int, decimal> studentPayments = new();

    private BulkPaymentDto bulkPayment = new()
    {
        PaymentDate = DateTime.Now
    };

    private bool isLoading = true;
    private bool isSaving = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadSelectedStudents();
    }

    private async Task LoadSelectedStudents()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            if (string.IsNullOrEmpty(Students))
            {
                await JSRuntime.InvokeVoidAsync("alert", "لم يتم تحديد أي طلاب");
                Navigation.NavigateTo("/accounting/fees");
                return;
            }

            var studentIds = Students.Split(',').Select(int.Parse).ToList();
            var allStudentFees = await ApiService.GetStudentFeesAsync() ?? new List<StudentFeeDto>();

            selectedStudents = allStudentFees.Where(s => studentIds.Contains(s.StudentId)).ToList();

            // Initialize payment amounts with outstanding amounts
            foreach (var student in selectedStudents)
            {
                studentPayments[student.StudentId] = student.OutstandingAmount;
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل بيانات الطلاب: {ex.Message}");
            Navigation.NavigateTo("/accounting/fees");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task ProcessBulkPayment()
    {
        if (!ValidateBulkPayment())
            return;

        try
        {
            isSaving = true;
            StateHasChanged();

            var validPayments = GetValidPayments();
            if (!validPayments.Any())
            {
                await JSRuntime.InvokeVoidAsync("alert", "لا توجد دفعات صالحة للمعالجة");
                return;
            }

            var successCount = 0;
            foreach (var payment in validPayments)
            {
                var success = await ApiService.RecordStudentPaymentAsync(payment);
                if (success) successCount++;
            }

            if (successCount > 0)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"تم تسجيل {successCount} دفعة بنجاح من أصل {validPayments.Count}");
                Navigation.NavigateTo("/accounting/fees");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "فشل في تسجيل الدفعات");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في معالجة الدفعات: {ex.Message}");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private bool ValidateBulkPayment()
    {
        if (string.IsNullOrWhiteSpace(bulkPayment.PaymentMethod))
        {
            JSRuntime.InvokeVoidAsync("alert", "يرجى اختيار طريقة الدفع");
            return false;
        }

        var validPayments = GetValidPayments();
        if (!validPayments.Any())
        {
            JSRuntime.InvokeVoidAsync("alert", "يرجى إدخال مبالغ صالحة للدفع");
            return false;
        }

        return true;
    }

    private List<StudentPaymentDto> GetValidPayments()
    {
        var payments = new List<StudentPaymentDto>();

        foreach (var student in selectedStudents)
        {
            if (studentPayments.TryGetValue(student.StudentId, out var amount) && amount > 0)
            {
                payments.Add(new StudentPaymentDto
                {
                    StudentId = student.StudentId,
                    Amount = amount,
                    PaymentMethod = bulkPayment.PaymentMethod,
                    Reference = bulkPayment.Reference ?? "",
                    Notes = $"{bulkPayment.Notes} - دفعة جماعية",
                    PaymentDate = bulkPayment.PaymentDate,
                    FeeItemIds = new List<int>()
                });
            }
        }

        return payments;
    }

    private void SelectAllAmounts()
    {
        foreach (var student in selectedStudents)
        {
            studentPayments[student.StudentId] = student.OutstandingAmount;
        }
        StateHasChanged();
    }

    private void ClearAllAmounts()
    {
        foreach (var student in selectedStudents)
        {
            studentPayments[student.StudentId] = 0;
        }
        StateHasChanged();
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/accounting/fees");
    }

    private decimal GetTotalAmount()
    {
        return selectedStudents.Sum(s => s.TotalFees);
    }

    private decimal GetAverageAmount()
    {
        return selectedStudents.Any() ? selectedStudents.Average(s => s.TotalFees) : 0;
    }

    private decimal GetTotalOutstanding()
    {
        return selectedStudents.Sum(s => s.OutstandingAmount);
    }

    private decimal GetTotalPaymentAmount()
    {
        return studentPayments.Values.Sum();
    }

    private int GetValidPaymentsCount()
    {
        return studentPayments.Values.Count(amount => amount > 0);
    }

    private string GetPaymentStatusBadge(string status)
    {
        return status switch
        {
            "paid" => "bg-success",
            "partial" => "bg-warning",
            "unpaid" => "bg-secondary",
            "overdue" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetPaymentStatusText(string status)
    {
        return status switch
        {
            "paid" => "مدفوع بالكامل",
            "partial" => "مدفوع جزئياً",
            "unpaid" => "غير مدفوع",
            "overdue" => "متأخر",
            _ => "غير محدد"
        };
    }

    public class BulkPaymentDto
    {
        public DateTime PaymentDate { get; set; } = DateTime.Now;
        public string PaymentMethod { get; set; } = string.Empty;
        public string? Reference { get; set; }
        public string? Notes { get; set; }
    }
}
