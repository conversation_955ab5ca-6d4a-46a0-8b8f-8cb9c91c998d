using Schools.Shared.DTOs;

namespace Schools.Client.Services
{
    public interface IApiService
    {
        // Academic Years
        Task<IEnumerable<AcademicYearDto>> GetAcademicYearsAsync();
        Task<AcademicYearDto?> CreateAcademicYearAsync(CreateAcademicYearDto academicYear);
        Task<bool> UpdateAcademicYearAsync(int id, CreateAcademicYearDto academicYear);
        Task<bool> DeleteAcademicYearAsync(int id);
        Task<bool> ActivateAcademicYearAsync(int id);

        // Grade Levels (Academic Grades)
        Task<IEnumerable<AcademicGradeDto>> GetGradesAsync();
        Task<IEnumerable<AcademicGradeDto>> GetAcademicGradesAsync();
        Task<AcademicGradeDto?> CreateGradeAsync(CreateAcademicGradeDto grade);
        Task<bool> UpdateGradeAsync(int id, CreateAcademicGradeDto grade);
        Task<bool> DeleteGradeAsync(int id);

        // Classes
        Task<IEnumerable<ClassDto>> GetClassesAsync();
        Task<ClassDto?> GetClassAsync(int id);
        Task<ClassDto?> CreateClassAsync(CreateClassDto classDto);
        Task<bool> UpdateClassAsync(int id, CreateClassDto classDto);
        Task<bool> DeleteClassAsync(int id);
        Task<IEnumerable<UserDto>> GetClassStudentsAsync(int classId);

        // Sections
        Task<IEnumerable<SectionDto>> GetSectionsAsync(int classId);
        Task<SectionDto?> CreateSectionAsync(int classId, CreateSectionDto section);
        Task<bool> UpdateSectionAsync(int id, CreateSectionDto section);
        Task<bool> DeleteSectionAsync(int id);

        // Subjects
        Task<IEnumerable<SubjectDto>> GetSubjectsAsync();
        Task<SubjectDto?> GetSubjectAsync(int id);
        Task<SubjectDto?> CreateSubjectAsync(CreateSubjectDto subject);
        Task<bool> UpdateSubjectAsync(int id, CreateSubjectDto subject);
        Task<bool> DeleteSubjectAsync(int id);

        // User Management
        Task<IEnumerable<UserDto>> GetUsersAsync();
        Task<IEnumerable<UserDto>> GetPendingUsersAsync();
        Task<UserDto?> GetUserAsync(string userId);
        Task<bool> ApproveUserAsync(string userId);
        Task<bool> RejectUserAsync(string userId);
        Task<bool> ActivateUserAsync(string userId);
        Task<bool> DeactivateUserAsync(string userId);
        Task<bool> DeleteUserAsync(string userId);

        // Teachers
        Task<IEnumerable<UserDto>> GetTeachersAsync();

        // Activities
        Task<IEnumerable<ActivityDto>> GetActivitiesAsync(string? type = null, string? status = null, DateTime? date = null, string? search = null, int page = 1, int pageSize = 20);
        Task<ActivityDto?> GetActivityAsync(int id);
        Task<ActivityDto?> CreateActivityAsync(CreateActivityDto activity);
        Task<bool> UpdateActivityAsync(int id, UpdateActivityDto activity);
        Task<bool> DeleteActivityAsync(int id);
        Task<bool> RegisterForActivityAsync(int activityId, string studentId);
        Task<ActivityStatisticsDto?> GetActivityStatisticsAsync();

        // Library
        Task<IEnumerable<BookDto>> GetBooksAsync(string? category = null, string? status = null, string? grade = null, string? search = null, int page = 1, int pageSize = 20);
        Task<BookDto?> GetBookAsync(int id);
        Task<BookDto?> CreateBookAsync(CreateBookDto book);
        Task<bool> UpdateBookAsync(int id, UpdateBookDto book);
        Task<bool> DeleteBookAsync(int id);
        Task<bool> BorrowBookAsync(int bookId, string studentId);
        Task<bool> ReturnBookAsync(int bookId);
        Task<LibraryStatisticsDto?> GetLibraryStatisticsAsync();

        // Attendance
        Task<IEnumerable<AttendanceDto>> GetAttendanceAsync(int? classId = null, DateTime? date = null, string? studentId = null, int page = 1, int pageSize = 20);
        Task<AttendanceDto?> CreateAttendanceAsync(CreateAttendanceDto attendance);
        Task<bool> UpdateAttendanceAsync(int id, UpdateAttendanceDto attendance);
        Task<bool> CreateBulkAttendanceAsync(BulkAttendanceDto bulkAttendance);
        Task<AttendanceStatisticsDto?> GetAttendanceStatisticsAsync(int? classId = null, DateTime? startDate = null, DateTime? endDate = null);
        Task<IEnumerable<AttendanceDto>> GetDailyAttendanceAsync(DateTime date);

        // Schedules
        Task<IEnumerable<ScheduleDto>> GetSchedulesAsync(int? classId = null, DateTime? date = null, string? teacherId = null);
        Task<ScheduleDto?> CreateScheduleAsync(CreateScheduleDto schedule);
        Task<bool> UpdateScheduleAsync(int id, UpdateScheduleDto schedule);
        Task<bool> DeleteScheduleAsync(int id);
        Task<WeeklyScheduleDto?> GetWeeklyScheduleAsync(int classId, DateTime startDate);

        // Student Grades Management
        Task<IEnumerable<GradeDto>> GetStudentGradesAsync(string? studentId = null, int? subjectId = null, string? examType = null, string? semester = null, int? academicYearId = null, int page = 1, int pageSize = 50);
        Task<GradeDto?> CreateStudentGradeAsync(CreateGradeDto grade);
        Task<bool> UpdateStudentGradeAsync(int id, UpdateGradeDto grade);
        Task<bool> DeleteStudentGradeAsync(int id);
        Task<bool> CreateBulkGradesAsync(BulkGradeDto bulkGrades);
        Task<GradeStatisticsDto?> GetGradeStatisticsAsync(int? subjectId = null, string? examType = null, string? semester = null, int? academicYearId = null);
        Task<StudentGradeSummaryDto?> GetStudentGradeSummaryAsync(string studentId, string? semester = null, int? academicYearId = null);

        // Parents Management
        Task<List<ParentDto>> GetParentsAsync(Dictionary<string, string?>? queryParams = null);
        Task<ParentDto?> GetParentAsync(string parentId);
        Task<ParentDto?> CreateParentAsync(CreateParentDto parent);
        Task<bool> UpdateParentAsync(string parentId, UpdateParentDto parent);
        Task<bool> DeleteParentAsync(string parentId);
        Task<List<ParentChildDto>> GetParentChildrenAsync(string parentId);
        Task<ParentDashboardDto?> GetParentDashboardAsync(string parentId);
        Task<bool> LinkParentStudentAsync(LinkParentStudentDto linkDto);
        Task<bool> UnlinkParentStudentAsync(string parentId, string studentId);
        Task<List<StudentSummaryDto>> GetAvailableStudentsForParentLinkAsync();
        Task<dynamic> GetParentsStatisticsAsync();
        Task<string?> GetCurrentUserIdAsync();

        // Electronic Exams
        Task<List<ExamDto>> GetExamsAsync(Dictionary<string, string?>? queryParams = null);
        Task<ExamDto?> GetExamAsync(int examId);
        Task<ExamDto?> CreateExamAsync(CreateExamDto exam);
        Task<bool> UpdateExamAsync(int examId, UpdateExamDto exam);
        Task<bool> DeleteExamAsync(int examId);
        Task<dynamic> GetExamStatisticsAsync();

        // Student Exam Methods
        Task<StudentExamDashboardDto?> GetStudentExamDashboardAsync();
        Task<dynamic> GetStudentExamStatusAsync(int examId);
        Task<ExamAttemptDto?> StartExamAsync(StartExamDto startDto);
        Task<ExamAttemptDto?> GetCurrentExamAttemptAsync(int examId);
        Task<bool> SaveExamAnswerAsync(SubmitAnswerDto answer);
        Task<ExamResultDto?> SubmitExamAsync(SubmitExamDto submitDto);
        Task<ExamResultDto?> GetExamResultAsync(int attemptId);

        // Exam Questions Management
        Task<ExamQuestionDto?> CreateExamQuestionAsync(CreateExamQuestionDto question);
        Task<bool> UpdateExamQuestionAsync(int questionId, CreateExamQuestionDto question);
        Task<bool> DeleteExamQuestionAsync(int questionId);

        // Exam Results Management
        Task<List<ExamResultDto>?> GetExamResultsAsync(int examId);
        Task<ExamStatisticsDto?> GetExamStatisticsDetailedAsync(int examId);

        // Exam Publishing
        Task<bool> PublishExamAsync(int examId);

        // Student Performance Analysis
        Task<List<StudentPerformanceDto>?> GetStudentPerformanceAsync(Dictionary<string, string?> queryParams);
        Task<List<PerformanceTrendDto>?> GetPerformanceTrendAsync(string period);

        // Exam Settings
        Task<ExamSettingsDto?> GetExamSettingsAsync();
        Task<bool> UpdateExamSettingsAsync(ExamSettingsDto settings);

        // Question Bank Management
        Task<List<QuestionBankDto>?> GetQuestionBankAsync();
        Task<bool> DuplicateQuestionAsync(int questionId);
        Task<bool> DeleteQuestionFromBankAsync(int questionId);

        // Exam Reports
        Task<ExamReportSummaryDto?> GetExamReportSummaryAsync(Dictionary<string, string?> filters);
        Task<List<DetailedExamReportDto>?> GetDetailedExamReportsAsync(Dictionary<string, string?> filters);
        Task<List<ComparativeDataDto>?> GetComparativeDataAsync(Dictionary<string, string?> filters);
        Task<AdvancedStatisticsDto?> GetAdvancedStatisticsAsync(Dictionary<string, string?> filters);

        // Accounting System
        Task<AccountingDashboardDto?> GetAccountingDashboardAsync();
        Task<List<ReceiptVoucherDto>?> GetReceiptVouchersAsync();
        Task<bool> CreateReceiptVoucherAsync(ReceiptVoucherDto voucher);
        Task<bool> ApproveReceiptVoucherAsync(int id);
        Task<bool> PostReceiptVoucherAsync(int id);
        Task<bool> DeleteReceiptVoucherAsync(int id);
        Task<List<PaymentVoucherDto>?> GetPaymentVouchersAsync();
        Task<bool> ApprovePaymentVoucherAsync(int id);
        Task<bool> PostPaymentVoucherAsync(int id);
        Task<bool> DeletePaymentVoucherAsync(int id);
        Task<List<JournalEntryDto>?> GetJournalEntriesAsync();
        Task<bool> PostJournalEntryAsync(int id);
        Task<bool> DeleteJournalEntryAsync(int id);
        Task<List<AccountDto>?> GetAccountsAsync();
        Task<bool> DeleteAccountAsync(int id);
        Task<AccountStatementDto?> GetAccountStatementAsync(int accountId, DateTime fromDate, DateTime toDate);
        Task<TrialBalanceDto?> GetTrialBalanceAsync(DateTime asOfDate);
        Task<IncomeStatementDto?> GetIncomeStatementAsync(DateTime fromDate, DateTime toDate);
        Task<BalanceSheetDto?> GetBalanceSheetAsync(DateTime asOfDate);

        // Student Fees
        Task<List<StudentFeeDto>?> GetStudentFeesAsync();
        Task<StudentFeeDto?> GetStudentFeeAsync(int studentId);
        Task<bool> RecordStudentPaymentAsync(StudentPaymentDto payment);
        Task<bool> SendPaymentReminderAsync(int studentId);
        Task<bool> SendBulkPaymentRemindersAsync(List<int> studentIds);
        Task<List<FeeStructureDto>?> GetFeeStructuresAsync();
        Task<bool> CreateFeeStructureAsync(FeeStructureDto feeStructure);

        // Settings
        Task<object?> GetAccountingSettingsAsync();
        Task<bool> SaveAccountingSettingsAsync(object settings);
        Task<object?> GetSystemInfoAsync();
    }
}
