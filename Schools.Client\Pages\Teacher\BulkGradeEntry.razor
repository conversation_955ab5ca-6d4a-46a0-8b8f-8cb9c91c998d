@page "/teacher/bulk-grades"
@using Schools.Shared.DTOs
@using Schools.Client.Services
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        إدخال الدرجات المتعددة
                    </h4>
                </div>

                <div class="card-body">
                    @if (!showStudentTable)
                    {
                        <!-- Setup Form -->
                        <div class="row">
                            <div class="col-md-8 mx-auto">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">إعداد الامتحان</h5>
                                    </div>
                                    <div class="card-body">
                                        <EditForm Model="bulkGradeModel" OnValidSubmit="LoadStudents">
                                            <DataAnnotationsValidator />
                                            <ValidationSummary class="text-danger" />

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">المادة *</label>
                                                        <InputSelect class="form-select" @bind-Value="bulkGradeModel.SubjectId">
                                                            <option value="">اختر المادة</option>
                                                            @if (subjects != null)
                                                            {
                                                                @foreach (var subject in subjects)
                                                                {
                                                                    <option value="@subject.Id">@subject.Name</option>
                                                                }
                                                            }
                                                        </InputSelect>
                                                        <ValidationMessage For="@(() => bulkGradeModel.SubjectId)" />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">السنة الأكاديمية *</label>
                                                        <InputSelect class="form-select" @bind-Value="bulkGradeModel.AcademicYearId">
                                                            <option value="">اختر السنة</option>
                                                            @if (academicYears != null)
                                                            {
                                                                @foreach (var year in academicYears)
                                                                {
                                                                    <option value="@year.Id">@year.Name</option>
                                                                }
                                                            }
                                                        </InputSelect>
                                                        <ValidationMessage For="@(() => bulkGradeModel.AcademicYearId)" />
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">نوع الامتحان *</label>
                                                        <InputSelect class="form-select" @bind-Value="bulkGradeModel.ExamType">
                                                            <option value="">اختر النوع</option>
                                                            <option value="Quiz">اختبار قصير</option>
                                                            <option value="Midterm">امتحان نصفي</option>
                                                            <option value="Final">امتحان نهائي</option>
                                                            <option value="Assignment">واجب</option>
                                                        </InputSelect>
                                                        <ValidationMessage For="@(() => bulkGradeModel.ExamType)" />
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">الدرجة الكاملة *</label>
                                                        <InputNumber class="form-control" @bind-Value="bulkGradeModel.MaxScore" />
                                                        <ValidationMessage For="@(() => bulkGradeModel.MaxScore)" />
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">التاريخ *</label>
                                                        <InputDate class="form-control" @bind-Value="bulkGradeModel.Date" />
                                                        <ValidationMessage For="@(() => bulkGradeModel.Date)" />
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">الفصل الدراسي *</label>
                                                        <InputSelect class="form-select" @bind-Value="bulkGradeModel.Semester">
                                                            <option value="">اختر الفصل</option>
                                                            <option value="First">الفصل الأول</option>
                                                            <option value="Second">الفصل الثاني</option>
                                                        </InputSelect>
                                                        <ValidationMessage For="@(() => bulkGradeModel.Semester)" />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">الصف</label>
                                                        <select class="form-select" @bind="selectedClassId">
                                                            <option value="">جميع الطلاب</option>
                                                            @if (classes != null)
                                                            {
                                                                @foreach (var cls in classes)
                                                                {
                                                                    <option value="@cls.Id">@cls.Name</option>
                                                                }
                                                            }
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="text-center">
                                                <button type="submit" class="btn btn-success btn-lg" disabled="@isLoading">
                                                    @if (isLoading)
                                                    {
                                                        <span class="spinner-border spinner-border-sm me-2"></span>
                                                    }
                                                    <i class="fas fa-users me-2"></i>
                                                    تحميل الطلاب
                                                </button>
                                            </div>
                                        </EditForm>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <!-- Students Grade Entry Table -->
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div>
                                <h5 class="mb-1">
                                    <span class="badge bg-info me-2">@selectedSubjectName</span>
                                    <span class="badge bg-secondary me-2">@GetExamTypeText(bulkGradeModel.ExamType)</span>
                                    <span class="badge bg-warning">الدرجة الكاملة: @bulkGradeModel.MaxScore</span>
                                </h5>
                                <small class="text-muted">
                                    @bulkGradeModel.Date.ToString("yyyy-MM-dd") - @GetSemesterText(bulkGradeModel.Semester)
                                </small>
                            </div>
                            <div>
                                <button class="btn btn-outline-secondary me-2" @onclick="ResetForm">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    العودة للإعداد
                                </button>
                                <button class="btn btn-success" @onclick="SaveAllGrades" disabled="@isSaving">
                                    @if (isSaving)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2"></span>
                                    }
                                    <i class="fas fa-save me-2"></i>
                                    حفظ جميع الدرجات
                                </button>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">درجة موحدة</span>
                                    <input type="number" class="form-control" @bind="uniformScore" placeholder="أدخل الدرجة" />
                                    <button class="btn btn-outline-primary" @onclick="ApplyUniformScore">
                                        <i class="fas fa-copy me-2"></i>
                                        تطبيق على الجميع
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">البحث</span>
                                    <input type="text" class="form-control" @bind="searchTerm" @oninput="FilterStudents" placeholder="ابحث عن طالب..." />
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Students Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 5%">#</th>
                                        <th style="width: 30%">اسم الطالب</th>
                                        <th style="width: 20%">البريد الإلكتروني</th>
                                        <th style="width: 15%">الدرجة</th>
                                        <th style="width: 15%">النسبة المئوية</th>
                                        <th style="width: 15%">ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (filteredStudents?.Any() == true)
                                    {
                                        @for (int i = 0; i < filteredStudents.Count; i++)
                                        {
                                            var index = i;
                                            var student = filteredStudents[index];
                                            var studentGrade = bulkGradeModel.StudentGrades.FirstOrDefault(sg => sg.StudentId == student.Id);
                                            if (studentGrade == null)
                                            {
                                                studentGrade = new StudentGradeInput { StudentId = student.Id };
                                                bulkGradeModel.StudentGrades.Add(studentGrade);
                                            }

                                            <tr class="@(studentGrade.Score > 0 ? "table-success" : "")">
                                                <td>@(index + 1)</td>
                                                <td>
                                                    <div>
                                                        <strong>@student.FirstName @student.LastName</strong>
                                                        <br>
                                                        <small class="text-muted">@student.PhoneNumber</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <small>@student.Email</small>
                                                </td>
                                                <td>
                                                    <div class="input-group input-group-sm">
                                                        <input type="number"
                                                               class="form-control"
                                                               @bind="studentGrade.Score"
                                                               @oninput="@((e) => UpdateScore(studentGrade, e))"
                                                               min="0"
                                                               max="@bulkGradeModel.MaxScore"
                                                               step="0.5" />
                                                        <span class="input-group-text">/ @bulkGradeModel.MaxScore</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge @GetScoreClass(studentGrade.Score, bulkGradeModel.MaxScore)">
                                                        @GetPercentage(studentGrade.Score, bulkGradeModel.MaxScore).ToString("F1")%
                                                    </span>
                                                </td>
                                                <td>
                                                    <input type="text"
                                                           class="form-control form-control-sm"
                                                           @bind="studentGrade.Notes"
                                                           placeholder="ملاحظات..." />
                                                </td>
                                            </tr>
                                        }
                                    }
                                    else
                                    {
                                        <tr>
                                            <td colspan="6" class="text-center py-4">
                                                <i class="fas fa-users fa-2x text-muted mb-2"></i>
                                                <p class="text-muted">لا يوجد طلاب</p>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Summary -->
                        @if (bulkGradeModel.StudentGrades?.Any() == true)
                        {
                            <div class="row mt-4">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h5>@GetCompletedCount()</h5>
                                            <p class="mb-0">تم إدخال الدرجة</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h5>@GetAverageScore().ToString("F1")</h5>
                                            <p class="mb-0">المتوسط</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h5>@GetPassCount()</h5>
                                            <p class="mb-0">ناجح</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h5>@GetFailCount()</h5>
                                            <p class="mb-0">راسب</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private List<UserDto>? students;
    private List<UserDto>? filteredStudents;
    private List<SubjectDto>? subjects;
    private List<AcademicYearDto>? academicYears;
    private List<ClassDto>? classes;

    private BulkGradeDto bulkGradeModel = new();
    private bool showStudentTable = false;
    private bool isLoading = false;
    private bool isSaving = false;

    private string selectedSubjectName = "";
    private int? selectedClassId;
    private string searchTerm = "";
    private decimal uniformScore = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadInitialData();
        bulkGradeModel.Date = DateTime.Today;
    }

    private async Task LoadInitialData()
    {
        try
        {
            var subjectsTask = ApiService.GetSubjectsAsync();
            var academicYearsTask = ApiService.GetAcademicYearsAsync();
            var classesTask = ApiService.GetClassesAsync();

            await Task.WhenAll(subjectsTask, academicYearsTask, classesTask);

            subjects = (await subjectsTask).ToList();
            academicYears = (await academicYearsTask).ToList();
            classes = (await classesTask).ToList();

            // Set default academic year
            var activeYear = academicYears.FirstOrDefault(y => y.IsActive);
            if (activeYear != null)
            {
                bulkGradeModel.AcademicYearId = activeYear.Id;
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
    }

    private async Task LoadStudents()
    {
        try
        {
            isLoading = true;

            var allUsers = await ApiService.GetUsersAsync();
            students = allUsers.Where(u => u.Role == "Student").ToList();

            // Filter by class if selected
            if (selectedClassId.HasValue)
            {
                students = (await ApiService.GetClassStudentsAsync(selectedClassId.Value)).ToList();
            }

            filteredStudents = students.ToList();

            // Set subject name for display
            selectedSubjectName = subjects?.FirstOrDefault(s => s.Id == bulkGradeModel.SubjectId)?.Name ?? "";

            // Initialize student grades
            bulkGradeModel.StudentGrades = students.Select(s => new StudentGradeInput
            {
                StudentId = s.Id,
                Score = 0
            }).ToList();

            showStudentTable = true;
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الطلاب: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void FilterStudents()
    {
        if (students == null) return;

        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            filteredStudents = students.ToList();
        }
        else
        {
            filteredStudents = students.Where(s =>
                s.FirstName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                s.LastName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                s.Email.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                s.PhoneNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
            ).ToList();
        }

        StateHasChanged();
    }

    private void ApplyUniformScore()
    {
        if (uniformScore < 0 || uniformScore > bulkGradeModel.MaxScore)
        {
            JSRuntime.InvokeVoidAsync("alert", $"الدرجة يجب أن تكون بين 0 و {bulkGradeModel.MaxScore}");
            return;
        }

        foreach (var studentGrade in bulkGradeModel.StudentGrades)
        {
            studentGrade.Score = uniformScore;
        }

        StateHasChanged();
    }

    private void UpdateScore(StudentGradeInput studentGrade, ChangeEventArgs e)
    {
        if (decimal.TryParse(e.Value?.ToString(), out var score))
        {
            if (score < 0) score = 0;
            if (score > bulkGradeModel.MaxScore) score = bulkGradeModel.MaxScore;
            studentGrade.Score = score;
        }
        StateHasChanged();
    }

    private async Task SaveAllGrades()
    {
        try
        {
            isSaving = true;

            // Filter out students with no score entered
            var gradesToSave = bulkGradeModel.StudentGrades.Where(sg => sg.Score > 0).ToList();

            if (!gradesToSave.Any())
            {
                await JSRuntime.InvokeVoidAsync("alert", "لم يتم إدخال أي درجات");
                return;
            }

            var bulkGradeToSave = new BulkGradeDto
            {
                SubjectId = bulkGradeModel.SubjectId,
                AcademicYearId = bulkGradeModel.AcademicYearId,
                ExamType = bulkGradeModel.ExamType,
                MaxScore = bulkGradeModel.MaxScore,
                Date = bulkGradeModel.Date,
                Semester = bulkGradeModel.Semester,
                StudentGrades = gradesToSave
            };

            var success = await ApiService.CreateBulkGradesAsync(bulkGradeToSave);

            if (success)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"تم حفظ {gradesToSave.Count} درجة بنجاح");
                ResetForm();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ أثناء حفظ الدرجات");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ الدرجات: {ex.Message}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private void ResetForm()
    {
        showStudentTable = false;
        bulkGradeModel = new BulkGradeDto { Date = DateTime.Today };
        students = null;
        filteredStudents = null;
        selectedSubjectName = "";
        selectedClassId = null;
        searchTerm = "";
        uniformScore = 0;

        // Reset academic year
        var activeYear = academicYears?.FirstOrDefault(y => y.IsActive);
        if (activeYear != null)
        {
            bulkGradeModel.AcademicYearId = activeYear.Id;
        }

        StateHasChanged();
    }

    private decimal GetPercentage(decimal score, decimal maxScore)
    {
        return maxScore > 0 ? (score / maxScore) * 100 : 0;
    }

    private string GetScoreClass(decimal score, decimal maxScore)
    {
        var percentage = GetPercentage(score, maxScore);
        return percentage switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-info",
            >= 70 => "bg-warning",
            >= 60 => "bg-secondary",
            _ => "bg-danger"
        };
    }

    private int GetCompletedCount()
    {
        return bulkGradeModel.StudentGrades?.Count(sg => sg.Score > 0) ?? 0;
    }

    private decimal GetAverageScore()
    {
        var completedGrades = bulkGradeModel.StudentGrades?.Where(sg => sg.Score > 0).ToList();
        if (completedGrades?.Any() != true) return 0;

        var averageScore = completedGrades.Average(sg => sg.Score);
        return GetPercentage(averageScore, bulkGradeModel.MaxScore);
    }

    private int GetPassCount()
    {
        return bulkGradeModel.StudentGrades?.Count(sg =>
            sg.Score > 0 && GetPercentage(sg.Score, bulkGradeModel.MaxScore) >= 60) ?? 0;
    }

    private int GetFailCount()
    {
        return bulkGradeModel.StudentGrades?.Count(sg =>
            sg.Score > 0 && GetPercentage(sg.Score, bulkGradeModel.MaxScore) < 60) ?? 0;
    }

    private string GetExamTypeText(string examType)
    {
        return examType switch
        {
            "Quiz" => "اختبار قصير",
            "Midterm" => "امتحان نصفي",
            "Final" => "امتحان نهائي",
            "Assignment" => "واجب",
            _ => examType
        };
    }

    private string GetSemesterText(string semester)
    {
        return semester switch
        {
            "First" => "الفصل الأول",
            "Second" => "الفصل الثاني",
            _ => semester
        };
    }
}
