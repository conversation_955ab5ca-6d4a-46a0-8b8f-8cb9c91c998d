using System.ComponentModel.DataAnnotations;
using Schools.Shared.Models;

namespace Schools.Shared.DTOs
{
    // Authentication DTOs
    public class LoginDto
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        public string Password { get; set; } = string.Empty;

        public bool RememberMe { get; set; } = false;
    }

    public class RegisterDto
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        [StringLength(100, MinimumLength = 6)]
        public string Password { get; set; } = string.Empty;

        [Required]
        [Compare("Password")]
        public string ConfirmPassword { get; set; } = string.Empty;

        [Phone]
        public string? PhoneNumber { get; set; }

        [StringLength(20)]
        public string? NationalId { get; set; }

        public DateTime? DateOfBirth { get; set; }

        public string? Address { get; set; }

        [Required]
        public UserRole Role { get; set; }
    }

    public class AuthResponseDto
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? Token { get; set; }
        public UserDto? User { get; set; }
    }

    public class UserDto
    {
        public string Id { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? NationalId { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public string? ProfilePicture { get; set; }
        public bool IsActive { get; set; }
        public bool IsApproved { get; set; }
        public DateTime CreatedAt { get; set; }
        public string Role { get; set; } = string.Empty;
        public List<string> Roles { get; set; } = new List<string>();
    }

    public class ChangePasswordDto
    {
        [Required]
        public string CurrentPassword { get; set; } = string.Empty;

        [Required]
        [StringLength(100, MinimumLength = 6)]
        public string NewPassword { get; set; } = string.Empty;

        [Required]
        [Compare("NewPassword")]
        public string ConfirmNewPassword { get; set; } = string.Empty;
    }

    public class UpdateProfileDto
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [Phone]
        public string? PhoneNumber { get; set; }

        public DateTime? DateOfBirth { get; set; }

        public string? Address { get; set; }
    }

    // Academic DTOs - moved to AcademicDTOs.cs to avoid duplication

    // Parent DTOs - moved to AcademicDTOs.cs to avoid duplication

    public class ParentChildDto
    {
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
        public string Relationship { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public double? AverageGrade { get; set; }
        public double? AttendanceRate { get; set; }
        public int? PendingAssignments { get; set; }
    }

    public class CreateParentDto
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Phone]
        public string? PhoneNumber { get; set; }

        [StringLength(20)]
        public string? NationalId { get; set; }

        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public string? EmergencyContact { get; set; }
        public string? Occupation { get; set; }
        public string? WorkPlace { get; set; }
        public string? WorkPhone { get; set; }
    }

    public class UpdateParentDto
    {
        [StringLength(100)]
        public string? FirstName { get; set; }

        [StringLength(100)]
        public string? LastName { get; set; }

        [Phone]
        public string? PhoneNumber { get; set; }

        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public string? EmergencyContact { get; set; }
        public string? Occupation { get; set; }
        public string? WorkPlace { get; set; }
        public string? WorkPhone { get; set; }
    }

    public class LinkParentStudentDto
    {
        [Required]
        public string ParentId { get; set; } = string.Empty;

        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Relationship { get; set; } = string.Empty; // Father, Mother, Guardian
    }

    public class ParentStudentDto
    {
        public int Id { get; set; }
        public string ParentId { get; set; } = string.Empty;
        public string StudentId { get; set; } = string.Empty;
        public string Relationship { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public ParentDto? Parent { get; set; }
        public StudentSummaryDto? Student { get; set; }
    }

    public class StudentSummaryDto
    {
        public string Id { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
        public string? ProfilePicture { get; set; }
        public bool IsActive { get; set; }
    }

    public class ParentDashboardDto
    {
        public ParentDto Parent { get; set; } = new();
        public List<ChildPerformanceDto> Children { get; set; } = new();
        public List<NotificationDto> RecentNotifications { get; set; } = new();
        public List<UpcomingEventDto> UpcomingEvents { get; set; } = new();
        public ParentStatisticsDto Statistics { get; set; } = new();
    }

    public class ChildPerformanceDto
    {
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
        public string? ProfilePicture { get; set; }
        public double AverageGrade { get; set; }
        public double AttendanceRate { get; set; }
        public int PendingAssignments { get; set; }
        public int TotalAssignments { get; set; }
        public List<RecentGradeDto> RecentGrades { get; set; } = new();
        public List<AttendanceRecordDto> RecentAttendance { get; set; } = new();
        public string PerformanceTrend { get; set; } = "stable"; // improving, declining, stable
    }

    // RecentGradeDto and AttendanceRecordDto moved to AcademicDTOs.cs

    public class ParentStatisticsDto
    {
        public int TotalChildren { get; set; }
        public double OverallAverageGrade { get; set; }
        public double OverallAttendanceRate { get; set; }
        public int TotalPendingAssignments { get; set; }
        public int UnreadNotifications { get; set; }
        public int UpcomingEvents { get; set; }
    }

    // NotificationDto and UpcomingEventDto moved to ResponseDTOs.cs

    // Electronic Exams DTOs - Moved to AcademicDTOs.cs to avoid duplication

    // Student Exam Dashboard DTOs
    public class StudentExamDashboardDto
    {
        public List<AvailableExamDto> AvailableExams { get; set; } = new();
        public List<ExamAttemptDto> RecentAttempts { get; set; } = new();
        public List<ExamResultDto> RecentResults { get; set; } = new();
        public StudentExamStatisticsDto Statistics { get; set; } = new();
    }

    // AvailableExamDto moved to AcademicDTOs.cs to avoid duplication

    public class StudentExamStatisticsDto
    {
        public int TotalExamsTaken { get; set; }
        public int TotalExamsPassed { get; set; }
        public double AverageScore { get; set; }
        public double PassRate { get; set; }
        public int PendingExams { get; set; }
        public int CompletedExams { get; set; }
    }
}
