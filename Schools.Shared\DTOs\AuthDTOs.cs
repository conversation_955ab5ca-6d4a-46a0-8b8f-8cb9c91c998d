using System.ComponentModel.DataAnnotations;
using Schools.Shared.Models;

namespace Schools.Shared.DTOs
{
    // Authentication DTOs
    public class LoginDto
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        public string Password { get; set; } = string.Empty;

        public bool RememberMe { get; set; } = false;
    }

    public class RegisterDto
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        [StringLength(100, MinimumLength = 6)]
        public string Password { get; set; } = string.Empty;

        [Required]
        [Compare("Password")]
        public string ConfirmPassword { get; set; } = string.Empty;

        [Phone]
        public string? PhoneNumber { get; set; }

        [StringLength(20)]
        public string? NationalId { get; set; }

        public DateTime? DateOfBirth { get; set; }

        public string? Address { get; set; }

        [Required]
        public UserRole Role { get; set; }
    }

    public class AuthResponseDto
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? Token { get; set; }
        public UserDto? User { get; set; }
    }

    public class UserDto
    {
        public string Id { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? NationalId { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public string? ProfilePicture { get; set; }
        public bool IsActive { get; set; }
        public bool IsApproved { get; set; }
        public DateTime CreatedAt { get; set; }
        public string Role { get; set; } = string.Empty;
        public List<string> Roles { get; set; } = new List<string>();
    }

    public class ChangePasswordDto
    {
        [Required]
        public string CurrentPassword { get; set; } = string.Empty;

        [Required]
        [StringLength(100, MinimumLength = 6)]
        public string NewPassword { get; set; } = string.Empty;

        [Required]
        [Compare("NewPassword")]
        public string ConfirmNewPassword { get; set; } = string.Empty;
    }

    public class UpdateProfileDto
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [Phone]
        public string? PhoneNumber { get; set; }

        public DateTime? DateOfBirth { get; set; }

        public string? Address { get; set; }
    }

    // Academic DTOs - moved to AcademicDTOs.cs to avoid duplication

    // Parent DTOs - moved to AcademicDTOs.cs to avoid duplication

    public class ParentChildDto
    {
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
        public string Relationship { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public double? AverageGrade { get; set; }
        public double? AttendanceRate { get; set; }
        public int? PendingAssignments { get; set; }
    }

    public class CreateParentDto
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Phone]
        public string? PhoneNumber { get; set; }

        [StringLength(20)]
        public string? NationalId { get; set; }

        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public string? EmergencyContact { get; set; }
        public string? Occupation { get; set; }
        public string? WorkPlace { get; set; }
        public string? WorkPhone { get; set; }
    }

    public class UpdateParentDto
    {
        [StringLength(100)]
        public string? FirstName { get; set; }

        [StringLength(100)]
        public string? LastName { get; set; }

        [Phone]
        public string? PhoneNumber { get; set; }

        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public string? EmergencyContact { get; set; }
        public string? Occupation { get; set; }
        public string? WorkPlace { get; set; }
        public string? WorkPhone { get; set; }
    }

    public class LinkParentStudentDto
    {
        [Required]
        public string ParentId { get; set; } = string.Empty;

        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Relationship { get; set; } = string.Empty; // Father, Mother, Guardian
    }

    public class ParentStudentDto
    {
        public int Id { get; set; }
        public string ParentId { get; set; } = string.Empty;
        public string StudentId { get; set; } = string.Empty;
        public string Relationship { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public ParentDto? Parent { get; set; }
        public StudentSummaryDto? Student { get; set; }
    }

    public class StudentSummaryDto
    {
        public string Id { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
        public string? ProfilePicture { get; set; }
        public bool IsActive { get; set; }
    }

    public class ParentDashboardDto
    {
        public ParentDto Parent { get; set; } = new();
        public List<ChildPerformanceDto> Children { get; set; } = new();
        public List<NotificationDto> RecentNotifications { get; set; } = new();
        public List<UpcomingEventDto> UpcomingEvents { get; set; } = new();
        public ParentStatisticsDto Statistics { get; set; } = new();
    }

    public class ChildPerformanceDto
    {
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
        public string? ProfilePicture { get; set; }
        public double AverageGrade { get; set; }
        public double AttendanceRate { get; set; }
        public int PendingAssignments { get; set; }
        public int TotalAssignments { get; set; }
        public List<RecentGradeDto> RecentGrades { get; set; } = new();
        public List<AttendanceRecordDto> RecentAttendance { get; set; } = new();
        public string PerformanceTrend { get; set; } = "stable"; // improving, declining, stable
    }

    // RecentGradeDto and AttendanceRecordDto moved to AcademicDTOs.cs

    public class ParentStatisticsDto
    {
        public int TotalChildren { get; set; }
        public double OverallAverageGrade { get; set; }
        public double OverallAttendanceRate { get; set; }
        public int TotalPendingAssignments { get; set; }
        public int UnreadNotifications { get; set; }
        public int UpcomingEvents { get; set; }
    }

    // NotificationDto and UpcomingEventDto moved to ResponseDTOs.cs

    // Electronic Exams DTOs
    public class ExamDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public int ClassId { get; set; }
        public string ClassName { get; set; } = string.Empty;
        public string TeacherId { get; set; } = string.Empty;
        public string TeacherName { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int DurationMinutes { get; set; }
        public int TotalMarks { get; set; }
        public int PassingMarks { get; set; }
        public bool IsActive { get; set; }
        public bool AllowRetake { get; set; }
        public int MaxAttempts { get; set; } = 1;
        public bool ShowResultsImmediately { get; set; }
        public bool RandomizeQuestions { get; set; }
        public bool RandomizeOptions { get; set; }
        public DateTime CreatedAt { get; set; }
        public List<ExamQuestionDto> Questions { get; set; } = new();
        public int TotalQuestions { get; set; }
        public ExamStatus Status { get; set; }
    }

    public class CreateExamDto
    {
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        [Required]
        public int SubjectId { get; set; }

        [Required]
        public int ClassId { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        [Required]
        [Range(1, 300)]
        public int DurationMinutes { get; set; }

        [Required]
        [Range(1, 1000)]
        public int TotalMarks { get; set; }

        [Required]
        [Range(1, 1000)]
        public int PassingMarks { get; set; }

        public bool AllowRetake { get; set; } = false;

        [Range(1, 10)]
        public int MaxAttempts { get; set; } = 1;

        public bool ShowResultsImmediately { get; set; } = true;
        public bool RandomizeQuestions { get; set; } = false;
        public bool RandomizeOptions { get; set; } = false;
    }

    public class UpdateExamDto
    {
        [StringLength(200)]
        public string? Title { get; set; }

        [StringLength(1000)]
        public string? Description { get; set; }

        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        [Range(1, 300)]
        public int? DurationMinutes { get; set; }

        [Range(1, 1000)]
        public int? TotalMarks { get; set; }

        [Range(1, 1000)]
        public int? PassingMarks { get; set; }

        public bool? AllowRetake { get; set; }

        [Range(1, 10)]
        public int? MaxAttempts { get; set; }

        public bool? ShowResultsImmediately { get; set; }
        public bool? RandomizeQuestions { get; set; }
        public bool? RandomizeOptions { get; set; }
        public bool? IsActive { get; set; }
    }

    public class ExamQuestionDto
    {
        public int Id { get; set; }
        public int ExamId { get; set; }
        public string QuestionText { get; set; } = string.Empty;
        public QuestionType Type { get; set; }
        public int Marks { get; set; }
        public int OrderIndex { get; set; }
        public bool IsRequired { get; set; } = true;
        public string? ImageUrl { get; set; }
        public string? AudioUrl { get; set; }
        public List<QuestionOptionDto> Options { get; set; } = new();
        public string? CorrectAnswer { get; set; }
        public string? Explanation { get; set; }
    }

    public class CreateExamQuestionDto
    {
        [Required]
        public int ExamId { get; set; }

        [Required]
        [StringLength(2000)]
        public string QuestionText { get; set; } = string.Empty;

        [Required]
        public QuestionType Type { get; set; }

        [Required]
        [Range(1, 100)]
        public int Marks { get; set; }

        [Required]
        [Range(1, 1000)]
        public int OrderIndex { get; set; }

        public bool IsRequired { get; set; } = true;
        public string? ImageUrl { get; set; }
        public string? AudioUrl { get; set; }
        public string? CorrectAnswer { get; set; }
        public string? Explanation { get; set; }
        public List<CreateQuestionOptionDto> Options { get; set; } = new();
    }

    public class QuestionOptionDto
    {
        public int Id { get; set; }
        public int QuestionId { get; set; }
        public string OptionText { get; set; } = string.Empty;
        public bool IsCorrect { get; set; }
        public int OrderIndex { get; set; }
        public string? ImageUrl { get; set; }
    }

    public class CreateQuestionOptionDto
    {
        [Required]
        [StringLength(500)]
        public string OptionText { get; set; } = string.Empty;

        public bool IsCorrect { get; set; }

        [Range(1, 10)]
        public int OrderIndex { get; set; }

        public string? ImageUrl { get; set; }
    }

    public enum QuestionType
    {
        MultipleChoice = 1,
        TrueFalse = 2,
        ShortAnswer = 3,
        Essay = 4,
        FillInTheBlank = 5,
        Matching = 6
    }

    public enum ExamStatus
    {
        Draft = 1,
        Published = 2,
        InProgress = 3,
        Completed = 4,
        Cancelled = 5
    }

    // Exam Attempts and Results DTOs
    public class ExamAttemptDto
    {
        public int Id { get; set; }
        public int ExamId { get; set; }
        public string ExamTitle { get; set; } = string.Empty;
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public int DurationMinutes { get; set; }
        public int RemainingMinutes { get; set; }
        public AttemptStatus Status { get; set; }
        public int AttemptNumber { get; set; }
        public double? Score { get; set; }
        public double? Percentage { get; set; }
        public string? Grade { get; set; }
        public bool IsPassed { get; set; }
        public List<StudentAnswerDto> Answers { get; set; } = new();
        public DateTime CreatedAt { get; set; }
    }

    public class StartExamDto
    {
        [Required]
        public int ExamId { get; set; }
    }

    public class StudentAnswerDto
    {
        public int Id { get; set; }
        public int AttemptId { get; set; }
        public int QuestionId { get; set; }
        public string QuestionText { get; set; } = string.Empty;
        public QuestionType QuestionType { get; set; }
        public string? AnswerText { get; set; }
        public int? SelectedOptionId { get; set; }
        public List<int> SelectedOptionIds { get; set; } = new();
        public bool IsCorrect { get; set; }
        public double MarksObtained { get; set; }
        public double TotalMarks { get; set; }
        public DateTime AnsweredAt { get; set; }
    }

    public class SubmitAnswerDto
    {
        [Required]
        public int AttemptId { get; set; }

        [Required]
        public int QuestionId { get; set; }

        public string? AnswerText { get; set; }
        public int? SelectedOptionId { get; set; }
        public List<int> SelectedOptionIds { get; set; } = new();
    }

    public class SubmitExamDto
    {
        [Required]
        public int AttemptId { get; set; }

        public List<SubmitAnswerDto> Answers { get; set; } = new();
    }

    public class ExamResultDto
    {
        public int AttemptId { get; set; }
        public int ExamId { get; set; }
        public string ExamTitle { get; set; } = string.Empty;
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public int DurationTaken { get; set; }
        public double TotalMarks { get; set; }
        public double MarksObtained { get; set; }
        public double Percentage { get; set; }
        public string Grade { get; set; } = string.Empty;
        public bool IsPassed { get; set; }
        public int CorrectAnswers { get; set; }
        public int TotalQuestions { get; set; }
        public List<QuestionResultDto> QuestionResults { get; set; } = new();
        public ExamStatisticsDto Statistics { get; set; } = new();
    }

    public class QuestionResultDto
    {
        public int QuestionId { get; set; }
        public string QuestionText { get; set; } = string.Empty;
        public QuestionType Type { get; set; }
        public string? StudentAnswer { get; set; }
        public string? CorrectAnswer { get; set; }
        public bool IsCorrect { get; set; }
        public double MarksObtained { get; set; }
        public double TotalMarks { get; set; }
        public string? Explanation { get; set; }
        public List<QuestionOptionDto> Options { get; set; } = new();
    }

    public class ExamStatisticsDto
    {
        public int TotalAttempts { get; set; }
        public double AverageScore { get; set; }
        public double HighestScore { get; set; }
        public double LowestScore { get; set; }
        public double PassRate { get; set; }
        public int TotalPassed { get; set; }
        public int TotalFailed { get; set; }
        public Dictionary<string, int> GradeDistribution { get; set; } = new();
    }

    public enum AttemptStatus
    {
        InProgress = 1,
        Completed = 2,
        Submitted = 3,
        TimeExpired = 4,
        Cancelled = 5
    }

    // Student Exam Dashboard DTOs
    public class StudentExamDashboardDto
    {
        public List<AvailableExamDto> AvailableExams { get; set; } = new();
        public List<ExamAttemptDto> RecentAttempts { get; set; } = new();
        public List<ExamResultDto> RecentResults { get; set; } = new();
        public StudentExamStatisticsDto Statistics { get; set; } = new();
    }

    // AvailableExamDto moved to AcademicDTOs.cs to avoid duplication

    public class StudentExamStatisticsDto
    {
        public int TotalExamsTaken { get; set; }
        public int TotalExamsPassed { get; set; }
        public double AverageScore { get; set; }
        public double PassRate { get; set; }
        public int PendingExams { get; set; }
        public int CompletedExams { get; set; }
    }
}
