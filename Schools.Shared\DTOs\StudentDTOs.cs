using System.ComponentModel.DataAnnotations;

namespace Schools.Shared.DTOs
{
    // Student DTOs
    public class StudentDto
    {
        public string Id { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string FullName => $"{FirstName} {LastName}";
        public string Email { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? NationalId { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public string? ProfilePicture { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public string StudentNumber { get; set; } = string.Empty;
        public DateTime EnrollmentDate { get; set; }
        public string? EmergencyContact { get; set; }
        public string? MedicalInfo { get; set; }
        public string? BloodType { get; set; }
        public string? Allergies { get; set; }
        public string? SpecialNeeds { get; set; }
        public string? PreviousSchool { get; set; }
        public string? TransferReason { get; set; }
        public string? GuardianName { get; set; }
        public string? GuardianPhone { get; set; }
        public string? GuardianEmail { get; set; }
        public string? GuardianRelation { get; set; }

        // Academic Information
        public int? CurrentGradeId { get; set; }
        public string? CurrentGradeName { get; set; }
        public int? CurrentClassId { get; set; }
        public string? CurrentClassName { get; set; }
        public int? CurrentSectionId { get; set; }
        public string? CurrentSectionName { get; set; }
        public double? OverallGPA { get; set; }
        public double? AttendanceRate { get; set; }
        public string? AcademicStatus { get; set; }

        // Financial Information
        public decimal TotalFees { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public string PaymentStatus { get; set; } = string.Empty;
        public DateTime? LastPaymentDate { get; set; }

        // Related Data
        public List<ParentDto> Parents { get; set; } = new();
        public List<StudentEnrollmentDto> Enrollments { get; set; } = new();
        public List<RecentGradeDto> RecentGrades { get; set; } = new();
        public List<AttendanceRecordDto> RecentAttendance { get; set; } = new();
    }

    public class CreateStudentDto
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Phone]
        public string? PhoneNumber { get; set; }

        [StringLength(20)]
        public string? NationalId { get; set; }

        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }

        [Required]
        [StringLength(20)]
        public string StudentNumber { get; set; } = string.Empty;

        [Required]
        public DateTime EnrollmentDate { get; set; }

        public string? EmergencyContact { get; set; }
        public string? MedicalInfo { get; set; }
        public string? BloodType { get; set; }
        public string? Allergies { get; set; }
        public string? SpecialNeeds { get; set; }
        public string? PreviousSchool { get; set; }
        public string? TransferReason { get; set; }
        public string? GuardianName { get; set; }
        public string? GuardianPhone { get; set; }
        public string? GuardianEmail { get; set; }
        public string? GuardianRelation { get; set; }

        // Academic Information
        public int? GradeId { get; set; }
        public int? ClassId { get; set; }
        public int? SectionId { get; set; }
    }

    public class UpdateStudentDto
    {
        [StringLength(100)]
        public string? FirstName { get; set; }

        [StringLength(100)]
        public string? LastName { get; set; }

        [Phone]
        public string? PhoneNumber { get; set; }

        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public string? EmergencyContact { get; set; }
        public string? MedicalInfo { get; set; }
        public string? BloodType { get; set; }
        public string? Allergies { get; set; }
        public string? SpecialNeeds { get; set; }
        public string? GuardianName { get; set; }
        public string? GuardianPhone { get; set; }
        public string? GuardianEmail { get; set; }
        public string? GuardianRelation { get; set; }
        public bool? IsActive { get; set; }
    }

    public class StudentEnrollmentDto
    {
        public int Id { get; set; }
        public string StudentId { get; set; } = string.Empty;
        public int GradeId { get; set; }
        public string GradeName { get; set; } = string.Empty;
        public int ClassId { get; set; }
        public string ClassName { get; set; } = string.Empty;
        public int? SectionId { get; set; }
        public string? SectionName { get; set; }
        public int AcademicYearId { get; set; }
        public string AcademicYearName { get; set; } = string.Empty;
        public DateTime EnrollmentDate { get; set; }
        public DateTime? WithdrawalDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? WithdrawalReason { get; set; }
        public bool IsActive { get; set; }
    }

    public class CreateStudentEnrollmentDto
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int GradeId { get; set; }

        [Required]
        public int ClassId { get; set; }

        public int? SectionId { get; set; }

        [Required]
        public int AcademicYearId { get; set; }

        [Required]
        public DateTime EnrollmentDate { get; set; }
    }

    public class StudentSearchDto
    {
        public string? SearchTerm { get; set; }
        public int? GradeId { get; set; }
        public int? ClassId { get; set; }
        public int? SectionId { get; set; }
        public bool? IsActive { get; set; }
        public string? PaymentStatus { get; set; }
        public DateTime? EnrollmentDateFrom { get; set; }
        public DateTime? EnrollmentDateTo { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; } = false;
    }

    public class StudentListDto
    {
        public List<StudentDto> Students { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    public class StudentDashboardDto
    {
        public StudentDto Student { get; set; } = new();
        public List<AvailableExamDto> UpcomingExams { get; set; } = new();
        public List<RecentGradeDto> RecentGrades { get; set; } = new();
        public List<AttendanceRecordDto> RecentAttendance { get; set; } = new();
        public List<NotificationDto> Notifications { get; set; } = new();
        public StudentStatisticsDto Statistics { get; set; } = new();
        public List<UpcomingEventDto> UpcomingEvents { get; set; } = new();
    }

    public class StudentStatisticsDto
    {
        public double OverallGPA { get; set; }
        public double AttendanceRate { get; set; }
        public int TotalSubjects { get; set; }
        public int CompletedExams { get; set; }
        public int PendingExams { get; set; }
        public int TotalAssignments { get; set; }
        public int CompletedAssignments { get; set; }
        public int PendingAssignments { get; set; }
        public string AcademicRank { get; set; } = string.Empty;
        public string PerformanceTrend { get; set; } = string.Empty;
    }

    public class BulkStudentOperationDto
    {
        public List<string> StudentIds { get; set; } = new();
        public string Operation { get; set; } = string.Empty; // activate, deactivate, transfer, etc.
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    public class StudentTransferDto
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int NewGradeId { get; set; }

        [Required]
        public int NewClassId { get; set; }

        public int? NewSectionId { get; set; }

        [Required]
        public DateTime TransferDate { get; set; }

        public string? TransferReason { get; set; }
        public string? Notes { get; set; }
    }

    public class StudentPromotionDto
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int NewGradeId { get; set; }

        [Required]
        public int NewClassId { get; set; }

        public int? NewSectionId { get; set; }

        [Required]
        public int NewAcademicYearId { get; set; }

        [Required]
        public DateTime PromotionDate { get; set; }

        public string? Notes { get; set; }
        public bool IsConditional { get; set; }
        public string? Conditions { get; set; }
    }

    public class StudentWithdrawalDto
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public DateTime WithdrawalDate { get; set; }

        [Required]
        public string WithdrawalReason { get; set; } = string.Empty;

        public string? Notes { get; set; }
        public string? TransferSchool { get; set; }
        public bool IssueTranscript { get; set; } = true;
        public bool IssueCertificate { get; set; } = true;
    }

    // Additional DTOs for Controllers
    public class StudentInfoDto
    {
        public string Id { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public string GradeName { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
    }

    public class StudentAttendanceDto
    {
        public int Id { get; set; }
        public DateTime Date { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public TimeSpan? CheckInTime { get; set; }
        public TimeSpan? CheckOutTime { get; set; }
        public string? Reason { get; set; }
        public string? Notes { get; set; }
    }

    public class CreateStudentDto
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Phone]
        public string? PhoneNumber { get; set; }

        [StringLength(20)]
        public string? NationalId { get; set; }

        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }

        [Required]
        [StringLength(20)]
        public string StudentNumber { get; set; } = string.Empty;

        public string? Password { get; set; }

        // Academic Information
        public int? AcademicGradeId { get; set; }
        public int? ClassId { get; set; }
        public int? SectionId { get; set; }
    }

    public class UpdateStudentDto
    {
        [StringLength(100)]
        public string? FirstName { get; set; }

        [StringLength(100)]
        public string? LastName { get; set; }

        [Phone]
        public string? PhoneNumber { get; set; }

        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public bool? IsActive { get; set; }
    }

    public class CreateStudentEnrollmentDto
    {
        [Required]
        public int AcademicYearId { get; set; }

        [Required]
        public int AcademicGradeId { get; set; }

        [Required]
        public int ClassId { get; set; }

        public int? SectionId { get; set; }

        [Required]
        public DateTime EnrollmentDate { get; set; }
    }

    public class TransferStudentDto
    {
        public int? NewClassId { get; set; }
        public int? NewSectionId { get; set; }
        public int? NewAcademicGradeId { get; set; }
        public string? TransferReason { get; set; }
    }

    public class WithdrawStudentDto
    {
        [Required]
        public DateTime WithdrawalDate { get; set; }

        [Required]
        public string WithdrawalReason { get; set; } = string.Empty;
    }
}
