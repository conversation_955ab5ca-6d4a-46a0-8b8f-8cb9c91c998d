using System.ComponentModel.DataAnnotations;

namespace Schools.Shared.DTOs;

// Employee Management DTOs
public class EmployeeDto
{
    public string Id { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string FullName => $"{FirstName} {LastName}";
    public string Email { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public string? NationalId { get; set; }
    public DateTime? DateOfBirth { get; set; }
    public string? Address { get; set; }
    public string Position { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public DateTime HireDate { get; set; }
    public decimal Salary { get; set; }
    public string EmploymentType { get; set; } = string.Empty; // FullTime, PartTime, Contract
    public string Status { get; set; } = string.Empty; // Active, Inactive, Terminated
    public string? Qualifications { get; set; }
    public int YearsOfExperience { get; set; }
    public string? EmergencyContact { get; set; }
    public string? EmergencyPhone { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime? LastModified { get; set; }
}

public class CreateEmployeeDto
{
    [Required]
    [StringLength(100)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string LastName { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    [Phone]
    public string? PhoneNumber { get; set; }

    [StringLength(20)]
    public string? NationalId { get; set; }

    public DateTime? DateOfBirth { get; set; }
    public string? Address { get; set; }

    [Required]
    [StringLength(100)]
    public string Position { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string Department { get; set; } = string.Empty;

    [Required]
    public DateTime HireDate { get; set; }

    [Range(0, double.MaxValue)]
    public decimal Salary { get; set; }

    [Required]
    public string EmploymentType { get; set; } = string.Empty;

    public string? Qualifications { get; set; }
    public int YearsOfExperience { get; set; }
    public string? EmergencyContact { get; set; }
    public string? EmergencyPhone { get; set; }
}

public class UpdateEmployeeDto
{
    [StringLength(100)]
    public string? FirstName { get; set; }

    [StringLength(100)]
    public string? LastName { get; set; }

    [Phone]
    public string? PhoneNumber { get; set; }

    public DateTime? DateOfBirth { get; set; }
    public string? Address { get; set; }
    public string? Position { get; set; }
    public string? Department { get; set; }
    public decimal? Salary { get; set; }
    public string? EmploymentType { get; set; }
    public string? Status { get; set; }
    public string? Qualifications { get; set; }
    public int? YearsOfExperience { get; set; }
    public string? EmergencyContact { get; set; }
    public string? EmergencyPhone { get; set; }
}

// Attendance Management DTOs
public class EmployeeAttendanceDto
{
    public int Id { get; set; }
    public string EmployeeId { get; set; } = string.Empty;
    public string EmployeeName { get; set; } = string.Empty;
    public DateTime Date { get; set; }
    public TimeSpan? CheckInTime { get; set; }
    public TimeSpan? CheckOutTime { get; set; }
    public TimeSpan? WorkingHours { get; set; }
    public TimeSpan? BreakTime { get; set; }
    public string Status { get; set; } = string.Empty; // Present, Absent, Late, EarlyLeave
    public string? Notes { get; set; }
    public bool IsHoliday { get; set; }
    public bool IsWeekend { get; set; }
}

public class CreateEmployeeAttendanceDto
{
    [Required]
    public string EmployeeId { get; set; } = string.Empty;

    [Required]
    public DateTime Date { get; set; }

    public TimeSpan? CheckInTime { get; set; }
    public TimeSpan? CheckOutTime { get; set; }
    public string? Notes { get; set; }
}

// Leave Management DTOs
public class LeaveRequestDto
{
    public int Id { get; set; }
    public string EmployeeId { get; set; } = string.Empty;
    public string EmployeeName { get; set; } = string.Empty;
    public string LeaveType { get; set; } = string.Empty; // Annual, Sick, Emergency, Maternity
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int TotalDays { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Pending, Approved, Rejected
    public DateTime RequestDate { get; set; }
    public string? ApprovedBy { get; set; }
    public DateTime? ApprovalDate { get; set; }
    public string? ApprovalNotes { get; set; }
    public List<string> Documents { get; set; } = new();
}

public class CreateLeaveRequestDto
{
    [Required]
    public string EmployeeId { get; set; } = string.Empty;

    [Required]
    public string LeaveType { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    [Required]
    [StringLength(500)]
    public string Reason { get; set; } = string.Empty;

    public List<string> Documents { get; set; } = new();
}

public class ApproveLeaveDto
{
    [Required]
    public string Status { get; set; } = string.Empty; // Approved, Rejected

    [StringLength(500)]
    public string? Notes { get; set; }
}

// Payroll Management DTOs
public class PayrollDto
{
    public int Id { get; set; }
    public string EmployeeId { get; set; } = string.Empty;
    public string EmployeeName { get; set; } = string.Empty;
    public int Month { get; set; }
    public int Year { get; set; }
    public decimal BaseSalary { get; set; }
    public decimal Allowances { get; set; }
    public decimal Overtime { get; set; }
    public decimal Deductions { get; set; }
    public decimal Tax { get; set; }
    public decimal NetSalary { get; set; }
    public int WorkingDays { get; set; }
    public int ActualDays { get; set; }
    public decimal HourlyRate { get; set; }
    public decimal OvertimeHours { get; set; }
    public string Status { get; set; } = string.Empty; // Draft, Processed, Paid
    public DateTime? ProcessedDate { get; set; }
    public DateTime? PaidDate { get; set; }
    public string? Notes { get; set; }
}

public class CreatePayrollDto
{
    [Required]
    public string EmployeeId { get; set; } = string.Empty;

    [Required]
    [Range(1, 12)]
    public int Month { get; set; }

    [Required]
    public int Year { get; set; }

    public decimal Allowances { get; set; }
    public decimal Overtime { get; set; }
    public decimal Deductions { get; set; }
    public decimal OvertimeHours { get; set; }
    public string? Notes { get; set; }
}

// Performance Management DTOs
public class PerformanceReviewDto
{
    public int Id { get; set; }
    public string EmployeeId { get; set; } = string.Empty;
    public string EmployeeName { get; set; } = string.Empty;
    public string ReviewerId { get; set; } = string.Empty;
    public string ReviewerName { get; set; } = string.Empty;
    public DateTime ReviewPeriodStart { get; set; }
    public DateTime ReviewPeriodEnd { get; set; }
    public int OverallRating { get; set; } // 1-5 scale
    public string Goals { get; set; } = string.Empty;
    public string Achievements { get; set; } = string.Empty;
    public string AreasForImprovement { get; set; } = string.Empty;
    public string Comments { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // Draft, Submitted, Approved
    public DateTime CreatedDate { get; set; }
    public List<PerformanceCriteriaDto> Criteria { get; set; } = new();
}

public class PerformanceCriteriaDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Rating { get; set; } // 1-5 scale
    public int Weight { get; set; } // Percentage weight
    public string Comments { get; set; } = string.Empty;
}

public class CreatePerformanceReviewDto
{
    [Required]
    public string EmployeeId { get; set; } = string.Empty;

    [Required]
    public DateTime ReviewPeriodStart { get; set; }

    [Required]
    public DateTime ReviewPeriodEnd { get; set; }

    [Required]
    [Range(1, 5)]
    public int OverallRating { get; set; }

    [Required]
    public string Goals { get; set; } = string.Empty;

    [Required]
    public string Achievements { get; set; } = string.Empty;

    public string? AreasForImprovement { get; set; }
    public string? Comments { get; set; }
    public List<PerformanceCriteriaDto> Criteria { get; set; } = new();
}

// HR Statistics DTOs
public class HRStatisticsDto
{
    public int TotalEmployees { get; set; }
    public int ActiveEmployees { get; set; }
    public int InactiveEmployees { get; set; }
    public int NewHires { get; set; }
    public int Terminations { get; set; }
    public double TurnoverRate { get; set; }
    public double AttendanceRate { get; set; }
    public int PendingLeaveRequests { get; set; }
    public int ApprovedLeaveRequests { get; set; }
    public decimal TotalPayroll { get; set; }
    public double AveragePerformanceRating { get; set; }
    public Dictionary<string, int> DepartmentDistribution { get; set; } = new();
    public Dictionary<string, int> PositionDistribution { get; set; } = new();
    public Dictionary<string, decimal> SalaryRanges { get; set; } = new();
}

public class EmployeeSearchDto
{
    public string? SearchTerm { get; set; }
    public string? Department { get; set; }
    public string? Position { get; set; }
    public string? EmploymentType { get; set; }
    public string? Status { get; set; }
    public DateTime? HireDateFrom { get; set; }
    public DateTime? HireDateTo { get; set; }
    public decimal? SalaryFrom { get; set; }
    public decimal? SalaryTo { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; } = false;
}

// Training and Development DTOs
public class TrainingProgramDto
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Instructor { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int MaxParticipants { get; set; }
    public int CurrentParticipants { get; set; }
    public string Location { get; set; } = string.Empty;
    public decimal Cost { get; set; }
    public string Status { get; set; } = string.Empty; // Planned, Active, Completed, Cancelled
    public List<string> Prerequisites { get; set; } = new();
    public List<string> LearningObjectives { get; set; } = new();
}

public class TrainingEnrollmentDto
{
    public int Id { get; set; }
    public int TrainingId { get; set; }
    public string TrainingTitle { get; set; } = string.Empty;
    public string EmployeeId { get; set; } = string.Empty;
    public string EmployeeName { get; set; } = string.Empty;
    public DateTime EnrollmentDate { get; set; }
    public string Status { get; set; } = string.Empty; // Enrolled, Completed, Dropped
    public int? CompletionScore { get; set; }
    public DateTime? CompletionDate { get; set; }
    public string? Certificate { get; set; }
    public string? Feedback { get; set; }
}

// Health DTOs for HealthController
public class HealthRecordDto
{
    public int Id { get; set; }
    public string StudentId { get; set; } = string.Empty;
    public string StudentName { get; set; } = string.Empty;
    public DateTime Date { get; set; }
    public string Type { get; set; } = string.Empty; // Checkup, Illness, Injury, Emergency
    public string Description { get; set; } = string.Empty;
    public string? Diagnosis { get; set; }
    public string? Treatment { get; set; }
    public List<string> Medications { get; set; } = new();
    public bool FollowUpRequired { get; set; }
    public DateTime? FollowUpDate { get; set; }
    public string NurseId { get; set; } = string.Empty;
    public string NurseName { get; set; } = string.Empty;
    public string? Notes { get; set; }
}

public class CreateHealthRecordDto
{
    [Required]
    public string StudentId { get; set; } = string.Empty;

    [Required]
    public DateTime Date { get; set; }

    [Required]
    public string Type { get; set; } = string.Empty;

    [Required]
    public string Description { get; set; } = string.Empty;

    public string? Diagnosis { get; set; }
    public string? Treatment { get; set; }
    public List<string> Medications { get; set; } = new();
    public bool FollowUpRequired { get; set; }
    public DateTime? FollowUpDate { get; set; }
    public string? Notes { get; set; }
}

public class VaccinationRecordDto
{
    public int Id { get; set; }
    public string StudentId { get; set; } = string.Empty;
    public string StudentName { get; set; } = string.Empty;
    public string VaccineName { get; set; } = string.Empty;
    public DateTime Date { get; set; }
    public int DoseNumber { get; set; }
    public DateTime? NextDueDate { get; set; }
    public string AdministeredBy { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string? BatchNumber { get; set; }
    public string? Manufacturer { get; set; }
    public List<string> SideEffects { get; set; } = new();
    public string? Notes { get; set; }
}

public class CreateVaccinationDto
{
    [Required]
    public string StudentId { get; set; } = string.Empty;

    [Required]
    public string VaccineName { get; set; } = string.Empty;

    [Required]
    public DateTime Date { get; set; }

    [Required]
    public int DoseNumber { get; set; }

    public DateTime? NextDueDate { get; set; }

    [Required]
    public string AdministeredBy { get; set; } = string.Empty;

    [Required]
    public string Location { get; set; } = string.Empty;

    public string? BatchNumber { get; set; }
    public string? Manufacturer { get; set; }
    public List<string> SideEffects { get; set; } = new();
    public string? Notes { get; set; }
}

public class HealthAlertDto
{
    public int Id { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public int StudentCount { get; set; }
    public DateTime DueDate { get; set; }
}

public class HealthStatisticsDto
{
    public int TotalHealthRecords { get; set; }
    public int TotalVaccinations { get; set; }
    public int ActiveFollowUps { get; set; }
    public int OverdueVaccinations { get; set; }
    public Dictionary<string, int> CommonIllnesses { get; set; } = new();
    public Dictionary<string, double> VaccinationCoverage { get; set; } = new();
    public Dictionary<string, int> MonthlyVisits { get; set; } = new();
}

public class MedicalConditionDto
{
    public int Id { get; set; }
    public string StudentId { get; set; } = string.Empty;
    public string StudentName { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public List<string> Medications { get; set; } = new();
    public string EmergencyContact { get; set; } = string.Empty;
    public string SpecialInstructions { get; set; } = string.Empty;
    public DateTime DiagnosisDate { get; set; }
    public string DoctorName { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
}

public class CreateMedicalConditionDto
{
    [Required]
    public string StudentId { get; set; } = string.Empty;

    [Required]
    public string Condition { get; set; } = string.Empty;

    [Required]
    public string Severity { get; set; } = string.Empty;

    public List<string> Medications { get; set; } = new();

    [Required]
    public string EmergencyContact { get; set; } = string.Empty;

    public string SpecialInstructions { get; set; } = string.Empty;
    public DateTime DiagnosisDate { get; set; }
    public string DoctorName { get; set; } = string.Empty;
}

public class HealthReportDto
{
    public string ReportType { get; set; } = string.Empty;
    public DateTime GeneratedDate { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int TotalRecords { get; set; }
    public string Summary { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
}

public class HealthReportRequestDto
{
    [Required]
    public string ReportType { get; set; } = string.Empty;

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    public List<string>? StudentIds { get; set; }
    public List<string>? Conditions { get; set; }
}
