#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo -e "    نظام إدارة المدارس المتكامل"
echo -e "    School Management System"
echo -e "========================================${NC}"
echo

echo -e "${BLUE}[1/5] التحقق من متطلبات النظام...${NC}"
if ! command -v dotnet &> /dev/null; then
    echo -e "${RED}❌ .NET 8 غير مثبت. يرجى تثبيت .NET 8 SDK أولاً${NC}"
    exit 1
fi
echo -e "${GREEN}✅ .NET 8 SDK متوفر${NC}"

echo
echo -e "${BLUE}[2/5] استعادة الحزم...${NC}"
if ! dotnet restore; then
    echo -e "${RED}❌ فشل في استعادة الحزم${NC}"
    exit 1
fi
echo -e "${GREEN}✅ تم استعادة الحزم بنجاح${NC}"

echo
echo -e "${BLUE}[3/5] بناء المشروع...${NC}"
if ! dotnet build Schools.API --configuration Release; then
    echo -e "${RED}❌ فشل في بناء المشروع${NC}"
    exit 1
fi
echo -e "${GREEN}✅ تم بناء المشروع بنجاح${NC}"

echo
echo -e "${BLUE}[4/5] تحديث قاعدة البيانات...${NC}"
cd Schools.API
if ! dotnet ef database update; then
    echo -e "${YELLOW}⚠️  تحذير: فشل في تحديث قاعدة البيانات. تأكد من إعدادات الاتصال${NC}"
fi
echo -e "${GREEN}✅ تم تحديث قاعدة البيانات${NC}"

echo
echo -e "${BLUE}[5/5] تشغيل النظام...${NC}"
echo
echo -e "${GREEN}🚀 النظام يعمل الآن على:${NC}"
echo -e "    API: https://localhost:5001"
echo -e "    Swagger: https://localhost:5001/swagger"
echo -e "    Client: https://localhost:5001"
echo
echo -e "${YELLOW}📝 بيانات تسجيل الدخول الافتراضية:${NC}"
echo -e "    المدير: <EMAIL> / Admin123!"
echo -e "    المعلم: <EMAIL> / Teacher123!"
echo -e "    الطالب: <EMAIL> / Student123!"
echo
echo -e "${BLUE}⏹️  اضغط Ctrl+C لإيقاف النظام${NC}"
echo

dotnet run --urls="https://localhost:5001;http://localhost:5000"
