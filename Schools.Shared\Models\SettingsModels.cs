namespace Schools.Shared.Models
{
    // JWT Settings
    public class JwtSettings
    {
        public string Secret { get; set; } = string.Empty;
        public string Issuer { get; set; } = string.Empty;
        public string Audience { get; set; } = string.Empty;
        public int ExpiryInHours { get; set; } = 24;
        public int RefreshTokenExpiryInDays { get; set; } = 7;
        public int ClockSkewInMinutes { get; set; } = 5;
        public bool ValidateIssuer { get; set; } = true;
        public bool ValidateAudience { get; set; } = true;
        public bool ValidateLifetime { get; set; } = true;
        public bool ValidateIssuerSigningKey { get; set; } = true;
        public bool RequireExpirationTime { get; set; } = true;
        public bool RequireSignedTokens { get; set; } = true;
    }

    // School Settings
    public class SchoolSettings
    {
        public string SchoolName { get; set; } = string.Empty;
        public string SchoolNameEn { get; set; } = string.Empty;
        public string SchoolCode { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Website { get; set; } = string.Empty;
        public string Logo { get; set; } = string.Empty;
        public int EstablishedYear { get; set; }
        public string Currency { get; set; } = "SAR";
        public string Language { get; set; } = "ar";
        public string TimeZone { get; set; } = "Asia/Riyadh";
        public string AcademicYearStart { get; set; } = "09-01";
        public string AcademicYearEnd { get; set; } = "06-30";
    }

    // Library Settings
    public class LibrarySettings
    {
        public int MaxBooksPerStudent { get; set; } = 3;
        public int BorrowDurationDays { get; set; } = 14;
        public int MaxRenewalCount { get; set; } = 2;
        public decimal FinePerDay { get; set; } = 1.0m;
        public decimal MaxFineAmount { get; set; } = 50.0m;
        public int ReservationExpiryDays { get; set; } = 3;
        public bool DigitalResourcesEnabled { get; set; } = true;
        public bool AllowOnlineReading { get; set; } = true;
        public bool RequireLibraryCard { get; set; } = true;
    }

    // Attendance Settings
    public class AttendanceSettings
    {
        public int LateThresholdMinutes { get; set; } = 15;
        public int AbsentThresholdMinutes { get; set; } = 60;
        public bool AllowSelfCheckIn { get; set; } = false;
        public bool RequireLocationVerification { get; set; } = true;
        public int AttendanceGracePeriodMinutes { get; set; } = 5;
        public bool NotifyParentsOnAbsence { get; set; } = true;
        public int MaxConsecutiveAbsences { get; set; } = 3;
    }

    // Grading Settings
    public class GradingSettings
    {
        public decimal PassingGrade { get; set; } = 60.0m;
        public decimal MaxGrade { get; set; } = 100.0m;
        public string GradeScale { get; set; } = "Percentage";
        public bool AllowGradeModification { get; set; } = true;
        public bool RequireApprovalForChanges { get; set; } = true;
        public bool ShowGradesToParents { get; set; } = true;
        public bool ShowGradesToStudents { get; set; } = true;
        public string GradeCalculationMethod { get; set; } = "Weighted";
    }

    // Activity Settings
    public class ActivitySettings
    {
        public int MaxParticipantsDefault { get; set; } = 30;
        public int RegistrationDeadlineDays { get; set; } = 3;
        public bool AllowLateRegistration { get; set; } = false;
        public bool RequireParentApproval { get; set; } = true;
        public bool AllowActivityFeedback { get; set; } = true;
        public bool ActivityPhotoUpload { get; set; } = true;
        public int MaxActivityDurationHours { get; set; } = 8;
    }

    // Notification Settings
    public class NotificationSettings
    {
        public bool EmailEnabled { get; set; } = true;
        public bool SMSEnabled { get; set; } = true;
        public bool PushNotificationsEnabled { get; set; } = true;
        public bool NotifyOnGradeUpdate { get; set; } = true;
        public bool NotifyOnAttendance { get; set; } = true;
        public bool NotifyOnActivityRegistration { get; set; } = true;
        public bool NotifyOnLibraryDueDate { get; set; } = true;
        public bool NotifyOnFeesDue { get; set; } = true;
    }

    // File Upload Settings
    public class FileUploadSettings
    {
        public int MaxFileSizeMB { get; set; } = 10;
        public List<string> AllowedImageTypes { get; set; } = new();
        public List<string> AllowedDocumentTypes { get; set; } = new();
        public List<string> AllowedVideoTypes { get; set; } = new();
        public string UploadPath { get; set; } = "wwwroot/uploads";
        public string ProfilePicturePath { get; set; } = "wwwroot/uploads/profiles";
        public string DocumentPath { get; set; } = "wwwroot/uploads/documents";
        public string ActivityPhotoPath { get; set; } = "wwwroot/uploads/activities";
    }

    // Security Settings
    public class SecuritySettings
    {
        public int PasswordMinLength { get; set; } = 8;
        public bool RequireDigit { get; set; } = true;
        public bool RequireLowercase { get; set; } = true;
        public bool RequireUppercase { get; set; } = true;
        public bool RequireNonAlphanumeric { get; set; } = true;
        public int MaxFailedAccessAttempts { get; set; } = 5;
        public int LockoutTimeSpanMinutes { get; set; } = 30;
        public bool RequireConfirmedEmail { get; set; } = false;
        public bool RequireConfirmedPhoneNumber { get; set; } = false;
        public bool AllowedForNewUsers { get; set; } = true;
    }

    // API Settings
    public class ApiSettings
    {
        public string Version { get; set; } = "v1";
        public string Title { get; set; } = "Schools Management API";
        public string Description { get; set; } = string.Empty;
        public string ContactName { get; set; } = string.Empty;
        public string ContactEmail { get; set; } = string.Empty;
        public string LicenseName { get; set; } = "MIT";
        public bool EnableSwagger { get; set; } = true;
        public bool EnableCors { get; set; } = true;
        public bool RateLimitingEnabled { get; set; } = true;
        public int RequestsPerMinute { get; set; } = 100;
    }

    // Database Settings
    public class DatabaseSettings
    {
        public int CommandTimeout { get; set; } = 30;
        public bool EnableSensitiveDataLogging { get; set; } = false;
        public bool EnableDetailedErrors { get; set; } = false;
        public int MaxRetryCount { get; set; } = 3;
        public string MaxRetryDelay { get; set; } = "00:00:30";
        public bool EnableServiceProviderCaching { get; set; } = true;
        public bool EnableQuerySplitting { get; set; } = true;
    }

    // Cache Settings
    public class CacheSettings
    {
        public int DefaultExpirationMinutes { get; set; } = 60;
        public int SlidingExpirationMinutes { get; set; } = 30;
        public int UserCacheExpirationMinutes { get; set; } = 15;
        public int StaticDataCacheHours { get; set; } = 24;
        public bool EnableDistributedCache { get; set; } = false;
        public string CacheProvider { get; set; } = "Memory";
    }

    // Email Settings
    public class EmailSettings
    {
        public string SmtpServer { get; set; } = string.Empty;
        public int SmtpPort { get; set; } = 587;
        public string SmtpUsername { get; set; } = string.Empty;
        public string SmtpPassword { get; set; } = string.Empty;
        public bool EnableSsl { get; set; } = true;
        public string FromName { get; set; } = string.Empty;
        public string FromEmail { get; set; } = string.Empty;
        public string ReplyToEmail { get; set; } = string.Empty;
    }

    // SMS Settings
    public class SmsSettings
    {
        public string Provider { get; set; } = "Twilio";
        public string AccountSid { get; set; } = string.Empty;
        public string AuthToken { get; set; } = string.Empty;
        public string FromNumber { get; set; } = string.Empty;
        public bool EnableSms { get; set; } = false;
    }

    // Report Settings
    public class ReportSettings
    {
        public int DefaultPageSize { get; set; } = 50;
        public int MaxPageSize { get; set; } = 1000;
        public bool AllowExportToExcel { get; set; } = true;
        public bool AllowExportToPdf { get; set; } = true;
        public int ReportCacheMinutes { get; set; } = 30;
        public bool EnableReportScheduling { get; set; } = true;
        public string ReportStoragePath { get; set; } = "wwwroot/reports";
    }
}
