using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;
using System.Text;

namespace Schools.API.Services
{
    public interface IReportService
    {
        Task<StudentPerformanceReportDto> GetStudentPerformanceReportAsync(string studentId, int academicYearId);
        Task<ClassPerformanceReportDto> GetClassPerformanceReportAsync(int classId, int academicYearId);
        Task<TeacherPerformanceReportDto> GetTeacherPerformanceReportAsync(string teacherId, int academicYearId);
        Task<FinancialReportDto> GetFinancialReportAsync(DateTime fromDate, DateTime toDate);
        Task<AttendanceReportDto> GetAttendanceReportAsync(int classId, DateTime fromDate, DateTime toDate);
        Task<ExamAnalysisReportDto> GetExamAnalysisReportAsync(int examId);
        Task<byte[]> ExportReportToPdfAsync(string reportType, object reportData);
        Task<byte[]> ExportReportToExcelAsync(string reportType, object reportData);
    }

    public class ReportService : IReportService
    {
        private readonly SchoolsDbContext _context;
        private readonly ILogger<ReportService> _logger;

        public ReportService(SchoolsDbContext context, ILogger<ReportService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<StudentPerformanceReportDto> GetStudentPerformanceReportAsync(string studentId, int academicYearId)
        {
            var student = await _context.Users.FindAsync(studentId);
            if (student == null) throw new KeyNotFoundException("Student not found");

            var enrollment = await _context.StudentEnrollments
                .Include(e => e.AcademicGrade)
                .Include(e => e.Class)
                .Include(e => e.Section)
                .FirstOrDefaultAsync(e => e.StudentId == studentId && e.AcademicYearId == academicYearId);

            var grades = await _context.Grades
                .Include(g => g.Subject)
                .Where(g => g.StudentId == studentId && g.AcademicYearId == academicYearId)
                .ToListAsync();

            var attendance = await _context.Attendances
                .Where(a => a.StudentId == studentId && a.StudentEnrollment.AcademicYearId == academicYearId)
                .ToListAsync();

            var examResults = await _context.ExamAttempts
                .Include(ea => ea.Exam)
                .ThenInclude(e => e.Subject)
                .Where(ea => ea.StudentId == studentId && ea.Exam.AcademicYearId == academicYearId)
                .ToListAsync();

            return new StudentPerformanceReportDto
            {
                StudentId = studentId,
                StudentName = $"{student.FirstName} {student.LastName}",
                StudentNumber = student.StudentNumber,
                GradeName = enrollment?.AcademicGrade.Name ?? "",
                ClassName = enrollment?.Class.Name ?? "",
                SectionName = enrollment?.Section?.Name ?? "",
                AcademicYear = academicYearId.ToString(),
                OverallGPA = grades.Any() ? grades.Average(g => (double)g.Percentage) : 0,
                TotalSubjects = grades.Select(g => g.SubjectId).Distinct().Count(),
                AttendanceRate = CalculateAttendanceRate(attendance),
                SubjectGrades = grades.GroupBy(g => g.Subject.Name)
                    .Select(g => new SubjectGradeDto
                    {
                        SubjectName = g.Key,
                        AverageScore = g.Average(grade => (double)grade.Percentage),
                        HighestScore = g.Max(grade => (double)grade.Percentage),
                        LowestScore = g.Min(grade => (double)grade.Percentage),
                        TotalExams = g.Count()
                    }).ToList(),
                ExamResults = examResults.Select(er => new ExamResultSummaryDto
                {
                    ExamTitle = er.Exam.Title,
                    SubjectName = er.Exam.Subject.Name,
                    Score = er.Score ?? 0,
                    Percentage = er.Percentage ?? 0,
                    IsPassed = er.IsPassed,
                    AttemptDate = er.StartTime
                }).ToList()
            };
        }

        public async Task<ClassPerformanceReportDto> GetClassPerformanceReportAsync(int classId, int academicYearId)
        {
            var classEntity = await _context.Classes
                .Include(c => c.AcademicGrade)
                .FirstOrDefaultAsync(c => c.Id == classId);

            if (classEntity == null) throw new KeyNotFoundException("Class not found");

            var students = await _context.StudentEnrollments
                .Include(e => e.Student)
                .Where(e => e.ClassId == classId && e.AcademicYearId == academicYearId)
                .ToListAsync();

            var grades = await _context.Grades
                .Include(g => g.Subject)
                .Include(g => g.Student)
                .Where(g => g.AcademicYearId == academicYearId && 
                           students.Select(s => s.StudentId).Contains(g.StudentId))
                .ToListAsync();

            var attendance = await _context.Attendances
                .Where(a => students.Select(s => s.StudentId).Contains(a.StudentId))
                .ToListAsync();

            return new ClassPerformanceReportDto
            {
                ClassId = classId,
                ClassName = classEntity.Name,
                GradeName = classEntity.AcademicGrade.Name,
                AcademicYear = academicYearId.ToString(),
                TotalStudents = students.Count,
                AverageGPA = grades.Any() ? grades.Average(g => (double)g.Percentage) : 0,
                AttendanceRate = CalculateAttendanceRate(attendance),
                SubjectPerformance = grades.GroupBy(g => g.Subject.Name)
                    .Select(g => new SubjectPerformanceDto
                    {
                        SubjectName = g.Key,
                        AverageScore = g.Average(grade => (double)grade.Percentage),
                        PassRate = g.Count(grade => grade.Percentage >= 60) * 100.0 / g.Count(),
                        TotalStudents = g.Select(grade => grade.StudentId).Distinct().Count()
                    }).ToList(),
                TopPerformers = students.Select(s => new StudentSummaryDto
                {
                    StudentId = s.StudentId,
                    StudentName = $"{s.Student.FirstName} {s.Student.LastName}",
                    StudentNumber = s.Student.StudentNumber,
                    GPA = grades.Where(g => g.StudentId == s.StudentId).Any() 
                        ? grades.Where(g => g.StudentId == s.StudentId).Average(g => (double)g.Percentage) 
                        : 0
                }).OrderByDescending(s => s.GPA).Take(10).ToList()
            };
        }

        public async Task<TeacherPerformanceReportDto> GetTeacherPerformanceReportAsync(string teacherId, int academicYearId)
        {
            var teacher = await _context.Users.FindAsync(teacherId);
            if (teacher == null) throw new KeyNotFoundException("Teacher not found");

            var assignments = await _context.TeacherAssignments
                .Include(ta => ta.Subject)
                .Include(ta => ta.Class)
                .Where(ta => ta.TeacherId == teacherId && ta.IsActive)
                .ToListAsync();

            var examsCreated = await _context.Exams
                .Where(e => e.TeacherId == teacherId && e.AcademicYearId == academicYearId)
                .CountAsync();

            var studentsGrades = await _context.Grades
                .Include(g => g.Subject)
                .Where(g => g.AcademicYearId == academicYearId && 
                           assignments.Select(a => a.SubjectId).Contains(g.SubjectId))
                .ToListAsync();

            return new TeacherPerformanceReportDto
            {
                TeacherId = teacherId,
                TeacherName = $"{teacher.FirstName} {teacher.LastName}",
                AcademicYear = academicYearId.ToString(),
                TotalClasses = assignments.Select(a => a.ClassId).Distinct().Count(),
                TotalSubjects = assignments.Select(a => a.SubjectId).Distinct().Count(),
                TotalStudents = await GetTotalStudentsForTeacher(teacherId, academicYearId),
                ExamsCreated = examsCreated,
                ClassAssignments = assignments.Select(a => new ClassAssignmentDto
                {
                    SubjectName = a.Subject.Name,
                    ClassName = a.Class.Name,
                    IsClassTeacher = a.IsClassTeacher,
                    AssignedDate = a.AssignedDate
                }).ToList(),
                SubjectPerformance = studentsGrades.GroupBy(g => g.Subject.Name)
                    .Select(g => new SubjectPerformanceDto
                    {
                        SubjectName = g.Key,
                        AverageScore = g.Average(grade => (double)grade.Percentage),
                        PassRate = g.Count(grade => grade.Percentage >= 60) * 100.0 / g.Count(),
                        TotalStudents = g.Select(grade => grade.StudentId).Distinct().Count()
                    }).ToList()
            };
        }

        public async Task<FinancialReportDto> GetFinancialReportAsync(DateTime fromDate, DateTime toDate)
        {
            var receiptVouchers = await _context.ReceiptVouchers
                .Include(rv => rv.Details)
                .Where(rv => rv.VoucherDate >= fromDate && rv.VoucherDate <= toDate)
                .ToListAsync();

            var paymentVouchers = await _context.PaymentVouchers
                .Include(pv => pv.Details)
                .Where(pv => pv.VoucherDate >= fromDate && pv.VoucherDate <= toDate)
                .ToListAsync();

            var studentPayments = await _context.StudentFeePayments
                .Where(sp => sp.PaymentDate >= fromDate && sp.PaymentDate <= toDate)
                .ToListAsync();

            return new FinancialReportDto
            {
                FromDate = fromDate,
                ToDate = toDate,
                TotalIncome = receiptVouchers.Sum(rv => rv.TotalAmount),
                TotalExpenses = paymentVouchers.Sum(pv => pv.TotalAmount),
                NetIncome = receiptVouchers.Sum(rv => rv.TotalAmount) - paymentVouchers.Sum(pv => pv.TotalAmount),
                StudentFeesCollected = studentPayments.Sum(sp => sp.AmountPaid),
                TotalReceiptVouchers = receiptVouchers.Count,
                TotalPaymentVouchers = paymentVouchers.Count,
                IncomeByCategory = receiptVouchers
                    .SelectMany(rv => rv.Details)
                    .GroupBy(d => d.Account.AccountName)
                    .Select(g => new CategoryAmountDto
                    {
                        Category = g.Key,
                        Amount = g.Sum(d => d.Amount)
                    }).ToList(),
                ExpensesByCategory = paymentVouchers
                    .SelectMany(pv => pv.Details)
                    .GroupBy(d => d.Account.AccountName)
                    .Select(g => new CategoryAmountDto
                    {
                        Category = g.Key,
                        Amount = g.Sum(d => d.Amount)
                    }).ToList()
            };
        }

        public async Task<AttendanceReportDto> GetAttendanceReportAsync(int classId, DateTime fromDate, DateTime toDate)
        {
            var classEntity = await _context.Classes
                .Include(c => c.AcademicGrade)
                .FirstOrDefaultAsync(c => c.Id == classId);

            if (classEntity == null) throw new KeyNotFoundException("Class not found");

            var students = await _context.StudentEnrollments
                .Include(e => e.Student)
                .Where(e => e.ClassId == classId && e.IsActive)
                .ToListAsync();

            var attendance = await _context.Attendances
                .Include(a => a.Subject)
                .Where(a => students.Select(s => s.StudentId).Contains(a.StudentId) &&
                           a.Date >= fromDate && a.Date <= toDate)
                .ToListAsync();

            return new AttendanceReportDto
            {
                ClassId = classId,
                ClassName = classEntity.Name,
                GradeName = classEntity.AcademicGrade.Name,
                FromDate = fromDate,
                ToDate = toDate,
                TotalStudents = students.Count,
                TotalSchoolDays = CalculateSchoolDays(fromDate, toDate),
                OverallAttendanceRate = CalculateAttendanceRate(attendance),
                StudentAttendance = students.Select(s => new StudentAttendanceReportDto
                {
                    StudentId = s.StudentId,
                    StudentName = $"{s.Student.FirstName} {s.Student.LastName}",
                    StudentNumber = s.Student.StudentNumber,
                    TotalPresent = attendance.Count(a => a.StudentId == s.StudentId && a.Status == "Present"),
                    TotalAbsent = attendance.Count(a => a.StudentId == s.StudentId && a.Status == "Absent"),
                    TotalLate = attendance.Count(a => a.StudentId == s.StudentId && a.Status == "Late"),
                    AttendanceRate = CalculateStudentAttendanceRate(attendance, s.StudentId)
                }).ToList()
            };
        }

        public async Task<ExamAnalysisReportDto> GetExamAnalysisReportAsync(int examId)
        {
            var exam = await _context.Exams
                .Include(e => e.Subject)
                .Include(e => e.Class)
                .Include(e => e.Questions)
                .ThenInclude(q => q.Options)
                .FirstOrDefaultAsync(e => e.Id == examId);

            if (exam == null) throw new KeyNotFoundException("Exam not found");

            var attempts = await _context.ExamAttempts
                .Include(ea => ea.Student)
                .Include(ea => ea.StudentAnswers)
                .Where(ea => ea.ExamId == examId && ea.Status == AttemptStatus.Completed)
                .ToListAsync();

            return new ExamAnalysisReportDto
            {
                ExamId = examId,
                ExamTitle = exam.Title,
                SubjectName = exam.Subject.Name,
                ClassName = exam.Class.Name,
                TotalQuestions = exam.Questions.Count,
                TotalAttempts = attempts.Count,
                AverageScore = attempts.Any() ? attempts.Average(a => (double)(a.Score ?? 0)) : 0,
                HighestScore = attempts.Any() ? attempts.Max(a => (double)(a.Score ?? 0)) : 0,
                LowestScore = attempts.Any() ? attempts.Min(a => (double)(a.Score ?? 0)) : 0,
                PassRate = attempts.Any() ? attempts.Count(a => a.IsPassed) * 100.0 / attempts.Count : 0,
                QuestionAnalysis = exam.Questions.Select(q => new QuestionAnalysisDto
                {
                    QuestionId = q.Id,
                    QuestionText = q.QuestionText,
                    CorrectAnswers = attempts.SelectMany(a => a.StudentAnswers)
                        .Count(sa => sa.QuestionId == q.Id && sa.IsCorrect),
                    TotalAnswers = attempts.SelectMany(a => a.StudentAnswers)
                        .Count(sa => sa.QuestionId == q.Id),
                    DifficultyLevel = CalculateQuestionDifficulty(q.Id, attempts)
                }).ToList()
            };
        }

        public async Task<byte[]> ExportReportToPdfAsync(string reportType, object reportData)
        {
            // Implementation for PDF export would go here
            // This would typically use a library like iTextSharp or similar
            await Task.CompletedTask;
            return Encoding.UTF8.GetBytes("PDF export not implemented yet");
        }

        public async Task<byte[]> ExportReportToExcelAsync(string reportType, object reportData)
        {
            // Implementation for Excel export would go here
            // This would typically use a library like EPPlus or similar
            await Task.CompletedTask;
            return Encoding.UTF8.GetBytes("Excel export not implemented yet");
        }

        private double CalculateAttendanceRate(List<Attendance> attendance)
        {
            if (!attendance.Any()) return 0;
            var present = attendance.Count(a => a.Status == "Present");
            return present * 100.0 / attendance.Count;
        }

        private double CalculateStudentAttendanceRate(List<Attendance> attendance, string studentId)
        {
            var studentAttendance = attendance.Where(a => a.StudentId == studentId).ToList();
            return CalculateAttendanceRate(studentAttendance);
        }

        private int CalculateSchoolDays(DateTime fromDate, DateTime toDate)
        {
            var days = 0;
            for (var date = fromDate; date <= toDate; date = date.AddDays(1))
            {
                if (date.DayOfWeek != DayOfWeek.Friday && date.DayOfWeek != DayOfWeek.Saturday)
                    days++;
            }
            return days;
        }

        private async Task<int> GetTotalStudentsForTeacher(string teacherId, int academicYearId)
        {
            var assignments = await _context.TeacherAssignments
                .Where(ta => ta.TeacherId == teacherId && ta.IsActive)
                .ToListAsync();

            var classIds = assignments.Select(a => a.ClassId).Distinct();

            return await _context.StudentEnrollments
                .Where(e => classIds.Contains(e.ClassId) && e.AcademicYearId == academicYearId)
                .Select(e => e.StudentId)
                .Distinct()
                .CountAsync();
        }

        private string CalculateQuestionDifficulty(int questionId, List<ExamAttempt> attempts)
        {
            var totalAnswers = attempts.SelectMany(a => a.StudentAnswers)
                .Count(sa => sa.QuestionId == questionId);
            
            if (totalAnswers == 0) return "Unknown";

            var correctAnswers = attempts.SelectMany(a => a.StudentAnswers)
                .Count(sa => sa.QuestionId == questionId && sa.IsCorrect);

            var correctRate = correctAnswers * 100.0 / totalAnswers;

            return correctRate switch
            {
                >= 80 => "Easy",
                >= 60 => "Medium",
                >= 40 => "Hard",
                _ => "Very Hard"
            };
        }
    }
}
