@page "/teacher/student-performance"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Teacher")]

<PageTitle>تحليل أداء الطلاب - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-chart-line text-primary me-2"></i>
                        تحليل أداء الطلاب
                    </h2>
                    <p class="text-muted mb-0">تحليل شامل لأداء الطلاب في الامتحانات الإلكترونية</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-outline-primary" @onclick="ExportReport">
                        <i class="fas fa-download me-2"></i>
                        تصدير التقرير
                    </button>
                    <button class="btn btn-outline-secondary" @onclick="RefreshData">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل بيانات الأداء...</p>
        </div>
    }
    else
    {
        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">الصف</label>
                                <select @bind="selectedClassId" @bind:after="FilterData" class="form-select">
                                    <option value="">جميع الصفوف</option>
                                    @if (classes != null)
                                    {
                                        @foreach (var classItem in classes)
                                        {
                                            <option value="@classItem.Id">@classItem.Name</option>
                                        }
                                    }
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">المادة</label>
                                <select @bind="selectedSubjectId" @bind:after="FilterData" class="form-select">
                                    <option value="">جميع المواد</option>
                                    @if (subjects != null)
                                    {
                                        @foreach (var subject in subjects)
                                        {
                                            <option value="@subject.Id">@subject.Name</option>
                                        }
                                    }
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الفترة الزمنية</label>
                                <select @bind="selectedPeriod" @bind:after="FilterData" class="form-select">
                                    <option value="week">الأسبوع الماضي</option>
                                    <option value="month">الشهر الماضي</option>
                                    <option value="quarter">الربع الماضي</option>
                                    <option value="year">السنة الماضية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" placeholder="اسم الطالب..." @bind="searchTerm" @bind:after="FilterData" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-lg bg-primary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-users fa-lg"></i>
                        </div>
                        <h3 class="text-primary mb-1">@filteredStudents.Count</h3>
                        <p class="text-muted mb-0">إجمالي الطلاب</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-lg bg-success text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-star fa-lg"></i>
                        </div>
                        <h3 class="text-success mb-1">@GetAverageScore().ToString("F1")</h3>
                        <p class="text-muted mb-0">متوسط الدرجات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-lg bg-info text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-percentage fa-lg"></i>
                        </div>
                        <h3 class="text-info mb-1">@GetPassRate().ToString("F1")%</h3>
                        <p class="text-muted mb-0">معدل النجاح</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-lg bg-warning text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-chart-line fa-lg"></i>
                        </div>
                        <h3 class="text-warning mb-1">@GetImprovementRate().ToString("F1")%</h3>
                        <p class="text-muted mb-0">معدل التحسن</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Charts -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2 text-primary"></i>
                            توزيع الدرجات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="grade-distribution">
                            @foreach (var grade in GetGradeDistribution())
                            {
                                var percentage = filteredStudents.Count > 0 ? (double)grade.Value / filteredStudents.Count * 100 : 0;
                                <div class="grade-item mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span class="fw-bold">@grade.Key</span>
                                        <span class="text-muted">@grade.Value طالب (@percentage.ToString("F1")%)</span>
                                    </div>
                                    <div class="progress" style="height: 10px;">
                                        <div class="progress-bar @GetGradeColor(grade.Key)" style="width: @percentage%"></div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2 text-success"></i>
                            اتجاه الأداء
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="performance-trend">
                            @if (performanceTrend?.Any() == true)
                            {
                                @foreach (var trend in performanceTrend.Take(5))
                                {
                                    <div class="trend-item d-flex justify-content-between align-items-center mb-3">
                                        <div>
                                            <strong>@trend.Period</strong>
                                            <br>
                                            <small class="text-muted">@trend.ExamCount امتحان</small>
                                        </div>
                                        <div class="text-end">
                                            <h5 class="mb-0 @(trend.AverageScore >= 70 ? "text-success" : trend.AverageScore >= 50 ? "text-warning" : "text-danger")">
                                                @trend.AverageScore.ToString("F1")%
                                            </h5>
                                            <small class="text-muted">متوسط</small>
                                        </div>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-chart-line fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">لا توجد بيانات كافية</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performers -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-trophy me-2 text-warning"></i>
                            أفضل الطلاب أداءً
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (topPerformers?.Any() == true)
                        {
                            @foreach (var student in topPerformers.Take(5))
                            {
                                <div class="top-performer-item d-flex align-items-center mb-3">
                                    <div class="avatar-sm bg-warning text-white rounded-circle me-3 d-flex align-items-center justify-content-center">
                                        @student.Name.Substring(0, 1)
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">@student.Name</h6>
                                        <small class="text-muted">@student.ClassName</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-success fs-6">@student.AverageScore.ToString("F1")%</span>
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="fas fa-trophy fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">لا توجد بيانات</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                            الطلاب المحتاجون للمساعدة
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (strugglingStudents?.Any() == true)
                        {
                            @foreach (var student in strugglingStudents.Take(5))
                            {
                                <div class="struggling-student-item d-flex align-items-center mb-3">
                                    <div class="avatar-sm bg-danger text-white rounded-circle me-3 d-flex align-items-center justify-content-center">
                                        @student.Name.Substring(0, 1)
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">@student.Name</h6>
                                        <small class="text-muted">@student.ClassName</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-danger fs-6">@student.AverageScore.ToString("F1")%</span>
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="fas fa-smile fa-2x text-success mb-2"></i>
                                <p class="text-success mb-0">جميع الطلاب يؤدون بشكل جيد!</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Students Table -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0">
                                    <i class="fas fa-table me-2 text-primary"></i>
                                    تفاصيل أداء الطلاب
                                </h5>
                            </div>
                            <div class="col-auto">
                                <div class="btn-group">
                                    <button class="btn btn-sm btn-outline-primary" @onclick="@(() => SortStudents("name"))">
                                        <i class="fas fa-sort-alpha-down me-1"></i>
                                        الاسم
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary" @onclick="@(() => SortStudents("score"))">
                                        <i class="fas fa-sort-numeric-down me-1"></i>
                                        الدرجة
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary" @onclick="@(() => SortStudents("improvement"))">
                                        <i class="fas fa-chart-line me-1"></i>
                                        التحسن
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (filteredStudents?.Any() == true)
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>الطالب</th>
                                            <th>الصف</th>
                                            <th>عدد الامتحانات</th>
                                            <th>متوسط الدرجات</th>
                                            <th>أعلى درجة</th>
                                            <th>أقل درجة</th>
                                            <th>معدل التحسن</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var student in filteredStudents)
                                        {
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center">
                                                            @student.Name.Substring(0, 1)
                                                        </div>
                                                        <div>
                                                            <strong>@student.Name</strong>
                                                            <br>
                                                            <small class="text-muted">@student.Email</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><span class="badge bg-info">@student.ClassName</span></td>
                                                <td>@student.ExamCount</td>
                                                <td>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar @GetScoreColor(student.AverageScore)" style="width: @student.AverageScore%">
                                                            @student.AverageScore.ToString("F1")%
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><span class="badge bg-success">@student.HighestScore.ToString("F1")%</span></td>
                                                <td><span class="badge bg-danger">@student.LowestScore.ToString("F1")%</span></td>
                                                <td>
                                                    <span class="badge @(student.ImprovementRate >= 0 ? "bg-success" : "bg-danger")">
                                                        @(student.ImprovementRate >= 0 ? "+" : "")@student.ImprovementRate.ToString("F1")%
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge @GetPerformanceStatusBadge(student.AverageScore)">
                                                        @GetPerformanceStatusText(student.AverageScore)
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewStudentDetails(student.Id)" title="التفاصيل">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-info" @onclick="() => ViewStudentProgress(student.Id)" title="التقدم">
                                                            <i class="fas fa-chart-line"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد بيانات</h5>
                                <p class="text-muted">لا توجد بيانات أداء للطلاب في الفترة المحددة</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .avatar-lg {
        width: 64px;
        height: 64px;
    }

    .avatar-sm {
        width: 32px;
        height: 32px;
    }

    .grade-item {
        padding: 10px;
        border-radius: 6px;
        background-color: #f8f9fa;
    }

    .trend-item {
        padding: 15px;
        border-radius: 8px;
        background-color: #f8f9fa;
        border-left: 4px solid #007bff;
    }

    .top-performer-item,
    .struggling-student-item {
        padding: 10px;
        border-radius: 6px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }

    .top-performer-item:hover,
    .struggling-student-item:hover {
        background-color: #e9ecef;
        transform: translateX(5px);
    }
</style>

@code {
    private List<StudentPerformanceDto> allStudents = new();
    private List<StudentPerformanceDto> filteredStudents = new();
    private List<StudentPerformanceDto>? topPerformers;
    private List<StudentPerformanceDto>? strugglingStudents;
    private List<PerformanceTrendDto>? performanceTrend;
    private List<ClassDto>? classes;
    private List<SubjectDto>? subjects;

    private bool isLoading = true;
    private string selectedClassId = "";
    private string selectedSubjectId = "";
    private string selectedPeriod = "month";
    private string searchTerm = "";
    private string sortBy = "name";
    private bool sortDescending = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            await Task.WhenAll(
                LoadStudentPerformance(),
                LoadClasses(),
                LoadSubjects(),
                LoadPerformanceTrend()
            );

            FilterData();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadStudentPerformance()
    {
        try
        {
            var queryParams = new Dictionary<string, string?>
            {
                ["period"] = selectedPeriod,
                ["classId"] = string.IsNullOrEmpty(selectedClassId) ? null : selectedClassId,
                ["subjectId"] = string.IsNullOrEmpty(selectedSubjectId) ? null : selectedSubjectId
            };

            allStudents = await ApiService.GetStudentPerformanceAsync(queryParams) ?? new List<StudentPerformanceDto>();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading student performance: {ex.Message}");
        }
    }

    private async Task LoadClasses()
    {
        try
        {
            var classesList = await ApiService.GetClassesAsync();
            classes = classesList?.ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading classes: {ex.Message}");
        }
    }

    private async Task LoadSubjects()
    {
        try
        {
            var subjectsList = await ApiService.GetSubjectsAsync();
            subjects = subjectsList?.ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading subjects: {ex.Message}");
        }
    }

    private async Task LoadPerformanceTrend()
    {
        try
        {
            performanceTrend = await ApiService.GetPerformanceTrendAsync(selectedPeriod);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading performance trend: {ex.Message}");
        }
    }

    private void FilterData()
    {
        filteredStudents = allStudents.Where(s =>
            (string.IsNullOrEmpty(selectedClassId) || s.ClassId.ToString() == selectedClassId) &&
            (string.IsNullOrEmpty(selectedSubjectId) || s.SubjectIds.Contains(int.Parse(selectedSubjectId))) &&
            (string.IsNullOrEmpty(searchTerm) || s.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
        ).ToList();

        SortStudents(sortBy);
        UpdateTopAndStrugglingStudents();
        StateHasChanged();
    }

    private void SortStudents(string sortField)
    {
        if (sortBy == sortField)
        {
            sortDescending = !sortDescending;
        }
        else
        {
            sortBy = sortField;
            sortDescending = false;
        }

        filteredStudents = sortField switch
        {
            "name" => sortDescending
                ? filteredStudents.OrderByDescending(s => s.Name).ToList()
                : filteredStudents.OrderBy(s => s.Name).ToList(),
            "score" => sortDescending
                ? filteredStudents.OrderByDescending(s => s.AverageScore).ToList()
                : filteredStudents.OrderBy(s => s.AverageScore).ToList(),
            "improvement" => sortDescending
                ? filteredStudents.OrderByDescending(s => s.ImprovementRate).ToList()
                : filteredStudents.OrderBy(s => s.ImprovementRate).ToList(),
            _ => filteredStudents.OrderBy(s => s.Name).ToList()
        };

        StateHasChanged();
    }

    private void UpdateTopAndStrugglingStudents()
    {
        topPerformers = filteredStudents
            .Where(s => s.AverageScore >= 80)
            .OrderByDescending(s => s.AverageScore)
            .ToList();

        strugglingStudents = filteredStudents
            .Where(s => s.AverageScore < 50)
            .OrderBy(s => s.AverageScore)
            .ToList();
    }

    private async Task RefreshData()
    {
        await LoadData();
        await JSRuntime.InvokeVoidAsync("alert", "تم تحديث البيانات بنجاح");
    }

    private async Task ExportReport()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة تصدير التقرير ستكون متاحة قريباً");
    }

    private void ViewStudentDetails(int studentId)
    {
        Navigation.NavigateTo($"/teacher/student-details/{studentId}");
    }

    private void ViewStudentProgress(int studentId)
    {
        Navigation.NavigateTo($"/teacher/student-progress/{studentId}");
    }

    private double GetAverageScore()
    {
        return filteredStudents.Any() ? filteredStudents.Average(s => s.AverageScore) : 0;
    }

    private double GetPassRate()
    {
        if (!filteredStudents.Any()) return 0;
        var passedCount = filteredStudents.Count(s => s.AverageScore >= 50);
        return (double)passedCount / filteredStudents.Count * 100;
    }

    private double GetImprovementRate()
    {
        if (!filteredStudents.Any()) return 0;
        var improvingCount = filteredStudents.Count(s => s.ImprovementRate > 0);
        return (double)improvingCount / filteredStudents.Count * 100;
    }

    private Dictionary<string, int> GetGradeDistribution()
    {
        var distribution = new Dictionary<string, int>
        {
            ["ممتاز (90-100)"] = filteredStudents.Count(s => s.AverageScore >= 90),
            ["جيد جداً (80-89)"] = filteredStudents.Count(s => s.AverageScore >= 80 && s.AverageScore < 90),
            ["جيد (70-79)"] = filteredStudents.Count(s => s.AverageScore >= 70 && s.AverageScore < 80),
            ["مقبول (60-69)"] = filteredStudents.Count(s => s.AverageScore >= 60 && s.AverageScore < 70),
            ["ضعيف (50-59)"] = filteredStudents.Count(s => s.AverageScore >= 50 && s.AverageScore < 60),
            ["راسب (أقل من 50)"] = filteredStudents.Count(s => s.AverageScore < 50)
        };

        return distribution.Where(d => d.Value > 0).ToDictionary(d => d.Key, d => d.Value);
    }

    private string GetGradeColor(string grade)
    {
        return grade switch
        {
            var g when g.Contains("ممتاز") => "bg-success",
            var g when g.Contains("جيد جداً") => "bg-info",
            var g when g.Contains("جيد") => "bg-primary",
            var g when g.Contains("مقبول") => "bg-warning",
            var g when g.Contains("ضعيف") => "bg-secondary",
            var g when g.Contains("راسب") => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetScoreColor(double score)
    {
        return score switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-info",
            >= 70 => "bg-primary",
            >= 60 => "bg-warning",
            >= 50 => "bg-secondary",
            _ => "bg-danger"
        };
    }

    private string GetPerformanceStatusBadge(double score)
    {
        return score switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-info",
            >= 70 => "bg-primary",
            >= 60 => "bg-warning",
            >= 50 => "bg-secondary",
            _ => "bg-danger"
        };
    }

    private string GetPerformanceStatusText(double score)
    {
        return score switch
        {
            >= 90 => "ممتاز",
            >= 80 => "جيد جداً",
            >= 70 => "جيد",
            >= 60 => "مقبول",
            >= 50 => "ضعيف",
            _ => "راسب"
        };
    }
}
