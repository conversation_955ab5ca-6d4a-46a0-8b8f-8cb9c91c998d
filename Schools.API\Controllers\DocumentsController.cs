using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Schools.Shared.DTOs;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class DocumentsController : ControllerBase
    {
        private readonly ILogger<DocumentsController> _logger;

        public DocumentsController(ILogger<DocumentsController> logger)
        {
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<DocumentDto>>> GetDocuments()
        {
            try
            {
                // Mock data for now
                var documents = new List<DocumentDto>
                {
                    new() { Id = 1, Name = "دليل الطالب 2024.pdf", FileType = "pdf", Category = "academic", Size = 2048576, UploadDate = DateTime.Now.AddDays(-5), UploadedBy = "Admin", ViewCount = 45, DownloadCount = 12, IsPublic = true, Description = "دليل شامل للطلاب الجدد" },
                    new() { Id = 2, Name = "نموذج طلب إجازة.docx", FileType = "doc", Category = "forms", Size = 524288, UploadDate = DateTime.Now.AddDays(-3), UploadedBy = "Admin", ViewCount = 23, DownloadCount = 8, IsPublic = true, Description = "نموذج طلب إجازة للموظفين" },
                    new() { Id = 3, Name = "التقرير المالي الشهري.xlsx", FileType = "xls", Category = "financial", Size = 1048576, UploadDate = DateTime.Now.AddDays(-2), UploadedBy = "Admin", ViewCount = 67, DownloadCount = 15, IsPublic = true, Description = "التقرير المالي لشهر ديسمبر" },
                    new() { Id = 4, Name = "صورة المدرسة.jpg", FileType = "img", Category = "administrative", Size = 3145728, UploadDate = DateTime.Now.AddDays(-1), UploadedBy = "Admin", ViewCount = 89, DownloadCount = 5, IsPublic = true, Description = "صورة رسمية للمدرسة" },
                    new() { Id = 5, Name = "فيديو تعريفي.mp4", FileType = "video", Category = "academic", Size = 52428800, UploadDate = DateTime.Now.AddHours(-6), UploadedBy = "Admin", ViewCount = 156, DownloadCount = 23, IsPublic = true, Description = "فيديو تعريفي بالمدرسة" },
                    new() { Id = 6, Name = "لائحة الامتحانات.pdf", FileType = "pdf", Category = "academic", Size = 1572864, UploadDate = DateTime.Now.AddHours(-3), UploadedBy = "Admin", ViewCount = 78, DownloadCount = 19, IsPublic = true, Description = "لائحة تنظيم الامتحانات" },
                    new() { Id = 7, Name = "تقرير الحضور.xlsx", FileType = "xls", Category = "reports", Size = 786432, UploadDate = DateTime.Now.AddHours(-1), UploadedBy = "Admin", ViewCount = 34, DownloadCount = 7, IsPublic = true, Description = "تقرير حضور الطلاب الشهري" }
                };

                return Ok(documents);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving documents");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<DocumentDto>> GetDocument(int id)
        {
            try
            {
                var documents = await GetDocuments();
                var document = ((OkObjectResult)documents.Result!)?.Value as List<DocumentDto>;
                var found = document?.FirstOrDefault(d => d.Id == id);
                
                if (found == null)
                    return NotFound();

                return Ok(found);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving document {DocumentId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("statistics")]
        public async Task<ActionResult<DocumentStatisticsDto>> GetStatistics()
        {
            try
            {
                var statistics = new DocumentStatisticsDto
                {
                    TotalDocuments = 25,
                    TotalSize = 52428800,
                    TotalDownloads = 156,
                    TotalViews = 423,
                    CategoryDistribution = new Dictionary<string, int> 
                    { 
                        { "academic", 15 }, 
                        { "forms", 10 }, 
                        { "financial", 8 }, 
                        { "administrative", 5 }, 
                        { "reports", 7 } 
                    },
                    TypeDistribution = new Dictionary<string, int> 
                    { 
                        { "pdf", 18 }, 
                        { "doc", 7 }, 
                        { "xls", 5 }, 
                        { "img", 3 }, 
                        { "video", 2 } 
                    },
                    TypeSizeDistribution = new Dictionary<string, long> 
                    { 
                        { "pdf", 30000000 }, 
                        { "doc", 15000000 }, 
                        { "xls", 8000000 }, 
                        { "img", 12000000 }, 
                        { "video", 45000000 } 
                    }
                };

                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving document statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("cleanup")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<CleanupResultDto>> CleanupOldDocuments([FromBody] CleanupRequestDto request)
        {
            try
            {
                // Mock cleanup operation
                var deletedCount = Math.Min(request.MonthsOld * 2, 10);
                var freedSpace = deletedCount * 2097152; // 2MB per file

                var result = new CleanupResultDto
                {
                    DeletedCount = deletedCount,
                    FreedSpace = freedSpace,
                    Message = $"Successfully deleted {deletedCount} old documents and freed {FormatFileSize(freedSpace)}"
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during cleanup operation");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("upload")]
        public async Task<ActionResult<DocumentDto>> UploadDocument()
        {
            try
            {
                // Mock upload - return a new document
                var newDocument = new DocumentDto
                {
                    Id = new Random().Next(100, 999),
                    Name = "ملف جديد.pdf",
                    FileType = "pdf",
                    Category = "general",
                    Size = 1024000,
                    UploadDate = DateTime.Now,
                    UploadedBy = User.Identity?.Name ?? "System",
                    ViewCount = 0,
                    DownloadCount = 0,
                    IsPublic = true,
                    Description = "ملف تم رفعه حديثاً",
                    FilePath = "/uploads/documents/new-file.pdf"
                };

                return CreatedAtAction(nameof(GetDocument), new { id = newDocument.Id }, newDocument);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading document");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteDocument(int id)
        {
            try
            {
                // Mock delete operation
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting document {DocumentId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
