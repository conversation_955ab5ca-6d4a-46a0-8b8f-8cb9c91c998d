using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.Models;
using Schools.Shared.DTOs;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ActivitiesController : ControllerBase
    {
        private readonly SchoolsDbContext _context;
        private readonly ILogger<ActivitiesController> _logger;

        public ActivitiesController(SchoolsDbContext context, ILogger<ActivitiesController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<ActivityDto>>> GetActivities(
            [FromQuery] string? type = null,
            [FromQuery] string? status = null,
            [FromQuery] DateTime? date = null,
            [FromQuery] string? search = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var query = _context.Activities
                    .Include(a => a.Organizer)
                    .Include(a => a.Participants)
                    .AsQueryable();

                if (!string.IsNullOrEmpty(type))
                    query = query.Where(a => a.Type == type);

                if (!string.IsNullOrEmpty(status) && Enum.TryParse<ActivityStatus>(status, out var statusEnum))
                    query = query.Where(a => a.Status == statusEnum);

                if (date.HasValue)
                    query = query.Where(a => a.StartDate.Date == date.Value.Date);

                if (!string.IsNullOrEmpty(search))
                    query = query.Where(a => a.Title.Contains(search) ||
                                           a.Description.Contains(search));

                var totalCount = await query.CountAsync();

                var activities = await query
                    .OrderBy(a => a.StartDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(a => new ActivityDto
                    {
                        Id = a.Id,
                        Title = a.Title,
                        Description = a.Description,
                        Type = a.Type,
                        Status = a.Status.ToString(),
                        StartDate = a.StartDate,
                        EndDate = a.EndDate,
                        StartTime = a.StartTime.ToString(@"hh\:mm"),
                        EndTime = a.EndTime.ToString(@"hh\:mm"),
                        Location = a.Location,
                        OrganizerId = a.OrganizerId,
                        OrganizerName = a.Organizer.FirstName + " " + a.Organizer.LastName,
                        MaxParticipants = a.MaxParticipants,
                        ParticipantsCount = a.Participants.Count,
                        Requirements = a.Requirements,
                        Notes = a.Notes
                    })
                    .ToListAsync();

                Response.Headers.Append("X-Total-Count", totalCount.ToString());
                return Ok(activities);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving activities");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ActivityDto>> GetActivity(int id)
        {
            try
            {
                var activity = await _context.Activities
                    .Include(a => a.Organizer)
                    .Include(a => a.Participants)
                    .ThenInclude(p => p.Student)
                    .FirstOrDefaultAsync(a => a.Id == id);

                if (activity == null)
                    return NotFound();

                var activityDto = new ActivityDto
                {
                    Id = activity.Id,
                    Title = activity.Title,
                    Description = activity.Description,
                    Type = activity.Type,
                    Status = activity.Status.ToString(),
                    StartDate = activity.StartDate,
                    EndDate = activity.EndDate,
                    StartTime = activity.StartTime.ToString(@"hh\:mm"),
                    EndTime = activity.EndTime.ToString(@"hh\:mm"),
                    Location = activity.Location,
                    OrganizerId = activity.OrganizerId,
                    OrganizerName = activity.Organizer.FirstName + " " + activity.Organizer.LastName,
                    MaxParticipants = activity.MaxParticipants,
                    ParticipantsCount = activity.Participants.Count,
                    Requirements = activity.Requirements,
                    Notes = activity.Notes,
                    Participants = activity.Participants.Select(p => new ActivityParticipantDto
                    {
                        Id = p.Id,
                        ActivityId = p.ActivityId,
                        StudentId = p.StudentId,
                        StudentName = p.Student.FirstName + " " + p.Student.LastName,
                        RegistrationDate = p.RegistrationDate,
                        Status = p.Status
                    }).ToList()
                };

                return Ok(activityDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving activity {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<ActionResult<ActivityDto>> CreateActivity(CreateActivityDto createActivityDto)
        {
            try
            {
                var activity = new Activity
                {
                    Title = createActivityDto.Title,
                    Description = createActivityDto.Description,
                    Type = createActivityDto.Type,
                    Status = ActivityStatus.Planned,
                    StartDate = createActivityDto.StartDate,
                    EndDate = createActivityDto.EndDate,
                    StartTime = createActivityDto.StartTime,
                    EndTime = createActivityDto.EndTime,
                    Location = createActivityDto.Location,
                    OrganizerId = createActivityDto.OrganizerId,
                    MaxParticipants = createActivityDto.MaxParticipants,
                    Requirements = createActivityDto.Requirements,
                    Notes = createActivityDto.Notes,
                    CreatedAt = DateTime.UtcNow
                };

                _context.Activities.Add(activity);
                await _context.SaveChangesAsync();

                // Reload with includes
                activity = await _context.Activities
                    .Include(a => a.Organizer)
                    .Include(a => a.Participants)
                    .FirstAsync(a => a.Id == activity.Id);

                var activityDto = new ActivityDto
                {
                    Id = activity.Id,
                    Title = activity.Title,
                    Description = activity.Description,
                    Type = activity.Type,
                    Status = activity.Status.ToString(),
                    StartDate = activity.StartDate,
                    EndDate = activity.EndDate,
                    StartTime = activity.StartTime.ToString(@"hh\:mm"),
                    EndTime = activity.EndTime.ToString(@"hh\:mm"),
                    Location = activity.Location,
                    OrganizerId = activity.OrganizerId,
                    OrganizerName = activity.Organizer.FirstName + " " + activity.Organizer.LastName,
                    MaxParticipants = activity.MaxParticipants,
                    ParticipantsCount = activity.Participants.Count,
                    Requirements = activity.Requirements,
                    Notes = activity.Notes
                };

                return CreatedAtAction(nameof(GetActivity), new { id = activity.Id }, activityDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating activity");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<IActionResult> UpdateActivity(int id, UpdateActivityDto updateActivityDto)
        {
            try
            {
                var activity = await _context.Activities.FindAsync(id);
                if (activity == null)
                    return NotFound();

                activity.Title = updateActivityDto.Title;
                activity.Description = updateActivityDto.Description;
                activity.Type = updateActivityDto.Type;
                activity.StartDate = updateActivityDto.StartDate;
                activity.EndDate = updateActivityDto.EndDate;
                activity.StartTime = updateActivityDto.StartTime;
                activity.EndTime = updateActivityDto.EndTime;
                activity.Location = updateActivityDto.Location;
                activity.MaxParticipants = updateActivityDto.MaxParticipants;
                activity.Requirements = updateActivityDto.Requirements;
                activity.Notes = updateActivityDto.Notes;
                activity.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating activity {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("{id}/status")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<IActionResult> UpdateActivityStatus(int id, UpdateActivityStatusDto statusDto)
        {
            try
            {
                var activity = await _context.Activities.FindAsync(id);
                if (activity == null)
                    return NotFound();

                if (Enum.TryParse<ActivityStatus>(statusDto.Status, out var status))
                    activity.Status = status;
                activity.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating activity status {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteActivity(int id)
        {
            try
            {
                var activity = await _context.Activities
                    .Include(a => a.Participants)
                    .FirstOrDefaultAsync(a => a.Id == id);

                if (activity == null)
                    return NotFound();

                // Check if activity has started
                if (activity.Status == ActivityStatus.Active || activity.Status == ActivityStatus.Completed)
                    return BadRequest("Cannot delete an activity that has started or completed");

                _context.Activities.Remove(activity);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting activity {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("{id}/participants")]
        [Authorize(Roles = "Admin,Teacher,Student")]
        public async Task<ActionResult<ActivityParticipantDto>> RegisterParticipant(int id, RegisterParticipantDto registerDto)
        {
            try
            {
                var activity = await _context.Activities
                    .Include(a => a.Participants)
                    .FirstOrDefaultAsync(a => a.Id == id);

                if (activity == null)
                    return NotFound("Activity not found");

                if (activity.Status != ActivityStatus.Planned)
                    return BadRequest("Cannot register for this activity");

                if (activity.Participants.Count >= activity.MaxParticipants)
                    return BadRequest("Activity is full");

                // Check if student is already registered
                var existingParticipant = await _context.ActivityParticipants
                    .FirstOrDefaultAsync(ap => ap.ActivityId == id && ap.StudentId == registerDto.StudentId);

                if (existingParticipant != null)
                    return BadRequest("Student is already registered for this activity");

                var participant = new ActivityParticipant
                {
                    ActivityId = id,
                    StudentId = registerDto.StudentId,
                    RegistrationDate = DateTime.Now,
                    Status = "Registered",
                    CreatedAt = DateTime.UtcNow
                };

                _context.ActivityParticipants.Add(participant);
                await _context.SaveChangesAsync();

                // Reload with student info
                participant = await _context.ActivityParticipants
                    .Include(ap => ap.Student)
                    .FirstAsync(ap => ap.Id == participant.Id);

                var participantDto = new ActivityParticipantDto
                {
                    Id = participant.Id,
                    ActivityId = participant.ActivityId,
                    StudentId = participant.StudentId,
                    StudentName = participant.Student.FirstName + " " + participant.Student.LastName,
                    RegistrationDate = participant.RegistrationDate,
                    Status = participant.Status
                };

                return Ok(participantDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering participant for activity {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("{id}/participants/{participantId}")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<IActionResult> RemoveParticipant(int id, int participantId)
        {
            try
            {
                var participant = await _context.ActivityParticipants
                    .FirstOrDefaultAsync(ap => ap.Id == participantId && ap.ActivityId == id);

                if (participant == null)
                    return NotFound();

                _context.ActivityParticipants.Remove(participant);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing participant {ParticipantId} from activity {Id}", participantId, id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("statistics")]
        public async Task<ActionResult<ActivityStatisticsDto>> GetActivityStatistics()
        {
            try
            {
                var totalActivities = await _context.Activities.CountAsync();
                var upcomingActivities = await _context.Activities.CountAsync(a => a.Status == ActivityStatus.Planned);
                var activeActivities = await _context.Activities.CountAsync(a => a.Status == ActivityStatus.Active);
                var completedActivities = await _context.Activities.CountAsync(a => a.Status == ActivityStatus.Completed);
                var totalParticipants = await _context.ActivityParticipants.CountAsync();

                var typeStats = await _context.Activities
                    .GroupBy(a => a.Type)
                    .Select(g => new ActivityTypeStatDto
                    {
                        Type = g.Key,
                        Count = g.Count()
                    })
                    .ToListAsync();

                var statistics = new ActivityStatisticsDto
                {
                    TotalActivities = totalActivities,
                    UpcomingActivities = upcomingActivities,
                    ActiveActivities = activeActivities,
                    CompletedActivities = completedActivities,
                    TotalParticipants = totalParticipants,
                    TypeStatistics = typeStats
                };

                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving activity statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("calendar")]
        public async Task<ActionResult<IEnumerable<ActivityCalendarDto>>> GetActivityCalendar(
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            try
            {
                var activities = await _context.Activities
                    .Where(a => a.StartDate >= startDate && a.StartDate <= endDate)
                    .Select(a => new ActivityCalendarDto
                    {
                        Id = a.Id,
                        Title = a.Title,
                        Type = a.Type,
                        Status = a.Status.ToString(),
                        StartDate = a.StartDate,
                        EndDate = a.EndDate,
                        StartTime = a.StartTime,
                        EndTime = a.EndTime,
                        Location = a.Location
                    })
                    .ToListAsync();

                return Ok(activities);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving activity calendar");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
