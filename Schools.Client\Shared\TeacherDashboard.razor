@using Schools.Client.Services
@using Schools.Shared.DTOs
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<div class="container-fluid py-4">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-success text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="text-white mb-1">مرحباً أستاذ محمد أحمد</h2>
                            <p class="text-white-75 mb-0">لوحة تحكم المعلم - اليوم @DateTime.Now.ToString("dddd، dd MMMM yyyy", new System.Globalization.CultureInfo("ar-SA"))</p>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chalkboard-teacher fa-3x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">الصفوف المُدرسة</div>
                            <div class="text-lg fw-bold">5</div>
                        </div>
                        <i class="fas fa-school fa-2x text-white-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="#" @onclick="@(() => NavigateToPage("/teacher/classes"))">عرض الصفوف</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">إجمالي الطلاب</div>
                            <div class="text-lg fw-bold">156</div>
                        </div>
                        <i class="fas fa-user-graduate fa-2x text-white-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="#" @onclick="@(() => NavigateToPage("/teacher/students"))">إدارة الطلاب</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">الواجبات المعلقة</div>
                            <div class="text-lg fw-bold">12</div>
                        </div>
                        <i class="fas fa-tasks fa-2x text-white-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="#" @onclick="@(() => NavigateToPage("/teacher/assignments"))">إدارة الواجبات</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">الامتحانات القادمة</div>
                            <div class="text-lg fw-bold">3</div>
                        </div>
                        <i class="fas fa-clipboard-list fa-2x text-white-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="#" @onclick="@(() => NavigateToPage("/teacher/exams"))">إدارة الامتحانات</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Schedule -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-day me-2"></i>
                        جدول اليوم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الوقت</th>
                                    <th>المادة</th>
                                    <th>الصف</th>
                                    <th>القاعة</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>8:00 - 8:45</td>
                                    <td>الرياضيات</td>
                                    <td>الصف الثالث أ</td>
                                    <td>قاعة 101</td>
                                    <td><span class="badge bg-success">مكتملة</span></td>
                                </tr>
                                <tr>
                                    <td>9:00 - 9:45</td>
                                    <td>الرياضيات</td>
                                    <td>الصف الثالث ب</td>
                                    <td>قاعة 102</td>
                                    <td><span class="badge bg-primary">جارية</span></td>
                                </tr>
                                <tr>
                                    <td>10:15 - 11:00</td>
                                    <td>الرياضيات</td>
                                    <td>الصف الثاني أ</td>
                                    <td>قاعة 103</td>
                                    <td><span class="badge bg-warning">قادمة</span></td>
                                </tr>
                                <tr>
                                    <td>11:15 - 12:00</td>
                                    <td>الرياضيات</td>
                                    <td>الصف الثاني ب</td>
                                    <td>قاعة 104</td>
                                    <td><span class="badge bg-secondary">قادمة</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bell me-2"></i>
                        التذكيرات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-circle text-warning"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">امتحان الرياضيات</h6>
                                    <p class="mb-1 small text-muted">غداً الساعة 10:00 صباحاً</p>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-tasks text-primary"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">تصحيح الواجبات</h6>
                                    <p class="mb-1 small text-muted">12 واجب في انتظار التصحيح</p>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-calendar text-success"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">اجتماع أولياء الأمور</h6>
                                    <p class="mb-1 small text-muted">الخميس القادم</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-primary w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/teacher/attendance"))">
                                <i class="fas fa-user-check fa-2x mb-2"></i>
                                <br>تسجيل الحضور
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-success w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/teacher/bulk-grades"))">
                                <i class="fas fa-table fa-2x mb-2"></i>
                                <br>إدخال درجات متعددة
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-info w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/teacher/assignments/new"))">
                                <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                <br>إضافة واجب جديد
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-warning w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/teacher/messages"))">
                                <i class="fas fa-envelope fa-2x mb-2"></i>
                                <br>الرسائل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        النشاط الأخير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم تسجيل حضور الصف الثالث أ</h6>
                                <p class="mb-1 text-muted small">منذ ساعة واحدة</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم إدخال درجات امتحان الرياضيات</h6>
                                <p class="mb-1 text-muted small">منذ 3 ساعات</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم إضافة واجب جديد للصف الثاني</h6>
                                <p class="mb-1 text-muted small">أمس</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }

    .timeline-marker {
        position: absolute;
        left: -35px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }

    .timeline-item:not(:last-child)::before {
        content: '';
        position: absolute;
        left: -30px;
        top: 17px;
        width: 2px;
        height: calc(100% + 5px);
        background-color: #dee2e6;
    }
</style>

@code {
    private void NavigateToPage(string url)
    {
        Navigation.NavigateTo(url);
    }
}
