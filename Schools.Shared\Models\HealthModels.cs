using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Schools.Shared.Models;

namespace Schools.Shared.Models
{
    // Health Record Types Enum
    public enum HealthRecordType
    {
        Checkup = 1,
        Illness = 2,
        Injury = 3,
        Emergency = 4,
        Vaccination = 5,
        MedicalExamination = 6
    }

    // Medical Condition Severity Enum
    public enum MedicalConditionSeverity
    {
        Mild = 1,
        Moderate = 2,
        Severe = 3,
        Critical = 4
    }

    // Health Alert Priority Enum
    public enum HealthAlertPriority
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }

    // Health Alert Type Enum
    public enum HealthAlertType
    {
        VaccinationDue = 1,
        FollowUpRequired = 2,
        MedicationReminder = 3,
        MedicalCheckup = 4,
        EmergencyContact = 5
    }

    // Health Record Entity
    public class HealthRecord : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public DateTime Date { get; set; }

        [Required]
        public HealthRecordType Type { get; set; }

        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Diagnosis { get; set; }

        [StringLength(1000)]
        public string? Treatment { get; set; }

        [Column(TypeName = "nvarchar(max)")]
        public string? Medications { get; set; } // JSON array of medications

        public bool FollowUpRequired { get; set; } = false;

        public DateTime? FollowUpDate { get; set; }

        [Required]
        public string NurseId { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Notes { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual ApplicationUser Nurse { get; set; } = null!;
    }

    // Vaccination Record Entity
    public class VaccinationRecord : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string VaccineName { get; set; } = string.Empty;

        [Required]
        public DateTime Date { get; set; }

        [Required]
        public int DoseNumber { get; set; }

        public DateTime? NextDueDate { get; set; }

        [Required]
        [StringLength(200)]
        public string AdministeredBy { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Location { get; set; } = string.Empty;

        [StringLength(100)]
        public string? BatchNumber { get; set; }

        [StringLength(200)]
        public string? Manufacturer { get; set; }

        [Column(TypeName = "nvarchar(max)")]
        public string? SideEffects { get; set; } // JSON array of side effects

        [StringLength(1000)]
        public string? Notes { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
    }

    // Medical Condition Entity
    public class MedicalCondition : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Condition { get; set; } = string.Empty;

        [Required]
        public MedicalConditionSeverity Severity { get; set; }

        [Column(TypeName = "nvarchar(max)")]
        public string? Medications { get; set; } // JSON array of medications

        [Required]
        [StringLength(200)]
        public string EmergencyContact { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? SpecialInstructions { get; set; }

        [Required]
        public DateTime DiagnosisDate { get; set; }

        [Required]
        [StringLength(200)]
        public string DoctorName { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
    }

    // Health Alert Entity
    public class HealthAlert : BaseEntity
    {
        [Required]
        public HealthAlertType Type { get; set; }

        [Required]
        public HealthAlertPriority Priority { get; set; }

        [Required]
        [StringLength(500)]
        public string Message { get; set; } = string.Empty;

        public int StudentCount { get; set; } = 0;

        [Required]
        public DateTime DueDate { get; set; }

        public bool IsResolved { get; set; } = false;

        public DateTime? ResolvedDate { get; set; }

        public string? ResolvedBy { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public bool IsActive { get; set; } = true;
    }

    // Health Report Entity
    public class HealthReport : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string ReportType { get; set; } = string.Empty;

        [Required]
        public DateTime GeneratedDate { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        public int TotalRecords { get; set; } = 0;

        [StringLength(1000)]
        public string Summary { get; set; } = string.Empty;

        [Column(TypeName = "nvarchar(max)")]
        public string? Data { get; set; } // JSON data

        [Required]
        public string GeneratedBy { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ApplicationUser GeneratedByUser { get; set; } = null!;
    }
}
