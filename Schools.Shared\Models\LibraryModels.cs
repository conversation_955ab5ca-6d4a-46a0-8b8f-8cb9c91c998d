using System.ComponentModel.DataAnnotations;

namespace Schools.Shared.Models
{
    // Book
    public class Book : BaseEntity
    {
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string Author { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string ISBN { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Category { get; set; } = string.Empty;

        [Required]
        public BookStatus Status { get; set; } = BookStatus.Available;

        [StringLength(20)]
        public string? Grade { get; set; }

        public int PublishYear { get; set; }

        public int Pages { get; set; }

        [StringLength(100)]
        public string Publisher { get; set; } = string.Empty;

        public string? Description { get; set; }

        [StringLength(50)]
        public string? Location { get; set; }

        [StringLength(50)]
        public string? ShelfNumber { get; set; }

        public decimal? Price { get; set; }

        public DateTime? PurchaseDate { get; set; }

        public string? Language { get; set; }

        public string? CoverImageUrl { get; set; }

        public int? Quantity { get; set; } = 1;

        public int? AvailableQuantity { get; set; } = 1;

        public bool IsDigital { get; set; } = false;

        public string? DigitalFileUrl { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<BorrowRecord> BorrowRecords { get; set; } = new List<BorrowRecord>();
        public virtual ICollection<BookReservation> BookReservations { get; set; } = new List<BookReservation>();
        public virtual ICollection<BookReview> BookReviews { get; set; } = new List<BookReview>();
    }

    // Borrow Record
    public class BorrowRecord : BaseEntity
    {
        [Required]
        public int BookId { get; set; }

        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public DateTime BorrowDate { get; set; }

        [Required]
        public DateTime DueDate { get; set; }

        public DateTime? ReturnDate { get; set; }

        public BorrowStatus Status { get; set; } = BorrowStatus.Active;

        public decimal? FineAmount { get; set; }

        public bool FinePaid { get; set; } = false;

        public string? Notes { get; set; }

        public string? IssuedBy { get; set; }

        public string? ReturnedTo { get; set; }

        public string? Condition { get; set; } // Good, Damaged, Lost

        public int? RenewalCount { get; set; } = 0;

        // Navigation properties
        public virtual Book Book { get; set; } = null!;
        public virtual ApplicationUser Student { get; set; } = null!;
    }

    // Book Reservation
    public class BookReservation : BaseEntity
    {
        [Required]
        public int BookId { get; set; }

        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public DateTime ReservationDate { get; set; }

        public DateTime? ExpiryDate { get; set; }

        public ReservationStatus Status { get; set; } = ReservationStatus.Active;

        public string? Notes { get; set; }

        public DateTime? NotifiedAt { get; set; }

        // Navigation properties
        public virtual Book Book { get; set; } = null!;
        public virtual ApplicationUser Student { get; set; } = null!;
    }

    // Book Review
    public class BookReview : BaseEntity
    {
        [Required]
        public int BookId { get; set; }

        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        [Range(1, 5)]
        public int Rating { get; set; }

        public string? Review { get; set; }

        public DateTime ReviewDate { get; set; } = DateTime.UtcNow;

        public bool IsApproved { get; set; } = false;

        public string? ApprovedBy { get; set; }

        // Navigation properties
        public virtual Book Book { get; set; } = null!;
        public virtual ApplicationUser Student { get; set; } = null!;
    }

    // Library Card
    public class LibraryCard : BaseEntity
    {
        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string CardNumber { get; set; } = string.Empty;

        [Required]
        public DateTime IssueDate { get; set; }

        public DateTime? ExpiryDate { get; set; }

        public LibraryCardStatus Status { get; set; } = LibraryCardStatus.Active;

        public int MaxBooksAllowed { get; set; } = 3;

        public int BorrowDurationDays { get; set; } = 14;

        public decimal? SecurityDeposit { get; set; }

        public string? Notes { get; set; }

        // Navigation properties
        public virtual ApplicationUser User { get; set; } = null!;
    }

    // Digital Resource
    public class DigitalResource : BaseEntity
    {
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public DigitalResourceType Type { get; set; }

        [StringLength(100)]
        public string? Author { get; set; }

        [StringLength(50)]
        public string? Category { get; set; }

        public string? Description { get; set; }

        [Required]
        public string FileUrl { get; set; } = string.Empty;

        public string? ThumbnailUrl { get; set; }

        public long? FileSize { get; set; }

        [StringLength(10)]
        public string? FileFormat { get; set; }

        public int? Duration { get; set; } // For videos/audio in seconds

        public string? Grade { get; set; }

        public string? Subject { get; set; }

        public bool IsPublic { get; set; } = true;

        public int DownloadCount { get; set; } = 0;

        public int ViewCount { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<DigitalResourceAccess> DigitalResourceAccesses { get; set; } = new List<DigitalResourceAccess>();
    }

    // Digital Resource Access
    public class DigitalResourceAccess : BaseEntity
    {
        [Required]
        public int DigitalResourceId { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        public DateTime AccessDate { get; set; }

        public AccessType AccessType { get; set; }

        public int? Duration { get; set; } // Time spent in seconds

        public bool IsCompleted { get; set; } = false;

        // Navigation properties
        public virtual DigitalResource DigitalResource { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
    }

}
