using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.Models;
using Schools.Shared.DTOs;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AttendanceController : ControllerBase
    {
        private readonly SchoolsDbContext _context;
        private readonly ILogger<AttendanceController> _logger;

        public AttendanceController(SchoolsDbContext context, ILogger<AttendanceController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<AttendanceDto>>> GetAttendances(
            [FromQuery] DateTime? date = null,
            [FromQuery] string? studentId = null,
            [FromQuery] int? scheduleId = null,
            [FromQuery] string? status = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50)
        {
            try
            {
                var query = _context.Attendances
                    .Include(a => a.Student)
                    .Include(a => a.Schedule)
                    .ThenInclude(s => s.Class)
                    .AsQueryable();

                if (date.HasValue)
                    query = query.Where(a => a.Date.Date == date.Value.Date);

                if (!string.IsNullOrEmpty(studentId))
                    query = query.Where(a => a.StudentId == studentId);

                if (scheduleId.HasValue)
                    query = query.Where(a => a.ScheduleId == scheduleId.Value);

                if (!string.IsNullOrEmpty(status) && Enum.TryParse<AttendanceStatus>(status, out var statusEnum))
                    query = query.Where(a => a.Status == statusEnum);

                var totalCount = await query.CountAsync();

                var attendances = await query
                    .OrderBy(a => a.Date)
                    .ThenBy(a => a.Student.FirstName)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(a => new AttendanceDto
                    {
                        Id = a.Id,
                        StudentId = a.StudentId,
                        StudentName = a.Student.FirstName + " " + a.Student.LastName,
                        StudentNumber = a.Student.UserName ?? "",
                        StudentEmail = a.Student.Email ?? "",
                        ClassId = a.Schedule.ClassId,
                        ClassName = a.Schedule.Class.Name,
                        Date = a.Date,
                        Status = a.Status.ToString(),
                        ArrivalTime = a.ArrivalTime.HasValue ? a.ArrivalTime.Value.ToString(@"hh\:mm") : null,
                        Notes = a.Notes
                    })
                    .ToListAsync();

                Response.Headers.Append("X-Total-Count", totalCount.ToString());
                return Ok(attendances);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving attendance records");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<AttendanceDto>> GetAttendanceById(int id)
        {
            try
            {
                var attendance = await _context.Attendances
                    .Include(a => a.Student)
                    .Include(a => a.Schedule)
                    .ThenInclude(s => s.Class)
                    .FirstOrDefaultAsync(a => a.Id == id);

                if (attendance == null)
                    return NotFound();

                var attendanceDto = new AttendanceDto
                {
                    Id = attendance.Id,
                    StudentId = attendance.StudentId,
                    StudentName = attendance.Student.FirstName + " " + attendance.Student.LastName,
                    StudentNumber = attendance.Student.UserName ?? "",
                    StudentEmail = attendance.Student.Email ?? "",
                    ClassId = attendance.Schedule.ClassId,
                    ClassName = attendance.Schedule.Class.Name,
                    Date = attendance.Date,
                    Status = attendance.Status.ToString(),
                    ArrivalTime = attendance.ArrivalTime?.ToString(@"hh\:mm"),
                    Notes = attendance.Notes
                };

                return Ok(attendanceDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving attendance record {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<ActionResult<AttendanceDto>> CreateAttendance(CreateAttendanceDto createAttendanceDto)
        {
            try
            {
                // Find schedule for the class and date
                var schedule = await _context.Schedules
                    .Include(s => s.Class)
                    .FirstOrDefaultAsync(s => s.ClassId == createAttendanceDto.ClassId &&
                                            s.Date.Date == createAttendanceDto.Date.Date);

                if (schedule == null)
                    return BadRequest("No schedule found for this class and date");

                // Check if attendance already exists
                var existingAttendance = await _context.Attendances
                    .FirstOrDefaultAsync(a => a.StudentId == createAttendanceDto.StudentId &&
                                            a.ScheduleId == schedule.Id &&
                                            a.Date.Date == createAttendanceDto.Date.Date);

                if (existingAttendance != null)
                    return BadRequest("Attendance record already exists for this student and schedule");

                if (!Enum.TryParse<AttendanceStatus>(createAttendanceDto.Status, out var status))
                    return BadRequest("Invalid attendance status");

                var attendance = new Attendance
                {
                    StudentId = createAttendanceDto.StudentId,
                    ScheduleId = schedule.Id,
                    Date = createAttendanceDto.Date,
                    Status = status,
                    ArrivalTime = createAttendanceDto.ArrivalTime,
                    Notes = createAttendanceDto.Notes,
                    RecordedBy = User.Identity?.Name,
                    RecordedAt = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow
                };

                _context.Attendances.Add(attendance);
                await _context.SaveChangesAsync();

                // Reload with includes
                attendance = await _context.Attendances
                    .Include(a => a.Student)
                    .Include(a => a.Schedule)
                    .ThenInclude(s => s.Class)
                    .FirstAsync(a => a.Id == attendance.Id);

                var attendanceDto = new AttendanceDto
                {
                    Id = attendance.Id,
                    StudentId = attendance.StudentId,
                    StudentName = attendance.Student.FirstName + " " + attendance.Student.LastName,
                    StudentNumber = attendance.Student.UserName ?? "",
                    StudentEmail = attendance.Student.Email ?? "",
                    ClassId = attendance.Schedule.ClassId,
                    ClassName = attendance.Schedule.Class.Name,
                    Date = attendance.Date,
                    Status = attendance.Status.ToString(),
                    ArrivalTime = attendance.ArrivalTime?.ToString(@"hh\:mm"),
                    Notes = attendance.Notes
                };

                return CreatedAtAction(nameof(GetAttendanceById), new { id = attendance.Id }, attendanceDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating attendance record");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<IActionResult> UpdateAttendance(int id, UpdateAttendanceDto updateAttendanceDto)
        {
            try
            {
                var attendance = await _context.Attendances.FindAsync(id);
                if (attendance == null)
                    return NotFound();

                if (Enum.TryParse<AttendanceStatus>(updateAttendanceDto.Status, out var status))
                    attendance.Status = status;

                attendance.ArrivalTime = updateAttendanceDto.ArrivalTime;
                attendance.Notes = updateAttendanceDto.Notes;
                attendance.UpdatedAt = DateTime.UtcNow;
                attendance.UpdatedBy = User.Identity?.Name;

                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating attendance record {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteAttendance(int id)
        {
            try
            {
                var attendance = await _context.Attendances.FindAsync(id);
                if (attendance == null)
                    return NotFound();

                _context.Attendances.Remove(attendance);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting attendance record {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("bulk")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<ActionResult> CreateBulkAttendance(BulkAttendanceDto bulkAttendanceDto)
        {
            try
            {
                // Find schedule for the class and date
                var schedule = await _context.Schedules
                    .FirstOrDefaultAsync(s => s.ClassId == bulkAttendanceDto.ClassId &&
                                            s.Date.Date == bulkAttendanceDto.Date.Date);

                if (schedule == null)
                    return BadRequest("No schedule found for this class and date");

                var attendanceRecords = new List<Attendance>();

                foreach (var studentAttendance in bulkAttendanceDto.StudentAttendances)
                {
                    // Check if attendance already exists
                    var existingAttendance = await _context.Attendances
                        .FirstOrDefaultAsync(a => a.StudentId == studentAttendance.StudentId &&
                                                a.ScheduleId == schedule.Id &&
                                                a.Date.Date == bulkAttendanceDto.Date.Date);

                    if (existingAttendance != null)
                        continue; // Skip if already exists

                    if (!Enum.TryParse<AttendanceStatus>(studentAttendance.Status, out var status))
                        continue; // Skip invalid status

                    var attendance = new Attendance
                    {
                        StudentId = studentAttendance.StudentId,
                        ScheduleId = schedule.Id,
                        Date = bulkAttendanceDto.Date,
                        Status = status,
                        ArrivalTime = studentAttendance.ArrivalTime,
                        Notes = studentAttendance.Notes,
                        RecordedBy = User.Identity?.Name,
                        RecordedAt = DateTime.UtcNow,
                        CreatedAt = DateTime.UtcNow
                    };

                    attendanceRecords.Add(attendance);
                }

                if (attendanceRecords.Any())
                {
                    _context.Attendances.AddRange(attendanceRecords);
                    await _context.SaveChangesAsync();
                }

                return Ok(new { message = $"Created {attendanceRecords.Count} attendance records" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating bulk attendance records");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("statistics")]
        public async Task<ActionResult<AttendanceStatisticsDto>> GetAttendanceStatistics(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int? classId = null,
            [FromQuery] string? studentId = null)
        {
            try
            {
                var query = _context.Attendances.AsQueryable();

                if (startDate.HasValue)
                    query = query.Where(a => a.Date >= startDate.Value);

                if (endDate.HasValue)
                    query = query.Where(a => a.Date <= endDate.Value);

                if (classId.HasValue)
                    query = query.Where(a => a.Schedule.ClassId == classId.Value);

                if (!string.IsNullOrEmpty(studentId))
                    query = query.Where(a => a.StudentId == studentId);

                var totalRecords = await query.CountAsync();
                var presentCount = await query.CountAsync(a => a.Status == AttendanceStatus.Present);
                var absentCount = await query.CountAsync(a => a.Status == AttendanceStatus.Absent);
                var lateCount = await query.CountAsync(a => a.Status == AttendanceStatus.Late);
                var excusedCount = await query.CountAsync(a => a.Status == AttendanceStatus.Excused);

                var statistics = new AttendanceStatisticsDto
                {
                    TotalRecords = totalRecords,
                    PresentCount = presentCount,
                    AbsentCount = absentCount,
                    LateCount = lateCount,
                    ExcusedCount = excusedCount,
                    AttendanceRate = totalRecords > 0 ? (double)(presentCount + lateCount) / totalRecords * 100 : 0
                };

                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving attendance statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("daily/{date}")]
        public async Task<ActionResult<IEnumerable<AttendanceDto>>> GetDailyAttendance(DateTime date, [FromQuery] int? classId = null)
        {
            try
            {
                var query = _context.Attendances
                    .Include(a => a.Student)
                    .Include(a => a.Schedule)
                    .ThenInclude(s => s.Class)
                    .Where(a => a.Date.Date == date.Date);

                if (classId.HasValue)
                    query = query.Where(a => a.Schedule.ClassId == classId.Value);

                var attendances = await query
                    .Select(a => new AttendanceDto
                    {
                        Id = a.Id,
                        StudentId = a.StudentId,
                        StudentName = a.Student.FirstName + " " + a.Student.LastName,
                        StudentNumber = a.Student.UserName ?? "",
                        StudentEmail = a.Student.Email ?? "",
                        ClassId = a.Schedule.ClassId,
                        ClassName = a.Schedule.Class.Name,
                        Date = a.Date,
                        Status = a.Status.ToString(),
                        ArrivalTime = a.ArrivalTime.HasValue ? a.ArrivalTime.Value.ToString(@"hh\:mm") : null,
                        Notes = a.Notes
                    })
                    .ToListAsync();

                return Ok(attendances);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving daily attendance for {Date}", date);
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
