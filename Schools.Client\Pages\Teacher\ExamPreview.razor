@page "/teacher/exams/{ExamId:int}/preview"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Teacher")]

<PageTitle>معاينة الامتحان - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/teacher/exams">الامتحانات</a></li>
                    <li class="breadcrumb-item"><a href="/teacher/exams/@ExamId/questions">إدارة الأسئلة</a></li>
                    <li class="breadcrumb-item active">معاينة الامتحان</li>
                </ol>
            </nav>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل معاينة الامتحان...</p>
        </div>
    }
    else if (exam != null)
    {
        <!-- Preview Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-gradient-info text-white border-0 shadow-lg">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="mb-1">
                                    <i class="fas fa-eye me-2"></i>
                                    معاينة: @exam.Title
                                </h2>
                                <p class="mb-0 opacity-75">@exam.Description</p>
                                <div class="mt-2">
                                    <span class="badge bg-light text-dark me-2">@exam.SubjectName</span>
                                    <span class="badge bg-light text-dark me-2">@exam.ClassName</span>
                                    <span class="badge bg-light text-dark me-2">@exam.TotalQuestions سؤال</span>
                                    <span class="badge bg-light text-dark">@exam.DurationMinutes دقيقة</span>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-light" @onclick="PublishExam" disabled="@(!CanPublish())">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        نشر الامتحان
                                    </button>
                                    <button class="btn btn-light" @onclick="BackToQuestions">
                                        <i class="fas fa-edit me-2"></i>
                                        تعديل الأسئلة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Exam Instructions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            تعليمات الامتحان
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="instruction-item">
                                    <i class="fas fa-clock text-warning me-2"></i>
                                    <strong>مدة الامتحان:</strong> @exam.DurationMinutes دقيقة
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="instruction-item">
                                    <i class="fas fa-star text-success me-2"></i>
                                    <strong>إجمالي الدرجات:</strong> @exam.TotalMarks درجة
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="instruction-item">
                                    <i class="fas fa-check-circle text-info me-2"></i>
                                    <strong>درجة النجاح:</strong> @exam.PassingMarks درجة
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="instruction-item">
                                    <i class="fas fa-redo text-primary me-2"></i>
                                    <strong>عدد المحاولات:</strong> @exam.MaxAttempts محاولة
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="alert alert-info mb-0">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-lightbulb me-2"></i>
                                        ملاحظات مهمة:
                                    </h6>
                                    <ul class="mb-0">
                                        <li>تأكد من اتصالك بالإنترنت طوال فترة الامتحان</li>
                                        <li>سيتم حفظ إجاباتك تلقائياً كل دقيقة</li>
                                        <li>@(exam.ShowResultsImmediately ? "ستظهر النتيجة فور انتهاء الامتحان" : "ستظهر النتيجة لاحقاً")</li>
                                        <li>@(exam.AllowRetake ? $"يمكنك إعادة المحاولة حتى {exam.MaxAttempts} مرات" : "لا يمكن إعادة المحاولة")</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Questions Preview -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0">
                                    <i class="fas fa-list-ol me-2 text-primary"></i>
                                    أسئلة الامتحان (@questions.Count سؤال)
                                </h5>
                            </div>
                            <div class="col-auto">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" @bind="showAnswers" @bind:after="StateHasChanged">
                                    <label class="form-check-label">عرض الإجابات الصحيحة</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (questions?.Any() == true)
                        {
                            <div class="questions-preview">
                                @foreach (var question in questions.OrderBy(q => q.OrderIndex))
                                {
                                    <div class="question-preview-item card mb-4 border">
                                        <div class="card-header bg-light">
                                            <div class="row align-items-center">
                                                <div class="col">
                                                    <div class="d-flex align-items-center">
                                                        <span class="badge bg-primary me-2">السؤال @question.OrderIndex</span>
                                                        <span class="badge @GetQuestionTypeBadgeClass(question.Type) me-2">@GetQuestionTypeText(question.Type)</span>
                                                        <span class="badge bg-success">@question.Marks درجة</span>
                                                        @if (question.IsRequired)
                                                        {
                                                            <span class="badge bg-danger ms-2">إجباري</span>
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="question-content">
                                                <h6 class="question-text">@question.QuestionText</h6>

                                                @if (!string.IsNullOrEmpty(question.ImageUrl))
                                                {
                                                    <div class="question-image mb-3">
                                                        <img src="@question.ImageUrl" class="img-thumbnail" style="max-height: 200px;" alt="صورة السؤال" />
                                                    </div>
                                                }

                                                @if (question.Type == QuestionType.MultipleChoice && question.Options?.Any() == true)
                                                {
                                                    <div class="options-preview">
                                                        <div class="row g-2">
                                                            @foreach (var option in question.Options.OrderBy(o => o.OrderIndex))
                                                            {
                                                                <div class="col-md-6">
                                                                    <div class="option-preview @(showAnswers && option.IsCorrect ? "correct-option" : "")">
                                                                        <div class="d-flex align-items-center">
                                                                            <span class="option-letter me-2">@GetOptionLetter(option.OrderIndex)</span>
                                                                            <span class="flex-grow-1">@option.OptionText</span>
                                                                            @if (showAnswers && option.IsCorrect)
                                                                            {
                                                                                <i class="fas fa-check text-success ms-2"></i>
                                                                            }
                                                                        </div>
                                                                        @if (!string.IsNullOrEmpty(option.ImageUrl))
                                                                        {
                                                                            <img src="@option.ImageUrl" class="option-image mt-2" alt="صورة الخيار" />
                                                                        }
                                                                    </div>
                                                                </div>
                                                            }
                                                        </div>
                                                    </div>
                                                }
                                                else if (question.Type == QuestionType.TrueFalse)
                                                {
                                                    <div class="true-false-preview">
                                                        <div class="row g-3">
                                                            <div class="col-md-6">
                                                                <div class="tf-option @(showAnswers && question.CorrectAnswer == "صحيح" ? "correct-answer" : "")">
                                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                                    صحيح
                                                                    @if (showAnswers && question.CorrectAnswer == "صحيح")
                                                                    {
                                                                        <i class="fas fa-check text-success ms-2"></i>
                                                                    }
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="tf-option @(showAnswers && question.CorrectAnswer == "خطأ" ? "correct-answer" : "")">
                                                                    <i class="fas fa-times-circle text-danger me-2"></i>
                                                                    خطأ
                                                                    @if (showAnswers && question.CorrectAnswer == "خطأ")
                                                                    {
                                                                        <i class="fas fa-check text-success ms-2"></i>
                                                                    }
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                                else if (question.Type == QuestionType.ShortAnswer)
                                                {
                                                    <div class="short-answer-preview">
                                                        <div class="form-group">
                                                            <input type="text" class="form-control" placeholder="اكتب إجابتك هنا..." disabled />
                                                        </div>
                                                        @if (showAnswers && !string.IsNullOrEmpty(question.CorrectAnswer))
                                                        {
                                                            <div class="mt-2">
                                                                <small class="text-muted">الإجابة النموذجية:</small>
                                                                <div class="alert alert-success py-2">@question.CorrectAnswer</div>
                                                            </div>
                                                        }
                                                    </div>
                                                }
                                                else if (question.Type == QuestionType.Essay)
                                                {
                                                    <div class="essay-preview">
                                                        <div class="form-group">
                                                            <textarea class="form-control" rows="4" placeholder="اكتب إجابتك المفصلة هنا..." disabled></textarea>
                                                        </div>
                                                        @if (showAnswers && !string.IsNullOrEmpty(question.CorrectAnswer))
                                                        {
                                                            <div class="mt-2">
                                                                <small class="text-muted">نموذج الإجابة:</small>
                                                                <div class="alert alert-success py-2">@question.CorrectAnswer</div>
                                                            </div>
                                                        }
                                                    </div>
                                                }

                                                @if (showAnswers && !string.IsNullOrEmpty(question.Explanation))
                                                {
                                                    <div class="explanation mt-3">
                                                        <div class="alert alert-info">
                                                            <h6 class="alert-heading">
                                                                <i class="fas fa-lightbulb me-2"></i>
                                                                التفسير:
                                                            </h6>
                                                            <p class="mb-0">@question.Explanation</p>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد أسئلة</h5>
                                <p class="text-muted">يجب إضافة أسئلة قبل نشر الامتحان</p>
                                <a href="/teacher/exams/@ExamId/questions" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة أسئلة
                                </a>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <div class="btn-group">
                    <a href="/teacher/exams" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للامتحانات
                    </a>
                    <a href="/teacher/exams/@ExamId/questions" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الأسئلة
                    </a>
                    @if (CanPublish())
                    {
                        <button class="btn btn-success" @onclick="PublishExam">
                            <i class="fas fa-paper-plane me-2"></i>
                            نشر الامتحان
                        </button>
                    }
                    else
                    {
                        <button class="btn btn-success" disabled title="@GetPublishBlockReason()">
                            <i class="fas fa-paper-plane me-2"></i>
                            نشر الامتحان
                        </button>
                    }
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h5 class="text-muted">لم يتم العثور على الامتحان</h5>
            <p class="text-muted">تأكد من صحة الرابط أو تواصل مع الإدارة</p>
            <a href="/teacher/exams" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للامتحانات
            </a>
        </div>
    }
</div>

<style>
    .bg-gradient-info {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    }

    .instruction-item {
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .instruction-item:last-child {
        border-bottom: none;
    }

    .question-preview-item {
        transition: all 0.3s ease;
    }

    .question-preview-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }

    .option-preview {
        padding: 10px 15px;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }

    .option-preview.correct-option {
        background-color: #d4edda;
        border-color: #c3e6cb;
    }

    .tf-option {
        padding: 10px 15px;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        background-color: #f8f9fa;
        text-align: center;
    }

    .tf-option.correct-answer {
        background-color: #d4edda;
        border-color: #c3e6cb;
    }

    .option-letter {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        background-color: #007bff;
        color: white;
        border-radius: 50%;
        font-size: 12px;
        font-weight: bold;
    }

    .option-image {
        max-width: 100%;
        max-height: 100px;
        border-radius: 4px;
    }

    .question-text {
        line-height: 1.6;
        margin-bottom: 15px;
    }
</style>

@code {
    [Parameter] public int ExamId { get; set; }

    private ExamDto? exam;
    private List<ExamQuestionDto> questions = new();
    private bool isLoading = true;
    private bool showAnswers = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadExamData();
    }

    private async Task LoadExamData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            exam = await ApiService.GetExamAsync(ExamId);
            if (exam != null)
            {
                questions = exam.Questions ?? new List<ExamQuestionDto>();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل بيانات الامتحان: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private bool CanPublish()
    {
        if (exam == null) return false;

        return questions.Any() &&
               exam.Status == ExamStatus.Draft &&
               exam.StartDate > DateTime.Now &&
               questions.Sum(q => q.Marks) == exam.TotalMarks;
    }

    private string GetPublishBlockReason()
    {
        if (exam == null) return "الامتحان غير موجود";

        if (!questions.Any())
            return "يجب إضافة أسئلة للامتحان";

        if (exam.Status != ExamStatus.Draft)
            return "الامتحان منشور بالفعل";

        if (exam.StartDate <= DateTime.Now)
            return "تاريخ بداية الامتحان قد مضى";

        if (questions.Sum(q => q.Marks) != exam.TotalMarks)
            return "مجموع درجات الأسئلة لا يطابق إجمالي درجات الامتحان";

        return "";
    }

    private async Task PublishExam()
    {
        if (!CanPublish())
        {
            await JSRuntime.InvokeVoidAsync("alert", GetPublishBlockReason());
            return;
        }

        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من نشر هذا الامتحان؟ لن تتمكن من تعديله بعد النشر."))
        {
            try
            {
                var success = await ApiService.PublishExamAsync(ExamId);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "تم نشر الامتحان بنجاح");
                    Navigation.NavigateTo("/teacher/exams");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "فشل في نشر الامتحان");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في نشر الامتحان: {ex.Message}");
            }
        }
    }

    private void BackToQuestions()
    {
        Navigation.NavigateTo($"/teacher/exams/{ExamId}/questions");
    }

    private string GetQuestionTypeBadgeClass(QuestionType type)
    {
        return type switch
        {
            QuestionType.MultipleChoice => "bg-primary",
            QuestionType.TrueFalse => "bg-success",
            QuestionType.ShortAnswer => "bg-info",
            QuestionType.Essay => "bg-warning",
            _ => "bg-secondary"
        };
    }

    private string GetQuestionTypeText(QuestionType type)
    {
        return type switch
        {
            QuestionType.MultipleChoice => "اختيار متعدد",
            QuestionType.TrueFalse => "صح/خطأ",
            QuestionType.ShortAnswer => "إجابة قصيرة",
            QuestionType.Essay => "مقال",
            _ => "غير محدد"
        };
    }

    private string GetOptionLetter(int index)
    {
        var letters = new[] { "أ", "ب", "ج", "د", "هـ", "و", "ز", "ح", "ط", "ي" };
        return index > 0 && index <= letters.Length ? letters[index - 1] : index.ToString();
    }
}
