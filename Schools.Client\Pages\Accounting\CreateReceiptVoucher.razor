@page "/accounting/receipt-vouchers/new"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Accountant,Admin")]

<PageTitle>سند قبض جديد - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-receipt text-success me-2"></i>
                        إنشاء سند قبض جديد
                    </h2>
                    <p class="text-muted mb-0">إنشاء سند قبض جديد لتسجيل المقبوضات</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-outline-secondary" @onclick="GoBack">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل البيانات...</p>
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-plus-circle me-2"></i>
                            بيانات سند القبض
                        </h5>
                    </div>
                    <div class="card-body">
                        <EditForm Model="voucher" OnValidSubmit="SaveVoucher">
                            <DataAnnotationsValidator />

                            <div class="row g-3">
                                <!-- Basic Information -->
                                <div class="col-md-6">
                                    <div class="card border-light">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">المعلومات الأساسية</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">رقم السند</label>
                                                <InputText @bind-Value="voucher.VoucherNumber" class="form-control" readonly />
                                                <small class="text-muted">سيتم إنشاء الرقم تلقائياً</small>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">تاريخ السند <span class="text-danger">*</span></label>
                                                <InputDate @bind-Value="voucher.VoucherDate" class="form-control" />
                                                <ValidationMessage For="() => voucher.VoucherDate" />
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">المقبوض من <span class="text-danger">*</span></label>
                                                <InputText @bind-Value="voucher.ReceivedFrom" class="form-control" placeholder="اسم الشخص أو الجهة" />
                                                <ValidationMessage For="() => voucher.ReceivedFrom" />
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                                                <InputNumber @bind-Value="voucher.TotalAmount" class="form-control" @oninput="UpdateAmountInWords" />
                                                <ValidationMessage For="() => voucher.TotalAmount" />
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">المبلغ بالكلمات</label>
                                                <InputText @bind-Value="voucher.AmountInWords" class="form-control" readonly />
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Payment Details -->
                                <div class="col-md-6">
                                    <div class="card border-light">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">تفاصيل الدفع</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                                                <InputSelect @bind-Value="voucher.PaymentMethod" class="form-select" @onchange="OnPaymentMethodChanged">
                                                    <option value="">اختر طريقة الدفع</option>
                                                    <option value="Cash">نقدي</option>
                                                    <option value="Bank">تحويل بنكي</option>
                                                    <option value="Check">شيك</option>
                                                </InputSelect>
                                                <ValidationMessage For="() => voucher.PaymentMethod" />
                                            </div>

                                            @if (voucher.PaymentMethod == "Bank")
                                            {
                                                <div class="mb-3">
                                                    <label class="form-label">البنك</label>
                                                    <InputText @bind-Value="voucher.BankName" class="form-control" placeholder="اسم البنك" />
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label">رقم الحساب</label>
                                                    <InputText @bind-Value="voucher.AccountNumber" class="form-control" placeholder="رقم الحساب البنكي" />
                                                </div>
                                            }

                                            @if (voucher.PaymentMethod == "Check")
                                            {
                                                <div class="mb-3">
                                                    <label class="form-label">رقم الشيك</label>
                                                    <InputText @bind-Value="voucher.CheckNumber" class="form-control" placeholder="رقم الشيك" />
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label">تاريخ الشيك</label>
                                                    <InputDate @bind-Value="voucher.CheckDate" class="form-control" />
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label">البنك المسحوب عليه</label>
                                                    <InputText @bind-Value="voucher.BankName" class="form-control" placeholder="اسم البنك" />
                                                </div>
                                            }

                                            <div class="mb-3">
                                                <label class="form-label">الوصف</label>
                                                <InputTextArea @bind-Value="voucher.Description" class="form-control" rows="3" placeholder="وصف السند أو سبب القبض" />
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Accounting Details -->
                                <div class="col-12">
                                    <div class="card border-light">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">التفاصيل المحاسبية</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label class="form-label">حساب الخزينة/البنك <span class="text-danger">*</span></label>
                                                    <select @bind="selectedCashAccount" class="form-select">
                                                        <option value="">اختر الحساب</option>
                                                        @foreach (var account in cashAccounts)
                                                        {
                                                            <option value="@account.Id">@account.AccountCode - @account.AccountName</option>
                                                        }
                                                    </select>
                                                </div>

                                                <div class="col-md-6">
                                                    <label class="form-label">حساب الإيراد <span class="text-danger">*</span></label>
                                                    <select @bind="selectedRevenueAccount" class="form-select">
                                                        <option value="">اختر الحساب</option>
                                                        @foreach (var account in revenueAccounts)
                                                        {
                                                            <option value="@account.Id">@account.AccountCode - @account.AccountName</option>
                                                        }
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="d-flex justify-content-end gap-2">
                                        <button type="button" class="btn btn-outline-secondary" @onclick="GoBack">
                                            <i class="fas fa-times me-2"></i>
                                            إلغاء
                                        </button>
                                        <button type="submit" class="btn btn-success" disabled="@isSaving">
                                            @if (isSaving)
                                            {
                                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                            }
                                            else
                                            {
                                                <i class="fas fa-save me-2"></i>
                                            }
                                            حفظ السند
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </EditForm>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private ReceiptVoucherDto voucher = new()
    {
        VoucherDate = DateTime.Now,
        VoucherNumber = "سيتم إنشاؤه تلقائياً",
        Status = VoucherStatus.Draft
    };

    private List<AccountDto> cashAccounts = new();
    private List<AccountDto> revenueAccounts = new();

    private bool isLoading = true;
    private bool isSaving = false;

    private int selectedCashAccount;
    private int selectedRevenueAccount;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            var allAccounts = await ApiService.GetAccountsAsync() ?? new List<AccountDto>();

            // Filter cash and bank accounts (Assets)
            cashAccounts = allAccounts.Where(a =>
                a.AccountType == AccountType.Asset &&
                a.IsActive &&
                (a.AccountName.Contains("خزينة", StringComparison.OrdinalIgnoreCase) ||
                 a.AccountName.Contains("بنك", StringComparison.OrdinalIgnoreCase) ||
                 a.AccountName.Contains("نقدية", StringComparison.OrdinalIgnoreCase))
            ).ToList();

            // Filter revenue accounts
            revenueAccounts = allAccounts.Where(a =>
                a.AccountType == AccountType.Revenue &&
                a.IsActive
            ).ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task SaveVoucher()
    {
        if (!ValidateVoucher())
            return;

        try
        {
            isSaving = true;
            StateHasChanged();

            // Create voucher details for accounting entries
            voucher.Details = new List<ReceiptVoucherDetailDto>
            {
                new ReceiptVoucherDetailDto
                {
                    AccountId = selectedCashAccount,
                    Amount = voucher.TotalAmount,
                    Description = $"قبض من {voucher.ReceivedFrom}"
                },
                new ReceiptVoucherDetailDto
                {
                    AccountId = selectedRevenueAccount,
                    Amount = voucher.TotalAmount,
                    Description = voucher.Description ?? $"إيراد من {voucher.ReceivedFrom}"
                }
            };

            var success = await ApiService.CreateReceiptVoucherAsync(voucher);
            if (success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "تم حفظ سند القبض بنجاح");
                Navigation.NavigateTo("/accounting/receipt-vouchers");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ أثناء حفظ السند");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ السند: {ex.Message}");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private bool ValidateVoucher()
    {
        if (string.IsNullOrWhiteSpace(voucher.ReceivedFrom))
        {
            JSRuntime.InvokeVoidAsync("alert", "يرجى إدخال اسم المقبوض منه");
            return false;
        }

        if (voucher.TotalAmount <= 0)
        {
            JSRuntime.InvokeVoidAsync("alert", "يرجى إدخال مبلغ صحيح");
            return false;
        }

        if (string.IsNullOrWhiteSpace(voucher.PaymentMethod))
        {
            JSRuntime.InvokeVoidAsync("alert", "يرجى اختيار طريقة الدفع");
            return false;
        }

        if (selectedCashAccount == 0)
        {
            JSRuntime.InvokeVoidAsync("alert", "يرجى اختيار حساب الخزينة/البنك");
            return false;
        }

        if (selectedRevenueAccount == 0)
        {
            JSRuntime.InvokeVoidAsync("alert", "يرجى اختيار حساب الإيراد");
            return false;
        }

        if (voucher.PaymentMethod == "Check")
        {
            if (string.IsNullOrWhiteSpace(voucher.CheckNumber))
            {
                JSRuntime.InvokeVoidAsync("alert", "يرجى إدخال رقم الشيك");
                return false;
            }

            if (!voucher.CheckDate.HasValue)
            {
                JSRuntime.InvokeVoidAsync("alert", "يرجى إدخال تاريخ الشيك");
                return false;
            }
        }

        if (voucher.PaymentMethod == "Bank" && string.IsNullOrWhiteSpace(voucher.AccountNumber))
        {
            JSRuntime.InvokeVoidAsync("alert", "يرجى إدخال رقم الحساب البنكي");
            return false;
        }

        return true;
    }

    private void OnPaymentMethodChanged(ChangeEventArgs e)
    {
        voucher.PaymentMethod = e.Value?.ToString() ?? "";

        // Clear related fields when payment method changes
        if (voucher.PaymentMethod != "Check")
        {
            voucher.CheckNumber = null;
            voucher.CheckDate = null;
        }

        if (voucher.PaymentMethod != "Bank")
        {
            voucher.AccountNumber = null;
        }

        if (voucher.PaymentMethod != "Bank" && voucher.PaymentMethod != "Check")
        {
            voucher.BankName = null;
        }

        StateHasChanged();
    }

    private void UpdateAmountInWords(ChangeEventArgs e)
    {
        if (decimal.TryParse(e.Value?.ToString(), out decimal amount))
        {
            voucher.TotalAmount = amount;
            voucher.AmountInWords = ConvertAmountToWords(amount);
            StateHasChanged();
        }
    }

    private string ConvertAmountToWords(decimal amount)
    {
        // Simple implementation - in a real application, you would use a proper number-to-words converter
        if (amount == 0)
            return "صفر";

        return $"{amount:N2} ريال سعودي";
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/accounting/receipt-vouchers");
    }
}
