using System.ComponentModel.DataAnnotations;

namespace Schools.Shared.Models
{
    // Student Grade (Academic Performance)
    public class StudentGrade : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int SubjectId { get; set; }

        [Required]
        public int AcademicYearId { get; set; }

        [Required]
        [StringLength(50)]
        public string ExamType { get; set; } = string.Empty; // Quiz, Midterm, Final, Assignment

        [Required]
        public decimal Score { get; set; }

        [Required]
        public decimal MaxScore { get; set; }

        public decimal Percentage => MaxScore > 0 ? (Score / MaxScore) * 100 : 0;

        [Required]
        public DateTime Date { get; set; }

        [Required]
        [StringLength(20)]
        public string Semester { get; set; } = string.Empty; // First, Second

        public string? Notes { get; set; }

        public string? RecordedBy { get; set; }

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual Subject Subject { get; set; } = null!;
        public virtual AcademicYear AcademicYear { get; set; } = null!;
    }

    // Grade Scale
    public class GradeScale : BaseEntity
    {
        [Required]
        [StringLength(10)]
        public string Grade { get; set; } = string.Empty; // A+, A, B+, B, etc.

        [Required]
        public decimal MinPercentage { get; set; }

        [Required]
        public decimal MaxPercentage { get; set; }

        [Required]
        public decimal GradePoint { get; set; }

        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;
    }

    // Student Report Card
    public class StudentReportCard : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int AcademicYearId { get; set; }

        [Required]
        [StringLength(20)]
        public string Semester { get; set; } = string.Empty;

        public decimal? TotalMarks { get; set; }

        public decimal? ObtainedMarks { get; set; }

        public decimal? Percentage { get; set; }

        public string? OverallGrade { get; set; }

        public decimal? GPA { get; set; }

        public int? Rank { get; set; }

        public string? TeacherComments { get; set; }

        public string? PrincipalComments { get; set; }

        public DateTime? GeneratedDate { get; set; }

        public string? GeneratedBy { get; set; }

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual AcademicYear AcademicYear { get; set; } = null!;
        public virtual ICollection<StudentGrade> StudentGrades { get; set; } = new List<StudentGrade>();
    }
}
