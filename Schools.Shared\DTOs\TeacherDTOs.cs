using System.ComponentModel.DataAnnotations;

namespace Schools.Shared.DTOs
{
    // Teacher DTOs
    public class TeacherDto
    {
        public string Id { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string FullName => $"{FirstName} {LastName}";
        public string Email { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? NationalId { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public string? ProfilePicture { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }

        // Teacher-specific Information
        public string EmployeeNumber { get; set; } = string.Empty;
        public DateTime HireDate { get; set; }
        public string? Department { get; set; }
        public string? Position { get; set; }
        public string? Qualification { get; set; }
        public string? Specialization { get; set; }
        public int YearsOfExperience { get; set; }
        public decimal Salary { get; set; }
        public string? EmergencyContact { get; set; }
        public string? EmergencyPhone { get; set; }
        public string? BankAccount { get; set; }
        public string? ContractType { get; set; }
        public DateTime? ContractEndDate { get; set; }
        public string? Notes { get; set; }

        // Academic Information
        public List<TeacherSubjectDto> Subjects { get; set; } = new();
        public List<TeacherClassDto> Classes { get; set; } = new();
        public List<TeacherScheduleDto> Schedule { get; set; } = new();
        public int TotalClasses { get; set; }
        public int TotalStudents { get; set; }
        public double WorkloadHours { get; set; }
    }

    public class CreateTeacherDto
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Phone]
        public string? PhoneNumber { get; set; }

        [StringLength(20)]
        public string? NationalId { get; set; }

        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }

        [Required]
        [StringLength(20)]
        public string EmployeeNumber { get; set; } = string.Empty;

        [Required]
        public DateTime HireDate { get; set; }

        public string? Department { get; set; }
        public string? Position { get; set; }
        public string? Qualification { get; set; }
        public string? Specialization { get; set; }
        public int YearsOfExperience { get; set; }
        public decimal Salary { get; set; }
        public string? EmergencyContact { get; set; }
        public string? EmergencyPhone { get; set; }
        public string? BankAccount { get; set; }
        public string? ContractType { get; set; }
        public DateTime? ContractEndDate { get; set; }
        public string? Notes { get; set; }
    }

    public class UpdateTeacherDto
    {
        [StringLength(100)]
        public string? FirstName { get; set; }

        [StringLength(100)]
        public string? LastName { get; set; }

        [Phone]
        public string? PhoneNumber { get; set; }

        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public string? Department { get; set; }
        public string? Position { get; set; }
        public string? Qualification { get; set; }
        public string? Specialization { get; set; }
        public int? YearsOfExperience { get; set; }
        public decimal? Salary { get; set; }
        public string? EmergencyContact { get; set; }
        public string? EmergencyPhone { get; set; }
        public string? BankAccount { get; set; }
        public string? ContractType { get; set; }
        public DateTime? ContractEndDate { get; set; }
        public string? Notes { get; set; }
        public bool? IsActive { get; set; }
    }

    public class TeacherSubjectDto
    {
        public int Id { get; set; }
        public string TeacherId { get; set; } = string.Empty;
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public string SubjectCode { get; set; } = string.Empty;
        public bool IsPrimary { get; set; }
        public DateTime AssignedDate { get; set; }
        public bool IsActive { get; set; }
    }

    public class TeacherClassDto
    {
        public int Id { get; set; }
        public string TeacherId { get; set; } = string.Empty;
        public int ClassId { get; set; }
        public string ClassName { get; set; } = string.Empty;
        public int GradeId { get; set; }
        public string GradeName { get; set; } = string.Empty;
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public bool IsClassTeacher { get; set; }
        public DateTime AssignedDate { get; set; }
        public bool IsActive { get; set; }
        public int StudentCount { get; set; }
        public int TotalStudents { get; set; }
    }

    // Teacher Assignment DTOs
    public class TeacherAssignmentDto
    {
        public int Id { get; set; }
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public int ClassId { get; set; }
        public string ClassName { get; set; } = string.Empty;
        public string GradeName { get; set; } = string.Empty;
        public bool IsClassTeacher { get; set; }
        public DateTime AssignedDate { get; set; }
        public bool IsActive { get; set; }
    }

    public class CreateTeacherAssignmentDto
    {
        [Required]
        public int ClassId { get; set; }

        [Required]
        public int SubjectId { get; set; }

        public bool IsClassTeacher { get; set; }
        public DateTime AssignedDate { get; set; } = DateTime.Now;
    }

    // Teacher Schedule DTOs
    public class TeacherScheduleDto
    {
        public int Id { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
        public string GradeName { get; set; } = string.Empty;
        public DayOfWeek DayOfWeek { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public string? Room { get; set; }
        public string? Notes { get; set; }
    }

    public class TeacherInfoDto
    {
        public string Id { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Specialization { get; set; } = string.Empty;
        public int YearsOfExperience { get; set; }
    }

    public class TodayScheduleDto
    {
        public string SubjectName { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public string? Room { get; set; }
    }

    public class PendingExamDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string SubjectName { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public int DurationMinutes { get; set; }
    }

    // TeacherScheduleDto moved to ScheduleModels.cs to avoid duplication

    public class AssignTeacherSubjectDto
    {
        [Required]
        public string TeacherId { get; set; } = string.Empty;

        [Required]
        public int SubjectId { get; set; }

        public bool IsPrimary { get; set; } = false;

        [Required]
        public DateTime AssignedDate { get; set; }
    }

    public class AssignTeacherClassDto
    {
        [Required]
        public string TeacherId { get; set; } = string.Empty;

        [Required]
        public int ClassId { get; set; }

        [Required]
        public int SubjectId { get; set; }

        public bool IsClassTeacher { get; set; } = false;

        [Required]
        public DateTime AssignedDate { get; set; }
    }

    public class TeacherSearchDto
    {
        public string? SearchTerm { get; set; }
        public string? Department { get; set; }
        public string? Position { get; set; }
        public string? Specialization { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? HireDateFrom { get; set; }
        public DateTime? HireDateTo { get; set; }
        public int? SubjectId { get; set; }
        public int? ClassId { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; } = false;
    }

    public class TeacherListDto
    {
        public List<TeacherDto> Teachers { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    public class TeacherDashboardDto
    {
        public TeacherDto Teacher { get; set; } = new();
        public List<TeacherClassDto> TodayClasses { get; set; } = new();
        public List<ExamDto> UpcomingExams { get; set; } = new();
        public List<NotificationDto> Notifications { get; set; } = new();
        public TeacherStatisticsDto Statistics { get; set; } = new();
        public List<StudentDto> RecentStudents { get; set; } = new();
        public List<UpcomingEventDto> UpcomingEvents { get; set; } = new();
    }

    public class TeacherStatisticsDto
    {
        public int TotalClasses { get; set; }
        public int TotalStudents { get; set; }
        public int TotalSubjects { get; set; }
        public double WeeklyHours { get; set; }
        public int ExamsCreated { get; set; }
        public int ExamsGraded { get; set; }
        public int PendingGrading { get; set; }
        public double AverageClassSize { get; set; }
        public double StudentAttendanceRate { get; set; }
        public double StudentPassRate { get; set; }
    }

    // These DTOs are defined in other files to avoid duplication

    public class BulkTeacherOperationDto
    {
        public List<string> TeacherIds { get; set; } = new();
        public string Operation { get; set; } = string.Empty; // activate, deactivate, transfer, etc.
        public Dictionary<string, object> Parameters { get; set; } = new();
    }
}
