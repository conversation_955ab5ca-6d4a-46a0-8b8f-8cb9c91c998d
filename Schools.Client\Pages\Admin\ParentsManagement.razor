@page "/admin/parents"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<PageTitle>إدارة أولياء الأمور - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4 class="mb-0">
                                <i class="fas fa-users me-2"></i>
                                إدارة أولياء الأمور
                            </h4>
                            <small class="opacity-75">إدارة شاملة لأولياء الأمور وربطهم بالطلاب</small>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-light" @onclick="ShowAddModal">
                                <i class="fas fa-plus me-2"></i>
                                إضافة ولي أمر جديد
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="card-body p-0">
                    <div class="p-3 border-bottom bg-light">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <div class="card border-0 bg-primary text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-users fa-2x mb-2"></i>
                                        <h5 class="mb-0">@totalParents</h5>
                                        <small>إجمالي أولياء الأمور</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-0 bg-success text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-user-check fa-2x mb-2"></i>
                                        <h5 class="mb-0">@activeParents</h5>
                                        <small>أولياء أمور نشطين</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-0 bg-info text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-child fa-2x mb-2"></i>
                                        <h5 class="mb-0">@totalChildren</h5>
                                        <small>إجمالي الأبناء</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-0 bg-warning text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-link fa-2x mb-2"></i>
                                        <h5 class="mb-0">@averageChildren.ToString("F1")</h5>
                                        <small>متوسط الأبناء لكل ولي أمر</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filters -->
                    <div class="p-3 border-bottom">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" placeholder="البحث بالاسم أو البريد الإلكتروني..."
                                           @bind="searchTerm" @bind:after="LoadParents" />
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" @bind="statusFilter" @bind:after="LoadParents">
                                    <option value="">جميع الحالات</option>
                                    <option value="true">نشط</option>
                                    <option value="false">غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" @bind="childrenFilter" @bind:after="LoadParents">
                                    <option value="">جميع أولياء الأمور</option>
                                    <option value="0">بدون أبناء</option>
                                    <option value="1">ابن واحد</option>
                                    <option value="2+">أكثر من ابن</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" @bind="pageSize" @bind:after="LoadParents">
                                    <option value="10">10 عناصر</option>
                                    <option value="25">25 عنصر</option>
                                    <option value="50">50 عنصر</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-secondary w-100" @onclick="ResetFilters">
                                    <i class="fas fa-undo me-2"></i>
                                    إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Parents Table -->
                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2 text-muted">جاري تحميل بيانات أولياء الأمور...</p>
                        </div>
                    }
                    else if (parents?.Any() == true)
                    {
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th>
                                            <input type="checkbox" class="form-check-input" @onchange="ToggleSelectAll" />
                                        </th>
                                        <th>الاسم الكامل</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>رقم الهاتف</th>
                                        <th>الهوية الوطنية</th>
                                        <th>عدد الأبناء</th>
                                        <th>المهنة</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var parent in parents)
                                    {
                                        <tr class="@(selectedParents.Contains(parent.Id) ? "table-active" : "")">
                                            <td>
                                                <input type="checkbox" class="form-check-input"
                                                       checked="@selectedParents.Contains(parent.Id)"
                                                       @onchange="@((e) => ToggleParentSelection(parent.Id, (bool)e.Value!))" />
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        @parent.FirstName.Substring(0, 1)@parent.LastName.Substring(0, 1)
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold">@parent.FirstName @parent.LastName</div>
                                                        <small class="text-muted">@parent.Email</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>@parent.Email</td>
                                            <td>@parent.PhoneNumber</td>
                                            <td>@parent.NationalId</td>
                                            <td>
                                                <span class="badge bg-info">@parent.TotalChildren</span>
                                            </td>
                                            <td>@parent.Occupation</td>
                                            <td>
                                                <span class="badge @(parent.IsActive ? "bg-success" : "bg-danger")">
                                                    @(parent.IsActive ? "نشط" : "غير نشط")
                                                </span>
                                            </td>
                                            <td>@parent.CreatedAt.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                <div class="btn-group">
                                                    <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewParent(parent.Id)" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" @onclick="() => ViewChildren(parent.Id)" title="عرض الأبناء">
                                                        <i class="fas fa-child"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning" @onclick="() => EditParent(parent)" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-success" @onclick="() => LinkStudent(parent.Id)" title="ربط طالب">
                                                        <i class="fas fa-link"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteParent(parent.Id)" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد بيانات أولياء أمور</h5>
                            <p class="text-muted">ابدأ بإضافة أولياء أمور جدد للنظام</p>
                            <button class="btn btn-primary" @onclick="ShowAddModal">
                                <i class="fas fa-plus me-2"></i>
                                إضافة ولي أمر جديد
                            </button>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Parent Modal -->
@if (showModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas @(editingParent == null ? "fa-plus" : "fa-edit") me-2"></i>
                        @(editingParent == null ? "إضافة ولي أمر جديد" : "تعديل بيانات ولي الأمر")
                    </h5>
                    <button type="button" class="btn-close btn-close-white" @onclick="CloseModal"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="parentModel" OnValidSubmit="SaveParent">
                        <DataAnnotationsValidator />

                        <div class="row g-3">
                            <!-- Personal Information -->
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-user me-2"></i>
                                    المعلومات الشخصية
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">الاسم الأول <span class="text-danger">*</span></label>
                                <InputText @bind-Value="parentModel.FirstName" class="form-control" placeholder="أدخل الاسم الأول" />
                                <ValidationMessage For="@(() => parentModel.FirstName)" class="text-danger" />
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">اسم العائلة <span class="text-danger">*</span></label>
                                <InputText @bind-Value="parentModel.LastName" class="form-control" placeholder="أدخل اسم العائلة" />
                                <ValidationMessage For="@(() => parentModel.LastName)" class="text-danger" />
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <InputText @bind-Value="parentModel.Email" class="form-control" placeholder="<EMAIL>" />
                                <ValidationMessage For="@(() => parentModel.Email)" class="text-danger" />
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">رقم الهاتف</label>
                                <InputText @bind-Value="parentModel.PhoneNumber" class="form-control" placeholder="+966501234567" />
                                <ValidationMessage For="@(() => parentModel.PhoneNumber)" class="text-danger" />
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">رقم الهوية الوطنية</label>
                                <InputText @bind-Value="parentModel.NationalId" class="form-control" placeholder="1234567890" />
                                <ValidationMessage For="@(() => parentModel.NationalId)" class="text-danger" />
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">تاريخ الميلاد</label>
                                <InputDate @bind-Value="parentModel.DateOfBirth" class="form-control" />
                                <ValidationMessage For="@(() => parentModel.DateOfBirth)" class="text-danger" />
                            </div>

                            <div class="col-12">
                                <label class="form-label">العنوان</label>
                                <InputTextArea @bind-Value="parentModel.Address" class="form-control" rows="2" placeholder="أدخل العنوان الكامل" />
                                <ValidationMessage For="@(() => parentModel.Address)" class="text-danger" />
                            </div>

                            <!-- Contact Information -->
                            <div class="col-12 mt-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-phone me-2"></i>
                                    معلومات الاتصال
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">رقم الطوارئ</label>
                                <InputText @bind-Value="parentModel.EmergencyContact" class="form-control" placeholder="+966502345678" />
                                <ValidationMessage For="@(() => parentModel.EmergencyContact)" class="text-danger" />
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">هاتف العمل</label>
                                <InputText @bind-Value="parentModel.WorkPhone" class="form-control" placeholder="+966112345678" />
                                <ValidationMessage For="@(() => parentModel.WorkPhone)" class="text-danger" />
                            </div>

                            <!-- Work Information -->
                            <div class="col-12 mt-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-briefcase me-2"></i>
                                    معلومات العمل
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">المهنة</label>
                                <InputText @bind-Value="parentModel.Occupation" class="form-control" placeholder="أدخل المهنة" />
                                <ValidationMessage For="@(() => parentModel.Occupation)" class="text-danger" />
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">مكان العمل</label>
                                <InputText @bind-Value="parentModel.WorkPlace" class="form-control" placeholder="أدخل مكان العمل" />
                                <ValidationMessage For="@(() => parentModel.WorkPlace)" class="text-danger" />
                            </div>
                        </div>
                    </EditForm>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseModal">
                        <i class="fas fa-times me-2"></i>
                        إلغاء
                    </button>
                    <button type="button" class="btn btn-primary" @onclick="SaveParent" disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        else
                        {
                            <i class="fas fa-save me-2"></i>
                        }
                        @(editingParent == null ? "إضافة" : "حفظ التغييرات")
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<!-- Link Student Modal -->
@if (showLinkModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-link me-2"></i>
                        ربط طالب بولي الأمر
                    </h5>
                    <button type="button" class="btn-close btn-close-white" @onclick="CloseLinkModal"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="linkModel" OnValidSubmit="LinkStudentToParent">
                        <DataAnnotationsValidator />

                        <div class="mb-3">
                            <label class="form-label">اختر الطالب <span class="text-danger">*</span></label>
                            <select @bind="linkModel.StudentId" class="form-select">
                                <option value="">-- اختر الطالب --</option>
                                @if (availableStudents != null)
                                {
                                    @foreach (var student in availableStudents)
                                    {
                                        <option value="@student.Id">@student.FirstName @student.LastName - @student.StudentNumber</option>
                                    }
                                }
                            </select>
                            <ValidationMessage For="@(() => linkModel.StudentId)" class="text-danger" />
                        </div>

                        <div class="mb-3">
                            <label class="form-label">نوع القرابة <span class="text-danger">*</span></label>
                            <select @bind="linkModel.Relationship" class="form-select">
                                <option value="">-- اختر نوع القرابة --</option>
                                <option value="Father">الأب</option>
                                <option value="Mother">الأم</option>
                                <option value="Guardian">الوصي</option>
                                <option value="Grandfather">الجد</option>
                                <option value="Grandmother">الجدة</option>
                                <option value="Uncle">العم</option>
                                <option value="Aunt">العمة</option>
                                <option value="Other">أخرى</option>
                            </select>
                            <ValidationMessage For="@(() => linkModel.Relationship)" class="text-danger" />
                        </div>
                    </EditForm>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseLinkModal">
                        <i class="fas fa-times me-2"></i>
                        إلغاء
                    </button>
                    <button type="button" class="btn btn-success" @onclick="LinkStudentToParent" disabled="@isLinking">
                        @if (isLinking)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        else
                        {
                            <i class="fas fa-link me-2"></i>
                        }
                        ربط الطالب
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<ParentDto>? parents;
    private List<StudentSummaryDto>? availableStudents;
    private bool isLoading = true;
    private bool showModal = false;
    private bool showLinkModal = false;
    private bool isSaving = false;
    private bool isLinking = false;
    private ParentDto? editingParent;
    private CreateParentDto parentModel = new();
    private LinkParentStudentDto linkModel = new();

    // Statistics
    private int totalParents = 0;
    private int activeParents = 0;
    private int totalChildren = 0;
    private double averageChildren = 0.0;

    // Filters and Pagination
    private string searchTerm = "";
    private string statusFilter = "";
    private string childrenFilter = "";
    private int pageSize = 25;
    private int currentPage = 1;
    private int totalPages = 1;

    // Selection
    private HashSet<string> selectedParents = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadParents();
        await LoadStatistics();
    }

    private async Task LoadParents()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // Build query parameters
            var queryParams = new Dictionary<string, string?>
            {
                ["search"] = string.IsNullOrEmpty(searchTerm) ? null : searchTerm,
                ["isActive"] = string.IsNullOrEmpty(statusFilter) ? null : statusFilter,
                ["page"] = currentPage.ToString(),
                ["pageSize"] = pageSize.ToString()
            };

            parents = await ApiService.GetParentsAsync(queryParams);

            // Calculate pagination
            totalPages = (int)Math.Ceiling((double)totalParents / pageSize);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadStatistics()
    {
        try
        {
            var stats = await ApiService.GetParentsStatisticsAsync();
            totalParents = stats.TotalParents;
            activeParents = stats.ActiveParents;
            totalChildren = stats.TotalParents * (int)stats.AverageChildrenPerParent;
            averageChildren = stats.AverageChildrenPerParent;
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading statistics: {ex.Message}");
        }
    }

    private async Task LoadAvailableStudents()
    {
        try
        {
            // Load students that are not linked to any parent or can be linked to multiple parents
            availableStudents = await ApiService.GetAvailableStudentsForParentLinkAsync();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل قائمة الطلاب: {ex.Message}");
        }
    }

    private void ShowAddModal()
    {
        editingParent = null;
        parentModel = new CreateParentDto();
        showModal = true;
    }

    private void EditParent(ParentDto parent)
    {
        editingParent = parent;
        parentModel = new CreateParentDto
        {
            FirstName = parent.FirstName,
            LastName = parent.LastName,
            Email = parent.Email,
            PhoneNumber = parent.PhoneNumber,
            NationalId = parent.NationalId,
            DateOfBirth = parent.DateOfBirth,
            Address = parent.Address,
            EmergencyContact = parent.EmergencyContact,
            Occupation = parent.Occupation,
            WorkPlace = parent.WorkPlace,
            WorkPhone = parent.WorkPhone
        };
        showModal = true;
    }

    private void CloseModal()
    {
        showModal = false;
        editingParent = null;
        parentModel = new();
    }

    private async Task SaveParent()
    {
        try
        {
            isSaving = true;
            StateHasChanged();

            if (editingParent == null)
            {
                await ApiService.CreateParentAsync(parentModel);
                await JSRuntime.InvokeVoidAsync("alert", "تم إضافة ولي الأمر بنجاح");
            }
            else
            {
                var updateDto = new UpdateParentDto
                {
                    FirstName = parentModel.FirstName,
                    LastName = parentModel.LastName,
                    PhoneNumber = parentModel.PhoneNumber,
                    DateOfBirth = parentModel.DateOfBirth,
                    Address = parentModel.Address,
                    EmergencyContact = parentModel.EmergencyContact,
                    Occupation = parentModel.Occupation,
                    WorkPlace = parentModel.WorkPlace,
                    WorkPhone = parentModel.WorkPhone
                };

                await ApiService.UpdateParentAsync(editingParent.Id, updateDto);
                await JSRuntime.InvokeVoidAsync("alert", "تم تحديث بيانات ولي الأمر بنجاح");
            }

            CloseModal();
            await LoadParents();
            await LoadStatistics();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ البيانات: {ex.Message}");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task DeleteParent(string parentId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف ولي الأمر؟ سيتم حذف جميع الروابط مع الطلاب."))
        {
            try
            {
                await ApiService.DeleteParentAsync(parentId);
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف ولي الأمر بنجاح");
                await LoadParents();
                await LoadStatistics();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف ولي الأمر: {ex.Message}");
            }
        }
    }

    private void ViewParent(string parentId)
    {
        Navigation.NavigateTo($"/admin/parents/{parentId}");
    }

    private void ViewChildren(string parentId)
    {
        Navigation.NavigateTo($"/admin/parents/{parentId}/children");
    }

    private async Task LinkStudent(string parentId)
    {
        linkModel = new LinkParentStudentDto { ParentId = parentId };
        await LoadAvailableStudents();
        showLinkModal = true;
    }

    private void CloseLinkModal()
    {
        showLinkModal = false;
        linkModel = new();
    }

    private async Task LinkStudentToParent()
    {
        try
        {
            isLinking = true;
            StateHasChanged();

            await ApiService.LinkParentStudentAsync(linkModel);
            await JSRuntime.InvokeVoidAsync("alert", "تم ربط الطالب بولي الأمر بنجاح");

            CloseLinkModal();
            await LoadParents();
            await LoadStatistics();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في ربط الطالب: {ex.Message}");
        }
        finally
        {
            isLinking = false;
            StateHasChanged();
        }
    }

    private async Task ResetFilters()
    {
        searchTerm = "";
        statusFilter = "";
        childrenFilter = "";
        currentPage = 1;
        await LoadParents();
    }

    private async Task ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            await LoadParents();
        }
    }

    private void ToggleParentSelection(string parentId, bool isSelected)
    {
        if (isSelected)
            selectedParents.Add(parentId);
        else
            selectedParents.Remove(parentId);
    }

    private void ToggleSelectAll(ChangeEventArgs e)
    {
        var isSelected = (bool)e.Value!;
        selectedParents.Clear();

        if (isSelected && parents != null)
        {
            foreach (var parent in parents)
            {
                selectedParents.Add(parent.Id);
            }
        }
    }
}