@page "/admin/events"
@using Schools.Shared.DTOs
@using Schools.Client.Services
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-gradient-purple text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>
                            إدارة الأنشطة والفعاليات
                        </h4>
                        <button class="btn btn-light" @onclick="ShowAddEventModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة فعالية جديدة
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Calendar View Toggle -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="btn-group" role="group">
                                <button class="btn @(viewMode == "calendar" ? "btn-primary" : "btn-outline-primary")"
                                        @onclick="@(() => SetViewMode("calendar"))">
                                    <i class="fas fa-calendar me-1"></i>
                                    عرض التقويم
                                </button>
                                <button class="btn @(viewMode == "list" ? "btn-primary" : "btn-outline-primary")"
                                        @onclick="@(() => SetViewMode("list"))">
                                    <i class="fas fa-list me-1"></i>
                                    عرض القائمة
                                </button>
                                <button class="btn @(viewMode == "timeline" ? "btn-primary" : "btn-outline-primary")"
                                        @onclick="@(() => SetViewMode("timeline"))">
                                    <i class="fas fa-stream me-1"></i>
                                    الخط الزمني
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <select class="form-select" @bind="selectedCategory" @bind:after="FilterEvents">
                                    <option value="">جميع الفئات</option>
                                    <option value="academic">أكاديمي</option>
                                    <option value="sports">رياضي</option>
                                    <option value="cultural">ثقافي</option>
                                    <option value="social">اجتماعي</option>
                                    <option value="administrative">إداري</option>
                                </select>
                                <input type="text" class="form-control" @bind="searchTerm" @bind:after="FilterEvents"
                                       placeholder="البحث في الفعاليات..." />
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-calendar-day fa-2x mb-2"></i>
                                    <h4>@totalEvents</h4>
                                    <p class="mb-0">إجمالي الفعاليات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-play fa-2x mb-2"></i>
                                    <h4>@activeEvents</h4>
                                    <p class="mb-0">فعاليات نشطة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-clock fa-2x mb-2"></i>
                                    <h4>@upcomingEvents</h4>
                                    <p class="mb-0">فعاليات قادمة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x mb-2"></i>
                                    <h4>@totalParticipants</h4>
                                    <p class="mb-0">إجمالي المشاركين</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else
                    {
                        @if (viewMode == "calendar")
                        {
                            <!-- Calendar View -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0">تقويم الفعاليات - @currentMonth.ToString("MMMM yyyy", new System.Globalization.CultureInfo("ar-SA"))</h5>
                                                <div class="btn-group">
                                                    <button class="btn btn-outline-secondary btn-sm" @onclick="PreviousMonth">
                                                        <i class="fas fa-chevron-right"></i>
                                                    </button>
                                                    <button class="btn btn-outline-secondary btn-sm" @onclick="NextMonth">
                                                        <i class="fas fa-chevron-left"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="calendar-grid">
                                                <!-- Calendar Header -->
                                                <div class="calendar-header">
                                                    <div class="calendar-day-header">الأحد</div>
                                                    <div class="calendar-day-header">الاثنين</div>
                                                    <div class="calendar-day-header">الثلاثاء</div>
                                                    <div class="calendar-day-header">الأربعاء</div>
                                                    <div class="calendar-day-header">الخميس</div>
                                                    <div class="calendar-day-header">الجمعة</div>
                                                    <div class="calendar-day-header">السبت</div>
                                                </div>
                                                <!-- Calendar Days -->
                                                @for (int week = 0; week < 6; week++)
                                                {
                                                    <div class="calendar-week">
                                                        @for (int day = 0; day < 7; day++)
                                                        {
                                                            var date = GetCalendarDate(week, day);
                                                            var dayEvents = GetEventsForDate(date);
                                                            <div class="calendar-day @(date.Month != currentMonth.Month ? "other-month" : "")">
                                                                <div class="day-number">@date.Day</div>
                                                                @foreach (var evt in dayEvents.Take(2))
                                                                {
                                                                    <div class="event-item @GetEventClass(evt.Category)"
                                                                         @onclick="@(() => ViewEvent(evt))"
                                                                         title="@evt.Title">
                                                                        <small>@evt.Title</small>
                                                                    </div>
                                                                }
                                                                @if (dayEvents.Count() > 2)
                                                                {
                                                                    <div class="more-events">
                                                                        <small>+@(dayEvents.Count() - 2) أخرى</small>
                                                                    </div>
                                                                }
                                                            </div>
                                                        }
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                        else if (viewMode == "list")
                        {
                            <!-- List View -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">قائمة الفعاليات</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-striped table-hover">
                                                    <thead class="table-dark">
                                                        <tr>
                                                            <th>الفعالية</th>
                                                            <th>الفئة</th>
                                                            <th>التاريخ</th>
                                                            <th>الوقت</th>
                                                            <th>المكان</th>
                                                            <th>المشاركين</th>
                                                            <th>الحالة</th>
                                                            <th>الإجراءات</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach (var evt in filteredEvents.Take(20))
                                                        {
                                                            <tr>
                                                                <td>
                                                                    <div class="d-flex align-items-center">
                                                                        <i class="@GetEventIcon(evt.Category) me-2 @GetEventColor(evt.Category)"></i>
                                                                        <div>
                                                                            <strong>@evt.Title</strong>
                                                                            <br>
                                                                            <small class="text-muted">@evt.Description</small>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <span class="badge @GetCategoryBadgeClass(evt.Category)">
                                                                        @GetCategoryText(evt.Category)
                                                                    </span>
                                                                </td>
                                                                <td>@evt.StartDate.ToString("yyyy-MM-dd")</td>
                                                                <td>
                                                                    @evt.StartDate.ToString("HH:mm") - @evt.EndDate.ToString("HH:mm")
                                                                </td>
                                                                <td>@evt.Location</td>
                                                                <td>
                                                                    <span class="badge bg-info">@evt.ParticipantCount</span>
                                                                </td>
                                                                <td>
                                                                    <span class="badge @GetStatusBadgeClass(evt.Status)">
                                                                        @GetStatusText(evt.Status)
                                                                    </span>
                                                                </td>
                                                                <td>
                                                                    <div class="btn-group btn-group-sm">
                                                                        <button class="btn btn-outline-primary" @onclick="@(() => ViewEvent(evt))">
                                                                            <i class="fas fa-eye"></i>
                                                                        </button>
                                                                        <button class="btn btn-outline-success" @onclick="@(() => EditEvent(evt))">
                                                                            <i class="fas fa-edit"></i>
                                                                        </button>
                                                                        <button class="btn btn-outline-danger" @onclick="@(() => DeleteEvent(evt))">
                                                                            <i class="fas fa-trash"></i>
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                        else if (viewMode == "timeline")
                        {
                            <!-- Timeline View -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">الخط الزمني للفعاليات</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="timeline-container">
                                                @foreach (var evt in filteredEvents.OrderBy(e => e.StartDate).Take(10))
                                                {
                                                    <div class="timeline-item">
                                                        <div class="timeline-marker @GetEventColor(evt.Category)">
                                                            <i class="@GetEventIcon(evt.Category) text-white"></i>
                                                        </div>
                                                        <div class="timeline-content">
                                                            <div class="timeline-header">
                                                                <h6 class="mb-1">@evt.Title</h6>
                                                                <small class="text-muted">@evt.StartDate.ToString("yyyy-MM-dd HH:mm")</small>
                                                            </div>
                                                            <p class="mb-2">@evt.Description</p>
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <div>
                                                                    <span class="badge @GetCategoryBadgeClass(evt.Category) me-2">
                                                                        @GetCategoryText(evt.Category)
                                                                    </span>
                                                                    <span class="badge bg-info">@evt.ParticipantCount مشارك</span>
                                                                </div>
                                                                <div class="btn-group btn-group-sm">
                                                                    <button class="btn btn-outline-primary" @onclick="@(() => ViewEvent(evt))">
                                                                        عرض
                                                                    </button>
                                                                    <button class="btn btn-outline-success" @onclick="@(() => EditEvent(evt))">
                                                                        تحرير
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .bg-gradient-purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .calendar-grid {
        display: grid;
        grid-template-rows: auto repeat(6, 1fr);
        gap: 1px;
        background-color: #dee2e6;
        border: 1px solid #dee2e6;
    }

    .calendar-header {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 1px;
    }

    .calendar-day-header {
        background-color: #495057;
        color: white;
        padding: 10px;
        text-align: center;
        font-weight: bold;
    }

    .calendar-week {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 1px;
    }

    .calendar-day {
        background-color: white;
        min-height: 120px;
        padding: 5px;
        position: relative;
    }

    .calendar-day.other-month {
        background-color: #f8f9fa;
        color: #6c757d;
    }

    .day-number {
        font-weight: bold;
        margin-bottom: 5px;
    }

    .event-item {
        background-color: #007bff;
        color: white;
        padding: 2px 5px;
        margin-bottom: 2px;
        border-radius: 3px;
        font-size: 10px;
        cursor: pointer;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .event-item.academic { background-color: #007bff; }
    .event-item.sports { background-color: #28a745; }
    .event-item.cultural { background-color: #ffc107; color: #212529; }
    .event-item.social { background-color: #17a2b8; }
    .event-item.administrative { background-color: #6c757d; }

    .more-events {
        color: #6c757d;
        font-size: 10px;
        text-align: center;
    }

    .timeline-container {
        position: relative;
        padding-left: 30px;
    }

    .timeline-container::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 30px;
    }

    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 0;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
    }

    .timeline-content {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #007bff;
    }

    .timeline-header {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
        margin-bottom: 15px;
    }
</style>

@code {
    private bool isLoading = true;
    private string viewMode = "calendar";
    private DateTime currentMonth = DateTime.Now;

    // Statistics
    private int totalEvents = 0;
    private int activeEvents = 0;
    private int upcomingEvents = 0;
    private int totalParticipants = 0;

    // Filters
    private string selectedCategory = "";
    private string searchTerm = "";

    // Data
    private List<EventItem> events = new();
    private List<EventItem> filteredEvents = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadEvents();
    }

    private async Task LoadEvents()
    {
        try
        {
            isLoading = true;

            // Mock events data
            events = new List<EventItem>
            {
                new() { Id = 1, Title = "حفل التخرج", Description = "حفل تخرج الطلاب للعام الدراسي", Category = "academic", StartDate = DateTime.Now.AddDays(15), EndDate = DateTime.Now.AddDays(15).AddHours(3), Location = "القاعة الكبرى", ParticipantCount = 200, Status = "scheduled" },
                new() { Id = 2, Title = "مباراة كرة القدم", Description = "مباراة ودية بين المدارس", Category = "sports", StartDate = DateTime.Now.AddDays(7), EndDate = DateTime.Now.AddDays(7).AddHours(2), Location = "الملعب الرياضي", ParticipantCount = 50, Status = "active" },
                new() { Id = 3, Title = "معرض العلوم", Description = "معرض مشاريع الطلاب العلمية", Category = "academic", StartDate = DateTime.Now.AddDays(20), EndDate = DateTime.Now.AddDays(22), Location = "مختبر العلوم", ParticipantCount = 80, Status = "scheduled" },
                new() { Id = 4, Title = "أمسية شعرية", Description = "أمسية شعرية للطلاب الموهوبين", Category = "cultural", StartDate = DateTime.Now.AddDays(10), EndDate = DateTime.Now.AddDays(10).AddHours(2), Location = "المسرح", ParticipantCount = 120, Status = "scheduled" },
                new() { Id = 5, Title = "اجتماع أولياء الأمور", Description = "اجتماع دوري مع أولياء الأمور", Category = "administrative", StartDate = DateTime.Now.AddDays(5), EndDate = DateTime.Now.AddDays(5).AddHours(2), Location = "القاعة الرئيسية", ParticipantCount = 150, Status = "active" },
                new() { Id = 6, Title = "رحلة ترفيهية", Description = "رحلة ترفيهية للطلاب المتفوقين", Category = "social", StartDate = DateTime.Now.AddDays(25), EndDate = DateTime.Now.AddDays(25).AddHours(8), Location = "الحديقة الوطنية", ParticipantCount = 60, Status = "scheduled" }
            };

            // Calculate statistics
            totalEvents = events.Count;
            activeEvents = events.Count(e => e.Status == "active");
            upcomingEvents = events.Count(e => e.StartDate > DateTime.Now);
            totalParticipants = events.Sum(e => e.ParticipantCount);

            FilterEvents();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الفعاليات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void FilterEvents()
    {
        filteredEvents = events.Where(e =>
            (string.IsNullOrEmpty(selectedCategory) || e.Category == selectedCategory) &&
            (string.IsNullOrEmpty(searchTerm) || e.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) || e.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
        ).OrderBy(e => e.StartDate).ToList();

        StateHasChanged();
    }

    private void SetViewMode(string mode)
    {
        viewMode = mode;
        StateHasChanged();
    }

    private void PreviousMonth()
    {
        currentMonth = currentMonth.AddMonths(-1);
        StateHasChanged();
    }

    private void NextMonth()
    {
        currentMonth = currentMonth.AddMonths(1);
        StateHasChanged();
    }

    private DateTime GetCalendarDate(int week, int day)
    {
        var firstDayOfMonth = new DateTime(currentMonth.Year, currentMonth.Month, 1);
        var startOfCalendar = firstDayOfMonth.AddDays(-(int)firstDayOfMonth.DayOfWeek);
        return startOfCalendar.AddDays(week * 7 + day);
    }

    private IEnumerable<EventItem> GetEventsForDate(DateTime date)
    {
        return filteredEvents.Where(e => e.StartDate.Date == date.Date);
    }

    private async Task ShowAddEventModal()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم فتح نافذة إضافة فعالية جديدة");
    }

    private async Task ViewEvent(EventItem evt)
    {
        await JSRuntime.InvokeVoidAsync("alert", $"عرض تفاصيل الفعالية: {evt.Title}");
    }

    private async Task EditEvent(EventItem evt)
    {
        await JSRuntime.InvokeVoidAsync("alert", $"تحرير الفعالية: {evt.Title}");
    }

    private async Task DeleteEvent(EventItem evt)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"هل تريد حذف الفعالية: {evt.Title}؟");
        if (confirmed)
        {
            events.Remove(evt);
            FilterEvents();
            await JSRuntime.InvokeVoidAsync("showNotification", "تم حذف الفعالية بنجاح", "success");
        }
    }

    private string GetEventClass(string category)
    {
        return category;
    }

    private string GetEventIcon(string category)
    {
        return category switch
        {
            "academic" => "fas fa-graduation-cap",
            "sports" => "fas fa-futbol",
            "cultural" => "fas fa-theater-masks",
            "social" => "fas fa-users",
            "administrative" => "fas fa-cog",
            _ => "fas fa-calendar"
        };
    }

    private string GetEventColor(string category)
    {
        return category switch
        {
            "academic" => "bg-primary",
            "sports" => "bg-success",
            "cultural" => "bg-warning",
            "social" => "bg-info",
            "administrative" => "bg-secondary",
            _ => "bg-primary"
        };
    }

    private string GetCategoryBadgeClass(string category)
    {
        return category switch
        {
            "academic" => "bg-primary",
            "sports" => "bg-success",
            "cultural" => "bg-warning",
            "social" => "bg-info",
            "administrative" => "bg-secondary",
            _ => "bg-primary"
        };
    }

    private string GetCategoryText(string category)
    {
        return category switch
        {
            "academic" => "أكاديمي",
            "sports" => "رياضي",
            "cultural" => "ثقافي",
            "social" => "اجتماعي",
            "administrative" => "إداري",
            _ => category
        };
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "active" => "bg-success",
            "scheduled" => "bg-warning",
            "completed" => "bg-info",
            "cancelled" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetStatusText(string status)
    {
        return status switch
        {
            "active" => "نشط",
            "scheduled" => "مجدول",
            "completed" => "مكتمل",
            "cancelled" => "ملغي",
            _ => status
        };
    }

    public class EventItem
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Category { get; set; } = "";
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Location { get; set; } = "";
        public int ParticipantCount { get; set; }
        public string Status { get; set; } = "";
    }
}
