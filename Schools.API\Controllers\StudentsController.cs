using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;
using System.Security.Claims;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class StudentsController : ControllerBase
    {
        private readonly SchoolsDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public StudentsController(SchoolsDbContext context, UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        #region Students Management

        [HttpGet]
        public async Task<ActionResult<IEnumerable<StudentDto>>> GetStudents()
        {
            try
            {
                var students = await _context.Users
                    .Where(u => u.StudentEnrollments.Any() && u.IsActive)
                    .Include(u => u.StudentEnrollments)
                    .ThenInclude(se => se.AcademicGrade)
                    .Include(u => u.StudentEnrollments)
                    .ThenInclude(se => se.Class)
                    .Include(u => u.StudentEnrollments)
                    .ThenInclude(se => se.Section)
                    .Select(u => new StudentDto
                    {
                        Id = u.Id,
                        FirstName = u.FirstName,
                        LastName = u.LastName,
                        FullName = $"{u.FirstName} {u.LastName}",
                        Email = u.Email ?? "",
                        PhoneNumber = u.PhoneNumber,
                        NationalId = u.NationalId,
                        DateOfBirth = u.DateOfBirth,
                        Address = u.Address,
                        ProfilePicture = u.ProfilePicture,
                        IsActive = u.IsActive,
                        CreatedAt = u.CreatedAt,
                        StudentNumber = u.UserName ?? "",
                        CurrentEnrollment = u.StudentEnrollments
                            .Where(se => se.IsActive)
                            .Select(se => new StudentEnrollmentDto
                            {
                                Id = se.Id,
                                StudentId = se.StudentId,
                                AcademicYearId = se.AcademicYearId,
                                AcademicGradeId = se.AcademicGradeId,
                                GradeName = se.AcademicGrade.Name,
                                ClassId = se.ClassId,
                                ClassName = se.Class.Name,
                                SectionId = se.SectionId,
                                SectionName = se.Section != null ? se.Section.Name : "",
                                EnrollmentDate = se.EnrollmentDate,
                                Status = se.Status,
                                IsActive = se.IsActive
                            }).FirstOrDefault()
                    })
                    .ToListAsync();

                return Ok(students);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب الطلاب", error = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<StudentDto>> GetStudent(string id)
        {
            try
            {
                var student = await _context.Users
                    .Where(u => u.Id == id && u.StudentEnrollments.Any())
                    .Include(u => u.StudentEnrollments)
                    .ThenInclude(se => se.AcademicGrade)
                    .Include(u => u.StudentEnrollments)
                    .ThenInclude(se => se.Class)
                    .Include(u => u.StudentEnrollments)
                    .ThenInclude(se => se.Section)
                    .Include(u => u.StudentEnrollments)
                    .ThenInclude(se => se.AcademicYear)
                    .FirstOrDefaultAsync();

                if (student == null)
                {
                    return NotFound(new { message = "الطالب غير موجود" });
                }

                var studentDto = new StudentDto
                {
                    Id = student.Id,
                    FirstName = student.FirstName,
                    LastName = student.LastName,
                    FullName = $"{student.FirstName} {student.LastName}",
                    Email = student.Email ?? "",
                    PhoneNumber = student.PhoneNumber,
                    NationalId = student.NationalId,
                    DateOfBirth = student.DateOfBirth,
                    Address = student.Address,
                    ProfilePicture = student.ProfilePicture,
                    IsActive = student.IsActive,
                    CreatedAt = student.CreatedAt,
                    StudentNumber = student.UserName ?? "",
                    CurrentEnrollment = student.StudentEnrollments
                        .Where(se => se.IsActive)
                        .Select(se => new StudentEnrollmentDto
                        {
                            Id = se.Id,
                            StudentId = se.StudentId,
                            AcademicYearId = se.AcademicYearId,
                            AcademicYearName = se.AcademicYear.Name,
                            AcademicGradeId = se.AcademicGradeId,
                            GradeName = se.AcademicGrade.Name,
                            ClassId = se.ClassId,
                            ClassName = se.Class.Name,
                            SectionId = se.SectionId,
                            SectionName = se.Section != null ? se.Section.Name : "",
                            EnrollmentDate = se.EnrollmentDate,
                            Status = se.Status,
                            IsActive = se.IsActive
                        }).FirstOrDefault(),
                    EnrollmentHistory = student.StudentEnrollments
                        .Select(se => new StudentEnrollmentDto
                        {
                            Id = se.Id,
                            StudentId = se.StudentId,
                            AcademicYearId = se.AcademicYearId,
                            AcademicYearName = se.AcademicYear.Name,
                            AcademicGradeId = se.AcademicGradeId,
                            GradeName = se.AcademicGrade.Name,
                            ClassId = se.ClassId,
                            ClassName = se.Class.Name,
                            SectionId = se.SectionId,
                            SectionName = se.Section != null ? se.Section.Name : "",
                            EnrollmentDate = se.EnrollmentDate,
                            WithdrawalDate = se.WithdrawalDate,
                            WithdrawalReason = se.WithdrawalReason,
                            Status = se.Status,
                            IsActive = se.IsActive
                        }).ToList()
                };

                return Ok(studentDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب الطالب", error = ex.Message });
            }
        }

        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<StudentDto>> CreateStudent(CreateStudentDto model)
        {
            try
            {
                // Check if user already exists
                var existingUser = await _userManager.FindByEmailAsync(model.Email);
                if (existingUser != null)
                {
                    return BadRequest(new { message = "البريد الإلكتروني مستخدم بالفعل" });
                }

                // Create user
                var user = new ApplicationUser
                {
                    UserName = model.StudentNumber,
                    Email = model.Email,
                    FirstName = model.FirstName,
                    LastName = model.LastName,
                    PhoneNumber = model.PhoneNumber,
                    NationalId = model.NationalId,
                    DateOfBirth = model.DateOfBirth,
                    Address = model.Address,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                };

                var result = await _userManager.CreateAsync(user, model.Password ?? "Student123!");
                if (!result.Succeeded)
                {
                    return BadRequest(new { message = string.Join(", ", result.Errors.Select(e => e.Description)) });
                }

                // Assign Student role
                await _userManager.AddToRoleAsync(user, "Student");

                // Create enrollment if provided
                if (model.AcademicGradeId.HasValue && model.ClassId.HasValue)
                {
                    var currentYear = await _context.AcademicYears
                        .FirstOrDefaultAsync(ay => ay.IsCurrent && ay.IsActive);

                    if (currentYear != null)
                    {
                        var enrollment = new StudentEnrollment
                        {
                            StudentId = user.Id,
                            AcademicYearId = currentYear.Id,
                            AcademicGradeId = model.AcademicGradeId.Value,
                            ClassId = model.ClassId.Value,
                            SectionId = model.SectionId,
                            EnrollmentDate = DateTime.UtcNow,
                            Status = "Active",
                            IsActive = true,
                            CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System",
                            CreatedAt = DateTime.UtcNow
                        };

                        _context.StudentEnrollments.Add(enrollment);
                        await _context.SaveChangesAsync();
                    }
                }

                return CreatedAtAction(nameof(GetStudent), new { id = user.Id }, new { id = user.Id });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في إنشاء الطالب", error = ex.Message });
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UpdateStudent(string id, UpdateStudentDto model)
        {
            try
            {
                var student = await _userManager.FindByIdAsync(id);
                if (student == null)
                {
                    return NotFound(new { message = "الطالب غير موجود" });
                }

                // Update user information
                if (!string.IsNullOrEmpty(model.FirstName))
                    student.FirstName = model.FirstName;
                if (!string.IsNullOrEmpty(model.LastName))
                    student.LastName = model.LastName;
                if (!string.IsNullOrEmpty(model.PhoneNumber))
                    student.PhoneNumber = model.PhoneNumber;
                if (!string.IsNullOrEmpty(model.Address))
                    student.Address = model.Address;
                if (model.DateOfBirth.HasValue)
                    student.DateOfBirth = model.DateOfBirth;
                if (model.IsActive.HasValue)
                    student.IsActive = model.IsActive.Value;

                student.UpdatedAt = DateTime.UtcNow;

                var result = await _userManager.UpdateAsync(student);
                if (!result.Succeeded)
                {
                    return BadRequest(new { message = string.Join(", ", result.Errors.Select(e => e.Description)) });
                }

                return Ok(new { message = "تم تحديث الطالب بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحديث الطالب", error = ex.Message });
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteStudent(string id)
        {
            try
            {
                var student = await _userManager.FindByIdAsync(id);
                if (student == null)
                {
                    return NotFound(new { message = "الطالب غير موجود" });
                }

                // Deactivate instead of delete
                student.IsActive = false;
                student.UpdatedAt = DateTime.UtcNow;

                var result = await _userManager.UpdateAsync(student);
                if (!result.Succeeded)
                {
                    return BadRequest(new { message = string.Join(", ", result.Errors.Select(e => e.Description)) });
                }

                return Ok(new { message = "تم إلغاء تفعيل الطالب بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في حذف الطالب", error = ex.Message });
            }
        }

        #endregion

        #region Student Enrollment

        [HttpPost("{id}/enroll")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> EnrollStudent(string id, CreateStudentEnrollmentDto model)
        {
            try
            {
                var student = await _userManager.FindByIdAsync(id);
                if (student == null)
                {
                    return NotFound(new { message = "الطالب غير موجود" });
                }

                // Check if student is already enrolled in the same academic year
                var existingEnrollment = await _context.StudentEnrollments
                    .FirstOrDefaultAsync(se => se.StudentId == id &&
                                             se.AcademicYearId == model.AcademicYearId &&
                                             se.IsActive);

                if (existingEnrollment != null)
                {
                    return BadRequest(new { message = "الطالب مسجل بالفعل في هذا العام الدراسي" });
                }

                var enrollment = new StudentEnrollment
                {
                    StudentId = id,
                    AcademicYearId = model.AcademicYearId,
                    AcademicGradeId = model.AcademicGradeId,
                    ClassId = model.ClassId,
                    SectionId = model.SectionId,
                    EnrollmentDate = model.EnrollmentDate,
                    Status = "Active",
                    IsActive = true,
                    CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System",
                    CreatedAt = DateTime.UtcNow
                };

                _context.StudentEnrollments.Add(enrollment);
                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تسجيل الطالب بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تسجيل الطالب", error = ex.Message });
            }
        }

        [HttpPut("{id}/transfer")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> TransferStudent(string id, TransferStudentDto model)
        {
            try
            {
                var currentEnrollment = await _context.StudentEnrollments
                    .FirstOrDefaultAsync(se => se.StudentId == id && se.IsActive);

                if (currentEnrollment == null)
                {
                    return NotFound(new { message = "تسجيل الطالب غير موجود" });
                }

                // Update current enrollment
                if (model.NewClassId.HasValue)
                    currentEnrollment.ClassId = model.NewClassId.Value;
                if (model.NewSectionId.HasValue)
                    currentEnrollment.SectionId = model.NewSectionId;
                if (model.NewAcademicGradeId.HasValue)
                    currentEnrollment.AcademicGradeId = model.NewAcademicGradeId.Value;

                currentEnrollment.UpdatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System";
                currentEnrollment.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم نقل الطالب بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في نقل الطالب", error = ex.Message });
            }
        }

        [HttpPut("{id}/withdraw")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> WithdrawStudent(string id, WithdrawStudentDto model)
        {
            try
            {
                var enrollment = await _context.StudentEnrollments
                    .FirstOrDefaultAsync(se => se.StudentId == id && se.IsActive);

                if (enrollment == null)
                {
                    return NotFound(new { message = "تسجيل الطالب غير موجود" });
                }

                enrollment.WithdrawalDate = model.WithdrawalDate;
                enrollment.WithdrawalReason = model.WithdrawalReason;
                enrollment.Status = "Withdrawn";
                enrollment.IsActive = false;
                enrollment.UpdatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System";
                enrollment.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم انسحاب الطالب بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في انسحاب الطالب", error = ex.Message });
            }
        }

        #endregion

        #region Student Performance

        [HttpGet("{id}/grades")]
        public async Task<ActionResult<IEnumerable<StudentGradeDto>>> GetStudentGrades(string id)
        {
            try
            {
                var grades = await _context.Grades
                    .Include(g => g.Subject)
                    .Include(g => g.AcademicYear)
                    .Where(g => g.StudentId == id)
                    .Select(g => new StudentGradeDto
                    {
                        Id = g.Id,
                        SubjectId = g.SubjectId,
                        SubjectName = g.Subject.Name,
                        ExamType = g.ExamType,
                        Score = g.Score,
                        MaxScore = g.MaxScore,
                        Percentage = g.Percentage,
                        Grade = GetLetterGrade(g.Percentage),
                        Date = g.Date,
                        Semester = g.Semester,
                        AcademicYearName = g.AcademicYear.Name,
                        Notes = g.Notes
                    })
                    .OrderByDescending(g => g.Date)
                    .ToListAsync();

                return Ok(grades);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب درجات الطالب", error = ex.Message });
            }
        }

        [HttpGet("{id}/attendance")]
        public async Task<ActionResult<IEnumerable<StudentAttendanceDto>>> GetStudentAttendance(string id)
        {
            try
            {
                var attendance = await _context.Attendances
                    .Include(a => a.Subject)
                    .Include(a => a.Schedule)
                    .Where(a => a.StudentId == id)
                    .Select(a => new StudentAttendanceDto
                    {
                        Id = a.Id,
                        Date = a.Date,
                        SubjectName = a.Subject != null ? a.Subject.Name : "عام",
                        Status = a.Status,
                        CheckInTime = a.CheckInTime,
                        CheckOutTime = a.CheckOutTime,
                        Reason = a.Reason,
                        Notes = a.Notes
                    })
                    .OrderByDescending(a => a.Date)
                    .ToListAsync();

                return Ok(attendance);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب حضور الطالب", error = ex.Message });
            }
        }

        [HttpGet("{id}/dashboard")]
        public async Task<ActionResult<StudentDashboardDto>> GetStudentDashboard(string id)
        {
            try
            {
                var student = await _context.Users
                    .Include(u => u.StudentEnrollments)
                    .ThenInclude(se => se.AcademicGrade)
                    .Include(u => u.StudentEnrollments)
                    .ThenInclude(se => se.Class)
                    .FirstOrDefaultAsync(u => u.Id == id);

                if (student == null)
                {
                    return NotFound(new { message = "الطالب غير موجود" });
                }

                var currentEnrollment = student.StudentEnrollments.FirstOrDefault(se => se.IsActive);
                if (currentEnrollment == null)
                {
                    return BadRequest(new { message = "الطالب غير مسجل في أي صف" });
                }

                // Get recent grades
                var recentGrades = await _context.Grades
                    .Include(g => g.Subject)
                    .Where(g => g.StudentId == id)
                    .OrderByDescending(g => g.Date)
                    .Take(5)
                    .Select(g => new RecentGradeDto
                    {
                        Id = g.Id,
                        SubjectName = g.Subject.Name,
                        ExamType = g.ExamType,
                        Score = g.Score,
                        MaxScore = g.MaxScore,
                        Percentage = g.Percentage,
                        Grade = GetLetterGrade(g.Percentage),
                        Date = g.Date,
                        IsPassed = g.Percentage >= 50
                    })
                    .ToListAsync();

                // Get attendance summary
                var totalDays = await _context.Attendances
                    .CountAsync(a => a.StudentId == id && a.Date >= DateTime.Now.AddMonths(-1));

                var presentDays = await _context.Attendances
                    .CountAsync(a => a.StudentId == id && a.Status == "Present" && a.Date >= DateTime.Now.AddMonths(-1));

                var dashboard = new StudentDashboardDto
                {
                    StudentInfo = new StudentInfoDto
                    {
                        Id = student.Id,
                        FirstName = student.FirstName,
                        LastName = student.LastName,
                        StudentNumber = student.UserName ?? "",
                        GradeName = currentEnrollment.AcademicGrade.Name,
                        ClassName = currentEnrollment.Class.Name
                    },
                    RecentGrades = recentGrades,
                    AttendanceRate = totalDays > 0 ? (double)presentDays / totalDays * 100 : 0,
                    TotalSubjects = await _context.TeacherAssignments
                        .CountAsync(ta => ta.ClassId == currentEnrollment.ClassId && ta.IsActive),
                    UpcomingExams = await GetUpcomingExams(currentEnrollment.ClassId)
                };

                return Ok(dashboard);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب لوحة الطالب", error = ex.Message });
            }
        }

        #endregion

        #region Helper Methods

        private string GetLetterGrade(decimal percentage)
        {
            return percentage switch
            {
                >= 90 => "ممتاز",
                >= 80 => "جيد جداً",
                >= 70 => "جيد",
                >= 60 => "مقبول",
                >= 50 => "ضعيف",
                _ => "راسب"
            };
        }

        private async Task<List<AvailableExamDto>> GetUpcomingExams(int classId)
        {
            return await _context.Exams
                .Where(e => e.ClassId == classId &&
                           e.StartDate > DateTime.Now &&
                           e.IsActive)
                .Select(e => new AvailableExamDto
                {
                    Id = e.Id,
                    Title = e.Title,
                    Description = e.Description,
                    SubjectName = e.Subject.Name,
                    StartDate = e.StartDate,
                    EndDate = e.EndDate,
                    DurationMinutes = e.DurationMinutes,
                    TotalMarks = e.TotalMarks,
                    TotalQuestions = e.Questions.Count,
                    IsAvailable = e.StartDate <= DateTime.Now && e.EndDate >= DateTime.Now,
                    Status = "قادم"
                })
                .Take(5)
                .ToListAsync();
        }

        #endregion
    }
}
