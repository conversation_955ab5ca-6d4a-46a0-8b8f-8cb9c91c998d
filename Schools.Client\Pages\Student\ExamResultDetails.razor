@page "/student/exam-result/{AttemptId:int}"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Student")]

<PageTitle>تفاصيل نتيجة الامتحان - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/student/exams">الامتحانات</a></li>
                    <li class="breadcrumb-item active">تفاصيل النتيجة</li>
                </ol>
            </nav>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل تفاصيل النتيجة...</p>
        </div>
    }
    else if (result != null)
    {
        <!-- Result Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card @(result.IsPassed ? "bg-gradient-success" : "bg-gradient-danger") text-white border-0 shadow-lg">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="mb-1">
                                    <i class="fas @(result.IsPassed ? "fa-check-circle" : "fa-times-circle") me-2"></i>
                                    @result.ExamTitle
                                </h2>
                                <p class="mb-0 opacity-75">
                                    @(result.IsPassed ? "مبروك! لقد نجحت في الامتحان" : "للأسف، لم تحقق درجة النجاح")
                                </p>
                                <div class="mt-2">
                                    <span class="badge bg-light text-dark me-2">@result.EndTime.ToString("dd/MM/yyyy")</span>
                                    <span class="badge bg-light text-dark">@result.DurationTaken دقيقة</span>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="result-score-large">
                                    <h1 class="display-4 mb-0">@result.Percentage.ToString("F1")%</h1>
                                    <p class="mb-0">@result.MarksObtained / @result.TotalMarks</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Summary -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-success text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-check fa-lg"></i>
                        </div>
                        <h4 class="text-success mb-1">@result.CorrectAnswers</h4>
                        <p class="text-muted mb-0">إجابات صحيحة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-danger text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-times fa-lg"></i>
                        </div>
                        <h4 class="text-danger mb-1">@(result.TotalQuestions - result.CorrectAnswers)</h4>
                        <p class="text-muted mb-0">إجابات خاطئة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-primary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-star fa-lg"></i>
                        </div>
                        <h4 class="text-primary mb-1">@result.Grade</h4>
                        <p class="text-muted mb-0">التقدير</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-info text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-clock fa-lg"></i>
                        </div>
                        <h4 class="text-info mb-1">@result.DurationTaken</h4>
                        <p class="text-muted mb-0">دقيقة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Question by Question Review -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0">
                                    <i class="fas fa-list-alt me-2 text-primary"></i>
                                    مراجعة الأسئلة والإجابات
                                </h5>
                            </div>
                            <div class="col-auto">
                                <div class="btn-group">
                                    <button class="btn btn-outline-success btn-sm" @onclick="@(() => FilterQuestions("correct"))">
                                        <i class="fas fa-check me-2"></i>
                                        الصحيحة (@result.CorrectAnswers)
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" @onclick="@(() => FilterQuestions("incorrect"))">
                                        <i class="fas fa-times me-2"></i>
                                        الخاطئة (@(result.TotalQuestions - result.CorrectAnswers))
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" @onclick="@(() => FilterQuestions("all"))">
                                        <i class="fas fa-list me-2"></i>
                                        الكل
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (filteredQuestions?.Any() == true)
                        {
                            <div class="questions-review">
                                @foreach (var question in filteredQuestions.Select((q, index) => new { Question = q, Index = index + 1 }))
                                {
                                    <div class="question-review-item card mb-4 border @(question.Question.IsCorrect ? "border-success" : "border-danger")">
                                        <div class="card-header @(question.Question.IsCorrect ? "bg-light-success" : "bg-light-danger")">
                                            <div class="row align-items-center">
                                                <div class="col">
                                                    <div class="d-flex align-items-center">
                                                        <span class="badge @(question.Question.IsCorrect ? "bg-success" : "bg-danger") me-2">
                                                            السؤال @question.Index
                                                        </span>
                                                        <span class="badge bg-primary me-2">@GetQuestionTypeText(question.Question.Type)</span>
                                                        <span class="badge bg-info">@question.Question.TotalMarks درجة</span>
                                                    </div>
                                                </div>
                                                <div class="col-auto">
                                                    <div class="score-indicator">
                                                        <span class="@(question.Question.IsCorrect ? "text-success" : "text-danger") fw-bold">
                                                            @question.Question.MarksObtained / @question.Question.TotalMarks
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="question-content mb-3">
                                                <h6 class="question-text">@question.Question.QuestionText</h6>
                                            </div>

                                            <div class="answer-section">
                                                @if (question.Question.Type == QuestionType.MultipleChoice)
                                                {
                                                    <div class="options-review">
                                                        <div class="row g-2">
                                                            @foreach (var option in question.Question.Options)
                                                            {
                                                                <div class="col-md-6">
                                                                    <div class="option-review @GetOptionClass(option, question.Question.StudentAnswer)">
                                                                        <div class="d-flex align-items-center">
                                                                            <span class="option-letter me-2">@GetOptionLetter(option.OrderIndex)</span>
                                                                            <span class="flex-grow-1">@option.OptionText</span>
                                                                            @if (option.IsCorrect)
                                                                            {
                                                                                <i class="fas fa-check text-success ms-2" title="الإجابة الصحيحة"></i>
                                                                            }
                                                                            @if (question.Question.StudentAnswer == option.Id.ToString())
                                                                            {
                                                                                <i class="fas fa-user text-primary ms-2" title="إجابتك"></i>
                                                                            }
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            }
                                                        </div>
                                                    </div>
                                                }
                                                else if (question.Question.Type == QuestionType.TrueFalse)
                                                {
                                                    <div class="true-false-review">
                                                        <div class="row g-3">
                                                            <div class="col-md-6">
                                                                <div class="answer-item @(question.Question.CorrectAnswer == "صحيح" ? "correct-answer" : "") @(question.Question.StudentAnswer == "صحيح" ? "student-answer" : "")">
                                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                                    صحيح
                                                                    @if (question.Question.CorrectAnswer == "صحيح")
                                                                    {
                                                                        <i class="fas fa-check text-success ms-2" title="الإجابة الصحيحة"></i>
                                                                    }
                                                                    @if (question.Question.StudentAnswer == "صحيح")
                                                                    {
                                                                        <i class="fas fa-user text-primary ms-2" title="إجابتك"></i>
                                                                    }
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="answer-item @(question.Question.CorrectAnswer == "خطأ" ? "correct-answer" : "") @(question.Question.StudentAnswer == "خطأ" ? "student-answer" : "")">
                                                                    <i class="fas fa-times-circle text-danger me-2"></i>
                                                                    خطأ
                                                                    @if (question.Question.CorrectAnswer == "خطأ")
                                                                    {
                                                                        <i class="fas fa-check text-success ms-2" title="الإجابة الصحيحة"></i>
                                                                    }
                                                                    @if (question.Question.StudentAnswer == "خطأ")
                                                                    {
                                                                        <i class="fas fa-user text-primary ms-2" title="إجابتك"></i>
                                                                    }
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <div class="text-answer-review">
                                                        <div class="mb-3">
                                                            <h6 class="text-muted mb-2">إجابتك:</h6>
                                                            <div class="student-text-answer">
                                                                @(string.IsNullOrEmpty(question.Question.StudentAnswer) ? "لم تتم الإجابة" : question.Question.StudentAnswer)
                                                            </div>
                                                        </div>
                                                        @if (!string.IsNullOrEmpty(question.Question.CorrectAnswer))
                                                        {
                                                            <div class="mb-3">
                                                                <h6 class="text-muted mb-2">الإجابة النموذجية:</h6>
                                                                <div class="correct-text-answer">
                                                                    @question.Question.CorrectAnswer
                                                                </div>
                                                            </div>
                                                        }
                                                    </div>
                                                }
                                            </div>

                                            @if (!string.IsNullOrEmpty(question.Question.Explanation))
                                            {
                                                <div class="explanation-section mt-3">
                                                    <div class="alert alert-info">
                                                        <h6 class="alert-heading">
                                                            <i class="fas fa-lightbulb me-2"></i>
                                                            التفسير:
                                                        </h6>
                                                        <p class="mb-0">@question.Question.Explanation</p>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">لا توجد أسئلة تطابق المرشح المحدد</h6>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <div class="btn-group">
                    <a href="/student/exams" class="btn btn-primary">
                        <i class="fas fa-list me-2"></i>
                        العودة للامتحانات
                    </a>
                    <button class="btn btn-outline-primary" @onclick="PrintResult">
                        <i class="fas fa-print me-2"></i>
                        طباعة النتيجة
                    </button>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h5 class="text-muted">لم يتم العثور على النتيجة</h5>
            <p class="text-muted">تأكد من صحة الرابط أو تواصل مع الإدارة</p>
            <a href="/student/exams" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للامتحانات
            </a>
        </div>
    }
</div>

<style>
    .avatar-md {
        width: 48px;
        height: 48px;
    }

    .bg-gradient-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }

    .bg-gradient-danger {
        background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    }

    .bg-light-success {
        background-color: rgba(40, 167, 69, 0.1);
    }

    .bg-light-danger {
        background-color: rgba(220, 53, 69, 0.1);
    }

    .result-score-large {
        text-align: center;
    }

    .question-review-item {
        transition: all 0.3s ease;
    }

    .question-review-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }

    .option-review {
        padding: 10px 15px;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }

    .option-review.correct-answer {
        background-color: #d4edda;
        border-color: #c3e6cb;
    }

    .option-review.student-answer {
        background-color: #cce7ff;
        border-color: #99d3ff;
    }

    .option-review.correct-answer.student-answer {
        background-color: #d1ecf1;
        border-color: #bee5eb;
    }

    .option-review.wrong-student-answer {
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }

    .option-letter {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        background-color: #007bff;
        color: white;
        border-radius: 50%;
        font-size: 12px;
        font-weight: bold;
    }

    .answer-item {
        padding: 10px 15px;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        background-color: #f8f9fa;
    }

    .answer-item.correct-answer {
        background-color: #d4edda;
        border-color: #c3e6cb;
    }

    .answer-item.student-answer {
        background-color: #cce7ff;
        border-color: #99d3ff;
    }

    .student-text-answer {
        padding: 10px;
        background-color: #cce7ff;
        border: 1px solid #99d3ff;
        border-radius: 6px;
        min-height: 50px;
    }

    .correct-text-answer {
        padding: 10px;
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 6px;
        min-height: 50px;
    }

    .question-text {
        line-height: 1.6;
        margin-bottom: 15px;
    }

    .score-indicator {
        font-size: 1.1rem;
    }
</style>

@code {
    [Parameter] public int AttemptId { get; set; }

    private ExamResultDto? result;
    private List<QuestionResultDto> filteredQuestions = new();
    private bool isLoading = true;
    private string currentFilter = "all";

    protected override async Task OnInitializedAsync()
    {
        await LoadExamResult();
    }

    private async Task LoadExamResult()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            result = await ApiService.GetExamResultAsync(AttemptId);
            if (result != null)
            {
                filteredQuestions = result.QuestionResults.ToList();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل تفاصيل النتيجة: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void FilterQuestions(string filter)
    {
        currentFilter = filter;

        if (result == null) return;

        filteredQuestions = filter switch
        {
            "correct" => result.QuestionResults.Where(q => q.IsCorrect).ToList(),
            "incorrect" => result.QuestionResults.Where(q => !q.IsCorrect).ToList(),
            _ => result.QuestionResults.ToList()
        };

        StateHasChanged();
    }

    private async Task PrintResult()
    {
        await JSRuntime.InvokeVoidAsync("window.print");
    }

    private string GetQuestionTypeText(QuestionType type)
    {
        return type switch
        {
            QuestionType.MultipleChoice => "اختيار متعدد",
            QuestionType.TrueFalse => "صح/خطأ",
            QuestionType.ShortAnswer => "إجابة قصيرة",
            QuestionType.Essay => "مقال",
            _ => "غير محدد"
        };
    }

    private string GetOptionClass(QuestionOptionDto option, string? studentAnswer)
    {
        var classes = new List<string> { "option-review" };

        if (option.IsCorrect)
        {
            classes.Add("correct-answer");
        }

        if (studentAnswer == option.Id.ToString())
        {
            classes.Add("student-answer");
            if (!option.IsCorrect)
            {
                classes.Add("wrong-student-answer");
            }
        }

        return string.Join(" ", classes);
    }

    private string GetOptionLetter(int index)
    {
        var letters = new[] { "أ", "ب", "ج", "د", "هـ", "و", "ز", "ح", "ط", "ي" };
        return index > 0 && index <= letters.Length ? letters[index - 1] : index.ToString();
    }
}
