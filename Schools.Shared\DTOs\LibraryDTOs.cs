namespace Schools.Shared.DTOs
{
    // Library DTOs
    public class BookDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public string ISBN { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Grade { get; set; } = string.Empty;
        public int PublishYear { get; set; }
        public int Pages { get; set; }
        public string Publisher { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Location { get; set; }
        public string? BorrowerName { get; set; }
        public DateTime? BorrowDate { get; set; }
        public DateTime? DueDate { get; set; }
    }

    public class CreateBookDto
    {
        public string Title { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public string ISBN { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Grade { get; set; } = string.Empty;
        public int PublishYear { get; set; }
        public int Pages { get; set; }
        public string Publisher { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Location { get; set; }
    }

    public class UpdateBookDto
    {
        public string Title { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public string ISBN { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Grade { get; set; } = string.Empty;
        public int PublishYear { get; set; }
        public int Pages { get; set; }
        public string Publisher { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Location { get; set; }
    }

    public class BorrowBookDto
    {
        public string StudentId { get; set; } = string.Empty;
    }

    public class BorrowRecordDto
    {
        public int Id { get; set; }
        public int BookId { get; set; }
        public string BookTitle { get; set; } = string.Empty;
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public DateTime BorrowDate { get; set; }
        public DateTime DueDate { get; set; }
        public DateTime? ReturnDate { get; set; }
    }

    public class LibraryStatisticsDto
    {
        public int TotalBooks { get; set; }
        public int AvailableBooks { get; set; }
        public int BorrowedBooks { get; set; }
        public int OverdueBooks { get; set; }
        public List<CategoryStatDto> CategoryStatistics { get; set; } = new();
    }

    public class CategoryStatDto
    {
        public string Category { get; set; } = string.Empty;
        public int Count { get; set; }
    }
}
