using Microsoft.EntityFrameworkCore;
using Schools.API.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;

namespace Schools.API.Services;

public class StudentService : IStudentService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<StudentService> _logger;

    public StudentService(ApplicationDbContext context, ILogger<StudentService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<PagedResult<StudentDto>> GetStudentsAsync(
        string? search = null,
        string? grade = null,
        string? className = null,
        bool? isActive = null,
        int page = 1,
        int pageSize = 20)
    {
        try
        {
            var query = _context.Users
                .Where(u => u.StudentEnrollments.Any())
                .Include(u => u.StudentEnrollments)
                .ThenInclude(se => se.Class)
                .ThenInclude(c => c.Grade)
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(search))
            {
                query = query.Where(u => 
                    u.FirstName.Contains(search) || 
                    u.LastName.Contains(search) || 
                    u.Email.Contains(search) ||
                    u.UserName.Contains(search));
            }

            if (!string.IsNullOrEmpty(grade))
            {
                query = query.Where(u => u.StudentEnrollments.Any(se => se.Class.Grade.Name == grade));
            }

            if (!string.IsNullOrEmpty(className))
            {
                query = query.Where(u => u.StudentEnrollments.Any(se => se.Class.Name == className));
            }

            if (isActive.HasValue)
            {
                query = query.Where(u => u.IsActive == isActive.Value);
            }

            var totalCount = await query.CountAsync();
            var students = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(u => new StudentDto
                {
                    Id = u.Id,
                    FirstName = u.FirstName,
                    LastName = u.LastName,
                    Email = u.Email,
                    PhoneNumber = u.PhoneNumber,
                    StudentNumber = u.UserName ?? "",
                    DateOfBirth = u.DateOfBirth,
                    Address = u.Address,
                    GradeName = u.StudentEnrollments.FirstOrDefault(se => se.IsActive).Class.Grade.Name ?? "",
                    ClassName = u.StudentEnrollments.FirstOrDefault(se => se.IsActive).Class.Name ?? "",
                    IsActive = u.IsActive,
                    CreatedDate = u.CreatedAt
                })
                .ToListAsync();

            return new PagedResult<StudentDto>
            {
                Items = students,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting students");
            throw;
        }
    }

    public async Task<StudentDto?> GetStudentByIdAsync(string id)
    {
        try
        {
            var student = await _context.Users
                .Where(u => u.Id == id && u.StudentEnrollments.Any())
                .Include(u => u.StudentEnrollments)
                .ThenInclude(se => se.Class)
                .ThenInclude(c => c.Grade)
                .FirstOrDefaultAsync();

            if (student == null)
                return null;

            var activeEnrollment = student.StudentEnrollments.FirstOrDefault(se => se.IsActive);

            return new StudentDto
            {
                Id = student.Id,
                FirstName = student.FirstName,
                LastName = student.LastName,
                Email = student.Email,
                PhoneNumber = student.PhoneNumber,
                StudentNumber = student.UserName ?? "",
                DateOfBirth = student.DateOfBirth,
                Address = student.Address,
                GradeName = activeEnrollment?.Class?.Grade?.Name ?? "",
                ClassName = activeEnrollment?.Class?.Name ?? "",
                IsActive = student.IsActive,
                CreatedDate = student.CreatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting student {Id}", id);
            throw;
        }
    }

    public async Task<StudentDto> CreateStudentAsync(CreateStudentDto createDto)
    {
        try
        {
            // This would typically involve creating a user account
            // For now, we'll return a placeholder implementation
            var studentDto = new StudentDto
            {
                Id = Guid.NewGuid().ToString(),
                FirstName = createDto.FirstName,
                LastName = createDto.LastName,
                Email = createDto.Email,
                PhoneNumber = createDto.PhoneNumber,
                StudentNumber = createDto.StudentNumber,
                DateOfBirth = createDto.DateOfBirth,
                Address = createDto.Address,
                IsActive = true,
                CreatedDate = DateTime.Now
            };

            return studentDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating student");
            throw;
        }
    }

    public async Task<StudentDto?> UpdateStudentAsync(string id, UpdateStudentDto updateDto)
    {
        try
        {
            var student = await _context.Users.FindAsync(id);
            if (student == null)
                return null;

            // Update properties
            student.FirstName = updateDto.FirstName;
            student.LastName = updateDto.LastName;
            student.Email = updateDto.Email;
            student.PhoneNumber = updateDto.PhoneNumber;
            student.DateOfBirth = updateDto.DateOfBirth;
            student.Address = updateDto.Address;

            await _context.SaveChangesAsync();

            return await GetStudentByIdAsync(id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating student {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteStudentAsync(string id)
    {
        try
        {
            var student = await _context.Users.FindAsync(id);
            if (student == null)
                return false;

            // Soft delete
            student.IsActive = false;
            await _context.SaveChangesAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting student {Id}", id);
            throw;
        }
    }

    public async Task<bool> ActivateStudentAsync(string id)
    {
        try
        {
            var student = await _context.Users.FindAsync(id);
            if (student == null)
                return false;

            student.IsActive = true;
            await _context.SaveChangesAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating student {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeactivateStudentAsync(string id)
    {
        try
        {
            var student = await _context.Users.FindAsync(id);
            if (student == null)
                return false;

            student.IsActive = false;
            await _context.SaveChangesAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating student {Id}", id);
            throw;
        }
    }

    public async Task<List<GradeDto>> GetStudentGradesAsync(string studentId, int? subjectId = null, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var query = _context.Grades
                .Where(g => g.StudentId == studentId)
                .Include(g => g.Subject)
                .AsQueryable();

            if (subjectId.HasValue)
                query = query.Where(g => g.SubjectId == subjectId.Value);

            if (startDate.HasValue)
                query = query.Where(g => g.Date >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(g => g.Date <= endDate.Value);

            return await query
                .Select(g => new GradeDto
                {
                    Id = g.Id,
                    StudentId = g.StudentId,
                    SubjectId = g.SubjectId,
                    SubjectName = g.Subject.Name,
                    Score = g.Score,
                    MaxScore = g.MaxScore,
                    GradeType = g.GradeType,
                    Date = g.Date,
                    Comments = g.Comments
                })
                .OrderByDescending(g => g.Date)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting grades for student {StudentId}", studentId);
            throw;
        }
    }

    public async Task<List<AttendanceRecordDto>> GetStudentAttendanceAsync(string studentId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            var query = _context.AttendanceRecords
                .Where(a => a.StudentId == studentId)
                .AsQueryable();

            if (startDate.HasValue)
                query = query.Where(a => a.Date >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(a => a.Date <= endDate.Value);

            return await query
                .Select(a => new AttendanceRecordDto
                {
                    Id = a.Id,
                    StudentId = a.StudentId,
                    Date = a.Date,
                    IsPresent = a.IsPresent,
                    IsLate = a.IsLate,
                    Notes = a.Notes,
                    RecordedBy = a.RecordedBy,
                    RecordedAt = a.RecordedAt
                })
                .OrderByDescending(a => a.Date)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting attendance for student {StudentId}", studentId);
            throw;
        }
    }

    // Placeholder implementations for remaining methods
    public async Task<StudentPerformanceSummaryDto> GetStudentPerformanceSummaryAsync(string studentId)
    {
        // Implementation would calculate performance metrics
        return new StudentPerformanceSummaryDto();
    }

    public async Task<List<StudentEnrollmentDto>> GetStudentEnrollmentsAsync(string studentId)
    {
        // Implementation would return enrollment history
        return new List<StudentEnrollmentDto>();
    }

    public async Task<StudentEnrollmentDto> EnrollStudentInClassAsync(string studentId, int classId)
    {
        // Implementation would create enrollment record
        return new StudentEnrollmentDto();
    }

    public async Task<bool> UnenrollStudentFromClassAsync(string studentId, int classId)
    {
        // Implementation would deactivate enrollment
        return true;
    }

    public async Task<StudentReportCardDto> GenerateReportCardAsync(string studentId, int academicYearId, int? termId = null)
    {
        // Implementation would generate comprehensive report card
        return new StudentReportCardDto();
    }

    public async Task<List<StudentDto>> GetStudentsByClassAsync(int classId)
    {
        // Implementation would return students in specific class
        return new List<StudentDto>();
    }

    public async Task<List<StudentDto>> GetStudentsByGradeAsync(int gradeId)
    {
        // Implementation would return students in specific grade
        return new List<StudentDto>();
    }
}
