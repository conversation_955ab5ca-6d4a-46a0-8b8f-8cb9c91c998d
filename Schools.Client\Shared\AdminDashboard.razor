@using Schools.Client.Services
@using Schools.Shared.DTOs
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<div class="container-fluid py-4">
    <!-- Dashboard Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-tachometer-alt text-primary me-2"></i>
                        لوحة تحكم الإدارة
                    </h2>
                    <p class="text-muted">مرحباً بك في لوحة التحكم الرئيسية</p>
                </div>
                <div class="text-end">
                    <small class="text-muted">آخر تحديث: @DateTime.Now.ToString("yyyy/MM/dd HH:mm")</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-users fa-2x text-primary"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">إجمالي المستخدمين</h6>
                            <h3 class="mb-0">@totalUsers</h3>
                            <small class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>
                                +12% من الشهر الماضي
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-user-graduate fa-2x text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">عدد الطلاب</h6>
                            <h3 class="mb-0">@totalStudents</h3>
                            <small class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>
                                +8% من الشهر الماضي
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-chalkboard-teacher fa-2x text-info"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">عدد المعلمين</h6>
                            <h3 class="mb-0">@totalTeachers</h3>
                            <small class="text-info">
                                <i class="fas fa-minus me-1"></i>
                                نفس الشهر الماضي
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                <i class="fas fa-clock fa-2x text-warning"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">طلبات الموافقة</h6>
                            <h3 class="mb-0">@pendingRequests</h3>
                            <small class="text-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                يتطلب مراجعة
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0 pb-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt text-primary me-2"></i>
                        الإجراءات السريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-lg-2 col-md-4 col-6">
                            <button class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3"
                                    @onclick='() => NavigateToPage("/admin/academic-years")'>
                                <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                                <span class="small">الأعوام الدراسية</span>
                            </button>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <button class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3"
                                    @onclick='() => NavigateToPage("/admin/grades")'>
                                <i class="fas fa-layer-group fa-2x mb-2"></i>
                                <span class="small">المراحل الدراسية</span>
                            </button>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <button class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3"
                                    @onclick='() => NavigateToPage("/admin/classes")'>
                                <i class="fas fa-school fa-2x mb-2"></i>
                                <span class="small">الصفوف الدراسية</span>
                            </button>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <button class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3"
                                    @onclick='() => NavigateToPage("/admin/subjects")'>
                                <i class="fas fa-book fa-2x mb-2"></i>
                                <span class="small">المواد الدراسية</span>
                            </button>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <button class="btn btn-outline-danger w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3"
                                    @onclick='() => NavigateToPage("/admin/users")'>
                                <i class="fas fa-user-check fa-2x mb-2"></i>
                                <span class="small">موافقة المستخدمين</span>
                            </button>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <button class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3"
                                    @onclick='() => NavigateToPage("/admin/reports")'>
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <span class="small">التقارير</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities & Pending Approvals -->
    <div class="row">
        <!-- Recent Activities -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history text-primary me-2"></i>
                        الأنشطة الأخيرة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم إنشاء عام دراسي جديد</h6>
                                <p class="text-muted mb-1">العام الدراسي 2024-2025</p>
                                <small class="text-muted">منذ ساعتين</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم الموافقة على معلم جديد</h6>
                                <p class="text-muted mb-1">أحمد محمد - معلم رياضيات</p>
                                <small class="text-muted">منذ 4 ساعات</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-warning"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">طلب موافقة جديد</h6>
                                <p class="text-muted mb-1">فاطمة علي - طالبة</p>
                                <small class="text-muted">منذ 6 ساعات</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Approvals -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-transparent border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock text-warning me-2"></i>
                        طلبات الموافقة
                    </h5>
                </div>
                <div class="card-body">
                    @if (pendingUsers.Any())
                    {
                        @foreach (var user in pendingUsers.Take(5))
                        {
                            <div class="d-flex align-items-center mb-3 p-2 bg-light rounded">
                                <div class="flex-shrink-0">
                                    <div class="bg-warning bg-opacity-20 rounded-circle p-2">
                                        <i class="fas fa-user fa-sm text-warning"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-2">
                                    <h6 class="mb-0 small">@user.FirstName @user.LastName</h6>
                                    <small class="text-muted">@user.Roles.FirstOrDefault()</small>
                                </div>
                                <div class="flex-shrink-0">
                                    <button class="btn btn-sm btn-success me-1" @onclick="() => ApproveUser(user.Id)">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" @onclick="() => RejectUser(user.Id)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        }

                        @if (pendingUsers.Count() > 5)
                        {
                            <div class="text-center">
                                <button class="btn btn-outline-primary btn-sm" @onclick='() => NavigateToPage("/admin/users")'>
                                    عرض الكل (@pendingUsers.Count())
                                </button>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-check-circle fa-3x mb-3"></i>
                            <p>لا توجد طلبات موافقة معلقة</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-primary w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/admin/users"))">
                                <i class="fas fa-users-cog fa-2x mb-2"></i>
                                <br>إدارة المستخدمين
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-success w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/admin/academic-years"))">
                                <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                                <br>الأعوام الدراسية
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-info w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/admin/grades"))">
                                <i class="fas fa-layer-group fa-2x mb-2"></i>
                                <br>المراحل الدراسية
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-warning w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/admin/subjects"))">
                                <i class="fas fa-book fa-2x mb-2"></i>
                                <br>المواد الدراسية
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-danger w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/admin/classes"))">
                                <i class="fas fa-chalkboard fa-2x mb-2"></i>
                                <br>إدارة الصفوف
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-dark w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/admin/reports"))">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <br>التقارير المتقدمة
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-secondary w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/admin/settings"))">
                                <i class="fas fa-cogs fa-2x mb-2"></i>
                                <br>إعدادات النظام
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-success w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/admin/students"))">
                                <i class="fas fa-user-graduate fa-2x mb-2"></i>
                                <br>إدارة الطلاب
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-info w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/admin/teachers"))">
                                <i class="fas fa-chalkboard-teacher fa-2x mb-2"></i>
                                <br>إدارة المعلمين
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-warning w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/admin/attendance"))">
                                <i class="fas fa-user-check fa-2x mb-2"></i>
                                <br>الحضور والغياب
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-primary w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/admin/grades-scores"))">
                                <i class="fas fa-star fa-2x mb-2"></i>
                                <br>إدارة الدرجات
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-success w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/admin/schedule"))">
                                <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                                <br>الجداول الدراسية
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-info w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/admin/library"))">
                                <i class="fas fa-book fa-2x mb-2"></i>
                                <br>المكتبة الرقمية
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-warning w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/admin/activities"))">
                                <i class="fas fa-calendar-check fa-2x mb-2"></i>
                                <br>الأنشطة والفعاليات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e9ecef;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }

    .timeline-marker {
        position: absolute;
        left: -23px;
        top: 5px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 3px solid white;
        box-shadow: 0 0 0 2px #e9ecef;
    }

    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 3px solid #007bff;
    }
</style>

@code {
    private int totalUsers = 0;
    private int totalStudents = 0;
    private int totalTeachers = 0;
    private int pendingRequests = 0;
    private IEnumerable<UserDto> pendingUsers = new List<UserDto>();

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        try
        {
            pendingUsers = await ApiService.GetPendingUsersAsync();
            pendingRequests = pendingUsers.Count();

            // Mock data for now - you can implement actual API calls
            totalUsers = 1250;
            totalStudents = 980;
            totalTeachers = 85;
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", "Error loading dashboard data:", ex.Message);
        }
    }

    private async Task ApproveUser(string userId)
    {
        try
        {
            var result = await ApiService.ApproveUserAsync(userId);
            if (result)
            {
                await JSRuntime.InvokeVoidAsync("alert", "تم الموافقة على المستخدم بنجاح");
                await LoadDashboardData();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "فشل في الموافقة على المستخدم");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ أثناء الموافقة على المستخدم");
        }
    }

    private async Task RejectUser(string userId)
    {
        try
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من رفض هذا المستخدم؟");
            if (confirmed)
            {
                var result = await ApiService.RejectUserAsync(userId);
                if (result)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "تم رفض المستخدم بنجاح");
                    await LoadDashboardData();
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "فشل في رفض المستخدم");
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ أثناء رفض المستخدم");
        }
    }

    private void NavigateToPage(string url)
    {
        Navigation.NavigateTo(url);
    }
}
