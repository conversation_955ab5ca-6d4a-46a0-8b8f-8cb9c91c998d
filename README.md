# 🏫 نظام إدارة المدارس - Schools Management System

نظام شامل ومتطور لإدارة جميع جوانب المدرسة من طلاب ومعلمين وموظفين وأولياء أمور باستخدام **Blazor WebAssembly** و **ASP.NET Core API**.

## 🚀 المميزات الرئيسية

### 🔐 نظام المصادقة والتفويض
- تسجيل دخول وخروج آمن
- إنشاء حسابات جديدة مع موافقة الإدارة
- أدوار متعددة: إدارة، معلم، طالب، ولي أمر، موظف، محاسب
- JWT Authentication للأمان

### 👨‍💼 لوحة تحكم الإدارة
- إدارة الأعوام الدراسية (إنشاء، تعديل، حذف، تفعيل)
- إدارة المراحل الدراسية والصفوف
- إدارة المواد الدراسية وإسنادها للمعلمين
- موافقة ورفض طلبات المستخدمين الجدد
- إحصائيات شاملة ولوحة معلومات تفاعلية

### 👨‍🏫 لوحة تحكم المعلمين
- عرض الجدول اليومي والحصص
- تسجيل الحضور والغياب للطلاب
- إنشاء وإدارة الامتحانات الإلكترونية
- إدارة الواجبات والتكليفات
- تقييم السلوك والمشاركة
- إحصائيات أداء الطلاب

### 🎓 لوحة تحكم الطلاب
- عرض الجدول الدراسي الشخصي
- أداء الامتحانات الإلكترونية
- حل وتسليم الواجبات
- عرض الدرجات والشهادات
- متابعة الحضور والغياب
- الإشعارات والتنبيهات

### 👨‍👩‍👧‍👦 لوحة تحكم أولياء الأمور
- ربط الأبناء بالرقم الوطني
- متابعة الأداء الأكاديمي للأبناء
- عرض الدرجات والشهادات
- متابعة الحضور والغياب
- إدارة الرسوم المالية والمدفوعات
- التواصل مع المدرسة

### 👔 لوحة تحكم الموظفين
- تسجيل الحضور والانصراف
- إرسال الطلبات للإدارة
- عرض كشوف الراتب
- طلب الإجازات والعطل
- إحصائيات الحضور الشخصية

### 💰 لوحة تحكم المحاسبين
- إدارة الرسوم الدراسية
- متابعة المدفوعات والمستحقات
- إدارة رواتب الموظفين
- التقارير المالية الشاملة
- إحصائيات الإيرادات والمصروفات

## 🏗️ البنية التقنية

### Backend (API)
- **ASP.NET Core Web API** (.NET 9)
- **Entity Framework Core** مع SQL Server
- **ASP.NET Core Identity** للمصادقة والتفويض
- **JWT Authentication** للأمان
- **SignalR** للإشعارات المباشرة (جاهز للتطوير)
- **Swagger** للتوثيق التفاعلي

### Frontend (Client)
- **Blazor WebAssembly** للواجهة الأمامية
- **Bootstrap 5 RTL** للتصميم المتجاوب
- **Font Awesome** للأيقونات
- **خطوط Cairo** للنصوص العربية
- **تصميم متجاوب** يعمل على جميع الأجهزة

### قاعدة البيانات
- **SQL Server** مع Entity Framework Core
- **25+ جدول مترابط** لتغطية جميع جوانب النظام
- **فهارس محسنة** للأداء الأمثل
- **علاقات معقدة** بين الكيانات
- **بيانات أولية** للاختبار

## 🚀 التشغيل والإعداد

### المتطلبات
- .NET 9 SDK
- SQL Server (LocalDB أو Express)
- Visual Studio 2022 أو VS Code

### التشغيل السريع ⚡

#### الطريقة الأسهل:
```bash
# تشغيل النظام بالكامل
.\start-system.ps1
# أو
.\start-system.bat
```

#### إيقاف النظام:
```bash
.\stop-system.ps1
```

### خطوات التشغيل اليدوي

1. **استنساخ المشروع:**
```bash
git clone [repository-url]
cd Schools4
```

2. **تحديث قاعدة البيانات:**
```bash
dotnet ef database update --project Schools.Data --startup-project Schools.API
```

3. **تشغيل API:**
```bash
dotnet run --project Schools.API
```

4. **تشغيل العميل:**
```bash
dotnet run --project Schools.Client
```

### الروابط
- **العميل:** http://localhost:5131
- **API:** http://localhost:5261
- **Swagger:** http://localhost:5261/swagger

### بيانات تسجيل الدخول الافتراضية
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** Admin123!

## 📊 هيكل المشروع

```
Schools4/
├── Schools.API/          # Web API Backend
├── Schools.Client/       # Blazor WebAssembly Frontend
├── Schools.Data/         # Entity Framework & Database
├── Schools.Shared/       # Shared Models & DTOs
└── README.md
```

## 🗄️ نماذج قاعدة البيانات

### الكيانات الرئيسية
- **المستخدمون:** ApplicationUser, Roles
- **الهيكل الأكاديمي:** AcademicYear, Grade, Class, Section, Subject
- **التسجيل:** StudentEnrollment, TeacherAssignment, ParentStudent
- **الجداول والحضور:** Schedule, Attendance, EmployeeAttendance
- **التقييم:** StudentBehavior, StudentParticipation
- **الامتحانات:** Exam, ExamQuestion, ExamQuestionOption, StudentExamResult
- **الواجبات:** Assignment, StudentAssignmentSubmission
- **النظام:** Request, Notification
- **المالية:** FeeStructure, StudentFeePayment, EmployeeSalary
- **التقارير:** Report

## 🎨 التصميم والواجهة

- **تصميم عربي متجاوب** باستخدام Bootstrap RTL
- **ألوان متناسقة** لكل نوع مستخدم
- **أيقونات واضحة** من Font Awesome
- **تجربة مستخدم ممتازة** مع انتقالات سلسة
- **دعم كامل للغة العربية** مع خطوط Cairo

## 🔧 التطوير المستقبلي

- [ ] تطوير نظام الإشعارات المباشرة باستخدام SignalR
- [ ] إضافة المزيد من التقارير التفصيلية
- [ ] تطوير ميزات الدردشة والتواصل
- [ ] إضافة نظام إدارة المكتبة
- [ ] تطوير تطبيق الهاتف المحمول
- [ ] إضافة نظام النقل المدرسي
- [ ] تطوير نظام إدارة الأنشطة اللاصفية

## 📝 الترخيص

هذا المشروع مطور لأغراض تعليمية وتجارية.

## 👥 المساهمة

نرحب بالمساهمات! يرجى إنشاء Pull Request أو فتح Issue للمناقشة.

## 📞 الدعم

للدعم والاستفسارات، يرجى فتح Issue في المستودع.

---

**تم تطوير هذا النظام باستخدام أحدث التقنيات لضمان الأداء والأمان والقابلية للتطوير.**
