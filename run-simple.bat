@echo off
echo ========================================
echo    تشغيل نظام إدارة المدارس - النسخة المبسطة
echo    School Management System - Simple Mode
echo ========================================
echo.

echo 🎯 تشغيل العميل فقط (بدون API)
echo Running Client Only (without API)
echo.

echo 🧹 تنظيف المشروع...
echo Cleaning project...
dotnet clean Schools.Client > nul 2>&1

echo 🔧 بناء العميل...
echo Building Client...
dotnet build Schools.Client --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء العميل
    echo Client Build failed
    pause
    exit /b 1
)

echo ✅ تم بناء العميل بنجاح
echo Client build successful
echo.

echo 🌐 تشغيل Blazor Client...
echo Starting Blazor Client...
start "Schools Client" cmd /k "echo تشغيل العميل على المنفذ 5131 && cd Schools.Client && dotnet run --urls=http://localhost:5131"

echo ⏳ انتظار تشغيل العميل...
echo Waiting for Client to start...
timeout /t 10 /nobreak > nul

echo.
echo ========================================
echo ✅ تم تشغيل العميل بنجاح!
echo Client started successfully!
echo ========================================
echo.
echo 🌐 الرابط المتاح:
echo Available URL:
echo.
echo 📱 العميل (Client): http://localhost:5131
echo.
echo ========================================
echo 🔐 بيانات تسجيل الدخول:
echo Login Credentials:
echo.
echo 📧 البريد: <EMAIL>
echo 🔑 كلمة المرور: Admin123!
echo.
echo ========================================
echo ⚠️ ملاحظة مهمة:
echo Important Note:
echo.
echo 🔄 النظام يعمل بالبيانات الوهمية
echo 📊 جميع الصفحات تعمل بشكل كامل
echo 🎨 التصميم والواجهات متكاملة
echo 💾 البيانات لا تُحفظ (وضع العرض)
echo.
echo ========================================
echo 📱 الصفحات المتاحة:
echo Available Pages:
echo.
echo ✅ لوحة تحكم الإدارة
echo ✅ إدارة الأعوام الدراسية
echo ✅ إدارة المراحل الدراسية
echo ✅ إدارة الصفوف الدراسية
echo ✅ إدارة المواد الدراسية
echo ✅ إدارة المستخدمين
echo ✅ إدارة الأنشطة والفعاليات
echo ✅ إدارة المكتبة الرقمية
echo ✅ إدارة الحضور والغياب
echo ✅ إدارة الجداول الدراسية
echo ✅ إدارة الدرجات والتقييم
echo ✅ لوحة تحكم المعلم
echo ✅ لوحة تحكم الطالب
echo ✅ لوحة تحكم ولي الأمر
echo.
echo ========================================
echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause > nul
