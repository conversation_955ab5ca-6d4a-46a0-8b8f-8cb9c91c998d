@echo off
echo ========================================
echo    تشغيل نظام إدارة المدارس - وضع التشخيص
echo    School Management System - Debug Mode
echo ========================================
echo.

echo 🧹 تنظيف المشروع...
echo Cleaning project...
dotnet clean > nul 2>&1

echo 🔧 بناء API...
echo Building API...
dotnet build Schools.API
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء API
    echo API Build failed
    pause
    exit /b 1
)

echo 🔧 بناء العميل...
echo Building Client...
dotnet build Schools.Client
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء العميل
    echo Client Build failed
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo Build successful
echo.

echo 🚀 بدء تشغيل الخدمات...
echo Starting services...
echo.

echo 📡 تشغيل API Server مع مخرجات مفصلة...
echo Starting API Server with detailed output...
start "Schools API Debug" cmd /k "echo تشغيل API على المنفذ 5261 && cd Schools.API && dotnet run --urls=http://localhost:5261"

echo ⏳ انتظار تشغيل API...
echo Waiting for API to start...
timeout /t 10 /nobreak > nul

echo 🌐 تشغيل Blazor Client مع مخرجات مفصلة...
echo Starting Blazor Client with detailed output...
start "Schools Client Debug" cmd /k "echo تشغيل العميل على المنفذ 5131 && cd Schools.Client && dotnet run --urls=http://localhost:5131"

echo ⏳ انتظار تشغيل العميل...
echo Waiting for Client to start...
timeout /t 15 /nobreak > nul

echo.
echo ========================================
echo ✅ تم تشغيل النظام في وضع التشخيص!
echo System started in debug mode!
echo ========================================
echo.
echo 🌐 الروابط المتاحة:
echo Available URLs:
echo.
echo 📱 العميل (Client): http://localhost:5131
echo 🔧 API: http://localhost:5261
echo 📚 Swagger: http://localhost:5261/swagger
echo.
echo ========================================
echo 🔐 بيانات تسجيل الدخول:
echo Login Credentials:
echo.
echo 📧 البريد: <EMAIL>
echo 🔑 كلمة المرور: Admin123!
echo.
echo ========================================
echo 🔧 إصلاحات تم تطبيقها:
echo Applied Fixes:
echo.
echo ✅ إصلاح إعدادات CORS
echo ✅ تعطيل HTTPS Redirection في التطوير
echo ✅ إضافة مخرجات تشخيصية
echo ✅ تحسين عملية البناء
echo.
echo ========================================
echo 📋 ملاحظات:
echo Notes:
echo.
echo 🔍 راقب نوافذ CMD للأخطاء
echo 🌐 تأكد من عمل API قبل استخدام العميل
echo 🔄 أعد تشغيل الملف إذا فشل الاتصال
echo.
echo ========================================
echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause > nul
