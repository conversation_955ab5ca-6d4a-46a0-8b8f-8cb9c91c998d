namespace Schools.Client.Models
{
    public class DashboardOverviewModel
    {
        public List<KPICardModel> KPICards { get; set; } = new();
        public List<ChartWidgetModel> Charts { get; set; } = new();
        public List<RecentActivityModel> RecentActivities { get; set; } = new();
        public List<NotificationModel> Notifications { get; set; } = new();
        public List<QuickActionModel> QuickActions { get; set; } = new();
    }

    public class KPICardModel
    {
        public string Title { get; set; } = "";
        public string Value { get; set; } = "";
        public string SubValue { get; set; } = "";
        public string Icon { get; set; } = "";
        public string Color { get; set; } = "";
        public string Change { get; set; } = "";
        public string ChangeType { get; set; } = ""; // positive, negative, neutral
        public string Trend { get; set; } = ""; // up, down, stable
        public string Link { get; set; } = "";
    }

    public class ChartWidgetModel
    {
        public string Id { get; set; } = "";
        public string Title { get; set; } = "";
        public string Type { get; set; } = ""; // line, bar, pie, doughnut
        public ChartDataModel Data { get; set; } = new();
        public object Options { get; set; } = new();
        public int Width { get; set; } = 6; // Bootstrap column width
        public int Height { get; set; } = 300;
    }

    public class RecentActivityModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Icon { get; set; } = "";
        public string Color { get; set; } = "";
        public DateTime Timestamp { get; set; }
        public string User { get; set; } = "";
        public string Type { get; set; } = "";
        public string Link { get; set; } = "";
    }

    public class NotificationModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string Message { get; set; } = "";
        public string Type { get; set; } = ""; // info, warning, error, success
        public DateTime CreatedAt { get; set; }
        public bool IsRead { get; set; }
        public string Icon { get; set; } = "";
        public string Link { get; set; } = "";
        public int Priority { get; set; } = 1; // 1-5
    }

    public class QuickActionModel
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Icon { get; set; } = "";
        public string Color { get; set; } = "";
        public string Link { get; set; } = "";
        public string Permission { get; set; } = "";
        public bool IsEnabled { get; set; } = true;
    }

    public class WidgetConfigModel
    {
        public string Id { get; set; } = "";
        public string Title { get; set; } = "";
        public string Type { get; set; } = "";
        public int Position { get; set; }
        public bool IsVisible { get; set; } = true;
        public Dictionary<string, object> Settings { get; set; } = new();
    }

    public class DashboardLayoutModel
    {
        public string UserId { get; set; } = "";
        public string Role { get; set; } = "";
        public List<WidgetConfigModel> Widgets { get; set; } = new();
        public Dictionary<string, object> Preferences { get; set; } = new();
        public DateTime LastModified { get; set; }
    }

    public class DashboardCalendarEventModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public DateTime Start { get; set; }
        public DateTime End { get; set; }
        public string Type { get; set; } = "";
        public string Color { get; set; } = "";
        public string Description { get; set; } = "";
        public bool AllDay { get; set; }
        public string Location { get; set; } = "";
    }

    public class TaskModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public DateTime DueDate { get; set; }
        public string Priority { get; set; } = "";
        public string Status { get; set; } = "";
        public string AssignedTo { get; set; } = "";
        public string Category { get; set; } = "";
        public int Progress { get; set; }
    }

    public class WeatherModel
    {
        public string Location { get; set; } = "";
        public double Temperature { get; set; }
        public string Condition { get; set; } = "";
        public string Icon { get; set; } = "";
        public double Humidity { get; set; }
        public double WindSpeed { get; set; }
        public List<WeatherForecastModel> Forecast { get; set; } = new();
    }

    public class WeatherForecastModel
    {
        public DateTime Date { get; set; }
        public double HighTemp { get; set; }
        public double LowTemp { get; set; }
        public string Condition { get; set; } = "";
        public string Icon { get; set; } = "";
    }

    public static class DashboardConstants
    {
        public static readonly Dictionary<string, string> WidgetTypes = new()
        {
            { "kpi", "مؤشر أداء" },
            { "chart", "رسم بياني" },
            { "table", "جدول" },
            { "calendar", "تقويم" },
            { "tasks", "المهام" },
            { "notifications", "الإشعارات" },
            { "weather", "الطقس" },
            { "news", "الأخبار" }
        };

        public static readonly Dictionary<string, string> ChartTypes = new()
        {
            { "line", "خطي" },
            { "bar", "أعمدة" },
            { "pie", "دائري" },
            { "doughnut", "حلقي" },
            { "radar", "رادار" },
            { "area", "منطقة" }
        };

        public static readonly Dictionary<string, string> NotificationTypes = new()
        {
            { "info", "معلومات" },
            { "warning", "تحذير" },
            { "error", "خطأ" },
            { "success", "نجاح" }
        };

        public static readonly Dictionary<string, string> ActivityTypes = new()
        {
            { "login", "تسجيل دخول" },
            { "grade", "درجة جديدة" },
            { "attendance", "حضور" },
            { "assignment", "واجب" },
            { "event", "فعالية" },
            { "announcement", "إعلان" }
        };

        public static readonly string[] Colors = new[]
        {
            "primary", "secondary", "success", "danger", "warning", "info", "light", "dark"
        };

        public static readonly string[] ChartColors = new[]
        {
            "#007bff", "#6c757d", "#28a745", "#dc3545", "#ffc107", "#17a2b8", "#f8f9fa", "#343a40"
        };
    }

    public class DashboardFilterModel
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? ClassId { get; set; }
        public string? SubjectId { get; set; }
        public string? Period { get; set; } = "month";
        public bool RefreshData { get; set; }
    }

    public class SystemStatusModel
    {
        public bool IsOnline { get; set; }
        public DateTime LastUpdate { get; set; }
        public int ActiveUsers { get; set; }
        public double SystemLoad { get; set; }
        public double MemoryUsage { get; set; }
        public double DiskUsage { get; set; }
        public List<ServiceStatusModel> Services { get; set; } = new();
    }

    public class ServiceStatusModel
    {
        public string Name { get; set; } = "";
        public bool IsHealthy { get; set; }
        public double ResponseTime { get; set; }
        public DateTime LastCheck { get; set; }
        public string Status { get; set; } = "";
    }
}
