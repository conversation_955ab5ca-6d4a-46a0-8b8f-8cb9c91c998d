@echo off
echo ========================================
echo    تشغيل نظام إدارة المدارس - النسخة المُصلحة
echo    School Management System - Fixed Version
echo ========================================
echo.

echo 🧹 تنظيف المشروع...
echo Cleaning project...
dotnet clean > nul 2>&1

echo 🔧 بناء المشروع...
echo Building project...
dotnet build Schools.Client --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    echo Build failed
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo Build successful
echo.

echo 🚀 بدء تشغيل الخدمات...
echo Starting services...
echo.

echo 📡 تشغيل API Server...
echo Starting API Server...
start "Schools API" cmd /k "cd Schools.API && dotnet run --urls=http://localhost:5261"

echo ⏳ انتظار تشغيل API...
echo Waiting for API to start...
timeout /t 8 /nobreak > nul

echo 🌐 تشغيل Blazor Client...
echo Starting Blazor Client...
start "Schools Client" cmd /k "cd Schools.Client && dotnet run --urls=http://localhost:5131"

echo ⏳ انتظار تشغيل العميل...
echo Waiting for Client to start...
timeout /t 12 /nobreak > nul

echo.
echo ========================================
echo ✅ تم تشغيل النظام بنجاح!
echo System started successfully!
echo ========================================
echo.
echo 🌐 الروابط المتاحة:
echo Available URLs:
echo.
echo 📱 العميل (Client): http://localhost:5131
echo 🔧 API: http://localhost:5261
echo 📚 Swagger: http://localhost:5261/swagger
echo.
echo ========================================
echo 🔐 بيانات تسجيل الدخول:
echo Login Credentials:
echo.
echo 📧 البريد: <EMAIL>
echo 🔑 كلمة المرور: Admin123!
echo.
echo ========================================
echo ✅ المشاكل المُصلحة:
echo Fixed Issues:
echo.
echo ✅ إصلاح مشكلة blazor.boot.json
echo ✅ إصلاح أخطاء البناء
echo ✅ تحسين عملية التشغيل
echo ✅ إضافة تنظيف تلقائي
echo.
echo ========================================
echo 🎯 الصفحات المحدثة للعمل مع API:
echo API-Integrated Pages:
echo.
echo ✅ إدارة الأنشطة والفعاليات
echo ✅ إدارة المكتبة الرقمية
echo ✅ إدارة الحضور والغياب
echo ✅ إدارة الجداول الدراسية
echo ⚠️ إدارة الدرجات (تعمل مع البيانات الوهمية)
echo.
echo ========================================
echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause > nul
