using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;
using System.Security.Claims;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Policy = "AdminOnly")]
    public class AdminController : ControllerBase
    {
        private readonly SchoolsDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public AdminController(SchoolsDbContext context, UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        #region Academic Year Management

        [HttpGet("academic-years")]
        public async Task<ActionResult<IEnumerable<AcademicYearDto>>> GetAcademicYears()
        {
            var academicYears = await _context.AcademicYears
                .Where(ay => !ay.IsDeleted)
                .Select(ay => new AcademicYearDto
                {
                    Id = ay.Id,
                    Name = ay.Name,
                    StartDate = ay.StartDate,
                    EndDate = ay.EndDate,
                    IsActive = ay.IsActive
                })
                .ToListAsync();

            return Ok(academicYears);
        }

        [HttpPost("academic-years")]
        public async Task<ActionResult<AcademicYearDto>> CreateAcademicYear(CreateAcademicYearDto model)
        {
            try
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                var academicYear = new AcademicYear
                {
                    Name = model.Name,
                    StartDate = model.StartDate,
                    EndDate = model.EndDate,
                    CreatedBy = userId
                };

                _context.AcademicYears.Add(academicYear);
                await _context.SaveChangesAsync();

                var result = new AcademicYearDto
                {
                    Id = academicYear.Id,
                    Name = academicYear.Name,
                    StartDate = academicYear.StartDate,
                    EndDate = academicYear.EndDate,
                    IsActive = academicYear.IsActive
                };

                return CreatedAtAction(nameof(GetAcademicYears), new { id = academicYear.Id }, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء إنشاء العام الدراسي" });
            }
        }

        [HttpPut("academic-years/{id}")]
        public async Task<IActionResult> UpdateAcademicYear(int id, CreateAcademicYearDto model)
        {
            try
            {
                var academicYear = await _context.AcademicYears.FindAsync(id);
                if (academicYear == null || academicYear.IsDeleted)
                {
                    return NotFound(new { message = "العام الدراسي غير موجود" });
                }

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                academicYear.Name = model.Name;
                academicYear.StartDate = model.StartDate;
                academicYear.EndDate = model.EndDate;
                academicYear.UpdatedAt = DateTime.UtcNow;
                academicYear.UpdatedBy = userId;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تحديث العام الدراسي بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء تحديث العام الدراسي" });
            }
        }

        [HttpDelete("academic-years/{id}")]
        public async Task<IActionResult> DeleteAcademicYear(int id)
        {
            try
            {
                var academicYear = await _context.AcademicYears.FindAsync(id);
                if (academicYear == null || academicYear.IsDeleted)
                {
                    return NotFound(new { message = "العام الدراسي غير موجود" });
                }

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                academicYear.IsDeleted = true;
                academicYear.UpdatedAt = DateTime.UtcNow;
                academicYear.UpdatedBy = userId;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم حذف العام الدراسي بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء حذف العام الدراسي" });
            }
        }

        [HttpPost("academic-years/{id}/activate")]
        public async Task<IActionResult> ActivateAcademicYear(int id)
        {
            try
            {
                // Deactivate all other academic years
                var allAcademicYears = await _context.AcademicYears
                    .Where(ay => !ay.IsDeleted)
                    .ToListAsync();

                foreach (var ay in allAcademicYears)
                {
                    ay.IsActive = ay.Id == id;
                }

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تفعيل العام الدراسي بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء تفعيل العام الدراسي" });
            }
        }

        #endregion

        #region Grade Management

        [HttpGet("grades")]
        public async Task<ActionResult<IEnumerable<AcademicGradeDto>>> GetGrades()
        {
            var grades = await _context.Grades
                .Where(g => !g.IsDeleted)
                .Select(g => new AcademicGradeDto
                {
                    Id = g.Id,
                    Name = g.Name,
                    Level = g.Level,
                    Description = g.Description,
                    ClassCount = g.Classes.Count(c => !c.IsDeleted)
                })
                .ToListAsync();

            return Ok(grades);
        }

        [HttpPost("grades")]
        public async Task<ActionResult<AcademicGradeDto>> CreateGrade(CreateAcademicGradeDto model)
        {
            try
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                var grade = new Grade
                {
                    Name = model.Name,
                    Level = model.Level,
                    Description = model.Description,
                    CreatedBy = userId
                };

                _context.Grades.Add(grade);
                await _context.SaveChangesAsync();

                var result = new AcademicGradeDto
                {
                    Id = grade.Id,
                    Name = grade.Name,
                    Level = grade.Level,
                    Description = grade.Description,
                    ClassCount = 0
                };

                return CreatedAtAction(nameof(GetGrades), new { id = grade.Id }, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء إنشاء المرحلة الدراسية" });
            }
        }

        [HttpPut("grades/{id}")]
        public async Task<IActionResult> UpdateGrade(int id, CreateAcademicGradeDto model)
        {
            try
            {
                var grade = await _context.Grades.FindAsync(id);
                if (grade == null || grade.IsDeleted)
                {
                    return NotFound(new { message = "المرحلة الدراسية غير موجودة" });
                }

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                grade.Name = model.Name;
                grade.Level = model.Level;
                grade.Description = model.Description;
                grade.UpdatedAt = DateTime.UtcNow;
                grade.UpdatedBy = userId;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تحديث المرحلة الدراسية بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء تحديث المرحلة الدراسية" });
            }
        }

        [HttpDelete("grades/{id}")]
        public async Task<IActionResult> DeleteGrade(int id)
        {
            try
            {
                var grade = await _context.Grades.FindAsync(id);
                if (grade == null || grade.IsDeleted)
                {
                    return NotFound(new { message = "المرحلة الدراسية غير موجودة" });
                }

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                grade.IsDeleted = true;
                grade.UpdatedAt = DateTime.UtcNow;
                grade.UpdatedBy = userId;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم حذف المرحلة الدراسية بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء حذف المرحلة الدراسية" });
            }
        }

        #endregion

        #region User Management

        [HttpGet("pending-users")]
        public async Task<ActionResult<IEnumerable<UserDto>>> GetPendingUsers()
        {
            var pendingUsers = await _userManager.Users
                .Where(u => !u.IsActive)
                .ToListAsync();

            var result = new List<UserDto>();

            foreach (var user in pendingUsers)
            {
                var roles = await _userManager.GetRolesAsync(user);
                result.Add(new UserDto
                {
                    Id = user.Id,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Email = user.Email,
                    PhoneNumber = user.PhoneNumber,
                    NationalId = user.NationalId,
                    DateOfBirth = user.DateOfBirth,
                    Address = user.Address,
                    IsActive = user.IsActive,
                    Roles = roles.ToList()
                });
            }

            return Ok(result);
        }

        [HttpPost("approve-user/{userId}")]
        public async Task<IActionResult> ApproveUser(string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    return NotFound(new { message = "المستخدم غير موجود" });
                }

                user.IsActive = true;
                user.UpdatedAt = DateTime.UtcNow;

                var result = await _userManager.UpdateAsync(user);

                if (!result.Succeeded)
                {
                    return BadRequest(new { message = "فشل في الموافقة على المستخدم" });
                }

                return Ok(new { message = "تم الموافقة على المستخدم بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء الموافقة على المستخدم" });
            }
        }

        [HttpPost("reject-user/{userId}")]
        public async Task<IActionResult> RejectUser(string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    return NotFound(new { message = "المستخدم غير موجود" });
                }

                var result = await _userManager.DeleteAsync(user);

                if (!result.Succeeded)
                {
                    return BadRequest(new { message = "فشل في رفض المستخدم" });
                }

                return Ok(new { message = "تم رفض المستخدم بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء رفض المستخدم" });
            }
        }

        #endregion
    }
}
