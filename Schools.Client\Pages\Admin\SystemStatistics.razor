@page "/admin/system-statistics"
@using Schools.Shared.DTOs
@using Schools.Client.Services
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        إحصائيات النظام الشاملة
                    </h4>
                </div>

                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-info" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2">جاري تحميل الإحصائيات...</p>
                        </div>
                    }
                    else
                    {
                        <!-- Overview Cards -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-users fa-3x mb-3"></i>
                                        <h3>@totalUsers</h3>
                                        <p class="mb-0">إجمالي المستخدمين</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-user-graduate fa-3x mb-3"></i>
                                        <h3>@totalStudents</h3>
                                        <p class="mb-0">إجمالي الطلاب</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chalkboard-teacher fa-3x mb-3"></i>
                                        <h3>@totalTeachers</h3>
                                        <p class="mb-0">إجمالي المعلمين</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-school fa-3x mb-3"></i>
                                        <h3>@totalClasses</h3>
                                        <p class="mb-0">إجمالي الصفوف</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Academic Statistics -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-graduation-cap me-2"></i>
                                            الإحصائيات الأكاديمية
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-6 mb-3">
                                                <div class="border rounded p-3">
                                                    <h4 class="text-primary">@totalGrades</h4>
                                                    <small class="text-muted">إجمالي الدرجات</small>
                                                </div>
                                            </div>
                                            <div class="col-6 mb-3">
                                                <div class="border rounded p-3">
                                                    <h4 class="text-success">@averageGrade.ToString("F1")%</h4>
                                                    <small class="text-muted">المعدل العام</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="border rounded p-3">
                                                    <h4 class="text-info">@totalSubjects</h4>
                                                    <small class="text-muted">إجمالي المواد</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="border rounded p-3">
                                                    <h4 class="text-warning">@totalExams</h4>
                                                    <small class="text-muted">إجمالي الامتحانات</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-calendar-check me-2"></i>
                                            إحصائيات الحضور
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-6 mb-3">
                                                <div class="border rounded p-3">
                                                    <h4 class="text-success">@attendanceRate.ToString("F1")%</h4>
                                                    <small class="text-muted">معدل الحضور</small>
                                                </div>
                                            </div>
                                            <div class="col-6 mb-3">
                                                <div class="border rounded p-3">
                                                    <h4 class="text-danger">@absenteeismRate.ToString("F1")%</h4>
                                                    <small class="text-muted">معدل الغياب</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="border rounded p-3">
                                                    <h4 class="text-primary">@totalAttendanceRecords</h4>
                                                    <small class="text-muted">سجلات الحضور</small>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="border rounded p-3">
                                                    <h4 class="text-warning">@lateArrivals</h4>
                                                    <small class="text-muted">حالات التأخير</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- User Distribution -->
                        <div class="row mb-4">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-users-cog me-2"></i>
                                            توزيع المستخدمين حسب الأدوار
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            @foreach (var role in userRoleDistribution)
                                            {
                                                <div class="col-md-4 mb-3">
                                                    <div class="text-center p-3 border rounded">
                                                        <i class="@GetRoleIcon(role.Key) fa-2x @GetRoleColor(role.Key) mb-2"></i>
                                                        <h5>@role.Value</h5>
                                                        <small class="text-muted">@GetRoleText(role.Key)</small>
                                                        <div class="progress mt-2" style="height: 8px;">
                                                            <div class="progress-bar @GetRoleColor(role.Key)"
                                                                 style="width: @GetRolePercentage(role.Value)%"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-chart-pie me-2"></i>
                                            نشاط النظام
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>المستخدمون النشطون اليوم</span>
                                                <strong>@activeUsersToday</strong>
                                            </div>
                                            <div class="progress mt-1" style="height: 6px;">
                                                <div class="progress-bar bg-success" style="width: @GetActiveUserPercentage()%"></div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>تسجيلات دخول اليوم</span>
                                                <strong>@loginsToday</strong>
                                            </div>
                                            <div class="progress mt-1" style="height: 6px;">
                                                <div class="progress-bar bg-info" style="width: 75%"></div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between">
                                                <span>الدرجات المدخلة اليوم</span>
                                                <strong>@gradesEnteredToday</strong>
                                            </div>
                                            <div class="progress mt-1" style="height: 6px;">
                                                <div class="progress-bar bg-warning" style="width: 60%"></div>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="d-flex justify-content-between">
                                                <span>سجلات الحضور اليوم</span>
                                                <strong>@attendanceRecordsToday</strong>
                                            </div>
                                            <div class="progress mt-1" style="height: 6px;">
                                                <div class="progress-bar bg-primary" style="width: 85%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Metrics -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-tachometer-alt me-2"></i>
                                            مؤشرات الأداء الرئيسية
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="text-center p-3">
                                                    <div class="progress mx-auto mb-3" style="width: 100px; height: 100px;">
                                                        <div class="progress-bar bg-success" style="width: @studentSatisfaction%">
                                                            <span class="position-absolute top-50 start-50 translate-middle">
                                                                @studentSatisfaction%
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <h6>رضا الطلاب</h6>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="text-center p-3">
                                                    <div class="progress mx-auto mb-3" style="width: 100px; height: 100px;">
                                                        <div class="progress-bar bg-info" style="width: @teacherEfficiency%">
                                                            <span class="position-absolute top-50 start-50 translate-middle">
                                                                @teacherEfficiency%
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <h6>كفاءة المعلمين</h6>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="text-center p-3">
                                                    <div class="progress mx-auto mb-3" style="width: 100px; height: 100px;">
                                                        <div class="progress-bar bg-warning" style="width: @systemUptime%">
                                                            <span class="position-absolute top-50 start-50 translate-middle">
                                                                @systemUptime%
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <h6>وقت تشغيل النظام</h6>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="text-center p-3">
                                                    <div class="progress mx-auto mb-3" style="width: 100px; height: 100px;">
                                                        <div class="progress-bar bg-danger" style="width: @dataAccuracy%">
                                                            <span class="position-absolute top-50 start-50 translate-middle">
                                                                @dataAccuracy%
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <h6>دقة البيانات</h6>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-history me-2"></i>
                                            النشاطات الأخيرة
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="timeline">
                                            @foreach (var activity in recentActivities.Take(10))
                                            {
                                                <div class="timeline-item">
                                                    <div class="timeline-marker @GetActivityColor(activity.Type)">
                                                        <i class="@GetActivityIcon(activity.Type)"></i>
                                                    </div>
                                                    <div class="timeline-content">
                                                        <h6 class="mb-1">@activity.Title</h6>
                                                        <p class="mb-1 text-muted">@activity.Description</p>
                                                        <small class="text-muted">
                                                            <i class="fas fa-clock me-1"></i>
                                                            @activity.Timestamp.ToString("yyyy-MM-dd HH:mm")
                                                        </small>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Refresh Button -->
                        <div class="row mt-4">
                            <div class="col-12 text-center">
                                <button class="btn btn-info btn-lg" @onclick="RefreshStatistics" disabled="@isRefreshing">
                                    @if (isRefreshing)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2"></span>
                                    }
                                    <i class="fas fa-sync-alt me-2"></i>
                                    تحديث الإحصائيات
                                </button>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }

    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 0;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
    }

    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #007bff;
    }

    .progress {
        border-radius: 50%;
        position: relative;
    }

    .progress .progress-bar {
        border-radius: 50%;
    }
</style>

@code {
    private bool isLoading = true;
    private bool isRefreshing = false;

    // Basic Statistics
    private int totalUsers = 0;
    private int totalStudents = 0;
    private int totalTeachers = 0;
    private int totalClasses = 0;
    private int totalSubjects = 0;
    private int totalGrades = 0;
    private int totalExams = 0;
    private int totalAttendanceRecords = 0;

    // Performance Metrics
    private double averageGrade = 0;
    private double attendanceRate = 0;
    private double absenteeismRate = 0;
    private int lateArrivals = 0;

    // Daily Activity
    private int activeUsersToday = 0;
    private int loginsToday = 0;
    private int gradesEnteredToday = 0;
    private int attendanceRecordsToday = 0;

    // KPIs
    private int studentSatisfaction = 85;
    private int teacherEfficiency = 92;
    private int systemUptime = 99;
    private int dataAccuracy = 96;

    // User Distribution
    private Dictionary<string, int> userRoleDistribution = new();

    // Recent Activities
    private List<ActivityItem> recentActivities = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadStatistics();
    }

    private async Task LoadStatistics()
    {
        try
        {
            isLoading = true;

            // Load basic statistics
            var users = await ApiService.GetUsersAsync();
            totalUsers = users.Count();

            totalStudents = users.Count(u => u.Role == "Student");
            totalTeachers = users.Count(u => u.Role == "Teacher");

            var classes = await ApiService.GetClassesAsync();
            totalClasses = classes.Count();

            var subjects = await ApiService.GetSubjectsAsync();
            totalSubjects = subjects.Count();

            // Calculate user role distribution
            userRoleDistribution = users.GroupBy(u => u.Role)
                                      .ToDictionary(g => g.Key, g => g.Count());

            // Mock additional statistics (in real app, these would come from API)
            totalGrades = 1250;
            totalExams = 45;
            totalAttendanceRecords = 3500;
            averageGrade = 78.5;
            attendanceRate = 92.3;
            absenteeismRate = 7.7;
            lateArrivals = 125;

            // Daily activity (mock data)
            activeUsersToday = (int)(totalUsers * 0.65);
            loginsToday = (int)(totalUsers * 0.8);
            gradesEnteredToday = 85;
            attendanceRecordsToday = 320;

            // Generate recent activities
            GenerateRecentActivities();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الإحصائيات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void GenerateRecentActivities()
    {
        recentActivities = new List<ActivityItem>
        {
            new() { Type = "grade", Title = "إدخال درجات جديدة", Description = "تم إدخال 25 درجة لمادة الرياضيات", Timestamp = DateTime.Now.AddMinutes(-15) },
            new() { Type = "attendance", Title = "تسجيل حضور", Description = "تم تسجيل حضور الصف الثالث الابتدائي", Timestamp = DateTime.Now.AddMinutes(-30) },
            new() { Type = "user", Title = "مستخدم جديد", Description = "تم إضافة معلم جديد للنظام", Timestamp = DateTime.Now.AddHours(-1) },
            new() { Type = "system", Title = "نسخ احتياطي", Description = "تم إنشاء نسخة احتياطية من قاعدة البيانات", Timestamp = DateTime.Now.AddHours(-2) },
            new() { Type = "grade", Title = "تحديث درجات", Description = "تم تحديث درجات امتحان العلوم", Timestamp = DateTime.Now.AddHours(-3) },
            new() { Type = "attendance", Title = "تقرير غياب", Description = "تم إرسال تقرير الغياب لأولياء الأمور", Timestamp = DateTime.Now.AddHours(-4) },
            new() { Type = "user", Title = "تسجيل دخول", Description = "تسجيل دخول المدير العام", Timestamp = DateTime.Now.AddHours(-5) },
            new() { Type = "system", Title = "تحديث النظام", Description = "تم تحديث النظام إلى الإصدار 2.1.0", Timestamp = DateTime.Now.AddHours(-6) }
        };
    }

    private async Task RefreshStatistics()
    {
        isRefreshing = true;
        await LoadStatistics();
        isRefreshing = false;
        await JSRuntime.InvokeVoidAsync("alert", "تم تحديث الإحصائيات بنجاح");
    }

    private string GetRoleIcon(string role)
    {
        return role switch
        {
            "Admin" => "fas fa-user-shield",
            "Teacher" => "fas fa-chalkboard-teacher",
            "Student" => "fas fa-user-graduate",
            "Parent" => "fas fa-users",
            "Accountant" => "fas fa-calculator",
            "Employee" => "fas fa-user-tie",
            _ => "fas fa-user"
        };
    }

    private string GetRoleColor(string role)
    {
        return role switch
        {
            "Admin" => "text-danger",
            "Teacher" => "text-warning",
            "Student" => "text-success",
            "Parent" => "text-info",
            "Accountant" => "text-primary",
            "Employee" => "text-secondary",
            _ => "text-muted"
        };
    }

    private string GetRoleText(string role)
    {
        return role switch
        {
            "Admin" => "مدراء",
            "Teacher" => "معلمين",
            "Student" => "طلاب",
            "Parent" => "أولياء أمور",
            "Accountant" => "محاسبين",
            "Employee" => "موظفين",
            _ => role
        };
    }

    private double GetRolePercentage(int count)
    {
        return totalUsers > 0 ? (double)count / totalUsers * 100 : 0;
    }

    private double GetActiveUserPercentage()
    {
        return totalUsers > 0 ? (double)activeUsersToday / totalUsers * 100 : 0;
    }

    private string GetActivityIcon(string type)
    {
        return type switch
        {
            "grade" => "fas fa-star",
            "attendance" => "fas fa-calendar-check",
            "user" => "fas fa-user-plus",
            "system" => "fas fa-cog",
            _ => "fas fa-info"
        };
    }

    private string GetActivityColor(string type)
    {
        return type switch
        {
            "grade" => "bg-success",
            "attendance" => "bg-info",
            "user" => "bg-warning",
            "system" => "bg-primary",
            _ => "bg-secondary"
        };
    }

    public class ActivityItem
    {
        public string Type { get; set; } = "";
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public DateTime Timestamp { get; set; }
    }
}
