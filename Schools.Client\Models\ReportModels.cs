namespace Schools.Client.Models
{
    public class StudentPerformanceReportModel
    {
        public int TotalStudents { get; set; }
        public double AverageGrade { get; set; }
        public double HighestGrade { get; set; }
        public double LowestGrade { get; set; }
        public Dictionary<string, int> GradeDistribution { get; set; } = new();
        public Dictionary<string, double> SubjectPerformance { get; set; } = new();
        public List<StudentPerformanceDetailModel> StudentDetails { get; set; } = new();
    }

    public class StudentPerformanceDetailModel
    {
        public int StudentId { get; set; }
        public string StudentName { get; set; } = "";
        public double AverageGrade { get; set; }
        public int TotalGrades { get; set; }
        public Dictionary<string, double> SubjectGrades { get; set; } = new();
        public string PerformanceLevel { get; set; } = "";
        public string Trend { get; set; } = "";
    }

    public class AttendanceReportModel
    {
        public int TotalDays { get; set; }
        public int TotalStudents { get; set; }
        public double OverallAttendanceRate { get; set; }
        public int PresentDays { get; set; }
        public int AbsentDays { get; set; }
        public Dictionary<DateTime, DailyAttendanceModel> DailyAttendance { get; set; } = new();
        public List<StudentAttendanceDetailModel> StudentAttendance { get; set; } = new();
    }

    public class StudentAttendanceDetailModel
    {
        public int StudentId { get; set; }
        public string StudentName { get; set; } = "";
        public int TotalDays { get; set; }
        public int PresentDays { get; set; }
        public double AttendanceRate { get; set; }
        public List<AbsenceDetailModel> RecentAbsences { get; set; } = new();
    }

    public class AbsenceDetailModel
    {
        public DateTime Date { get; set; }
        public string Reason { get; set; } = "";
        public bool IsExcused { get; set; }
    }

    public class FinancialReportModel
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TuitionFees { get; set; }
        public decimal OtherFees { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetIncome { get; set; }
        public decimal OutstandingFees { get; set; }
        public double PaymentRate { get; set; }
        public List<MonthlyFinancialModel> MonthlyBreakdown { get; set; } = new();
        public Dictionary<string, decimal> ExpenseCategories { get; set; } = new();
        public Dictionary<string, decimal> RevenueStreams { get; set; } = new();
    }

    public class MonthlyFinancialModel
    {
        public string Month { get; set; } = "";
        public decimal Revenue { get; set; }
        public decimal Expenses { get; set; }
        public decimal NetIncome { get; set; }
        public int StudentCount { get; set; }
    }

    public class ClassSummaryReportModel
    {
        public int TotalClasses { get; set; }
        public int TotalStudents { get; set; }
        public double OverallAverageGrade { get; set; }
        public double OverallAttendanceRate { get; set; }
        public List<ClassSummaryDetailModel> ClassDetails { get; set; } = new();
    }

    public class ClassSummaryDetailModel
    {
        public int ClassId { get; set; }
        public string ClassName { get; set; } = "";
        public int TotalStudents { get; set; }
        public double AverageGrade { get; set; }
        public double AttendanceRate { get; set; }
        public int SubjectCount { get; set; }
        public List<string> TopPerformers { get; set; } = new();
        public List<string> SubjectsOffered { get; set; } = new();
        public string ClassTeacher { get; set; } = "";
    }

    public class TeacherPerformanceReportModel
    {
        public int TotalTeachers { get; set; }
        public double OverallAverageGrade { get; set; }
        public List<TeacherPerformanceDetailModel> TeacherDetails { get; set; } = new();
    }

    public class TeacherPerformanceDetailModel
    {
        public int TeacherId { get; set; }
        public string TeacherName { get; set; } = "";
        public int SubjectsCount { get; set; }
        public int StudentsCount { get; set; }
        public double AverageGrade { get; set; }
        public int TotalGrades { get; set; }
        public List<string> SubjectsTaught { get; set; } = new();
        public double StudentSatisfactionRate { get; set; }
        public string PerformanceRating { get; set; } = "";
    }

    public class ReportFilterModel
    {
        public int? StudentId { get; set; }
        public int? ClassId { get; set; }
        public int? TeacherId { get; set; }
        public int? SubjectId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? ReportType { get; set; }
        public string? Format { get; set; } = "html";
    }

    public class ExportOptionsModel
    {
        public string Format { get; set; } = "pdf"; // pdf, excel, csv
        public bool IncludeCharts { get; set; } = true;
        public bool IncludeDetails { get; set; } = true;
        public string Language { get; set; } = "ar";
        public string Template { get; set; } = "default";
    }

    public static class ReportTypes
    {
        public static readonly Dictionary<string, string> Types = new()
        {
            { "student-performance", "تقرير أداء الطلاب" },
            { "attendance", "تقرير الحضور" },
            { "financial", "التقرير المالي" },
            { "class-summary", "ملخص الصفوف" },
            { "teacher-performance", "تقرير أداء المعلمين" },
            { "grade-analysis", "تحليل الدرجات" },
            { "behavior", "تقرير السلوك" },
            { "activities", "تقرير الأنشطة" }
        };

        public static readonly Dictionary<string, string> Icons = new()
        {
            { "student-performance", "fas fa-chart-line" },
            { "attendance", "fas fa-calendar-check" },
            { "financial", "fas fa-dollar-sign" },
            { "class-summary", "fas fa-users" },
            { "teacher-performance", "fas fa-chalkboard-teacher" },
            { "grade-analysis", "fas fa-chart-bar" },
            { "behavior", "fas fa-user-check" },
            { "activities", "fas fa-running" }
        };
    }

    public static class ExportFormats
    {
        public static readonly Dictionary<string, string> Formats = new()
        {
            { "pdf", "PDF" },
            { "excel", "Excel" },
            { "csv", "CSV" },
            { "json", "JSON" },
            { "html", "HTML" }
        };

        public static readonly Dictionary<string, string> MimeTypes = new()
        {
            { "pdf", "application/pdf" },
            { "excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" },
            { "csv", "text/csv" },
            { "json", "application/json" },
            { "html", "text/html" }
        };
    }

    public class ReportScheduleModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string ReportType { get; set; } = "";
        public string Frequency { get; set; } = ""; // daily, weekly, monthly
        public DateTime NextRun { get; set; }
        public List<string> Recipients { get; set; } = new();
        public ReportFilterModel Filters { get; set; } = new();
        public ExportOptionsModel ExportOptions { get; set; } = new();
        public bool IsActive { get; set; } = true;
    }

    public static class ReportFrequencies
    {
        public static readonly Dictionary<string, string> Frequencies = new()
        {
            { "daily", "يومي" },
            { "weekly", "أسبوعي" },
            { "monthly", "شهري" },
            { "quarterly", "ربع سنوي" },
            { "yearly", "سنوي" }
        };
    }
}
