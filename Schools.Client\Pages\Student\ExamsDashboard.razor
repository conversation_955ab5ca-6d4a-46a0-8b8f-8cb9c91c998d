@page "/student/exams"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Student")]

<PageTitle>الامتحانات الإلكترونية - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white border-0 shadow-lg">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-1">
                                <i class="fas fa-laptop-code me-2"></i>
                                الامتحانات الإلكترونية
                            </h2>
                            <p class="mb-0 opacity-75">
                                أدِّ امتحاناتك الإلكترونية واطلع على نتائجك
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-light btn-lg" @onclick="RefreshData">
                                <i class="fas fa-sync-alt me-2"></i>
                                تحديث البيانات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل بيانات الامتحانات...</p>
        </div>
    }
    else if (dashboardData != null)
    {
        <!-- Statistics Cards -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-primary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-clipboard-list fa-lg"></i>
                        </div>
                        <h4 class="text-primary mb-1">@dashboardData.Statistics.TotalExamsTaken</h4>
                        <p class="text-muted mb-0">امتحانات مكتملة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-success text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-check-circle fa-lg"></i>
                        </div>
                        <h4 class="text-success mb-1">@dashboardData.Statistics.TotalExamsPassed</h4>
                        <p class="text-muted mb-0">امتحانات ناجحة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-info text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-star fa-lg"></i>
                        </div>
                        <h4 class="text-info mb-1">@dashboardData.Statistics.AverageScore.ToString("F1")%</h4>
                        <p class="text-muted mb-0">المعدل العام</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-warning text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-clock fa-lg"></i>
                        </div>
                        <h4 class="text-warning mb-1">@dashboardData.Statistics.PendingExams</h4>
                        <p class="text-muted mb-0">امتحانات معلقة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Available Exams -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-play-circle me-2 text-success"></i>
                            الامتحانات المتاحة
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (dashboardData.AvailableExams?.Any() == true)
                        {
                            <div class="row g-4">
                                @foreach (var exam in dashboardData.AvailableExams)
                                {
                                    <div class="col-lg-6">
                                        <div class="card border h-100 exam-card @(exam.CanTakeExam ? "border-success" : "border-warning")">
                                            <div class="card-header @(exam.CanTakeExam ? "bg-light-success" : "bg-light-warning")">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span class="badge @(exam.CanTakeExam ? "bg-success" : "bg-warning")">
                                                        @(exam.CanTakeExam ? "متاح الآن" : "غير متاح")
                                                    </span>
                                                    <small class="text-muted">
                                                        @exam.AttemptsUsed / @exam.MaxAttempts محاولات
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <h6 class="card-title text-primary">@exam.Title</h6>
                                                <p class="card-text text-muted small mb-3">@exam.Description</p>

                                                <div class="row g-2 mb-3">
                                                    <div class="col-6">
                                                        <small class="text-muted d-block">المادة</small>
                                                        <span class="badge bg-primary">@exam.SubjectName</span>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted d-block">الأسئلة</small>
                                                        <strong>@exam.TotalQuestions سؤال</strong>
                                                    </div>
                                                </div>

                                                <div class="row g-2 mb-3">
                                                    <div class="col-6">
                                                        <small class="text-muted d-block">المدة</small>
                                                        <strong>@exam.DurationMinutes دقيقة</strong>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted d-block">الدرجات</small>
                                                        <strong>@exam.TotalMarks درجة</strong>
                                                    </div>
                                                </div>

                                                <div class="row g-2 mb-3">
                                                    <div class="col-6">
                                                        <small class="text-muted d-block">تاريخ البداية</small>
                                                        <strong>@exam.StartDate.ToString("dd/MM/yyyy")</strong>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted d-block">تاريخ النهاية</small>
                                                        <strong>@exam.EndDate.ToString("dd/MM/yyyy")</strong>
                                                    </div>
                                                </div>

                                                @if (exam.BestScore.HasValue)
                                                {
                                                    <div class="alert alert-info py-2">
                                                        <small>
                                                            <i class="fas fa-trophy me-1"></i>
                                                            أفضل نتيجة: @exam.BestScore.Value.ToString("F1")%
                                                        </small>
                                                    </div>
                                                }
                                            </div>
                                            <div class="card-footer text-center">
                                                @if (exam.CanTakeExam)
                                                {
                                                    <button class="btn btn-success w-100" @onclick="() => TakeExam(exam.Id)">
                                                        <i class="fas fa-play me-2"></i>
                                                        بدء الامتحان
                                                    </button>
                                                }
                                                else
                                                {
                                                    <button class="btn btn-outline-secondary w-100" disabled>
                                                        <i class="fas fa-lock me-2"></i>
                                                        غير متاح حالياً
                                                    </button>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">لا توجد امتحانات متاحة حالياً</h6>
                                <p class="text-muted">تحقق لاحقاً من الامتحانات الجديدة</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Results -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2 text-info"></i>
                            النتائج الأخيرة
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (dashboardData.RecentResults?.Any() == true)
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الامتحان</th>
                                            <th>المادة</th>
                                            <th>التاريخ</th>
                                            <th>النتيجة</th>
                                            <th>النسبة</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var result in dashboardData.RecentResults.Take(10))
                                        {
                                            <tr>
                                                <td>
                                                    <strong>@result.ExamTitle</strong>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary">@result.ExamTitle</span>
                                                </td>
                                                <td>@result.EndTime.ToString("dd/MM/yyyy")</td>
                                                <td>
                                                    <strong>@result.MarksObtained / @result.TotalMarks</strong>
                                                </td>
                                                <td>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar @GetResultColor(result.Percentage)"
                                                             style="width: @result.Percentage%">
                                                            @result.Percentage.ToString("F1")%
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge @(result.IsPassed ? "bg-success" : "bg-danger")">
                                                        @(result.IsPassed ? "ناجح" : "راسب")
                                                    </span>
                                                </td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info" @onclick="() => ViewResult(result.AttemptId)">
                                                        <i class="fas fa-eye"></i>
                                                        عرض التفاصيل
                                                    </button>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">لا توجد نتائج بعد</h6>
                                <p class="text-muted">ابدأ بأداء امتحان لرؤية النتائج هنا</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Attempts -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2 text-secondary"></i>
                            المحاولات الأخيرة
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (dashboardData.RecentAttempts?.Any() == true)
                        {
                            <div class="list-group list-group-flush">
                                @foreach (var attempt in dashboardData.RecentAttempts.Take(5))
                                {
                                    <div class="list-group-item border-0 px-0">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm @GetAttemptStatusColor(attempt.Status) text-white rounded-circle me-3 d-flex align-items-center justify-content-center flex-shrink-0">
                                                <i class="fas @GetAttemptStatusIcon(attempt.Status) fa-sm"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">@attempt.ExamTitle</h6>
                                                <p class="mb-1 text-muted small">
                                                    بدأ في: @attempt.StartTime.ToString("dd/MM/yyyy HH:mm")
                                                    @if (attempt.EndTime.HasValue)
                                                    {
                                                        <span> - انتهى في: @attempt.EndTime.Value.ToString("HH:mm")</span>
                                                    }
                                                </p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        المحاولة رقم @attempt.AttemptNumber
                                                    </small>
                                                    @if (attempt.Score.HasValue)
                                                    {
                                                        <span class="badge @(attempt.IsPassed ? "bg-success" : "bg-danger")">
                                                            @attempt.Score.Value.ToString("F1") / @attempt.ExamTitle
                                                        </span>
                                                    }
                                                </div>
                                            </div>
                                            <div class="ms-2">
                                                <span class="badge @GetAttemptStatusBadgeColor(attempt.Status)">
                                                    @GetAttemptStatusText(attempt.Status)
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">لا توجد محاولات سابقة</h6>
                                <p class="text-muted">ستظهر محاولاتك هنا بعد بدء الامتحانات</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h5 class="text-muted">خطأ في تحميل البيانات</h5>
            <p class="text-muted">حدث خطأ أثناء تحميل بيانات الامتحانات</p>
            <button class="btn btn-primary" @onclick="RefreshData">
                <i class="fas fa-sync-alt me-2"></i>
                إعادة المحاولة
            </button>
        </div>
    }
</div>

<style>
    .exam-card {
        transition: all 0.3s ease;
    }

    .exam-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }

    .avatar-md {
        width: 48px;
        height: 48px;
    }

    .avatar-sm {
        width: 32px;
        height: 32px;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }

    .bg-light-success {
        background-color: rgba(40, 167, 69, 0.1);
    }

    .bg-light-warning {
        background-color: rgba(255, 193, 7, 0.1);
    }

    .border-success {
        border-color: #28a745 !important;
    }

    .border-warning {
        border-color: #ffc107 !important;
    }
</style>

@code {
    private StudentExamDashboardDto? dashboardData;
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            dashboardData = await ApiService.GetStudentExamDashboardAsync();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل بيانات الامتحانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshData()
    {
        await LoadDashboardData();
        await JSRuntime.InvokeVoidAsync("alert", "تم تحديث البيانات بنجاح");
    }

    private void TakeExam(int examId)
    {
        Navigation.NavigateTo($"/student/exam/{examId}");
    }

    private void ViewResult(int attemptId)
    {
        Navigation.NavigateTo($"/student/exam-result/{attemptId}");
    }

    private string GetResultColor(double percentage)
    {
        return percentage switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-info",
            >= 70 => "bg-warning",
            >= 60 => "bg-secondary",
            _ => "bg-danger"
        };
    }

    private string GetAttemptStatusColor(AttemptStatus status)
    {
        return status switch
        {
            AttemptStatus.InProgress => "bg-warning",
            AttemptStatus.Completed => "bg-success",
            AttemptStatus.Submitted => "bg-success",
            AttemptStatus.TimeExpired => "bg-danger",
            AttemptStatus.Cancelled => "bg-secondary",
            _ => "bg-secondary"
        };
    }

    private string GetAttemptStatusIcon(AttemptStatus status)
    {
        return status switch
        {
            AttemptStatus.InProgress => "fa-clock",
            AttemptStatus.Completed => "fa-check",
            AttemptStatus.Submitted => "fa-check-circle",
            AttemptStatus.TimeExpired => "fa-times",
            AttemptStatus.Cancelled => "fa-ban",
            _ => "fa-question"
        };
    }

    private string GetAttemptStatusBadgeColor(AttemptStatus status)
    {
        return status switch
        {
            AttemptStatus.InProgress => "bg-warning",
            AttemptStatus.Completed => "bg-success",
            AttemptStatus.Submitted => "bg-success",
            AttemptStatus.TimeExpired => "bg-danger",
            AttemptStatus.Cancelled => "bg-secondary",
            _ => "bg-secondary"
        };
    }

    private string GetAttemptStatusText(AttemptStatus status)
    {
        return status switch
        {
            AttemptStatus.InProgress => "قيد التنفيذ",
            AttemptStatus.Completed => "مكتمل",
            AttemptStatus.Submitted => "مرسل",
            AttemptStatus.TimeExpired => "انتهى الوقت",
            AttemptStatus.Cancelled => "ملغي",
            _ => "غير محدد"
        };
    }
}
