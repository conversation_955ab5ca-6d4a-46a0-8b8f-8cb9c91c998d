using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Schools.Shared.DTOs;

namespace Schools.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = "Admin,HR")]
public class HRController : ControllerBase
{
    private readonly ILogger<HRController> _logger;

    public HRController(ILogger<HRController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Get all employees with filtering and pagination
    /// </summary>
    [HttpGet("employees")]
    public async Task<ActionResult<PagedResult<EmployeeDto>>> GetEmployees(
        [FromQuery] string? search = null,
        [FromQuery] string? department = null,
        [FromQuery] string? position = null,
        [FromQuery] string? status = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var employees = new List<EmployeeDto>
            {
                new() { Id = "emp001", FirstName = "أحمد", LastName = "محمد", Email = "<EMAIL>",
                        Position = "مدرس رياضيات", Department = "التعليم", HireDate = DateTime.Now.AddYears(-3),
                        Salary = 5000, EmploymentType = "FullTime", Status = "Active", YearsOfExperience = 5 },
                new() { Id = "emp002", FirstName = "فاطمة", LastName = "علي", Email = "<EMAIL>",
                        Position = "مدرسة علوم", Department = "التعليم", HireDate = DateTime.Now.AddYears(-2),
                        Salary = 4800, EmploymentType = "FullTime", Status = "Active", YearsOfExperience = 4 },
                new() { Id = "emp003", FirstName = "محمد", LastName = "سالم", Email = "<EMAIL>",
                        Position = "أمين مكتبة", Department = "الخدمات", HireDate = DateTime.Now.AddYears(-1),
                        Salary = 3500, EmploymentType = "FullTime", Status = "Active", YearsOfExperience = 2 }
            };

            // Apply filters
            if (!string.IsNullOrEmpty(search))
                employees = employees.Where(e => e.FirstName.Contains(search) || e.LastName.Contains(search) || e.Email.Contains(search)).ToList();

            if (!string.IsNullOrEmpty(department))
                employees = employees.Where(e => e.Department == department).ToList();

            if (!string.IsNullOrEmpty(position))
                employees = employees.Where(e => e.Position.Contains(position)).ToList();

            if (!string.IsNullOrEmpty(status))
                employees = employees.Where(e => e.Status == status).ToList();

            var totalCount = employees.Count;
            var pagedEmployees = employees.Skip((page - 1) * pageSize).Take(pageSize).ToList();

            var result = new PagedResult<EmployeeDto>
            {
                Items = pagedEmployees,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employees");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get employee by ID
    /// </summary>
    [HttpGet("employees/{id}")]
    public async Task<ActionResult<EmployeeDto>> GetEmployee(string id)
    {
        try
        {
            var employee = new EmployeeDto
            {
                Id = id,
                FirstName = "أحمد",
                LastName = "محمد",
                Email = "<EMAIL>",
                PhoneNumber = "+966501234567",
                NationalId = "1234567890",
                DateOfBirth = new DateTime(1985, 5, 15),
                Address = "الرياض، المملكة العربية السعودية",
                Position = "مدرس رياضيات",
                Department = "التعليم",
                HireDate = DateTime.Now.AddYears(-3),
                Salary = 5000,
                EmploymentType = "FullTime",
                Status = "Active",
                Qualifications = "بكالوريوس رياضيات، ماجستير تربية",
                YearsOfExperience = 5,
                EmergencyContact = "فاطمة محمد",
                EmergencyPhone = "+966507654321",
                CreatedDate = DateTime.Now.AddYears(-3)
            };

            return Ok(employee);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting employee {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create new employee
    /// </summary>
    [HttpPost("employees")]
    public async Task<ActionResult<EmployeeDto>> CreateEmployee([FromBody] CreateEmployeeDto createDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var employee = new EmployeeDto
            {
                Id = $"emp{new Random().Next(100, 999)}",
                FirstName = createDto.FirstName,
                LastName = createDto.LastName,
                Email = createDto.Email,
                PhoneNumber = createDto.PhoneNumber,
                NationalId = createDto.NationalId,
                DateOfBirth = createDto.DateOfBirth,
                Address = createDto.Address,
                Position = createDto.Position,
                Department = createDto.Department,
                HireDate = createDto.HireDate,
                Salary = createDto.Salary,
                EmploymentType = createDto.EmploymentType,
                Status = "Active",
                Qualifications = createDto.Qualifications,
                YearsOfExperience = createDto.YearsOfExperience,
                EmergencyContact = createDto.EmergencyContact,
                EmergencyPhone = createDto.EmergencyPhone,
                CreatedDate = DateTime.Now
            };

            return CreatedAtAction(nameof(GetEmployee), new { id = employee.Id }, employee);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating employee");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get employee attendance records
    /// </summary>
    [HttpGet("employees/{id}/attendance")]
    public async Task<ActionResult<List<EmployeeAttendanceDto>>> GetEmployeeAttendance(
        string id,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var attendance = new List<EmployeeAttendanceDto>
            {
                new() { Id = 1, EmployeeId = id, EmployeeName = "أحمد محمد", Date = DateTime.Today,
                        CheckInTime = new TimeSpan(7, 30, 0), CheckOutTime = new TimeSpan(15, 30, 0),
                        WorkingHours = new TimeSpan(8, 0, 0), Status = "Present" },
                new() { Id = 2, EmployeeId = id, EmployeeName = "أحمد محمد", Date = DateTime.Today.AddDays(-1),
                        CheckInTime = new TimeSpan(7, 45, 0), CheckOutTime = new TimeSpan(15, 30, 0),
                        WorkingHours = new TimeSpan(7, 45, 0), Status = "Late" }
            };

            if (startDate.HasValue)
                attendance = attendance.Where(a => a.Date >= startDate.Value).ToList();

            if (endDate.HasValue)
                attendance = attendance.Where(a => a.Date <= endDate.Value).ToList();

            return Ok(attendance);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting attendance for employee {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Record employee attendance
    /// </summary>
    [HttpPost("attendance")]
    public async Task<ActionResult<EmployeeAttendanceDto>> RecordAttendance([FromBody] CreateEmployeeAttendanceDto attendanceDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var attendance = new EmployeeAttendanceDto
            {
                Id = new Random().Next(1000, 9999),
                EmployeeId = attendanceDto.EmployeeId,
                EmployeeName = "أحمد محمد",
                Date = attendanceDto.Date,
                CheckInTime = attendanceDto.CheckInTime,
                CheckOutTime = attendanceDto.CheckOutTime,
                Status = "Present",
                Notes = attendanceDto.Notes
            };

            return CreatedAtAction(nameof(GetAttendance), new { id = attendance.Id }, attendance);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording attendance");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get attendance record by ID
    /// </summary>
    [HttpGet("attendance/{id}")]
    public async Task<ActionResult<EmployeeAttendanceDto>> GetAttendance(int id)
    {
        try
        {
            var attendance = new EmployeeAttendanceDto
            {
                Id = id,
                EmployeeId = "emp001",
                EmployeeName = "أحمد محمد",
                Date = DateTime.Today,
                CheckInTime = new TimeSpan(7, 30, 0),
                CheckOutTime = new TimeSpan(15, 30, 0),
                WorkingHours = new TimeSpan(8, 0, 0),
                Status = "Present"
            };

            return Ok(attendance);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting attendance {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get leave requests
    /// </summary>
    [HttpGet("leave-requests")]
    public async Task<ActionResult<PagedResult<LeaveRequestDto>>> GetLeaveRequests(
        [FromQuery] string? employeeId = null,
        [FromQuery] string? status = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var leaveRequests = new List<LeaveRequestDto>
            {
                new() { Id = 1, EmployeeId = "emp001", EmployeeName = "أحمد محمد", LeaveType = "Annual",
                        StartDate = DateTime.Now.AddDays(10), EndDate = DateTime.Now.AddDays(15), TotalDays = 5,
                        Reason = "إجازة سنوية", Status = "Pending", RequestDate = DateTime.Now.AddDays(-2) },
                new() { Id = 2, EmployeeId = "emp002", EmployeeName = "فاطمة علي", LeaveType = "Sick",
                        StartDate = DateTime.Now.AddDays(-3), EndDate = DateTime.Now.AddDays(-1), TotalDays = 2,
                        Reason = "مرض", Status = "Approved", RequestDate = DateTime.Now.AddDays(-5),
                        ApprovedBy = "admin", ApprovalDate = DateTime.Now.AddDays(-4) }
            };

            // Apply filters
            if (!string.IsNullOrEmpty(employeeId))
                leaveRequests = leaveRequests.Where(lr => lr.EmployeeId == employeeId).ToList();

            if (!string.IsNullOrEmpty(status))
                leaveRequests = leaveRequests.Where(lr => lr.Status == status).ToList();

            var totalCount = leaveRequests.Count;
            var pagedRequests = leaveRequests.Skip((page - 1) * pageSize).Take(pageSize).ToList();

            var result = new PagedResult<LeaveRequestDto>
            {
                Items = pagedRequests,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting leave requests");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create leave request
    /// </summary>
    [HttpPost("leave-requests")]
    public async Task<ActionResult<LeaveRequestDto>> CreateLeaveRequest([FromBody] CreateLeaveRequestDto createDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var totalDays = (createDto.EndDate - createDto.StartDate).Days + 1;

            var leaveRequest = new LeaveRequestDto
            {
                Id = new Random().Next(1000, 9999),
                EmployeeId = createDto.EmployeeId,
                EmployeeName = "أحمد محمد",
                LeaveType = createDto.LeaveType,
                StartDate = createDto.StartDate,
                EndDate = createDto.EndDate,
                TotalDays = totalDays,
                Reason = createDto.Reason,
                Status = "Pending",
                RequestDate = DateTime.Now,
                Documents = createDto.Documents
            };

            return CreatedAtAction(nameof(GetLeaveRequest), new { id = leaveRequest.Id }, leaveRequest);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating leave request");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get leave request by ID
    /// </summary>
    [HttpGet("leave-requests/{id}")]
    public async Task<ActionResult<LeaveRequestDto>> GetLeaveRequest(int id)
    {
        try
        {
            var leaveRequest = new LeaveRequestDto
            {
                Id = id,
                EmployeeId = "emp001",
                EmployeeName = "أحمد محمد",
                LeaveType = "Annual",
                StartDate = DateTime.Now.AddDays(10),
                EndDate = DateTime.Now.AddDays(15),
                TotalDays = 5,
                Reason = "إجازة سنوية",
                Status = "Pending",
                RequestDate = DateTime.Now.AddDays(-2)
            };

            return Ok(leaveRequest);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting leave request {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Approve or reject leave request
    /// </summary>
    [HttpPut("leave-requests/{id}/approve")]
    public async Task<ActionResult<LeaveRequestDto>> ApproveLeaveRequest(int id, [FromBody] ApproveLeaveDto approveDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var leaveRequest = new LeaveRequestDto
            {
                Id = id,
                EmployeeId = "emp001",
                EmployeeName = "أحمد محمد",
                LeaveType = "Annual",
                StartDate = DateTime.Now.AddDays(10),
                EndDate = DateTime.Now.AddDays(15),
                TotalDays = 5,
                Reason = "إجازة سنوية",
                Status = approveDto.Status,
                RequestDate = DateTime.Now.AddDays(-2),
                ApprovedBy = "current-user-id",
                ApprovalDate = DateTime.Now,
                ApprovalNotes = approveDto.Notes
            };

            return Ok(leaveRequest);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving leave request {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get HR statistics
    /// </summary>
    [HttpGet("statistics")]
    public async Task<ActionResult<HRStatisticsDto>> GetHRStatistics()
    {
        try
        {
            var statistics = new HRStatisticsDto
            {
                TotalEmployees = 45,
                ActiveEmployees = 42,
                InactiveEmployees = 3,
                NewHires = 5,
                Terminations = 2,
                TurnoverRate = 4.4,
                AttendanceRate = 94.5,
                PendingLeaveRequests = 8,
                ApprovedLeaveRequests = 25,
                TotalPayroll = 180000,
                AveragePerformanceRating = 4.2,
                DepartmentDistribution = new Dictionary<string, int>
                {
                    { "التعليم", 30 },
                    { "الإدارة", 8 },
                    { "الخدمات", 7 }
                },
                PositionDistribution = new Dictionary<string, int>
                {
                    { "مدرس", 25 },
                    { "إداري", 10 },
                    { "فني", 5 },
                    { "أمن", 5 }
                },
                SalaryRanges = new Dictionary<string, decimal>
                {
                    { "3000-4000", 15 },
                    { "4000-5000", 20 },
                    { "5000-6000", 8 },
                    { "6000+", 2 }
                }
            };

            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting HR statistics");
            return StatusCode(500, "Internal server error");
        }
    }
}
