using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.Models;
using Schools.Shared.DTOs;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class LibraryController : ControllerBase
    {
        private readonly SchoolsDbContext _context;
        private readonly ILogger<LibraryController> _logger;

        public LibraryController(SchoolsDbContext context, ILogger<LibraryController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpGet("books")]
        public async Task<ActionResult<IEnumerable<BookDto>>> GetBooks(
            [FromQuery] string? category = null,
            [FromQuery] string? status = null,
            [FromQuery] string? grade = null,
            [FromQuery] string? search = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var query = _context.Books
                    .Include(b => b.BorrowRecords.Where(br => br.ReturnDate == null))
                    .ThenInclude(br => br.Student)
                    .AsQueryable();

                if (!string.IsNullOrEmpty(category))
                    query = query.Where(b => b.Category == category);

                if (!string.IsNullOrEmpty(status) && Enum.TryParse<BookStatus>(status, out var statusEnum))
                    query = query.Where(b => b.Status == statusEnum);

                if (!string.IsNullOrEmpty(grade))
                    query = query.Where(b => b.Grade == grade);

                if (!string.IsNullOrEmpty(search))
                    query = query.Where(b => b.Title.Contains(search) ||
                                           b.Author.Contains(search) ||
                                           b.ISBN.Contains(search));

                var totalCount = await query.CountAsync();

                var books = await query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(b => new BookDto
                    {
                        Id = b.Id,
                        Title = b.Title,
                        Author = b.Author,
                        ISBN = b.ISBN,
                        Category = b.Category,
                        Status = b.Status.ToString(),
                        Grade = b.Grade,
                        PublishYear = b.PublishYear,
                        Pages = b.Pages,
                        Publisher = b.Publisher,
                        Description = b.Description,
                        Location = b.Location,
                        BorrowerName = b.BorrowRecords
                            .Where(br => br.ReturnDate == null)
                            .Select(br => br.Student.FirstName + " " + br.Student.LastName)
                            .FirstOrDefault(),
                        BorrowDate = b.BorrowRecords
                            .Where(br => br.ReturnDate == null)
                            .Select(br => br.BorrowDate)
                            .FirstOrDefault(),
                        DueDate = b.BorrowRecords
                            .Where(br => br.ReturnDate == null)
                            .Select(br => br.DueDate)
                            .FirstOrDefault()
                    })
                    .ToListAsync();

                Response.Headers.Append("X-Total-Count", totalCount.ToString());
                return Ok(books);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving books");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("books/{id}")]
        public async Task<ActionResult<BookDto>> GetBook(int id)
        {
            try
            {
                var book = await _context.Books
                    .Include(b => b.BorrowRecords.Where(br => br.ReturnDate == null))
                    .ThenInclude(br => br.Student)
                    .FirstOrDefaultAsync(b => b.Id == id);

                if (book == null)
                    return NotFound();

                var bookDto = new BookDto
                {
                    Id = book.Id,
                    Title = book.Title,
                    Author = book.Author,
                    ISBN = book.ISBN,
                    Category = book.Category,
                    Status = book.Status.ToString(),
                    Grade = book.Grade,
                    PublishYear = book.PublishYear,
                    Pages = book.Pages,
                    Publisher = book.Publisher,
                    Description = book.Description,
                    Location = book.Location,
                    BorrowerName = book.BorrowRecords
                        .Where(br => br.ReturnDate == null)
                        .Select(br => br.Student.FirstName + " " + br.Student.LastName)
                        .FirstOrDefault(),
                    BorrowDate = book.BorrowRecords
                        .Where(br => br.ReturnDate == null)
                        .Select(br => br.BorrowDate)
                        .FirstOrDefault(),
                    DueDate = book.BorrowRecords
                        .Where(br => br.ReturnDate == null)
                        .Select(br => br.DueDate)
                        .FirstOrDefault()
                };

                return Ok(bookDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving book {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("books")]
        [Authorize(Roles = "Admin,Librarian")]
        public async Task<ActionResult<BookDto>> CreateBook(CreateBookDto createBookDto)
        {
            try
            {
                // Check if ISBN already exists
                var existingBook = await _context.Books
                    .FirstOrDefaultAsync(b => b.ISBN == createBookDto.ISBN);

                if (existingBook != null)
                    return BadRequest("A book with this ISBN already exists");

                var book = new Book
                {
                    Title = createBookDto.Title,
                    Author = createBookDto.Author,
                    ISBN = createBookDto.ISBN,
                    Category = createBookDto.Category,
                    Status = BookStatus.Available,
                    Grade = createBookDto.Grade,
                    PublishYear = createBookDto.PublishYear,
                    Pages = createBookDto.Pages,
                    Publisher = createBookDto.Publisher,
                    Description = createBookDto.Description,
                    Location = createBookDto.Location,
                    CreatedAt = DateTime.UtcNow
                };

                _context.Books.Add(book);
                await _context.SaveChangesAsync();

                var bookDto = new BookDto
                {
                    Id = book.Id,
                    Title = book.Title,
                    Author = book.Author,
                    ISBN = book.ISBN,
                    Category = book.Category,
                    Status = book.Status.ToString(),
                    Grade = book.Grade,
                    PublishYear = book.PublishYear,
                    Pages = book.Pages,
                    Publisher = book.Publisher,
                    Description = book.Description,
                    Location = book.Location
                };

                return CreatedAtAction(nameof(GetBook), new { id = book.Id }, bookDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating book");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("books/{id}")]
        [Authorize(Roles = "Admin,Librarian")]
        public async Task<IActionResult> UpdateBook(int id, UpdateBookDto updateBookDto)
        {
            try
            {
                var book = await _context.Books.FindAsync(id);
                if (book == null)
                    return NotFound();

                // Check if ISBN already exists (excluding current book)
                var existingBook = await _context.Books
                    .FirstOrDefaultAsync(b => b.ISBN == updateBookDto.ISBN && b.Id != id);

                if (existingBook != null)
                    return BadRequest("A book with this ISBN already exists");

                book.Title = updateBookDto.Title;
                book.Author = updateBookDto.Author;
                book.ISBN = updateBookDto.ISBN;
                book.Category = updateBookDto.Category;
                book.Grade = updateBookDto.Grade;
                book.PublishYear = updateBookDto.PublishYear;
                book.Pages = updateBookDto.Pages;
                book.Publisher = updateBookDto.Publisher;
                book.Description = updateBookDto.Description;
                book.Location = updateBookDto.Location;
                book.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating book {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("books/{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteBook(int id)
        {
            try
            {
                var book = await _context.Books
                    .Include(b => b.BorrowRecords)
                    .FirstOrDefaultAsync(b => b.Id == id);

                if (book == null)
                    return NotFound();

                // Check if book is currently borrowed
                var isCurrentlyBorrowed = book.BorrowRecords
                    .Any(br => br.ReturnDate == null);

                if (isCurrentlyBorrowed)
                    return BadRequest("Cannot delete a book that is currently borrowed");

                _context.Books.Remove(book);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting book {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("books/{id}/borrow")]
        [Authorize(Roles = "Admin,Librarian")]
        public async Task<ActionResult<BorrowRecordDto>> BorrowBook(int id, BorrowBookDto borrowBookDto)
        {
            try
            {
                var book = await _context.Books.FindAsync(id);
                if (book == null)
                    return NotFound("Book not found");

                if (book.Status != BookStatus.Available)
                    return BadRequest("Book is not available for borrowing");

                var student = await _context.Users
                    .FirstOrDefaultAsync(u => u.Id == borrowBookDto.StudentId);
                if (student == null)
                    return NotFound("Student not found");

                // Check if student has overdue books
                var overdueBooks = await _context.BorrowRecords
                    .Where(br => br.StudentId == borrowBookDto.StudentId &&
                                br.ReturnDate == null &&
                                br.DueDate < DateTime.Now)
                    .CountAsync();

                if (overdueBooks > 0)
                    return BadRequest("Student has overdue books");

                var borrowRecord = new BorrowRecord
                {
                    BookId = id,
                    StudentId = borrowBookDto.StudentId,
                    BorrowDate = DateTime.Now,
                    DueDate = DateTime.Now.AddDays(14), // 2 weeks default
                    CreatedAt = DateTime.UtcNow
                };

                book.Status = BookStatus.Borrowed;

                _context.BorrowRecords.Add(borrowRecord);
                await _context.SaveChangesAsync();

                var borrowRecordDto = new BorrowRecordDto
                {
                    Id = borrowRecord.Id,
                    BookId = borrowRecord.BookId,
                    BookTitle = book.Title,
                    StudentId = borrowRecord.StudentId,
                    StudentName = student.FirstName + " " + student.LastName,
                    BorrowDate = borrowRecord.BorrowDate,
                    DueDate = borrowRecord.DueDate,
                    ReturnDate = borrowRecord.ReturnDate
                };

                return Ok(borrowRecordDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error borrowing book {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("books/{id}/return")]
        [Authorize(Roles = "Admin,Librarian")]
        public async Task<IActionResult> ReturnBook(int id)
        {
            try
            {
                var book = await _context.Books.FindAsync(id);
                if (book == null)
                    return NotFound("Book not found");

                var borrowRecord = await _context.BorrowRecords
                    .FirstOrDefaultAsync(br => br.BookId == id && br.ReturnDate == null);

                if (borrowRecord == null)
                    return BadRequest("Book is not currently borrowed");

                borrowRecord.ReturnDate = DateTime.Now;
                book.Status = BookStatus.Available;

                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error returning book {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("statistics")]
        public async Task<ActionResult<LibraryStatisticsDto>> GetLibraryStatistics()
        {
            try
            {
                var totalBooks = await _context.Books.CountAsync();
                var availableBooks = await _context.Books.CountAsync(b => b.Status == BookStatus.Available);
                var borrowedBooks = await _context.Books.CountAsync(b => b.Status == BookStatus.Borrowed);
                var overdueBooks = await _context.BorrowRecords
                    .CountAsync(br => br.ReturnDate == null && br.DueDate < DateTime.Now);

                var categoryStats = await _context.Books
                    .GroupBy(b => b.Category)
                    .Select(g => new CategoryStatDto
                    {
                        Category = g.Key,
                        Count = g.Count()
                    })
                    .ToListAsync();

                var statistics = new LibraryStatisticsDto
                {
                    TotalBooks = totalBooks,
                    AvailableBooks = availableBooks,
                    BorrowedBooks = borrowedBooks,
                    OverdueBooks = overdueBooks,
                    CategoryStatistics = categoryStats
                };

                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving library statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("borrow-records")]
        [Authorize(Roles = "Admin,Librarian")]
        public async Task<ActionResult<IEnumerable<BorrowRecordDto>>> GetBorrowRecords(
            [FromQuery] string? studentId = null,
            [FromQuery] bool? isOverdue = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var query = _context.BorrowRecords
                    .Include(br => br.Book)
                    .Include(br => br.Student)
                    .AsQueryable();

                if (!string.IsNullOrEmpty(studentId))
                    query = query.Where(br => br.StudentId == studentId);

                if (isOverdue.HasValue && isOverdue.Value)
                    query = query.Where(br => br.ReturnDate == null && br.DueDate < DateTime.Now);

                var totalCount = await query.CountAsync();

                var borrowRecords = await query
                    .OrderByDescending(br => br.BorrowDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(br => new BorrowRecordDto
                    {
                        Id = br.Id,
                        BookId = br.BookId,
                        BookTitle = br.Book.Title,
                        StudentId = br.StudentId,
                        StudentName = br.Student.FirstName + " " + br.Student.LastName,
                        BorrowDate = br.BorrowDate,
                        DueDate = br.DueDate,
                        ReturnDate = br.ReturnDate
                    })
                    .ToListAsync();

                Response.Headers.Append("X-Total-Count", totalCount.ToString());
                return Ok(borrowRecords);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving borrow records");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
