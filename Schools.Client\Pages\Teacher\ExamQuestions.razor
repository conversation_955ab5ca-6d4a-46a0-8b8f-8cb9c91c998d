@page "/teacher/exams/{ExamId:int}/questions"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Teacher")]

<PageTitle>إدارة أسئلة الامتحان - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/teacher/exams">الامتحانات</a></li>
                    <li class="breadcrumb-item active">إدارة الأسئلة</li>
                </ol>
            </nav>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل بيانات الامتحان...</p>
        </div>
    }
    else if (exam != null)
    {
        <!-- Exam Info Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-gradient-primary text-white border-0 shadow-lg">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="mb-1">@exam.Title</h2>
                                <p class="mb-0 opacity-75">@exam.Description</p>
                                <div class="mt-2">
                                    <span class="badge bg-light text-dark me-2">@exam.SubjectName</span>
                                    <span class="badge bg-light text-dark me-2">@exam.ClassName</span>
                                    <span class="badge bg-light text-dark">@exam.DurationMinutes دقيقة</span>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <button class="btn btn-light btn-lg" @onclick="ShowAddQuestionModal">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة سؤال جديد
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Questions Statistics -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-primary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-question-circle fa-lg"></i>
                        </div>
                        <h4 class="text-primary mb-1">@questions.Count</h4>
                        <p class="text-muted mb-0">إجمالي الأسئلة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-success text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-star fa-lg"></i>
                        </div>
                        <h4 class="text-success mb-1">@totalMarks</h4>
                        <p class="text-muted mb-0">إجمالي الدرجات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-info text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-list fa-lg"></i>
                        </div>
                        <h4 class="text-info mb-1">@GetQuestionTypeCount(QuestionType.MultipleChoice)</h4>
                        <p class="text-muted mb-0">اختيار متعدد</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-warning text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-check-circle fa-lg"></i>
                        </div>
                        <h4 class="text-warning mb-1">@GetQuestionTypeCount(QuestionType.TrueFalse)</h4>
                        <p class="text-muted mb-0">صح/خطأ</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Questions List -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2 text-primary"></i>
                                    قائمة الأسئلة
                                </h5>
                            </div>
                            <div class="col-auto">
                                <div class="btn-group">
                                    <button class="btn btn-outline-secondary btn-sm" @onclick="ReorderQuestions">
                                        <i class="fas fa-sort me-2"></i>
                                        إعادة ترتيب
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" @onclick="PreviewExam">
                                        <i class="fas fa-eye me-2"></i>
                                        معاينة الامتحان
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (questions?.Any() == true)
                        {
                            <div class="questions-list">
                                @foreach (var question in questions.OrderBy(q => q.OrderIndex))
                                {
                                    <div class="question-item card mb-3 border">
                                        <div class="card-header bg-light">
                                            <div class="row align-items-center">
                                                <div class="col">
                                                    <div class="d-flex align-items-center">
                                                        <span class="badge bg-primary me-2">السؤال @question.OrderIndex</span>
                                                        <span class="badge @GetQuestionTypeBadgeClass(question.Type) me-2">@GetQuestionTypeText(question.Type)</span>
                                                        <span class="badge bg-success">@question.Marks درجة</span>
                                                        @if (question.IsRequired)
                                                        {
                                                            <span class="badge bg-danger ms-2">إجباري</span>
                                                        }
                                                    </div>
                                                </div>
                                                <div class="col-auto">
                                                    <div class="btn-group">
                                                        <button class="btn btn-sm btn-outline-primary" @onclick="() => EditQuestion(question)" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-secondary" @onclick="() => DuplicateQuestion(question)" title="نسخ">
                                                            <i class="fas fa-copy"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteQuestion(question.Id)" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="question-content">
                                                <h6 class="question-text">@question.QuestionText</h6>

                                                @if (!string.IsNullOrEmpty(question.ImageUrl))
                                                {
                                                    <div class="question-image mb-3">
                                                        <img src="@question.ImageUrl" class="img-thumbnail" style="max-height: 200px;" alt="صورة السؤال" />
                                                    </div>
                                                }

                                                @if (question.Type == QuestionType.MultipleChoice && question.Options?.Any() == true)
                                                {
                                                    <div class="options-list">
                                                        <h6 class="text-muted mb-2">الخيارات:</h6>
                                                        <div class="row g-2">
                                                            @foreach (var option in question.Options.OrderBy(o => o.OrderIndex))
                                                            {
                                                                <div class="col-md-6">
                                                                    <div class="option-preview @(option.IsCorrect ? "correct-option" : "")">
                                                                        <div class="d-flex align-items-center">
                                                                            <span class="option-letter me-2">@GetOptionLetter(option.OrderIndex)</span>
                                                                            <span class="flex-grow-1">@option.OptionText</span>
                                                                            @if (option.IsCorrect)
                                                                            {
                                                                                <i class="fas fa-check text-success ms-2"></i>
                                                                            }
                                                                        </div>
                                                                        @if (!string.IsNullOrEmpty(option.ImageUrl))
                                                                        {
                                                                            <img src="@option.ImageUrl" class="option-image mt-2" alt="صورة الخيار" />
                                                                        }
                                                                    </div>
                                                                </div>
                                                            }
                                                        </div>
                                                    </div>
                                                }
                                                else if (question.Type == QuestionType.TrueFalse)
                                                {
                                                    <div class="true-false-answer">
                                                        <h6 class="text-muted mb-2">الإجابة الصحيحة:</h6>
                                                        <span class="badge @(question.CorrectAnswer == "صحيح" ? "bg-success" : "bg-danger")">
                                                            @question.CorrectAnswer
                                                        </span>
                                                    </div>
                                                }

                                                @if (!string.IsNullOrEmpty(question.Explanation))
                                                {
                                                    <div class="explanation mt-3">
                                                        <h6 class="text-muted mb-2">التفسير:</h6>
                                                        <p class="text-muted small">@question.Explanation</p>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد أسئلة بعد</h5>
                                <p class="text-muted">ابدأ بإضافة أسئلة للامتحان</p>
                                <button class="btn btn-primary" @onclick="ShowAddQuestionModal">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة السؤال الأول
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h5 class="text-muted">لم يتم العثور على الامتحان</h5>
            <p class="text-muted">تأكد من صحة الرابط أو تواصل مع الإدارة</p>
            <a href="/teacher/exams" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للامتحانات
            </a>
        </div>
    }
</div>

<!-- Add/Edit Question Modal -->
@if (showQuestionModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas @(editingQuestion == null ? "fa-plus" : "fa-edit") me-2"></i>
                        @(editingQuestion == null ? "إضافة سؤال جديد" : "تعديل السؤال")
                    </h5>
                    <button type="button" class="btn-close btn-close-white" @onclick="CloseQuestionModal"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="questionModel" OnValidSubmit="SaveQuestion">
                        <DataAnnotationsValidator />

                        <div class="row g-3">
                            <!-- Question Basic Info -->
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-info-circle me-2"></i>
                                    معلومات السؤال
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">نوع السؤال <span class="text-danger">*</span></label>
                                <select @bind="questionModel.Type" @bind:after="OnQuestionTypeChanged" class="form-select">
                                    <option value="">-- اختر نوع السؤال --</option>
                                    <option value="@QuestionType.MultipleChoice">اختيار من متعدد</option>
                                    <option value="@QuestionType.TrueFalse">صح/خطأ</option>
                                    <option value="@QuestionType.ShortAnswer">إجابة قصيرة</option>
                                    <option value="@QuestionType.Essay">مقال</option>
                                </select>
                                <ValidationMessage For="@(() => questionModel.Type)" class="text-danger" />
                            </div>

                            <div class="col-md-3">
                                <label class="form-label">الدرجات <span class="text-danger">*</span></label>
                                <InputNumber @bind-Value="questionModel.Marks" class="form-control" min="1" max="100" />
                                <ValidationMessage For="@(() => questionModel.Marks)" class="text-danger" />
                            </div>

                            <div class="col-md-3">
                                <label class="form-label">ترتيب السؤال</label>
                                <InputNumber @bind-Value="questionModel.OrderIndex" class="form-control" min="1" />
                                <ValidationMessage For="@(() => questionModel.OrderIndex)" class="text-danger" />
                            </div>

                            <div class="col-12">
                                <label class="form-label">نص السؤال <span class="text-danger">*</span></label>
                                <InputTextArea @bind-Value="questionModel.QuestionText" class="form-control" rows="3" placeholder="اكتب نص السؤال هنا..." />
                                <ValidationMessage For="@(() => questionModel.QuestionText)" class="text-danger" />
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">رابط صورة السؤال (اختياري)</label>
                                <InputText @bind-Value="questionModel.ImageUrl" class="form-control" placeholder="https://example.com/image.jpg" />
                            </div>

                            <div class="col-md-6">
                                <div class="form-check mt-4">
                                    <InputCheckbox @bind-Value="questionModel.IsRequired" class="form-check-input" />
                                    <label class="form-check-label">سؤال إجباري</label>
                                </div>
                            </div>

                            <!-- Question Type Specific Fields -->
                            @if (questionModel.Type == QuestionType.MultipleChoice)
                            {
                                <div class="col-12 mt-4">
                                    <h6 class="text-primary border-bottom pb-2">
                                        <i class="fas fa-list me-2"></i>
                                        خيارات الإجابة
                                    </h6>
                                </div>

                                @for (int i = 0; i < questionModel.Options.Count; i++)
                                {
                                    var index = i;
                                    <div class="col-12">
                                        <div class="card border">
                                            <div class="card-body">
                                                <div class="row g-2 align-items-center">
                                                    <div class="col-md-6">
                                                        <label class="form-label">الخيار @(index + 1)</label>
                                                        <input type="text" class="form-control" @bind="questionModel.Options[index].OptionText" placeholder="نص الخيار" />
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label class="form-label">رابط الصورة (اختياري)</label>
                                                        <input type="text" class="form-control" @bind="questionModel.Options[index].ImageUrl" placeholder="رابط الصورة" />
                                                    </div>
                                                    <div class="col-md-1">
                                                        <div class="form-check mt-4">
                                                            <input type="radio" class="form-check-input" name="correctOption"
                                                                   @onchange="@((e) => SetCorrectOption(index))"
                                                                   checked="@questionModel.Options[index].IsCorrect" />
                                                            <label class="form-check-label">صحيح</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-1">
                                                        @if (questionModel.Options.Count > 2)
                                                        {
                                                            <button type="button" class="btn btn-outline-danger btn-sm mt-4" @onclick="() => RemoveOption(index)">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }

                                <div class="col-12">
                                    <button type="button" class="btn btn-outline-primary" @onclick="AddOption">
                                        <i class="fas fa-plus me-2"></i>
                                        إضافة خيار جديد
                                    </button>
                                </div>
                            }
                            else if (questionModel.Type == QuestionType.TrueFalse)
                            {
                                <div class="col-12 mt-4">
                                    <h6 class="text-primary border-bottom pb-2">
                                        <i class="fas fa-check-circle me-2"></i>
                                        الإجابة الصحيحة
                                    </h6>
                                </div>

                                <div class="col-12">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input type="radio" class="form-check-input" name="trueFalseAnswer"
                                                       @onchange="@((e) => questionModel.CorrectAnswer = "صحيح")"
                                                       checked="@(questionModel.CorrectAnswer == "صحيح")" />
                                                <label class="form-check-label">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    صحيح
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input type="radio" class="form-check-input" name="trueFalseAnswer"
                                                       @onchange="@((e) => questionModel.CorrectAnswer = "خطأ")"
                                                       checked="@(questionModel.CorrectAnswer == "خطأ")" />
                                                <label class="form-check-label">
                                                    <i class="fas fa-times-circle text-danger me-2"></i>
                                                    خطأ
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }

                            <!-- Explanation -->
                            <div class="col-12 mt-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    التفسير (اختياري)
                                </h6>
                            </div>

                            <div class="col-12">
                                <label class="form-label">تفسير الإجابة</label>
                                <InputTextArea @bind-Value="questionModel.Explanation" class="form-control" rows="3" placeholder="اكتب تفسيراً للإجابة الصحيحة..." />
                            </div>
                        </div>
                    </EditForm>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseQuestionModal">
                        <i class="fas fa-times me-2"></i>
                        إلغاء
                    </button>
                    <button type="button" class="btn btn-primary" @onclick="SaveQuestion" disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        else
                        {
                            <i class="fas fa-save me-2"></i>
                        }
                        @(editingQuestion == null ? "إضافة السؤال" : "حفظ التغييرات")
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .avatar-md {
        width: 48px;
        height: 48px;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }

    .question-item {
        transition: all 0.3s ease;
    }

    .question-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }

    .option-preview {
        padding: 8px 12px;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        background-color: #f8f9fa;
    }

    .correct-option {
        background-color: #d4edda;
        border-color: #c3e6cb;
    }

    .option-letter {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        background-color: #007bff;
        color: white;
        border-radius: 50%;
        font-size: 12px;
        font-weight: bold;
    }

    .option-image {
        max-width: 100%;
        max-height: 100px;
        border-radius: 4px;
    }

    .question-text {
        line-height: 1.6;
        margin-bottom: 15px;
    }
</style>

@code {
    [Parameter] public int ExamId { get; set; }

    private ExamDto? exam;
    private List<ExamQuestionDto> questions = new();
    private bool isLoading = true;
    private bool showQuestionModal = false;
    private bool isSaving = false;
    private ExamQuestionDto? editingQuestion;
    private CreateExamQuestionDto questionModel = new();
    private int totalMarks = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadExamData();
    }

    private async Task LoadExamData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            exam = await ApiService.GetExamAsync(ExamId);
            if (exam != null)
            {
                questions = exam.Questions ?? new List<ExamQuestionDto>();
                CalculateTotalMarks();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل بيانات الامتحان: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void CalculateTotalMarks()
    {
        totalMarks = questions.Sum(q => q.Marks);
    }

    private int GetQuestionTypeCount(QuestionType type)
    {
        return questions.Count(q => q.Type == type);
    }

    private void ShowAddQuestionModal()
    {
        editingQuestion = null;
        questionModel = new CreateExamQuestionDto
        {
            ExamId = ExamId,
            Type = QuestionType.MultipleChoice,
            Marks = 1,
            OrderIndex = questions.Count + 1,
            IsRequired = true,
            Options = new List<CreateQuestionOptionDto>
            {
                new() { OptionText = "", OrderIndex = 1, IsCorrect = true },
                new() { OptionText = "", OrderIndex = 2, IsCorrect = false }
            }
        };
        showQuestionModal = true;
    }

    private void EditQuestion(ExamQuestionDto question)
    {
        editingQuestion = question;
        questionModel = new CreateExamQuestionDto
        {
            ExamId = ExamId,
            QuestionText = question.QuestionText,
            Type = question.Type,
            Marks = question.Marks,
            OrderIndex = question.OrderIndex,
            IsRequired = question.IsRequired,
            ImageUrl = question.ImageUrl,
            CorrectAnswer = question.CorrectAnswer,
            Explanation = question.Explanation,
            Options = question.Options?.Select(o => new CreateQuestionOptionDto
            {
                OptionText = o.OptionText,
                IsCorrect = o.IsCorrect,
                OrderIndex = o.OrderIndex,
                ImageUrl = o.ImageUrl
            }).ToList() ?? new List<CreateQuestionOptionDto>()
        };
        showQuestionModal = true;
    }

    private void CloseQuestionModal()
    {
        showQuestionModal = false;
        editingQuestion = null;
        questionModel = new();
    }

    private void OnQuestionTypeChanged()
    {
        var type = questionModel.Type;

        if (type == QuestionType.MultipleChoice && questionModel.Options.Count == 0)
        {
            questionModel.Options = new List<CreateQuestionOptionDto>
            {
                new() { OptionText = "", OrderIndex = 1, IsCorrect = true },
                new() { OptionText = "", OrderIndex = 2, IsCorrect = false }
            };
        }
        else if (type == QuestionType.TrueFalse)
        {
            questionModel.Options.Clear();
            questionModel.CorrectAnswer = "صحيح";
        }
        else if (type == QuestionType.ShortAnswer || type == QuestionType.Essay)
        {
            questionModel.Options.Clear();
            questionModel.CorrectAnswer = "";
        }
    }

    private void AddOption()
    {
        questionModel.Options.Add(new CreateQuestionOptionDto
        {
            OptionText = "",
            OrderIndex = questionModel.Options.Count + 1,
            IsCorrect = false
        });
    }

    private void RemoveOption(int index)
    {
        if (questionModel.Options.Count > 2)
        {
            questionModel.Options.RemoveAt(index);

            // Update order indices
            for (int i = 0; i < questionModel.Options.Count; i++)
            {
                questionModel.Options[i].OrderIndex = i + 1;
            }
        }
    }

    private void SetCorrectOption(int index)
    {
        for (int i = 0; i < questionModel.Options.Count; i++)
        {
            questionModel.Options[i].IsCorrect = i == index;
        }
    }

    private async Task SaveQuestion()
    {
        try
        {
            isSaving = true;
            StateHasChanged();

            if (editingQuestion == null)
            {
                var newQuestion = await ApiService.CreateExamQuestionAsync(questionModel);
                if (newQuestion != null)
                {
                    questions.Add(newQuestion);
                    await JSRuntime.InvokeVoidAsync("alert", "تم إضافة السؤال بنجاح");
                }
            }
            else
            {
                var success = await ApiService.UpdateExamQuestionAsync(editingQuestion.Id, questionModel);
                if (success)
                {
                    await LoadExamData(); // Reload to get updated data
                    await JSRuntime.InvokeVoidAsync("alert", "تم تحديث السؤال بنجاح");
                }
            }

            CalculateTotalMarks();
            CloseQuestionModal();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ السؤال: {ex.Message}");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task DeleteQuestion(int questionId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا السؤال؟"))
        {
            try
            {
                var success = await ApiService.DeleteExamQuestionAsync(questionId);
                if (success)
                {
                    questions.RemoveAll(q => q.Id == questionId);
                    CalculateTotalMarks();
                    await JSRuntime.InvokeVoidAsync("alert", "تم حذف السؤال بنجاح");
                    StateHasChanged();
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف السؤال: {ex.Message}");
            }
        }
    }

    private async Task DuplicateQuestion(ExamQuestionDto question)
    {
        try
        {
            var duplicateModel = new CreateExamQuestionDto
            {
                ExamId = ExamId,
                QuestionText = question.QuestionText + " (نسخة)",
                Type = question.Type,
                Marks = question.Marks,
                OrderIndex = questions.Count + 1,
                IsRequired = question.IsRequired,
                ImageUrl = question.ImageUrl,
                CorrectAnswer = question.CorrectAnswer,
                Explanation = question.Explanation,
                Options = question.Options?.Select(o => new CreateQuestionOptionDto
                {
                    OptionText = o.OptionText,
                    IsCorrect = o.IsCorrect,
                    OrderIndex = o.OrderIndex,
                    ImageUrl = o.ImageUrl
                }).ToList() ?? new List<CreateQuestionOptionDto>()
            };

            var newQuestion = await ApiService.CreateExamQuestionAsync(duplicateModel);
            if (newQuestion != null)
            {
                questions.Add(newQuestion);
                CalculateTotalMarks();
                await JSRuntime.InvokeVoidAsync("alert", "تم نسخ السؤال بنجاح");
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في نسخ السؤال: {ex.Message}");
        }
    }

    private async Task ReorderQuestions()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة إعادة الترتيب ستكون متاحة قريباً");
    }

    private async Task PreviewExam()
    {
        Navigation.NavigateTo($"/teacher/exams/{ExamId}/preview");
    }

    private string GetQuestionTypeBadgeClass(QuestionType type)
    {
        return type switch
        {
            QuestionType.MultipleChoice => "bg-primary",
            QuestionType.TrueFalse => "bg-success",
            QuestionType.ShortAnswer => "bg-info",
            QuestionType.Essay => "bg-warning",
            _ => "bg-secondary"
        };
    }

    private string GetQuestionTypeText(QuestionType type)
    {
        return type switch
        {
            QuestionType.MultipleChoice => "اختيار متعدد",
            QuestionType.TrueFalse => "صح/خطأ",
            QuestionType.ShortAnswer => "إجابة قصيرة",
            QuestionType.Essay => "مقال",
            _ => "غير محدد"
        };
    }

    private string GetOptionLetter(int index)
    {
        var letters = new[] { "أ", "ب", "ج", "د", "هـ", "و", "ز", "ح", "ط", "ي" };
        return index > 0 && index <= letters.Length ? letters[index - 1] : index.ToString();
    }
}
