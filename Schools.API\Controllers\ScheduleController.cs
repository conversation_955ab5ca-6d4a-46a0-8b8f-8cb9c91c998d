using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.Models;
using Schools.Shared.DTOs;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ScheduleController : ControllerBase
    {
        private readonly SchoolsDbContext _context;
        private readonly ILogger<ScheduleController> _logger;

        public ScheduleController(SchoolsDbContext context, ILogger<ScheduleController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<ScheduleDto>>> GetSchedules(
            [FromQuery] int? classId = null,
            [FromQuery] string? teacherId = null,
            [FromQuery] DateTime? date = null)
        {
            try
            {
                var query = _context.Schedules
                    .Include(s => s.Class)
                    .Include(s => s.Subject)
                    .Include(s => s.Teacher)
                    .AsQueryable();

                if (classId.HasValue)
                    query = query.Where(s => s.ClassId == classId.Value);

                if (!string.IsNullOrEmpty(teacherId))
                    query = query.Where(s => s.TeacherId == teacherId);

                if (date.HasValue)
                    query = query.Where(s => s.Date.Date == date.Value.Date);

                var schedules = await query
                    .Select(s => new ScheduleDto
                    {
                        Id = s.Id,
                        ClassId = s.ClassId,
                        ClassName = s.Class.Name,
                        SubjectId = s.SubjectId,
                        SubjectName = s.Subject.Name,
                        TeacherId = s.TeacherId,
                        TeacherName = s.Teacher.FirstName + " " + s.Teacher.LastName,
                        DayOfWeek = s.Date.DayOfWeek,
                        TimeSlot = s.StartTime.ToString(@"hh\:mm") + " - " + s.EndTime.ToString(@"hh\:mm"),
                        StartTime = s.StartTime,
                        EndTime = s.EndTime,
                        Room = s.Room,
                        Date = s.Date
                    })
                    .ToListAsync();

                return Ok(schedules);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving schedules");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ScheduleDto>> GetSchedule(int id)
        {
            try
            {
                var schedule = await _context.Schedules
                    .Include(s => s.Class)
                    .Include(s => s.Subject)
                    .Include(s => s.Teacher)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (schedule == null)
                    return NotFound();

                var scheduleDto = new ScheduleDto
                {
                    Id = schedule.Id,
                    ClassId = schedule.ClassId,
                    ClassName = schedule.Class.Name,
                    SubjectId = schedule.SubjectId,
                    SubjectName = schedule.Subject.Name,
                    TeacherId = schedule.TeacherId,
                    TeacherName = schedule.Teacher.FirstName + " " + schedule.Teacher.LastName,
                    DayOfWeek = schedule.Date.DayOfWeek,
                    TimeSlot = schedule.StartTime.ToString(@"hh\:mm") + " - " + schedule.EndTime.ToString(@"hh\:mm"),
                    StartTime = schedule.StartTime,
                    EndTime = schedule.EndTime,
                    Room = schedule.Room,
                    Date = schedule.Date
                };

                return Ok(scheduleDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving schedule {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ScheduleDto>> CreateSchedule(CreateScheduleDto createScheduleDto)
        {
            try
            {
                // Check for conflicts
                var conflict = await _context.Schedules
                    .AnyAsync(s => s.ClassId == createScheduleDto.ClassId &&
                                  s.Date.Date == createScheduleDto.Date.Date &&
                                  s.StartTime < createScheduleDto.EndTime &&
                                  s.EndTime > createScheduleDto.StartTime);

                if (conflict)
                    return BadRequest("Schedule conflict detected");

                var schedule = new Schedule
                {
                    ClassId = createScheduleDto.ClassId,
                    SubjectId = createScheduleDto.SubjectId,
                    TeacherId = createScheduleDto.TeacherId,
                    Date = createScheduleDto.Date,
                    StartTime = createScheduleDto.StartTime,
                    EndTime = createScheduleDto.EndTime,
                    Room = createScheduleDto.Room,
                    CreatedAt = DateTime.UtcNow
                };

                _context.Schedules.Add(schedule);
                await _context.SaveChangesAsync();

                // Reload with includes
                schedule = await _context.Schedules
                    .Include(s => s.Class)
                    .Include(s => s.Subject)
                    .Include(s => s.Teacher)
                    .FirstAsync(s => s.Id == schedule.Id);

                var scheduleDto = new ScheduleDto
                {
                    Id = schedule.Id,
                    ClassId = schedule.ClassId,
                    ClassName = schedule.Class.Name,
                    SubjectId = schedule.SubjectId,
                    SubjectName = schedule.Subject.Name,
                    TeacherId = schedule.TeacherId,
                    TeacherName = schedule.Teacher.FirstName + " " + schedule.Teacher.LastName,
                    DayOfWeek = schedule.Date.DayOfWeek,
                    TimeSlot = schedule.StartTime.ToString(@"hh\:mm") + " - " + schedule.EndTime.ToString(@"hh\:mm"),
                    StartTime = schedule.StartTime,
                    EndTime = schedule.EndTime,
                    Room = schedule.Room,
                    Date = schedule.Date
                };

                return CreatedAtAction(nameof(GetSchedule), new { id = schedule.Id }, scheduleDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating schedule");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UpdateSchedule(int id, UpdateScheduleDto updateScheduleDto)
        {
            try
            {
                var schedule = await _context.Schedules.FindAsync(id);
                if (schedule == null)
                    return NotFound();

                // Check for conflicts (excluding current schedule)
                var conflict = await _context.Schedules
                    .AnyAsync(s => s.Id != id &&
                                  s.ClassId == updateScheduleDto.ClassId &&
                                  updateScheduleDto.Date.HasValue && s.Date.Date == updateScheduleDto.Date.Value.Date &&
                                  s.StartTime < updateScheduleDto.EndTime &&
                                  s.EndTime > updateScheduleDto.StartTime);

                if (conflict)
                    return BadRequest("Schedule conflict detected");

                if (updateScheduleDto.ClassId.HasValue)
                    schedule.ClassId = updateScheduleDto.ClassId.Value;
                if (updateScheduleDto.SubjectId.HasValue)
                    schedule.SubjectId = updateScheduleDto.SubjectId.Value;
                if (!string.IsNullOrEmpty(updateScheduleDto.TeacherId))
                    schedule.TeacherId = updateScheduleDto.TeacherId;
                if (updateScheduleDto.Date.HasValue)
                    schedule.Date = updateScheduleDto.Date.Value;
                if (updateScheduleDto.StartTime.HasValue)
                    schedule.StartTime = updateScheduleDto.StartTime.Value;
                if (updateScheduleDto.EndTime.HasValue)
                    schedule.EndTime = updateScheduleDto.EndTime.Value;
                schedule.Room = updateScheduleDto.Room;
                schedule.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating schedule {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteSchedule(int id)
        {
            try
            {
                var schedule = await _context.Schedules.FindAsync(id);
                if (schedule == null)
                    return NotFound();

                _context.Schedules.Remove(schedule);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting schedule {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/schedule/weekly - Enhanced weekly schedule
        [HttpGet("weekly")]
        public async Task<ActionResult<WeeklyScheduleDto>> GetWeeklySchedule(
            [FromQuery] int? classId = null,
            [FromQuery] string? teacherId = null,
            [FromQuery] DateTime? weekStart = null)
        {
            try
            {
                var currentUser = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                var userRole = User.FindFirst(System.Security.Claims.ClaimTypes.Role)?.Value;

                // Default to current week if not specified
                var startOfWeek = weekStart?.Date ?? GetStartOfWeek(DateTime.Today);
                var endOfWeek = startOfWeek.AddDays(6);

                var query = _context.Schedules
                    .Include(s => s.Subject)
                    .Include(s => s.Class)
                    .Include(s => s.Section)
                    .Include(s => s.Teacher)
                    .Include(s => s.SubstituteTeacher)
                    .Where(s => s.Date >= startOfWeek && s.Date <= endOfWeek && s.IsActive);

                // Apply filters based on user role and parameters
                if (userRole == "Teacher" && string.IsNullOrEmpty(teacherId))
                {
                    query = query.Where(s => s.TeacherId == currentUser || s.SubstituteTeacherId == currentUser);
                }
                else if (!string.IsNullOrEmpty(teacherId) && (userRole == "Admin" || userRole == "Teacher"))
                {
                    query = query.Where(s => s.TeacherId == teacherId || s.SubstituteTeacherId == teacherId);
                }

                if (classId.HasValue)
                {
                    query = query.Where(s => s.ClassId == classId.Value);
                }

                var schedules = await query.OrderBy(s => s.Date).ThenBy(s => s.StartTime).ToListAsync();

                var weeklySchedule = new WeeklyScheduleDto
                {
                    WeekStart = startOfWeek,
                    WeekEnd = endOfWeek,
                    ClassId = classId,
                    TeacherId = teacherId,
                    Days = new List<DayScheduleDto>()
                };

                // Group schedules by day
                for (int i = 0; i < 7; i++)
                {
                    var currentDate = startOfWeek.AddDays(i);
                    var daySchedules = schedules.Where(s => s.Date.Date == currentDate.Date).ToList();

                    var daySchedule = new DayScheduleDto
                    {
                        Date = currentDate,
                        DayOfWeek = currentDate.DayOfWeek,
                        DayName = GetArabicDayName(currentDate.DayOfWeek),
                        IsToday = currentDate.Date == DateTime.Today,
                        IsWeekend = currentDate.DayOfWeek == DayOfWeek.Friday || currentDate.DayOfWeek == DayOfWeek.Saturday,
                        Periods = daySchedules.Select(s => new SchedulePeriodDto
                        {
                            Id = s.Id,
                            SubjectId = s.SubjectId,
                            SubjectName = s.Subject.Name,
                            SubjectNameAr = s.Subject.NameAr,
                            ClassId = s.ClassId,
                            ClassName = s.Class.Name,
                            SectionId = s.SectionId,
                            SectionName = s.Section?.Name,
                            TeacherId = s.TeacherId,
                            TeacherName = s.Teacher.FirstName + " " + s.Teacher.LastName,
                            SubstituteTeacherId = s.SubstituteTeacherId,
                            SubstituteTeacherName = s.SubstituteTeacher != null ?
                                s.SubstituteTeacher.FirstName + " " + s.SubstituteTeacher.LastName : null,
                            StartTime = s.StartTime,
                            EndTime = s.EndTime,
                            Duration = s.Duration,
                            TimeSlot = s.TimeSlot,
                            Room = s.Room,
                            Type = s.Type,
                            Status = s.Status,
                            Notes = s.Notes,
                            IsSubstitute = !string.IsNullOrEmpty(s.SubstituteTeacherId),
                            IsCancelled = s.Status == ScheduleStatus.Cancelled,
                            IsCompleted = s.Status == ScheduleStatus.Completed
                        }).OrderBy(p => p.StartTime).ToList()
                    };

                    weeklySchedule.Days.Add(daySchedule);
                }

                return Ok(weeklySchedule);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting weekly schedule");
                return StatusCode(500, "حدث خطأ أثناء جلب الجدول الأسبوعي");
            }
        }

        [HttpGet("conflicts")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<IEnumerable<ScheduleConflictDto>>> GetScheduleConflicts()
        {
            try
            {
                var conflicts = await _context.Schedules
                    .Include(s => s.Class)
                    .Include(s => s.Subject)
                    .Include(s => s.Teacher)
                    .GroupBy(s => new { s.TeacherId, s.Date, s.StartTime, s.EndTime })
                    .Where(g => g.Count() > 1)
                    .Select(g => new ScheduleConflictDto
                    {
                        TeacherId = g.Key.TeacherId,
                        TeacherName = g.First().Teacher.FirstName + " " + g.First().Teacher.LastName,
                        Date = g.Key.Date,
                        StartTime = g.Key.StartTime,
                        EndTime = g.Key.EndTime,
                        ConflictingSchedules = g.Select(s => new ScheduleDto
                        {
                            Id = s.Id,
                            ClassId = s.ClassId,
                            ClassName = s.Class.Name,
                            SubjectName = s.Subject.Name,
                            Room = s.Room
                        }).ToList()
                    })
                    .ToListAsync();

                return Ok(conflicts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving schedule conflicts");
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/schedule/daily
        [HttpGet("daily")]
        public async Task<ActionResult<DayScheduleDto>> GetDailySchedule(
            [FromQuery] DateTime? date = null,
            [FromQuery] int? classId = null,
            [FromQuery] string? teacherId = null)
        {
            try
            {
                var currentUser = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                var userRole = User.FindFirst(System.Security.Claims.ClaimTypes.Role)?.Value;
                var targetDate = date?.Date ?? DateTime.Today;

                var query = _context.Schedules
                    .Include(s => s.Subject)
                    .Include(s => s.Class)
                    .Include(s => s.Section)
                    .Include(s => s.Teacher)
                    .Include(s => s.SubstituteTeacher)
                    .Where(s => s.Date.Date == targetDate && s.IsActive);

                // Apply filters based on user role
                if (userRole == "Teacher" && string.IsNullOrEmpty(teacherId))
                {
                    query = query.Where(s => s.TeacherId == currentUser || s.SubstituteTeacherId == currentUser);
                }
                else if (!string.IsNullOrEmpty(teacherId))
                {
                    query = query.Where(s => s.TeacherId == teacherId || s.SubstituteTeacherId == teacherId);
                }

                if (classId.HasValue)
                {
                    query = query.Where(s => s.ClassId == classId.Value);
                }

                var schedules = await query.OrderBy(s => s.StartTime).ToListAsync();

                var daySchedule = new DayScheduleDto
                {
                    Date = targetDate,
                    DayOfWeek = targetDate.DayOfWeek,
                    DayName = GetArabicDayName(targetDate.DayOfWeek),
                    IsToday = targetDate.Date == DateTime.Today,
                    IsWeekend = targetDate.DayOfWeek == DayOfWeek.Friday || targetDate.DayOfWeek == DayOfWeek.Saturday,
                    Periods = schedules.Select(s => new SchedulePeriodDto
                    {
                        Id = s.Id,
                        SubjectId = s.SubjectId,
                        SubjectName = s.Subject.Name,
                        SubjectNameAr = s.Subject.NameAr,
                        ClassId = s.ClassId,
                        ClassName = s.Class.Name,
                        SectionId = s.SectionId,
                        SectionName = s.Section?.Name,
                        TeacherId = s.TeacherId,
                        TeacherName = s.Teacher.FirstName + " " + s.Teacher.LastName,
                        SubstituteTeacherId = s.SubstituteTeacherId,
                        SubstituteTeacherName = s.SubstituteTeacher != null ?
                            s.SubstituteTeacher.FirstName + " " + s.SubstituteTeacher.LastName : null,
                        StartTime = s.StartTime,
                        EndTime = s.EndTime,
                        Duration = s.Duration,
                        TimeSlot = s.TimeSlot,
                        Room = s.Room,
                        Type = s.Type,
                        Status = s.Status,
                        Notes = s.Notes,
                        IsSubstitute = !string.IsNullOrEmpty(s.SubstituteTeacherId),
                        IsCancelled = s.Status == ScheduleStatus.Cancelled,
                        IsCompleted = s.Status == ScheduleStatus.Completed
                    }).ToList()
                };

                return Ok(daySchedule);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting daily schedule");
                return StatusCode(500, "حدث خطأ أثناء جلب الجدول اليومي");
            }
        }

        // GET: api/schedule/teacher/{teacherId}
        [HttpGet("teacher/{teacherId}")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<ActionResult<TeacherScheduleDto>> GetTeacherSchedule(
            string teacherId,
            [FromQuery] DateTime? weekStart = null)
        {
            try
            {
                var currentUser = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                var userRole = User.FindFirst(System.Security.Claims.ClaimTypes.Role)?.Value;

                // Teachers can only view their own schedule unless they're admin
                if (userRole == "Teacher" && currentUser != teacherId)
                {
                    return Forbid("لا يمكنك عرض جدول معلم آخر");
                }

                var teacher = await _context.Users.FindAsync(teacherId);
                if (teacher == null)
                {
                    return NotFound("المعلم غير موجود");
                }

                var startOfWeek = weekStart?.Date ?? GetStartOfWeek(DateTime.Today);
                var endOfWeek = startOfWeek.AddDays(6);

                var schedules = await _context.Schedules
                    .Include(s => s.Subject)
                    .Include(s => s.Class)
                    .Include(s => s.Section)
                    .Include(s => s.SubstituteTeacher)
                    .Where(s => (s.TeacherId == teacherId || s.SubstituteTeacherId == teacherId) &&
                               s.Date >= startOfWeek && s.Date <= endOfWeek && s.IsActive)
                    .OrderBy(s => s.Date).ThenBy(s => s.StartTime)
                    .ToListAsync();

                var teacherSchedule = new TeacherScheduleDto
                {
                    TeacherId = teacherId,
                    TeacherName = teacher.FirstName + " " + teacher.LastName,
                    WeekStart = startOfWeek,
                    WeekEnd = endOfWeek,
                    TotalHours = schedules.Sum(s => s.Duration.TotalHours),
                    TotalClasses = schedules.Count,
                    WeeklySchedule = new WeeklyScheduleDto
                    {
                        WeekStart = startOfWeek,
                        WeekEnd = endOfWeek,
                        TeacherId = teacherId,
                        Days = new List<DayScheduleDto>()
                    }
                };

                // Group by days
                for (int i = 0; i < 7; i++)
                {
                    var currentDate = startOfWeek.AddDays(i);
                    var daySchedules = schedules.Where(s => s.Date.Date == currentDate.Date).ToList();

                    var daySchedule = new DayScheduleDto
                    {
                        Date = currentDate,
                        DayOfWeek = currentDate.DayOfWeek,
                        DayName = GetArabicDayName(currentDate.DayOfWeek),
                        IsToday = currentDate.Date == DateTime.Today,
                        IsWeekend = currentDate.DayOfWeek == DayOfWeek.Friday || currentDate.DayOfWeek == DayOfWeek.Saturday,
                        Periods = daySchedules.Select(s => new SchedulePeriodDto
                        {
                            Id = s.Id,
                            SubjectId = s.SubjectId,
                            SubjectName = s.Subject.Name,
                            SubjectNameAr = s.Subject.NameAr,
                            ClassId = s.ClassId,
                            ClassName = s.Class.Name,
                            SectionId = s.SectionId,
                            SectionName = s.Section?.Name,
                            TeacherId = s.TeacherId,
                            TeacherName = teacher.FirstName + " " + teacher.LastName,
                            SubstituteTeacherId = s.SubstituteTeacherId,
                            SubstituteTeacherName = s.SubstituteTeacher != null ?
                                s.SubstituteTeacher.FirstName + " " + s.SubstituteTeacher.LastName : null,
                            StartTime = s.StartTime,
                            EndTime = s.EndTime,
                            Duration = s.Duration,
                            TimeSlot = s.TimeSlot,
                            Room = s.Room,
                            Type = s.Type,
                            Status = s.Status,
                            Notes = s.Notes,
                            IsSubstitute = s.SubstituteTeacherId == teacherId,
                            IsCancelled = s.Status == ScheduleStatus.Cancelled,
                            IsCompleted = s.Status == ScheduleStatus.Completed
                        }).OrderBy(p => p.StartTime).ToList()
                    };

                    teacherSchedule.WeeklySchedule.Days.Add(daySchedule);
                }

                return Ok(teacherSchedule);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting teacher schedule for {TeacherId}", teacherId);
                return StatusCode(500, "حدث خطأ أثناء جلب جدول المعلم");
            }
        }

        private static DateTime GetStartOfWeek(DateTime date)
        {
            // In Arabic culture, week starts on Saturday
            var diff = (7 + (date.DayOfWeek - DayOfWeek.Saturday)) % 7;
            return date.AddDays(-1 * diff).Date;
        }

        private static string GetArabicDayName(DayOfWeek dayOfWeek)
        {
            return dayOfWeek switch
            {
                DayOfWeek.Saturday => "السبت",
                DayOfWeek.Sunday => "الأحد",
                DayOfWeek.Monday => "الاثنين",
                DayOfWeek.Tuesday => "الثلاثاء",
                DayOfWeek.Wednesday => "الأربعاء",
                DayOfWeek.Thursday => "الخميس",
                DayOfWeek.Friday => "الجمعة",
                _ => dayOfWeek.ToString()
            };
        }
    }
}
