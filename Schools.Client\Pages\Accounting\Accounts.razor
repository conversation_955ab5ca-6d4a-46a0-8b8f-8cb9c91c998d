@page "/accounting/accounts"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Accountant,Admin")]

<PageTitle>دليل الحسابات - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-list-alt text-primary me-2"></i>
                        دليل الحسابات
                    </h2>
                    <p class="text-muted mb-0">إدارة وتنظيم شجرة الحسابات المحاسبية</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary" @onclick="CreateNewAccount">
                        <i class="fas fa-plus me-2"></i>
                        حساب جديد
                    </button>
                    <button class="btn btn-outline-success" @onclick="RefreshAccounts">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث
                    </button>
                    <button class="btn btn-outline-info" @onclick="ExportAccounts">
                        <i class="fas fa-download me-2"></i>
                        تصدير
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل دليل الحسابات...</p>
        </div>
    }
    else
    {
        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">نوع الحساب</label>
                                <select @bind="selectedAccountType" @bind:after="FilterAccounts" class="form-select">
                                    <option value="">جميع الأنواع</option>
                                    <option value="@AccountType.Asset">الأصول</option>
                                    <option value="@AccountType.Liability">الخصوم</option>
                                    <option value="@AccountType.Equity">حقوق الملكية</option>
                                    <option value="@AccountType.Revenue">الإيرادات</option>
                                    <option value="@AccountType.Expense">المصروفات</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الحالة</label>
                                <select @bind="selectedStatus" @bind:after="FilterAccounts" class="form-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="true">نشط</option>
                                    <option value="false">غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">المستوى</label>
                                <select @bind="selectedLevel" @bind:after="FilterAccounts" class="form-select">
                                    <option value="">جميع المستويات</option>
                                    <option value="1">المستوى الأول</option>
                                    <option value="2">المستوى الثاني</option>
                                    <option value="3">المستوى الثالث</option>
                                    <option value="4">المستوى الرابع</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" placeholder="رمز أو اسم الحساب..."
                                           @bind="searchTerm" @bind:after="FilterAccounts" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row g-3 mb-4">
            <div class="col-md-2">
                <div class="card border-0 shadow-sm bg-primary text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@filteredAccounts.Count</h4>
                        <p class="mb-0">إجمالي الحسابات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card border-0 shadow-sm bg-info text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@GetAccountsByType(AccountType.Asset).Count</h4>
                        <p class="mb-0">الأصول</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card border-0 shadow-sm bg-danger text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@GetAccountsByType(AccountType.Liability).Count</h4>
                        <p class="mb-0">الخصوم</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card border-0 shadow-sm bg-success text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@GetAccountsByType(AccountType.Revenue).Count</h4>
                        <p class="mb-0">الإيرادات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card border-0 shadow-sm bg-warning text-dark">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@GetAccountsByType(AccountType.Expense).Count</h4>
                        <p class="mb-0">المصروفات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card border-0 shadow-sm bg-secondary text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@GetAccountsByType(AccountType.Equity).Count</h4>
                        <p class="mb-0">حقوق الملكية</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Accounts Tree -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-sitemap me-2 text-primary"></i>
                                شجرة الحسابات
                            </h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn @(viewMode == "tree" ? "btn-primary" : "btn-outline-primary")"
                                        @onclick="() => SetViewMode(\"tree\")">
                                    <i class="fas fa-sitemap me-1"></i>
                                    شجرة
                                </button>
                                <button class="btn @(viewMode == "table" ? "btn-primary" : "btn-outline-primary")"
                                        @onclick="() => SetViewMode(\"table\")">
                                    <i class="fas fa-table me-1"></i>
                                    جدول
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (filteredAccounts?.Any() == true)
                        {
                            @if (viewMode == "tree")
                            {
                                <!-- Tree View -->
                                <div class="accounts-tree">
                                    @foreach (var account in GetRootAccounts())
                                    {
                                        <div class="account-node">
                                            @RenderAccountNode(account)
                                        </div>
                                    }
                                </div>
                            }
                            else
                            {
                                <!-- Table View -->
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>رمز الحساب</th>
                                                <th>اسم الحساب</th>
                                                <th>نوع الحساب</th>
                                                <th>الحساب الأب</th>
                                                <th>المستوى</th>
                                                <th>الرصيد</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var account in filteredAccounts.Skip((currentPage - 1) * pageSize).Take(pageSize))
                                            {
                                                <tr>
                                                    <td>
                                                        <strong class="text-primary">@account.AccountCode</strong>
                                                    </td>
                                                    <td>
                                                        <div style="padding-left: @((account.Level - 1) * 20)px;">
                                                            @if (account.Level > 1)
                                                            {
                                                                <i class="fas fa-level-up-alt text-muted me-1"></i>
                                                            }
                                                            <strong>@account.AccountName</strong>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge @GetAccountTypeBadge(account.AccountType)">
                                                            @GetAccountTypeText(account.AccountType)
                                                        </span>
                                                    </td>
                                                    <td>
                                                        @if (account.ParentAccountId.HasValue)
                                                        {
                                                            var parent = allAccounts.FirstOrDefault(a => a.Id == account.ParentAccountId);
                                                            if (parent != null)
                                                            {
                                                                <small class="text-muted">@parent.AccountName</small>
                                                            }
                                                        }
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info">المستوى @account.Level</span>
                                                    </td>
                                                    <td>
                                                        <strong class="@GetBalanceClass(account.Balance)">
                                                            @account.Balance.ToString("C")
                                                        </strong>
                                                    </td>
                                                    <td>
                                                        <span class="badge @(account.IsActive ? "bg-success" : "bg-secondary")">
                                                            @(account.IsActive ? "نشط" : "غير نشط")
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewAccount(account.Id)" title="عرض">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-secondary" @onclick="() => EditAccount(account.Id)" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-info" @onclick="() => ViewStatement(account.Id)" title="كشف حساب">
                                                                <i class="fas fa-file-alt"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-success" @onclick="() => AddSubAccount(account.Id)" title="حساب فرعي">
                                                                <i class="fas fa-plus"></i>
                                                            </button>
                                                            @if (!HasSubAccounts(account.Id))
                                                            {
                                                                <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteAccount(account.Id)" title="حذف">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            }
                                                        </div>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                @if (totalPages > 1)
                                {
                                    <nav class="mt-4">
                                        <ul class="pagination justify-content-center">
                                            <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                                <button class="page-link" @onclick="() => ChangePage(currentPage - 1)">السابق</button>
                                            </li>
                                            @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                                            {
                                                <li class="page-item @(i == currentPage ? "active" : "")">
                                                    <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                                                </li>
                                            }
                                            <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                                                <button class="page-link" @onclick="() => ChangePage(currentPage + 1)">التالي</button>
                                            </li>
                                        </ul>
                                    </nav>
                                }
                            }
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-list-alt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد حسابات</h5>
                                <p class="text-muted">ابدأ بإنشاء دليل الحسابات</p>
                                <button class="btn btn-primary" @onclick="CreateNewAccount">
                                    <i class="fas fa-plus me-2"></i>
                                    إنشاء حساب جديد
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .accounts-tree {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .account-node {
        margin-bottom: 8px;
    }

    .account-item {
        padding: 8px 12px;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }

    .account-item:hover {
        background-color: #e9ecef;
        transform: translateX(5px);
    }

    .account-children {
        margin-left: 30px;
        margin-top: 8px;
        border-left: 2px solid #dee2e6;
        padding-left: 15px;
    }

    .account-toggle {
        cursor: pointer;
        color: #6c757d;
        margin-right: 8px;
    }

    .account-toggle:hover {
        color: #495057;
    }
</style>

@code {
    private List<AccountDto> allAccounts = new();
    private List<AccountDto> filteredAccounts = new();

    private bool isLoading = true;
    private string selectedAccountType = "";
    private string selectedStatus = "";
    private string selectedLevel = "";
    private string searchTerm = "";
    private string viewMode = "table";

    private int currentPage = 1;
    private int pageSize = 15;
    private int totalPages = 1;

    private HashSet<int> expandedNodes = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadAccounts();
    }

    private async Task LoadAccounts()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            allAccounts = await ApiService.GetAccountsAsync() ?? new List<AccountDto>();
            FilterAccounts();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void FilterAccounts()
    {
        filteredAccounts = allAccounts.Where(a =>
            (string.IsNullOrEmpty(selectedAccountType) || a.AccountType.ToString() == selectedAccountType) &&
            (string.IsNullOrEmpty(selectedStatus) || a.IsActive.ToString().ToLower() == selectedStatus.ToLower()) &&
            (string.IsNullOrEmpty(selectedLevel) || a.Level.ToString() == selectedLevel) &&
            (string.IsNullOrEmpty(searchTerm) ||
             a.AccountCode.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
             a.AccountName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
        ).OrderBy(a => a.AccountCode).ToList();

        UpdatePagination();
        StateHasChanged();
    }

    private void UpdatePagination()
    {
        totalPages = (int)Math.Ceiling((double)filteredAccounts.Count / pageSize);
        if (currentPage > totalPages && totalPages > 0)
        {
            currentPage = totalPages;
        }
        else if (currentPage < 1)
        {
            currentPage = 1;
        }
    }

    private void ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            StateHasChanged();
        }
    }

    private void SetViewMode(string mode)
    {
        viewMode = mode;
        StateHasChanged();
    }

    private List<AccountDto> GetRootAccounts()
    {
        return filteredAccounts.Where(a => !a.ParentAccountId.HasValue).OrderBy(a => a.AccountCode).ToList();
    }

    private List<AccountDto> GetChildAccounts(int parentId)
    {
        return filteredAccounts.Where(a => a.ParentAccountId == parentId).OrderBy(a => a.AccountCode).ToList();
    }

    private bool HasChildren(int accountId)
    {
        return filteredAccounts.Any(a => a.ParentAccountId == accountId);
    }

    private bool HasSubAccounts(int accountId)
    {
        return allAccounts.Any(a => a.ParentAccountId == accountId);
    }

    private void ToggleNode(int accountId)
    {
        if (expandedNodes.Contains(accountId))
        {
            expandedNodes.Remove(accountId);
        }
        else
        {
            expandedNodes.Add(accountId);
        }
        StateHasChanged();
    }

    private RenderFragment RenderAccountNode(AccountDto account)
    {
        return @<div>
            <div class="account-item d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    @if (HasChildren(account.Id))
                    {
                        <i class="account-toggle fas @(expandedNodes.Contains(account.Id) ? "fa-minus-square" : "fa-plus-square")"
                           @onclick="() => ToggleNode(account.Id)"></i>
                    }
                    else
                    {
                        <i class="fas fa-circle" style="font-size: 8px; margin-right: 8px; color: #dee2e6;"></i>
                    }
                    <div>
                        <strong class="text-primary">@account.AccountCode</strong>
                        <span class="ms-2">@account.AccountName</span>
                        <span class="badge @GetAccountTypeBadge(account.AccountType) ms-2">
                            @GetAccountTypeText(account.AccountType)
                        </span>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <span class="me-3 @GetBalanceClass(account.Balance)">
                        <strong>@account.Balance.ToString("C")</strong>
                    </span>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" @onclick="() => ViewAccount(account.Id)" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary" @onclick="() => EditAccount(account.Id)" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" @onclick="() => ViewStatement(account.Id)" title="كشف حساب">
                            <i class="fas fa-file-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
            @if (expandedNodes.Contains(account.Id) && HasChildren(account.Id))
            {
                <div class="account-children">
                    @foreach (var child in GetChildAccounts(account.Id))
                    {
                        @RenderAccountNode(child)
                    }
                </div>
            }
        </div>;
    }

    private async Task RefreshAccounts()
    {
        await LoadAccounts();
    }

    private void CreateNewAccount()
    {
        Navigation.NavigateTo("/accounting/accounts/new");
    }

    private void ViewAccount(int id)
    {
        Navigation.NavigateTo($"/accounting/accounts/{id}");
    }

    private void EditAccount(int id)
    {
        Navigation.NavigateTo($"/accounting/accounts/{id}/edit");
    }

    private void ViewStatement(int id)
    {
        Navigation.NavigateTo($"/accounting/accounts/{id}/statement");
    }

    private void AddSubAccount(int parentId)
    {
        Navigation.NavigateTo($"/accounting/accounts/new?parentId={parentId}");
    }

    private async Task DeleteAccount(int id)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا الحساب؟"))
        {
            try
            {
                var success = await ApiService.DeleteAccountAsync(id);
                if (success)
                {
                    await LoadAccounts();
                    await JSRuntime.InvokeVoidAsync("alert", "تم حذف الحساب بنجاح");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف الحساب: {ex.Message}");
            }
        }
    }

    private async Task ExportAccounts()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة تصدير دليل الحسابات ستكون متاحة قريباً");
    }

    private List<AccountDto> GetAccountsByType(AccountType type)
    {
        return filteredAccounts.Where(a => a.AccountType == type).ToList();
    }

    private string GetAccountTypeBadge(AccountType accountType)
    {
        return accountType switch
        {
            AccountType.Asset => "bg-primary",
            AccountType.Liability => "bg-danger",
            AccountType.Equity => "bg-success",
            AccountType.Revenue => "bg-info",
            AccountType.Expense => "bg-warning",
            _ => "bg-secondary"
        };
    }

    private string GetAccountTypeText(AccountType accountType)
    {
        return accountType switch
        {
            AccountType.Asset => "أصول",
            AccountType.Liability => "خصوم",
            AccountType.Equity => "حقوق ملكية",
            AccountType.Revenue => "إيرادات",
            AccountType.Expense => "مصروفات",
            _ => "غير محدد"
        };
    }

    private string GetBalanceClass(decimal balance)
    {
        if (balance > 0)
            return "text-success";
        else if (balance < 0)
            return "text-danger";
        else
            return "text-muted";
    }
}
