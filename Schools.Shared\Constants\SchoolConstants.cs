namespace Schools.Shared.Constants
{
    /// <summary>
    /// Schools.Shared Library Constants
    /// Contains all shared constants for the School Management System
    /// </summary>
    public static class SchoolConstants
    {
        public const string Version = "1.0.0";
        public const string Description = "Shared library for School Management System";
        
        public static readonly string[] SupportedLanguages = { "ar", "en" };
        public static readonly string[] SupportedCurrencies = { "SAR", "USD", "EUR", "AED" };
        
        public static class Pagination
        {
            public const int DefaultPageSize = 20;
            public const int MaxPageSize = 100;
            public const int MinPageSize = 5;
        }
        
        public static class Security
        {
            public const int DefaultPasswordLength = 8;
            public const int MaxPasswordLength = 128;
            public const int MinPasswordLength = 6;
            public const int TokenExpirationHours = 24;
            public const int RefreshTokenExpirationDays = 30;
        }
        
        public static class Files
        {
            public const int MaxFileSize = 10 * 1024 * 1024; // 10MB
            public const int MaxImageSize = 5 * 1024 * 1024; // 5MB
            public const int MaxDocumentSize = 20 * 1024 * 1024; // 20MB
            
            public static readonly string[] AllowedImageExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
            public static readonly string[] AllowedDocumentExtensions = { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx" };
        }
        
        public static class Roles
        {
            public const string Admin = "Admin";
            public const string Teacher = "Teacher";
            public const string Student = "Student";
            public const string Parent = "Parent";
            public const string Accountant = "Accountant";
            public const string Employee = "Employee";
            public const string Librarian = "Librarian";
            
            public static readonly string[] AllRoles = { Admin, Teacher, Student, Parent, Accountant, Employee, Librarian };
            public static readonly string[] StaffRoles = { Admin, Teacher, Accountant, Employee, Librarian };
            public static readonly string[] AcademicRoles = { Admin, Teacher };
            public static readonly string[] FinancialRoles = { Admin, Accountant };
        }
        
        public static class PaymentMethods
        {
            public const string Cash = "Cash";
            public const string Bank = "Bank";
            public const string Check = "Check";
            public const string Card = "Card";
            public const string Online = "Online";
            
            public static readonly string[] AllMethods = { Cash, Bank, Check, Card, Online };
        }
        
        public static class PaymentStatus
        {
            public const string Paid = "paid";
            public const string Partial = "partial";
            public const string Unpaid = "unpaid";
            public const string Overdue = "overdue";
            public const string Cancelled = "cancelled";
            
            public static readonly string[] AllStatuses = { Paid, Partial, Unpaid, Overdue, Cancelled };
        }
        
        public static class ExamTypes
        {
            public const string Midterm = "Midterm";
            public const string Final = "Final";
            public const string Quiz = "Quiz";
            public const string Assignment = "Assignment";
            public const string Project = "Project";
            public const string Practical = "Practical";
            public const string Oral = "Oral";
            
            public static readonly string[] AllTypes = { Midterm, Final, Quiz, Assignment, Project, Practical, Oral };
        }
        
        public static class QuestionTypes
        {
            public const string MultipleChoice = "MultipleChoice";
            public const string TrueFalse = "TrueFalse";
            public const string ShortAnswer = "ShortAnswer";
            public const string Essay = "Essay";
            public const string FillInTheBlank = "FillInTheBlank";
            public const string Matching = "Matching";
            
            public static readonly string[] AllTypes = { MultipleChoice, TrueFalse, ShortAnswer, Essay, FillInTheBlank, Matching };
        }
        
        public static class GradeLetters
        {
            public const string A_Plus = "A+";
            public const string A = "A";
            public const string B_Plus = "B+";
            public const string B = "B";
            public const string C_Plus = "C+";
            public const string C = "C";
            public const string D_Plus = "D+";
            public const string D = "D";
            public const string F = "F";
            
            public static readonly string[] AllGrades = { A_Plus, A, B_Plus, B, C_Plus, C, D_Plus, D, F };
            public static readonly string[] PassingGrades = { A_Plus, A, B_Plus, B, C_Plus, C, D_Plus, D };
        }
        
        public static class GradePoints
        {
            public const double A_Plus_Points = 4.0;
            public const double A_Points = 3.7;
            public const double B_Plus_Points = 3.3;
            public const double B_Points = 3.0;
            public const double C_Plus_Points = 2.7;
            public const double C_Points = 2.3;
            public const double D_Plus_Points = 2.0;
            public const double D_Points = 1.7;
            public const double F_Points = 0.0;
        }
        
        public static class AttendanceStatus
        {
            public const string Present = "Present";
            public const string Absent = "Absent";
            public const string Late = "Late";
            public const string Excused = "Excused";
            public const string Sick = "Sick";
            
            public static readonly string[] AllStatuses = { Present, Absent, Late, Excused, Sick };
        }
        
        public static class AccountTypes
        {
            public const string Asset = "Asset";
            public const string Liability = "Liability";
            public const string Equity = "Equity";
            public const string Revenue = "Revenue";
            public const string Expense = "Expense";
            
            public static readonly string[] AllTypes = { Asset, Liability, Equity, Revenue, Expense };
        }
        
        public static class VoucherTypes
        {
            public const string Receipt = "Receipt";
            public const string Payment = "Payment";
            public const string Journal = "Journal";
            
            public static readonly string[] AllTypes = { Receipt, Payment, Journal };
        }
        
        public static class VoucherStatus
        {
            public const string Draft = "Draft";
            public const string Approved = "Approved";
            public const string Posted = "Posted";
            public const string Cancelled = "Cancelled";
            
            public static readonly string[] AllStatuses = { Draft, Approved, Posted, Cancelled };
        }
        
        public static class ReportFormats
        {
            public const string PDF = "PDF";
            public const string Excel = "Excel";
            public const string CSV = "CSV";
            public const string Word = "Word";
            
            public static readonly string[] AllFormats = { PDF, Excel, CSV, Word };
        }
        
        public static class NotificationTypes
        {
            public const string Academic = "Academic";
            public const string Financial = "Financial";
            public const string Administrative = "Administrative";
            public const string Emergency = "Emergency";
            public const string Reminder = "Reminder";
            public const string Announcement = "Announcement";
            
            public static readonly string[] AllTypes = { Academic, Financial, Administrative, Emergency, Reminder, Announcement };
        }
        
        public static class EventTypes
        {
            public const string Academic = "Academic";
            public const string Sports = "Sports";
            public const string Cultural = "Cultural";
            public const string Social = "Social";
            public const string Religious = "Religious";
            public const string Scientific = "Scientific";
            public const string Arts = "Arts";
            public const string Competition = "Competition";
            public const string Workshop = "Workshop";
            public const string Seminar = "Seminar";
            public const string Conference = "Conference";
            public const string Exhibition = "Exhibition";
            public const string Trip = "Trip";
            public const string Ceremony = "Ceremony";
            public const string Meeting = "Meeting";
            
            public static readonly string[] AllTypes = { Academic, Sports, Cultural, Social, Religious, Scientific, Arts, Competition, Workshop, Seminar, Conference, Exhibition, Trip, Ceremony, Meeting };
        }
        
        public static class Semesters
        {
            public const string First = "First";
            public const string Second = "Second";
            public const string Summer = "Summer";
            
            public static readonly string[] AllSemesters = { First, Second, Summer };
        }
        
        public static class BloodTypes
        {
            public const string A_Positive = "A+";
            public const string A_Negative = "A-";
            public const string B_Positive = "B+";
            public const string B_Negative = "B-";
            public const string AB_Positive = "AB+";
            public const string AB_Negative = "AB-";
            public const string O_Positive = "O+";
            public const string O_Negative = "O-";
            
            public static readonly string[] AllTypes = { A_Positive, A_Negative, B_Positive, B_Negative, AB_Positive, AB_Negative, O_Positive, O_Negative };
        }
        
        public static class Relationships
        {
            public const string Father = "Father";
            public const string Mother = "Mother";
            public const string Guardian = "Guardian";
            public const string Grandfather = "Grandfather";
            public const string Grandmother = "Grandmother";
            public const string Uncle = "Uncle";
            public const string Aunt = "Aunt";
            public const string Brother = "Brother";
            public const string Sister = "Sister";
            public const string Other = "Other";
            
            public static readonly string[] AllRelationships = { Father, Mother, Guardian, Grandfather, Grandmother, Uncle, Aunt, Brother, Sister, Other };
        }
        
        public static class DefaultValues
        {
            public const int DefaultClassCapacity = 30;
            public const int DefaultSectionCapacity = 30;
            public const int DefaultExamDuration = 60; // minutes
            public const int DefaultMaxAttempts = 1;
            public const int DefaultCreditHours = 1;
            public const decimal DefaultLateFeePercentage = 5.0m;
            public const int DefaultPaymentGracePeriod = 30; // days
            public const int DefaultReminderFrequency = 7; // days
        }
    }
}
