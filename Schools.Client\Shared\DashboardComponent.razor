@using Microsoft.AspNetCore.Components.Authorization
@inject AuthenticationStateProvider AuthStateProvider

<div class="dashboard-container">
    <AuthorizeView Roles="Admin">
        <AdminDashboard />
    </AuthorizeView>

    <AuthorizeView Roles="Teacher">
        <SimpleTeacherDashboard />
    </AuthorizeView>

    <AuthorizeView Roles="Student">
        <SimpleStudentDashboard />
    </AuthorizeView>

    <AuthorizeView Roles="Parent">
        <SimpleParentDashboard />
    </AuthorizeView>

    <AuthorizeView Roles="Employee">
        <SimpleEmployeeDashboard />
    </AuthorizeView>

    <AuthorizeView Roles="Accountant">
        <SimpleAccountantDashboard />
    </AuthorizeView>
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (!authState.User.Identity?.IsAuthenticated == true)
        {
            // Handle unauthorized access
        }
    }
}
