@page "/accounting/student-reports"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Accountant,Admin")]

<PageTitle>التقارير المالية للطلاب - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-chart-bar text-primary me-2"></i>
                        التقارير المالية للطلاب
                    </h2>
                    <p class="text-muted mb-0">تقارير شاملة ومفصلة للوضع المالي للطلاب</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary" @onclick="RefreshReports">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث
                    </button>
                    <button class="btn btn-outline-success" @onclick="ExportAllReports">
                        <i class="fas fa-download me-2"></i>
                        تصدير شامل
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل التقارير المالية...</p>
        </div>
    }
    else
    {
        <!-- Report Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            مرشحات التقارير
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">نوع التقرير</label>
                                <select @bind="selectedReportType" @bind:after="LoadReportData" class="form-select">
                                    <option value="summary">ملخص عام</option>
                                    <option value="detailed">تقرير مفصل</option>
                                    <option value="overdue">المتأخرين في الدفع</option>
                                    <option value="collections">تقرير التحصيل</option>
                                    <option value="grade-analysis">تحليل حسب الصف</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الصف الدراسي</label>
                                <select @bind="selectedGrade" @bind:after="LoadReportData" class="form-select">
                                    <option value="">جميع الصفوف</option>
                                    @foreach (var grade in grades)
                                    {
                                        <option value="@grade.Id">@grade.Name</option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" @bind="fromDate" @bind:after="LoadReportData" class="form-control" />
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" @bind="toDate" @bind:after="LoadReportData" class="form-control" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm bg-gradient-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h4 class="mb-1">@reportData.TotalStudents</h4>
                        <p class="mb-0">إجمالي الطلاب</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm bg-gradient-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                        <h4 class="mb-1">@reportData.TotalFees.ToString("C")</h4>
                        <p class="mb-0">إجمالي الرسوم</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm bg-gradient-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h4 class="mb-1">@reportData.TotalCollected.ToString("C")</h4>
                        <p class="mb-0">المحصل</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm bg-gradient-danger text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h4 class="mb-1">@reportData.TotalOutstanding.ToString("C")</h4>
                        <p class="mb-0">المستحق</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Collection Rate Progress -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">نسبة التحصيل الإجمالية</h6>
                            <span class="badge bg-success fs-6">@GetCollectionRate().ToString("F1")%</span>
                        </div>
                        <div class="progress" style="height: 15px;">
                            <div class="progress-bar bg-success progress-bar-striped progress-bar-animated"
                                 style="width: @GetCollectionRate()%"></div>
                        </div>
                        <small class="text-muted mt-1">
                            @reportData.TotalCollected.ToString("C") من أصل @reportData.TotalFees.ToString("C")
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Content -->
        @if (selectedReportType == "summary")
        {
            <!-- Summary Report -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-white border-bottom">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-pie me-2 text-primary"></i>
                                توزيع الطلاب حسب حالة الدفع
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="text-center p-3 bg-success bg-opacity-10 rounded">
                                        <h5 class="text-success mb-1">@reportData.StudentsFullyPaid</h5>
                                        <small class="text-muted">مدفوع بالكامل</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-3 bg-warning bg-opacity-10 rounded">
                                        <h5 class="text-warning mb-1">@reportData.StudentsPartiallyPaid</h5>
                                        <small class="text-muted">مدفوع جزئياً</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-3 bg-secondary bg-opacity-10 rounded">
                                        <h5 class="text-secondary mb-1">@reportData.StudentsNotPaid</h5>
                                        <small class="text-muted">غير مدفوع</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-3 bg-danger bg-opacity-10 rounded">
                                        <h5 class="text-danger mb-1">@reportData.StudentsOverdue</h5>
                                        <small class="text-muted">متأخر</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-white border-bottom">
                            <h6 class="mb-0">
                                <i class="fas fa-calendar me-2 text-info"></i>
                                التحصيل الشهري
                            </h6>
                        </div>
                        <div class="card-body">
                            @if (reportData.MonthlyCollections?.Any() == true)
                            {
                                @foreach (var month in reportData.MonthlyCollections.Take(6))
                                {
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>@month.Month</span>
                                        <div class="d-flex align-items-center">
                                            <div class="progress me-2" style="width: 100px; height: 8px;">
                                                <div class="progress-bar bg-info" style="width: @GetMonthPercentage(month.Amount)%"></div>
                                            </div>
                                            <strong class="text-info">@month.Amount.ToString("C")</strong>
                                        </div>
                                    </div>
                                }
                            }
                            else
                            {
                                <p class="text-muted text-center">لا توجد بيانات شهرية</p>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (selectedReportType == "overdue")
        {
            <!-- Overdue Students Report -->
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                الطلاب المتأخرين في الدفع
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (overdueStudents?.Any() == true)
                            {
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>الطالب</th>
                                                <th>الصف</th>
                                                <th>المبلغ المستحق</th>
                                                <th>آخر دفعة</th>
                                                <th>أيام التأخير</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var student in overdueStudents)
                                            {
                                                <tr class="table-danger">
                                                    <td>
                                                        <strong>@student.StudentName</strong>
                                                        <br>
                                                        <small class="text-muted">@student.StudentNumber</small>
                                                    </td>
                                                    <td>@student.GradeName</td>
                                                    <td>
                                                        <strong class="text-danger">@student.OutstandingAmount.ToString("C")</strong>
                                                    </td>
                                                    <td>
                                                        @if (student.LastPaymentDate.HasValue)
                                                        {
                                                            @student.LastPaymentDate.Value.ToString("dd/MM/yyyy")
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">لا يوجد</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-danger">
                                                            @GetOverdueDays(student.LastPaymentDate) يوم
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-primary" @onclick="() => ViewStudent(student.StudentId)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button class="btn btn-outline-success" @onclick="() => RecordPayment(student.StudentId)">
                                                                <i class="fas fa-money-bill"></i>
                                                            </button>
                                                            <button class="btn btn-outline-warning" @onclick="() => SendReminder(student.StudentId)">
                                                                <i class="fas fa-bell"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-5">
                                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                    <h5 class="text-success">ممتاز! لا يوجد طلاب متأخرين</h5>
                                    <p class="text-muted">جميع الطلاب محدثين في دفعاتهم</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (selectedReportType == "grade-analysis")
        {
            <!-- Grade Analysis Report -->
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                تحليل مالي حسب الصفوف
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (gradeAnalysis?.Any() == true)
                            {
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>الصف</th>
                                                <th>عدد الطلاب</th>
                                                <th>إجمالي الرسوم</th>
                                                <th>المحصل</th>
                                                <th>المستحق</th>
                                                <th>نسبة التحصيل</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var grade in gradeAnalysis)
                                            {
                                                <tr>
                                                    <td>
                                                        <strong class="text-primary">@grade.GradeName</strong>
                                                    </td>
                                                    <td>@grade.StudentCount</td>
                                                    <td>
                                                        <strong class="text-primary">@grade.TotalFees.ToString("C")</strong>
                                                    </td>
                                                    <td>
                                                        <strong class="text-success">@grade.CollectedAmount.ToString("C")</strong>
                                                    </td>
                                                    <td>
                                                        <strong class="text-danger">@grade.OutstandingAmount.ToString("C")</strong>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="progress me-2" style="width: 80px; height: 10px;">
                                                                <div class="progress-bar bg-success" style="width: @grade.CollectionRate%"></div>
                                                            </div>
                                                            <span class="badge bg-success">@grade.CollectionRate.ToString("F1")%</span>
                                                        </div>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">لا توجد بيانات للتحليل</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
    }
</div>

@code {
    private List<AcademicGradeDto> grades = new();
    private List<StudentFeeDto> overdueStudents = new();
    private List<GradeAnalysisDto> gradeAnalysis = new();

    private string selectedReportType = "summary";
    private string selectedGrade = "";
    private DateTime fromDate = DateTime.Now.AddMonths(-1);
    private DateTime toDate = DateTime.Now;

    private bool isLoading = true;

    private FinancialReportDataDto reportData = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadInitialData();
        await LoadReportData();
    }

    private async Task LoadInitialData()
    {
        try
        {
            var gradesResult = await ApiService.GetAcademicGradesAsync();
            grades = gradesResult?.ToList() ?? new List<AcademicGradeDto>();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات الأساسية: {ex.Message}");
        }
    }

    private async Task LoadReportData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // Load student fees data
            var allStudentFees = await ApiService.GetStudentFeesAsync() ?? new List<StudentFeeDto>();

            // Filter by grade if selected
            if (!string.IsNullOrEmpty(selectedGrade))
            {
                allStudentFees = allStudentFees.Where(s => s.GradeId.ToString() == selectedGrade).ToList();
            }

            // Calculate report data
            reportData = new FinancialReportDataDto
            {
                TotalStudents = allStudentFees.Count,
                TotalFees = allStudentFees.Sum(s => s.TotalFees),
                TotalCollected = allStudentFees.Sum(s => s.PaidAmount),
                TotalOutstanding = allStudentFees.Sum(s => s.OutstandingAmount),
                StudentsFullyPaid = allStudentFees.Count(s => s.PaymentStatus == "paid"),
                StudentsPartiallyPaid = allStudentFees.Count(s => s.PaymentStatus == "partial"),
                StudentsNotPaid = allStudentFees.Count(s => s.PaymentStatus == "unpaid"),
                StudentsOverdue = allStudentFees.Count(s => s.PaymentStatus == "overdue"),
                MonthlyCollections = GetMonthlyCollections(allStudentFees)
            };

            // Load specific report data based on type
            switch (selectedReportType)
            {
                case "overdue":
                    overdueStudents = allStudentFees.Where(s => s.PaymentStatus == "overdue").ToList();
                    break;
                case "grade-analysis":
                    gradeAnalysis = GetGradeAnalysis(allStudentFees);
                    break;
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل بيانات التقرير: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private List<MonthlyCollectionDto> GetMonthlyCollections(List<StudentFeeDto> studentFees)
    {
        var monthlyData = new List<MonthlyCollectionDto>();

        for (int i = 5; i >= 0; i--)
        {
            var month = DateTime.Now.AddMonths(-i);
            var monthName = month.ToString("MMMM yyyy");

            // This would normally come from payment history data
            var amount = studentFees.Sum(s => s.PaidAmount) / 6; // Simplified calculation

            monthlyData.Add(new MonthlyCollectionDto
            {
                Month = monthName,
                Amount = amount
            });
        }

        return monthlyData;
    }

    private List<GradeAnalysisDto> GetGradeAnalysis(List<StudentFeeDto> studentFees)
    {
        return studentFees.GroupBy(s => new { s.GradeId, s.GradeName })
            .Select(g => new GradeAnalysisDto
            {
                GradeId = g.Key.GradeId,
                GradeName = g.Key.GradeName,
                StudentCount = g.Count(),
                TotalFees = g.Sum(s => s.TotalFees),
                CollectedAmount = g.Sum(s => s.PaidAmount),
                OutstandingAmount = g.Sum(s => s.OutstandingAmount),
                CollectionRate = g.Sum(s => s.TotalFees) > 0 ?
                    (double)(g.Sum(s => s.PaidAmount) / g.Sum(s => s.TotalFees)) * 100 : 0
            }).ToList();
    }

    private async Task RefreshReports()
    {
        await LoadReportData();
    }

    private async Task ExportAllReports()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة تصدير التقارير ستكون متاحة قريباً");
    }

    private void ViewStudent(int studentId)
    {
        Navigation.NavigateTo($"/accounting/students/{studentId}/payment-history");
    }

    private void RecordPayment(int studentId)
    {
        Navigation.NavigateTo($"/accounting/students/{studentId}/payment");
    }

    private async Task SendReminder(int studentId)
    {
        try
        {
            var success = await ApiService.SendPaymentReminderAsync(studentId);
            if (success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "تم إرسال التذكير بنجاح");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في إرسال التذكير: {ex.Message}");
        }
    }

    private double GetCollectionRate()
    {
        if (reportData.TotalFees == 0) return 0;
        return (double)(reportData.TotalCollected / reportData.TotalFees) * 100;
    }

    private double GetMonthPercentage(decimal amount)
    {
        var maxAmount = reportData.MonthlyCollections?.Max(m => m.Amount) ?? 1;
        return maxAmount > 0 ? (double)(amount / maxAmount) * 100 : 0;
    }

    private int GetOverdueDays(DateTime? lastPaymentDate)
    {
        if (!lastPaymentDate.HasValue)
            return (DateTime.Now - DateTime.Now.AddMonths(-1)).Days;

        return Math.Max(0, (DateTime.Now - lastPaymentDate.Value).Days - 30);
    }

    // DTOs for report data
    public class FinancialReportDataDto
    {
        public int TotalStudents { get; set; }
        public decimal TotalFees { get; set; }
        public decimal TotalCollected { get; set; }
        public decimal TotalOutstanding { get; set; }
        public int StudentsFullyPaid { get; set; }
        public int StudentsPartiallyPaid { get; set; }
        public int StudentsNotPaid { get; set; }
        public int StudentsOverdue { get; set; }
        public List<MonthlyCollectionDto> MonthlyCollections { get; set; } = new();
    }

    public class MonthlyCollectionDto
    {
        public string Month { get; set; } = string.Empty;
        public decimal Amount { get; set; }
    }

    public class GradeAnalysisDto
    {
        public int GradeId { get; set; }
        public string GradeName { get; set; } = string.Empty;
        public int StudentCount { get; set; }
        public decimal TotalFees { get; set; }
        public decimal CollectedAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public double CollectionRate { get; set; }
    }
}
