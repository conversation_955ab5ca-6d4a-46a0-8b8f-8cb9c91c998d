using System.ComponentModel.DataAnnotations;

namespace Schools.Shared.Models
{
    // Request
    public class Request : BaseEntity
    {
        [Required]
        public string RequesterId { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Description { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string RequestType { get; set; } = string.Empty; // Leave, Transfer, Complaint, etc.

        [Required]
        public RequestStatus Status { get; set; } = RequestStatus.Pending;

        public string? AttachmentUrl { get; set; }

        public string? ResponseMessage { get; set; }

        public string? ProcessedBy { get; set; }

        public DateTime? ProcessedAt { get; set; }

        public int Priority { get; set; } = 1; // 1=Low, 2=Medium, 3=High

        // Navigation properties
        public virtual ApplicationUser Requester { get; set; } = null!;
    }

    // Notification
    public class Notification : BaseEntity
    {
        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Message { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Type { get; set; } = string.Empty; // Info, Warning, Success, Error

        public bool IsRead { get; set; } = false;

        public string? ActionUrl { get; set; }

        public DateTime? ReadAt { get; set; }

        // Navigation properties
        public virtual ApplicationUser User { get; set; } = null!;
    }

    // FeeStructure and StudentFeePayment moved to AccountingModels.cs to avoid duplication

    // Employee Salary
    public class EmployeeSalary : BaseEntity
    {
        [Required]
        public string EmployeeId { get; set; } = string.Empty;

        [Required]
        public decimal BasicSalary { get; set; }

        public decimal? Allowances { get; set; }

        public decimal? Deductions { get; set; }

        [Required]
        public decimal NetSalary { get; set; }

        [Required]
        public int Month { get; set; }

        [Required]
        public int Year { get; set; }

        [Required]
        public PaymentStatus Status { get; set; } = PaymentStatus.Pending;

        public DateTime? PaymentDate { get; set; }

        public string? Notes { get; set; }

        public string? ProcessedBy { get; set; }

        // Navigation properties
        public virtual ApplicationUser Employee { get; set; } = null!;
    }

    // Employee Attendance
    public class EmployeeAttendance : BaseEntity
    {
        [Required]
        public string EmployeeId { get; set; } = string.Empty;

        [Required]
        public DateTime Date { get; set; }

        public DateTime? CheckInTime { get; set; }

        public DateTime? CheckOutTime { get; set; }

        public TimeSpan? WorkingHours { get; set; }

        [Required]
        public AttendanceStatus Status { get; set; }

        public string? Notes { get; set; }

        // Navigation properties
        public virtual ApplicationUser Employee { get; set; } = null!;
    }

    // Report
    public class Report : BaseEntity
    {
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string ReportType { get; set; } = string.Empty;

        public string? Description { get; set; }

        [Required]
        public string GeneratedBy { get; set; } = string.Empty;

        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

        public string? FilePath { get; set; }

        public string? Parameters { get; set; } // JSON string of report parameters

        // Navigation properties
        public virtual ApplicationUser GeneratedByUser { get; set; } = null!;
    }
}
