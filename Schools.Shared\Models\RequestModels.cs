using System.ComponentModel.DataAnnotations;

namespace Schools.Shared.Models
{
    // Request
    public class Request : BaseEntity
    {
        [Required]
        public string RequesterId { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Description { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string RequestType { get; set; } = string.Empty; // Leave, Transfer, Complaint, etc.

        [Required]
        public RequestStatus Status { get; set; } = RequestStatus.Pending;

        public string? AttachmentUrl { get; set; }

        public string? ResponseMessage { get; set; }

        public string? ProcessedBy { get; set; }

        public DateTime? ProcessedAt { get; set; }

        public int Priority { get; set; } = 1; // 1=Low, 2=Medium, 3=High

        // Navigation properties
        public virtual ApplicationUser Requester { get; set; } = null!;
    }

    // Notification
    public class Notification : BaseEntity
    {
        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Message { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Type { get; set; } = string.Empty; // Info, Warning, Success, Error

        public bool IsRead { get; set; } = false;

        public string? ActionUrl { get; set; }

        public DateTime? ReadAt { get; set; }

        // Navigation properties
        public virtual ApplicationUser User { get; set; } = null!;
    }

    // Fee Structure
    public class FeeStructure : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public int AcademicYearId { get; set; }

        public int? GradeId { get; set; }

        public int? ClassId { get; set; }

        [Required]
        public decimal Amount { get; set; }

        [Required]
        [StringLength(50)]
        public string FeeType { get; set; } = string.Empty; // Tuition, Transport, Books, etc.

        public DateTime DueDate { get; set; }

        public bool IsActive { get; set; } = true;

        public string? Description { get; set; }

        // Navigation properties
        public virtual AcademicYear AcademicYear { get; set; } = null!;
        public virtual Grade? Grade { get; set; }
        public virtual Class? Class { get; set; }
        public virtual ICollection<StudentFeePayment> StudentFeePayments { get; set; } = new List<StudentFeePayment>();
    }

    // Student Fee Payment
    public class StudentFeePayment : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int FeeStructureId { get; set; }

        [Required]
        public decimal AmountPaid { get; set; }

        public decimal? Discount { get; set; }

        public decimal? Fine { get; set; }

        [Required]
        public PaymentStatus Status { get; set; } = PaymentStatus.Pending;

        [Required]
        public DateTime PaymentDate { get; set; }

        [StringLength(100)]
        public string? PaymentMethod { get; set; }

        [StringLength(100)]
        public string? TransactionId { get; set; }

        public string? Notes { get; set; }

        public string? ProcessedBy { get; set; }

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual FeeStructure FeeStructure { get; set; } = null!;
    }

    // Employee Salary
    public class EmployeeSalary : BaseEntity
    {
        [Required]
        public string EmployeeId { get; set; } = string.Empty;

        [Required]
        public decimal BasicSalary { get; set; }

        public decimal? Allowances { get; set; }

        public decimal? Deductions { get; set; }

        [Required]
        public decimal NetSalary { get; set; }

        [Required]
        public int Month { get; set; }

        [Required]
        public int Year { get; set; }

        [Required]
        public PaymentStatus Status { get; set; } = PaymentStatus.Pending;

        public DateTime? PaymentDate { get; set; }

        public string? Notes { get; set; }

        public string? ProcessedBy { get; set; }

        // Navigation properties
        public virtual ApplicationUser Employee { get; set; } = null!;
    }

    // Employee Attendance
    public class EmployeeAttendance : BaseEntity
    {
        [Required]
        public string EmployeeId { get; set; } = string.Empty;

        [Required]
        public DateTime Date { get; set; }

        public DateTime? CheckInTime { get; set; }

        public DateTime? CheckOutTime { get; set; }

        public TimeSpan? WorkingHours { get; set; }

        [Required]
        public AttendanceStatus Status { get; set; }

        public string? Notes { get; set; }

        // Navigation properties
        public virtual ApplicationUser Employee { get; set; } = null!;
    }

    // Report
    public class Report : BaseEntity
    {
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string ReportType { get; set; } = string.Empty;

        public string? Description { get; set; }

        [Required]
        public string GeneratedBy { get; set; } = string.Empty;

        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

        public string? FilePath { get; set; }

        public string? Parameters { get; set; } // JSON string of report parameters

        // Navigation properties
        public virtual ApplicationUser GeneratedByUser { get; set; } = null!;
    }
}
