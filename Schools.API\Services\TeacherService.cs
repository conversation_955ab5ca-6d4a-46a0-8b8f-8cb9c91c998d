using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Schools.API.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;

namespace Schools.API.Services;

public class TeacherService : ITeacherService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<TeacherService> _logger;

    public TeacherService(ApplicationDbContext context, ILogger<TeacherService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<PagedResult<TeacherDto>> GetTeachersAsync(
        string? search = null,
        string? department = null,
        string? subject = null,
        bool? isActive = null,
        int page = 1,
        int pageSize = 20)
    {
        try
        {
            var query = _context.Users
                .Where(u => u.TeacherAssignments.Any())
                .Include(u => u.TeacherAssignments)
                .ThenInclude(ta => ta.Subject)
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(search))
            {
                query = query.Where(u =>
                    u.FirstName.Contains(search) ||
                    u.LastName.Contains(search) ||
                    u.Email.Contains(search));
            }

            if (!string.IsNullOrEmpty(department))
            {
                // Assuming department is stored in a property or related table
                // This would need to be adjusted based on actual data model
            }

            if (!string.IsNullOrEmpty(subject))
            {
                query = query.Where(u => u.TeacherAssignments.Any(ta => ta.Subject.Name.Contains(subject)));
            }

            if (isActive.HasValue)
            {
                query = query.Where(u => u.IsActive == isActive.Value);
            }

            var totalCount = await query.CountAsync();
            var teachers = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(u => new TeacherDto
                {
                    Id = u.Id,
                    FirstName = u.FirstName,
                    LastName = u.LastName,
                    Email = u.Email ?? "",
                    PhoneNumber = u.PhoneNumber,
                    IsActive = u.IsActive,
                    CreatedAt = u.CreatedAt,
                    Subjects = u.TeacherAssignments.Select(ta => new TeacherSubjectDto
                    {
                        SubjectId = ta.SubjectId,
                        SubjectName = ta.Subject.Name,
                        SubjectCode = ta.Subject.Code,
                        AssignedDate = ta.AssignedDate,
                        IsActive = ta.IsActive
                    }).ToList()
                })
                .ToListAsync();

            return new PagedResult<TeacherDto>
            {
                Items = teachers,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting teachers");
            throw;
        }
    }

    public async Task<TeacherDto?> GetTeacherByIdAsync(string id)
    {
        try
        {
            var teacher = await _context.Users
                .Where(u => u.Id == id && u.TeacherAssignments.Any())
                .Include(u => u.TeacherAssignments)
                .ThenInclude(ta => ta.Subject)
                .FirstOrDefaultAsync();

            if (teacher == null)
                return null;

            return new TeacherDto
            {
                Id = teacher.Id,
                FirstName = teacher.FirstName,
                LastName = teacher.LastName,
                Email = teacher.Email ?? "",
                PhoneNumber = teacher.PhoneNumber,
                IsActive = teacher.IsActive,
                CreatedAt = teacher.CreatedAt,
                EmployeeNumber = teacher.UserName ?? "",
                HireDate = teacher.HireDate,
                Specialization = teacher.Specialization,
                YearsOfExperience = teacher.YearsOfExperience,
                Salary = teacher.Salary,
                Subjects = teacher.TeacherAssignments.Select(ta => new TeacherSubjectDto
                {
                    SubjectId = ta.SubjectId,
                    SubjectName = ta.Subject.Name,
                    SubjectCode = ta.Subject.Code,
                    AssignedDate = ta.AssignedDate,
                    IsActive = ta.IsActive
                }).ToList()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting teacher {Id}", id);
            throw;
        }
    }

    public async Task<TeacherDto> CreateTeacherAsync(CreateTeacherDto createDto)
    {
        try
        {
            // Check if email already exists
            var existingUser = await _context.Users.FirstOrDefaultAsync(u => u.Email == createDto.Email);
            if (existingUser != null)
                throw new InvalidOperationException("User with this email already exists");

            // Check if employee number already exists
            var existingEmployee = await _context.Users.FirstOrDefaultAsync(u => u.UserName == createDto.EmployeeNumber);
            if (existingEmployee != null)
                throw new InvalidOperationException("Employee number already exists");

            // Create new teacher user
            var teacher = new ApplicationUser
            {
                UserName = createDto.EmployeeNumber,
                Email = createDto.Email,
                FirstName = createDto.FirstName,
                LastName = createDto.LastName,
                PhoneNumber = createDto.PhoneNumber,
                NationalId = createDto.NationalId,
                DateOfBirth = createDto.DateOfBirth,
                Address = createDto.Address,
                HireDate = createDto.HireDate,
                Specialization = createDto.Specialization,
                YearsOfExperience = createDto.YearsOfExperience,
                Salary = createDto.Salary,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            _context.Users.Add(teacher);
            await _context.SaveChangesAsync();

            // Assign Teacher role
            var teacherRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == "Teacher");
            if (teacherRole != null)
            {
                var userRole = new IdentityUserRole<string>
                {
                    UserId = teacher.Id,
                    RoleId = teacherRole.Id
                };
                _context.UserRoles.Add(userRole);
                await _context.SaveChangesAsync();
            }

            return new TeacherDto
            {
                Id = teacher.Id,
                FirstName = teacher.FirstName,
                LastName = teacher.LastName,
                Email = teacher.Email,
                PhoneNumber = teacher.PhoneNumber,
                NationalId = teacher.NationalId,
                DateOfBirth = teacher.DateOfBirth,
                Address = teacher.Address,
                IsActive = teacher.IsActive,
                CreatedAt = teacher.CreatedAt,
                EmployeeNumber = teacher.UserName ?? "",
                HireDate = teacher.HireDate,
                Specialization = teacher.Specialization,
                YearsOfExperience = teacher.YearsOfExperience,
                Salary = teacher.Salary
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating teacher");
            throw;
        }
    }

    public async Task<TeacherDto?> UpdateTeacherAsync(string id, UpdateTeacherDto updateDto)
    {
        try
        {
            var teacher = await _context.Users.FirstOrDefaultAsync(u => u.Id == id);
            if (teacher == null)
                return null;

            // Update fields if provided
            if (!string.IsNullOrEmpty(updateDto.FirstName))
                teacher.FirstName = updateDto.FirstName;

            if (!string.IsNullOrEmpty(updateDto.LastName))
                teacher.LastName = updateDto.LastName;

            if (!string.IsNullOrEmpty(updateDto.PhoneNumber))
                teacher.PhoneNumber = updateDto.PhoneNumber;

            if (updateDto.DateOfBirth.HasValue)
                teacher.DateOfBirth = updateDto.DateOfBirth;

            if (!string.IsNullOrEmpty(updateDto.Address))
                teacher.Address = updateDto.Address;

            if (!string.IsNullOrEmpty(updateDto.Specialization))
                teacher.Specialization = updateDto.Specialization;

            if (updateDto.YearsOfExperience.HasValue)
                teacher.YearsOfExperience = updateDto.YearsOfExperience.Value;

            if (updateDto.Salary.HasValue)
                teacher.Salary = updateDto.Salary.Value;

            if (updateDto.IsActive.HasValue)
                teacher.IsActive = updateDto.IsActive.Value;

            teacher.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return new TeacherDto
            {
                Id = teacher.Id,
                FirstName = teacher.FirstName,
                LastName = teacher.LastName,
                Email = teacher.Email ?? "",
                PhoneNumber = teacher.PhoneNumber,
                NationalId = teacher.NationalId,
                DateOfBirth = teacher.DateOfBirth,
                Address = teacher.Address,
                IsActive = teacher.IsActive,
                CreatedAt = teacher.CreatedAt,
                EmployeeNumber = teacher.UserName ?? "",
                HireDate = teacher.HireDate,
                Specialization = teacher.Specialization,
                YearsOfExperience = teacher.YearsOfExperience,
                Salary = teacher.Salary
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating teacher {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeleteTeacherAsync(string id)
    {
        try
        {
            var teacher = await _context.Users.FirstOrDefaultAsync(u => u.Id == id);
            if (teacher == null)
                return false;

            // Soft delete - deactivate instead of removing
            teacher.IsActive = false;
            teacher.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting teacher {Id}", id);
            throw;
        }
    }

    public async Task<bool> ActivateTeacherAsync(string id)
    {
        try
        {
            var teacher = await _context.Users.FirstOrDefaultAsync(u => u.Id == id);
            if (teacher == null)
                return false;

            teacher.IsActive = true;
            teacher.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating teacher {Id}", id);
            throw;
        }
    }

    public async Task<bool> DeactivateTeacherAsync(string id)
    {
        try
        {
            var teacher = await _context.Users.FirstOrDefaultAsync(u => u.Id == id);
            if (teacher == null)
                return false;

            teacher.IsActive = false;
            teacher.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating teacher {Id}", id);
            throw;
        }
    }

    public async Task<List<TeacherSubjectDto>> GetTeacherSubjectsAsync(string teacherId)
    {
        try
        {
            var assignments = await _context.TeacherAssignments
                .Where(ta => ta.TeacherId == teacherId && ta.IsActive)
                .Include(ta => ta.Subject)
                .Select(ta => new TeacherSubjectDto
                {
                    Id = ta.Id,
                    TeacherId = ta.TeacherId,
                    SubjectId = ta.SubjectId,
                    SubjectName = ta.Subject.Name,
                    SubjectCode = ta.Subject.Code,
                    AssignedDate = ta.AssignedDate,
                    IsActive = ta.IsActive
                })
                .ToListAsync();

            return assignments;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting teacher subjects for {TeacherId}", teacherId);
            throw;
        }
    }

    public async Task<TeacherSubjectDto> AssignSubjectToTeacherAsync(string teacherId, int subjectId)
    {
        try
        {
            // Check if teacher exists
            var teacher = await _context.Users.FirstOrDefaultAsync(u => u.Id == teacherId);
            if (teacher == null)
                throw new InvalidOperationException("Teacher not found");

            // Check if subject exists
            var subject = await _context.Subjects.FirstOrDefaultAsync(s => s.Id == subjectId);
            if (subject == null)
                throw new InvalidOperationException("Subject not found");

            // Check if assignment already exists
            var existingAssignment = await _context.TeacherAssignments
                .FirstOrDefaultAsync(ta => ta.TeacherId == teacherId && ta.SubjectId == subjectId);

            if (existingAssignment != null)
            {
                if (existingAssignment.IsActive)
                    throw new InvalidOperationException("Teacher is already assigned to this subject");

                // Reactivate existing assignment
                existingAssignment.IsActive = true;
                existingAssignment.AssignedDate = DateTime.UtcNow;
                existingAssignment.UpdatedAt = DateTime.UtcNow;
            }
            else
            {
                // Create new assignment
                var assignment = new TeacherAssignment
                {
                    TeacherId = teacherId,
                    SubjectId = subjectId,
                    ClassId = 1, // This should be provided or handled differently
                    AssignedDate = DateTime.UtcNow,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                };

                _context.TeacherAssignments.Add(assignment);
            }

            await _context.SaveChangesAsync();

            return new TeacherSubjectDto
            {
                TeacherId = teacherId,
                SubjectId = subjectId,
                SubjectName = subject.Name,
                SubjectCode = subject.Code,
                AssignedDate = DateTime.UtcNow,
                IsActive = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning subject {SubjectId} to teacher {TeacherId}", subjectId, teacherId);
            throw;
        }
    }

    public async Task<bool> UnassignSubjectFromTeacherAsync(string teacherId, int subjectId)
    {
        try
        {
            var assignment = await _context.TeacherAssignments
                .FirstOrDefaultAsync(ta => ta.TeacherId == teacherId && ta.SubjectId == subjectId && ta.IsActive);

            if (assignment == null)
                return false;

            assignment.IsActive = false;
            assignment.UnassignedDate = DateTime.UtcNow;
            assignment.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unassigning subject {SubjectId} from teacher {TeacherId}", subjectId, teacherId);
            throw;
        }
    }

    public async Task<List<TeacherClassDto>> GetTeacherClassesAsync(string teacherId)
    {
        try
        {
            var assignments = await _context.TeacherAssignments
                .Where(ta => ta.TeacherId == teacherId && ta.IsActive)
                .Include(ta => ta.Class)
                .ThenInclude(c => c.AcademicGrade)
                .Include(ta => ta.Subject)
                .Select(ta => new TeacherClassDto
                {
                    Id = ta.Id,
                    TeacherId = ta.TeacherId,
                    ClassId = ta.ClassId,
                    ClassName = ta.Class.Name,
                    GradeId = ta.Class.AcademicGradeId,
                    GradeName = ta.Class.AcademicGrade.Name,
                    SubjectId = ta.SubjectId,
                    SubjectName = ta.Subject.Name,
                    IsClassTeacher = ta.IsClassTeacher,
                    AssignedDate = ta.AssignedDate,
                    IsActive = ta.IsActive,
                    StudentCount = ta.Class.StudentEnrollments.Count(se => se.IsActive)
                })
                .ToListAsync();

            return assignments;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting teacher classes for {TeacherId}", teacherId);
            throw;
        }
    }

    public async Task<TeacherClassDto> AssignClassToTeacherAsync(string teacherId, int classId, int subjectId)
    {
        try
        {
            // Check if teacher exists
            var teacher = await _context.Users.FirstOrDefaultAsync(u => u.Id == teacherId);
            if (teacher == null)
                throw new InvalidOperationException("Teacher not found");

            // Check if class exists
            var classEntity = await _context.Classes
                .Include(c => c.AcademicGrade)
                .FirstOrDefaultAsync(c => c.Id == classId);
            if (classEntity == null)
                throw new InvalidOperationException("Class not found");

            // Check if subject exists
            var subject = await _context.Subjects.FirstOrDefaultAsync(s => s.Id == subjectId);
            if (subject == null)
                throw new InvalidOperationException("Subject not found");

            // Check if assignment already exists
            var existingAssignment = await _context.TeacherAssignments
                .FirstOrDefaultAsync(ta => ta.TeacherId == teacherId && ta.ClassId == classId && ta.SubjectId == subjectId);

            if (existingAssignment != null)
            {
                if (existingAssignment.IsActive)
                    throw new InvalidOperationException("Teacher is already assigned to this class for this subject");

                // Reactivate existing assignment
                existingAssignment.IsActive = true;
                existingAssignment.AssignedDate = DateTime.UtcNow;
                existingAssignment.UpdatedAt = DateTime.UtcNow;
            }
            else
            {
                // Create new assignment
                var assignment = new TeacherAssignment
                {
                    TeacherId = teacherId,
                    ClassId = classId,
                    SubjectId = subjectId,
                    AssignedDate = DateTime.UtcNow,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                };

                _context.TeacherAssignments.Add(assignment);
            }

            await _context.SaveChangesAsync();

            return new TeacherClassDto
            {
                TeacherId = teacherId,
                ClassId = classId,
                ClassName = classEntity.Name,
                GradeId = classEntity.AcademicGradeId,
                GradeName = classEntity.AcademicGrade.Name,
                SubjectId = subjectId,
                SubjectName = subject.Name,
                AssignedDate = DateTime.UtcNow,
                IsActive = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning class {ClassId} to teacher {TeacherId} for subject {SubjectId}", classId, teacherId, subjectId);
            throw;
        }
    }

    public async Task<bool> UnassignClassFromTeacherAsync(string teacherId, int classId, int subjectId)
    {
        try
        {
            var assignment = await _context.TeacherAssignments
                .FirstOrDefaultAsync(ta => ta.TeacherId == teacherId && ta.ClassId == classId && ta.SubjectId == subjectId && ta.IsActive);

            if (assignment == null)
                return false;

            assignment.IsActive = false;
            assignment.UnassignedDate = DateTime.UtcNow;
            assignment.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing assignment of class {ClassId} from teacher {TeacherId} for subject {SubjectId}", classId, teacherId, subjectId);
            throw;
        }
    }

    public async Task<List<TeacherScheduleDto>> GetTeacherScheduleAsync(string teacherId, DateTime? date = null)
    {
        // Implementation would return teacher's schedule
        return new List<TeacherScheduleDto>();
    }

    public async Task<TeacherWorkloadDto> GetTeacherWorkloadAsync(string teacherId)
    {
        // Implementation would calculate teacher's workload
        return new TeacherWorkloadDto();
    }

    public async Task<TeacherPerformanceDto> GetTeacherPerformanceAsync(string teacherId, DateTime? startDate = null, DateTime? endDate = null)
    {
        // Implementation would calculate teacher's performance
        return new TeacherPerformanceDto();
    }

    public async Task<List<TeacherEvaluationDto>> GetTeacherEvaluationsAsync(string teacherId)
    {
        // Implementation would return teacher's evaluations
        return new List<TeacherEvaluationDto>();
    }

    public async Task<TeacherEvaluationDto> CreateTeacherEvaluationAsync(CreateTeacherEvaluationDto createDto)
    {
        // Implementation would create teacher evaluation
        return new TeacherEvaluationDto();
    }

    public async Task<List<TeacherDto>> GetTeachersBySubjectAsync(int subjectId)
    {
        try
        {
            var teachers = await _context.TeacherAssignments
                .Where(ta => ta.SubjectId == subjectId && ta.IsActive)
                .Include(ta => ta.Teacher)
                .Include(ta => ta.Subject)
                .Select(ta => new TeacherDto
                {
                    Id = ta.TeacherId,
                    FirstName = ta.Teacher.FirstName,
                    LastName = ta.Teacher.LastName,
                    Email = ta.Teacher.Email ?? "",
                    PhoneNumber = ta.Teacher.PhoneNumber,
                    IsActive = ta.Teacher.IsActive,
                    CreatedAt = ta.Teacher.CreatedAt,
                    EmployeeNumber = ta.Teacher.UserName ?? "",
                    HireDate = ta.Teacher.HireDate,
                    Specialization = ta.Teacher.Specialization,
                    YearsOfExperience = ta.Teacher.YearsOfExperience,
                    Salary = ta.Teacher.Salary
                })
                .Distinct()
                .ToListAsync();

            return teachers;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting teachers by subject {SubjectId}", subjectId);
            throw;
        }
    }

    public async Task<List<TeacherDto>> GetTeachersByDepartmentAsync(string department)
    {
        try
        {
            var teachers = await _context.Users
                .Where(u => u.TeacherAssignments.Any() &&
                           (string.IsNullOrEmpty(department) || u.Specialization == department))
                .Include(u => u.TeacherAssignments)
                .ThenInclude(ta => ta.Subject)
                .Select(u => new TeacherDto
                {
                    Id = u.Id,
                    FirstName = u.FirstName,
                    LastName = u.LastName,
                    Email = u.Email ?? "",
                    PhoneNumber = u.PhoneNumber,
                    IsActive = u.IsActive,
                    CreatedAt = u.CreatedAt,
                    EmployeeNumber = u.UserName ?? "",
                    HireDate = u.HireDate,
                    Specialization = u.Specialization,
                    YearsOfExperience = u.YearsOfExperience,
                    Salary = u.Salary
                })
                .ToListAsync();

            return teachers;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting teachers by department {Department}", department);
            throw;
        }
    }

    public async Task<TeacherStatisticsDto> GetTeacherStatisticsAsync(string teacherId)
    {
        try
        {
            var teacher = await _context.Users
                .Include(u => u.TeacherAssignments)
                .ThenInclude(ta => ta.Class)
                .ThenInclude(c => c.StudentEnrollments)
                .Include(u => u.TeacherAssignments)
                .ThenInclude(ta => ta.Subject)
                .FirstOrDefaultAsync(u => u.Id == teacherId);

            if (teacher == null)
                throw new InvalidOperationException("Teacher not found");

            var activeAssignments = teacher.TeacherAssignments.Where(ta => ta.IsActive).ToList();
            var totalClasses = activeAssignments.Count;
            var totalSubjects = activeAssignments.Select(ta => ta.SubjectId).Distinct().Count();
            var totalStudents = activeAssignments
                .SelectMany(ta => ta.Class.StudentEnrollments)
                .Where(se => se.IsActive)
                .Select(se => se.StudentId)
                .Distinct()
                .Count();

            // Calculate weekly hours (assuming each class is 1 hour per week)
            var weeklyHours = totalClasses * 1.0; // This should be calculated based on actual schedule

            return new TeacherStatisticsDto
            {
                TotalClasses = totalClasses,
                TotalStudents = totalStudents,
                TotalSubjects = totalSubjects,
                WeeklyHours = weeklyHours,
                ExamsCreated = 0, // This would need to be calculated from exams table
                ExamsGraded = 0, // This would need to be calculated from exam results
                PendingGrading = 0, // This would need to be calculated
                AverageClassSize = totalClasses > 0 ? (double)totalStudents / totalClasses : 0,
                StudentAttendanceRate = 0, // This would need to be calculated from attendance
                StudentPassRate = 0 // This would need to be calculated from grades
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating teacher statistics for {TeacherId}", teacherId);
            throw;
        }
    }
}
