@page "/teacher/exam-reports"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Teacher")]

<PageTitle>تقارير الامتحانات - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-chart-bar text-primary me-2"></i>
                        تقارير الامتحانات
                    </h2>
                    <p class="text-muted mb-0">تقارير شاملة ومفصلة لجميع الامتحانات والنتائج</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary" @onclick="GenerateReport">
                        <i class="fas fa-chart-line me-2"></i>
                        إنشاء تقرير
                    </button>
                    <button class="btn btn-outline-success" @onclick="ExportAllReports">
                        <i class="fas fa-download me-2"></i>
                        تصدير التقارير
                    </button>
                    <button class="btn btn-outline-info" @onclick="ScheduleReport">
                        <i class="fas fa-clock me-2"></i>
                        جدولة التقارير
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل التقارير...</p>
        </div>
    }
    else
    {
        <!-- Report Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            مرشحات التقارير
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">نوع التقرير</label>
                                <select @bind="selectedReportType" @bind:after="LoadReportData" class="form-select">
                                    <option value="overview">نظرة عامة</option>
                                    <option value="detailed">تقرير مفصل</option>
                                    <option value="comparative">تقرير مقارن</option>
                                    <option value="performance">تقرير الأداء</option>
                                    <option value="statistics">إحصائيات متقدمة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الفترة الزمنية</label>
                                <select @bind="selectedPeriod" @bind:after="LoadReportData" class="form-select">
                                    <option value="week">الأسبوع الماضي</option>
                                    <option value="month">الشهر الماضي</option>
                                    <option value="quarter">الربع الماضي</option>
                                    <option value="semester">الفصل الدراسي</option>
                                    <option value="year">السنة الدراسية</option>
                                    <option value="custom">فترة مخصصة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">المادة</label>
                                <select @bind="selectedSubjectId" @bind:after="LoadReportData" class="form-select">
                                    <option value="">جميع المواد</option>
                                    @if (subjects != null)
                                    {
                                        @foreach (var subject in subjects)
                                        {
                                            <option value="@subject.Id">@subject.Name</option>
                                        }
                                    }
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الصف</label>
                                <select @bind="selectedClassId" @bind:after="LoadReportData" class="form-select">
                                    <option value="">جميع الصفوف</option>
                                    @if (classes != null)
                                    {
                                        @foreach (var classItem in classes)
                                        {
                                            <option value="@classItem.Id">@classItem.Name</option>
                                        }
                                    }
                                </select>
                            </div>
                            @if (selectedPeriod == "custom")
                            {
                                <div class="col-md-3">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" @bind="startDate" @bind:after="LoadReportData" />
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" @bind="endDate" @bind:after="LoadReportData" />
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Summary Cards -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100 bg-gradient-primary text-white">
                    <div class="card-body text-center">
                        <div class="avatar-lg bg-white bg-opacity-25 text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-file-alt fa-lg"></i>
                        </div>
                        <h3 class="mb-1">@reportSummary.TotalExams</h3>
                        <p class="mb-0 opacity-75">إجمالي الامتحانات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100 bg-gradient-success text-white">
                    <div class="card-body text-center">
                        <div class="avatar-lg bg-white bg-opacity-25 text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-users fa-lg"></i>
                        </div>
                        <h3 class="mb-1">@reportSummary.TotalStudents</h3>
                        <p class="mb-0 opacity-75">إجمالي الطلاب</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100 bg-gradient-info text-white">
                    <div class="card-body text-center">
                        <div class="avatar-lg bg-white bg-opacity-25 text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-star fa-lg"></i>
                        </div>
                        <h3 class="mb-1">@reportSummary.AverageScore.ToString("F1")%</h3>
                        <p class="mb-0 opacity-75">متوسط الدرجات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100 bg-gradient-warning text-white">
                    <div class="card-body text-center">
                        <div class="avatar-lg bg-white bg-opacity-25 text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-percentage fa-lg"></i>
                        </div>
                        <h3 class="mb-1">@reportSummary.PassRate.ToString("F1")%</h3>
                        <p class="mb-0 opacity-75">معدل النجاح</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Content Based on Type -->
        @if (selectedReportType == "overview")
        {
            <!-- Overview Report -->
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-white border-bottom">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-pie me-2 text-primary"></i>
                                توزيع النتائج
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (reportSummary.GradeDistribution?.Any() == true)
                            {
                                @foreach (var grade in reportSummary.GradeDistribution)
                                {
                                    var percentage = reportSummary.TotalStudents > 0 ? (double)grade.Value / reportSummary.TotalStudents * 100 : 0;
                                    <div class="grade-item mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <span class="fw-bold">@grade.Key</span>
                                            <span class="text-muted">@grade.Value طالب (@percentage.ToString("F1")%)</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar @GetGradeColor(grade.Key)" style="width: @percentage%"></div>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-white border-bottom">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2 text-success"></i>
                                اتجاه الأداء
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (performanceTrend?.Any() == true)
                            {
                                @foreach (var trend in performanceTrend.Take(5))
                                {
                                    <div class="trend-item d-flex justify-content-between align-items-center mb-3">
                                        <div>
                                            <strong>@trend.Period</strong>
                                            <br>
                                            <small class="text-muted">@trend.ExamCount امتحان</small>
                                        </div>
                                        <div class="text-end">
                                            <h5 class="mb-0 @(trend.AverageScore >= 70 ? "text-success" : trend.AverageScore >= 50 ? "text-warning" : "text-danger")">
                                                @trend.AverageScore.ToString("F1")%
                                            </h5>
                                            <small class="text-muted">متوسط</small>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (selectedReportType == "detailed")
        {
            <!-- Detailed Report -->
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-bottom">
                            <h5 class="mb-0">
                                <i class="fas fa-table me-2 text-primary"></i>
                                التقرير المفصل
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (detailedReports?.Any() == true)
                            {
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>الامتحان</th>
                                                <th>المادة</th>
                                                <th>الصف</th>
                                                <th>التاريخ</th>
                                                <th>عدد الطلاب</th>
                                                <th>متوسط الدرجات</th>
                                                <th>معدل النجاح</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var report in detailedReports)
                                            {
                                                <tr>
                                                    <td>
                                                        <strong>@report.ExamTitle</strong>
                                                        <br>
                                                        <small class="text-muted">@report.ExamDescription</small>
                                                    </td>
                                                    <td><span class="badge bg-info">@report.SubjectName</span></td>
                                                    <td><span class="badge bg-secondary">@report.ClassName</span></td>
                                                    <td>@report.ExamDate.ToString("dd/MM/yyyy")</td>
                                                    <td>@report.TotalStudents</td>
                                                    <td>
                                                        <div class="progress" style="height: 20px;">
                                                            <div class="progress-bar @GetScoreColor(report.AverageScore)" style="width: @report.AverageScore%">
                                                                @report.AverageScore.ToString("F1")%
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge @(report.PassRate >= 70 ? "bg-success" : report.PassRate >= 50 ? "bg-warning" : "bg-danger")">
                                                            @report.PassRate.ToString("F1")%
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewExamReport(report.ExamId)" title="عرض التقرير">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-success" @onclick="() => ExportExamReport(report.ExamId)" title="تصدير">
                                                                <i class="fas fa-download"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-5">
                                    <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد تقارير</h5>
                                    <p class="text-muted">لا توجد بيانات للفترة المحددة</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (selectedReportType == "comparative")
        {
            <!-- Comparative Report -->
            <div class="row g-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-bottom">
                            <h5 class="mb-0">
                                <i class="fas fa-balance-scale me-2 text-primary"></i>
                                التقرير المقارن
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (comparativeData?.Any() == true)
                            {
                                <div class="row g-3">
                                    @foreach (var comparison in comparativeData)
                                    {
                                        <div class="col-md-6">
                                            <div class="comparison-card card border">
                                                <div class="card-header bg-light">
                                                    <h6 class="mb-0">@comparison.Title</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="comparison-metrics">
                                                        @foreach (var metric in comparison.Metrics)
                                                        {
                                                            <div class="metric-item d-flex justify-content-between align-items-center mb-2">
                                                                <span>@metric.Key</span>
                                                                <span class="fw-bold @GetMetricColor(metric.Value)">@metric.Value</span>
                                                            </div>
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (selectedReportType == "performance")
        {
            <!-- Performance Report -->
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-trophy me-2"></i>
                                أفضل الطلاب
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (topPerformers?.Any() == true)
                            {
                                @foreach (var student in topPerformers.Take(5))
                                {
                                    <div class="performer-item d-flex align-items-center mb-3">
                                        <div class="avatar-sm bg-success text-white rounded-circle me-3 d-flex align-items-center justify-content-center">
                                            @student.Name.Substring(0, 1)
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">@student.Name</h6>
                                            <small class="text-muted">@student.ClassName</small>
                                        </div>
                                        <span class="badge bg-success">@student.AverageScore.ToString("F1")%</span>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                يحتاجون مساعدة
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (strugglingStudents?.Any() == true)
                            {
                                @foreach (var student in strugglingStudents.Take(5))
                                {
                                    <div class="performer-item d-flex align-items-center mb-3">
                                        <div class="avatar-sm bg-warning text-dark rounded-circle me-3 d-flex align-items-center justify-content-center">
                                            @student.Name.Substring(0, 1)
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">@student.Name</h6>
                                            <small class="text-muted">@student.ClassName</small>
                                        </div>
                                        <span class="badge bg-warning text-dark">@student.AverageScore.ToString("F1")%</span>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                الأكثر تحسناً
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (improvingStudents?.Any() == true)
                            {
                                @foreach (var student in improvingStudents.Take(5))
                                {
                                    <div class="performer-item d-flex align-items-center mb-3">
                                        <div class="avatar-sm bg-info text-white rounded-circle me-3 d-flex align-items-center justify-content-center">
                                            @student.Name.Substring(0, 1)
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">@student.Name</h6>
                                            <small class="text-muted">@student.ClassName</small>
                                        </div>
                                        <span class="badge bg-info">+@student.ImprovementRate.ToString("F1")%</span>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (selectedReportType == "statistics")
        {
            <!-- Advanced Statistics -->
            <div class="row g-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-bottom">
                            <h5 class="mb-0">
                                <i class="fas fa-calculator me-2 text-primary"></i>
                                إحصائيات متقدمة
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (advancedStats != null)
                            {
                                <div class="row g-4">
                                    <div class="col-md-6">
                                        <div class="stats-section">
                                            <h6 class="text-primary mb-3">إحصائيات الدرجات</h6>
                                            <div class="stats-grid">
                                                <div class="stat-item">
                                                    <span class="stat-label">أعلى درجة:</span>
                                                    <span class="stat-value text-success">@advancedStats.HighestScore.ToString("F1")%</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">أقل درجة:</span>
                                                    <span class="stat-value text-danger">@advancedStats.LowestScore.ToString("F1")%</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">الوسيط:</span>
                                                    <span class="stat-value text-info">@advancedStats.MedianScore.ToString("F1")%</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">الانحراف المعياري:</span>
                                                    <span class="stat-value text-warning">@advancedStats.StandardDeviation.ToString("F2")</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="stats-section">
                                            <h6 class="text-primary mb-3">إحصائيات الأسئلة</h6>
                                            <div class="stats-grid">
                                                <div class="stat-item">
                                                    <span class="stat-label">أصعب سؤال:</span>
                                                    <span class="stat-value text-danger">@advancedStats.HardestQuestionAccuracy.ToString("F1")% صحيح</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">أسهل سؤال:</span>
                                                    <span class="stat-value text-success">@advancedStats.EasiestQuestionAccuracy.ToString("F1")% صحيح</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">متوسط وقت الإجابة:</span>
                                                    <span class="stat-value text-info">@advancedStats.AverageTimePerQuestion.ToString("F1") دقيقة</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">معدل الإكمال:</span>
                                                    <span class="stat-value text-warning">@advancedStats.CompletionRate.ToString("F1")%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
    }
</div>

<style>
    .avatar-lg {
        width: 64px;
        height: 64px;
    }

    .avatar-sm {
        width: 32px;
        height: 32px;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }

    .bg-gradient-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }

    .bg-gradient-info {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    }

    .bg-gradient-warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    }

    .grade-item {
        padding: 10px;
        border-radius: 6px;
        background-color: #f8f9fa;
    }

    .trend-item {
        padding: 15px;
        border-radius: 8px;
        background-color: #f8f9fa;
        border-left: 4px solid #007bff;
    }

    .comparison-card {
        transition: all 0.3s ease;
    }

    .comparison-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }

    .performer-item {
        padding: 10px;
        border-radius: 6px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }

    .performer-item:hover {
        background-color: #e9ecef;
        transform: translateX(5px);
    }

    .stats-section {
        padding: 20px;
        border-radius: 8px;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
    }

    .stats-grid {
        display: grid;
        gap: 15px;
    }

    .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        background-color: white;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    .stat-label {
        font-weight: 500;
        color: #6c757d;
    }

    .stat-value {
        font-weight: bold;
        font-size: 1.1rem;
    }

    .metric-item {
        padding: 8px 0;
        border-bottom: 1px solid #e9ecef;
    }

    .metric-item:last-child {
        border-bottom: none;
    }
</style>

@code {
    private List<SubjectDto>? subjects;
    private List<ClassDto>? classes;
    private ExamReportSummaryDto reportSummary = new();
    private List<PerformanceTrendDto>? performanceTrend;
    private List<DetailedExamReportDto>? detailedReports;
    private List<ComparativeDataDto>? comparativeData;
    private List<StudentPerformanceDto>? topPerformers;
    private List<StudentPerformanceDto>? strugglingStudents;
    private List<StudentPerformanceDto>? improvingStudents;
    private AdvancedStatisticsDto? advancedStats;

    private bool isLoading = true;
    private string selectedReportType = "overview";
    private string selectedPeriod = "month";
    private string selectedSubjectId = "";
    private string selectedClassId = "";
    private DateTime startDate = DateTime.Now.AddMonths(-1);
    private DateTime endDate = DateTime.Now;

    protected override async Task OnInitializedAsync()
    {
        await LoadInitialData();
    }

    private async Task LoadInitialData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            await Task.WhenAll(
                LoadSubjects(),
                LoadClasses()
            );

            await LoadReportData();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadSubjects()
    {
        try
        {
            var subjectsList = await ApiService.GetSubjectsAsync();
            subjects = subjectsList?.ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading subjects: {ex.Message}");
        }
    }

    private async Task LoadClasses()
    {
        try
        {
            var classesList = await ApiService.GetClassesAsync();
            classes = classesList?.ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading classes: {ex.Message}");
        }
    }

    private async Task LoadReportData()
    {
        try
        {
            var filters = new Dictionary<string, string?>
            {
                ["reportType"] = selectedReportType,
                ["period"] = selectedPeriod,
                ["subjectId"] = string.IsNullOrEmpty(selectedSubjectId) ? null : selectedSubjectId,
                ["classId"] = string.IsNullOrEmpty(selectedClassId) ? null : selectedClassId,
                ["startDate"] = selectedPeriod == "custom" ? startDate.ToString("yyyy-MM-dd") : null,
                ["endDate"] = selectedPeriod == "custom" ? endDate.ToString("yyyy-MM-dd") : null
            };

            await Task.WhenAll(
                LoadReportSummary(filters),
                LoadSpecificReportData(filters)
            );
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading report data: {ex.Message}");
        }
    }

    private async Task LoadReportSummary(Dictionary<string, string?> filters)
    {
        try
        {
            reportSummary = await ApiService.GetExamReportSummaryAsync(filters) ?? new ExamReportSummaryDto();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading report summary: {ex.Message}");
        }
    }

    private async Task LoadSpecificReportData(Dictionary<string, string?> filters)
    {
        try
        {
            switch (selectedReportType)
            {
                case "overview":
                    performanceTrend = await ApiService.GetPerformanceTrendAsync(selectedPeriod);
                    break;
                case "detailed":
                    detailedReports = await ApiService.GetDetailedExamReportsAsync(filters);
                    break;
                case "comparative":
                    comparativeData = await ApiService.GetComparativeDataAsync(filters);
                    break;
                case "performance":
                    var performanceData = await ApiService.GetStudentPerformanceAsync(filters);
                    if (performanceData != null)
                    {
                        topPerformers = performanceData.Where(s => s.AverageScore >= 80).OrderByDescending(s => s.AverageScore).ToList();
                        strugglingStudents = performanceData.Where(s => s.AverageScore < 50).OrderBy(s => s.AverageScore).ToList();
                        improvingStudents = performanceData.Where(s => s.ImprovementRate > 0).OrderByDescending(s => s.ImprovementRate).ToList();
                    }
                    break;
                case "statistics":
                    advancedStats = await ApiService.GetAdvancedStatisticsAsync(filters);
                    break;
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading specific report data: {ex.Message}");
        }
    }

    private async Task GenerateReport()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة إنشاء التقارير المخصصة ستكون متاحة قريباً");
    }

    private async Task ExportAllReports()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة تصدير جميع التقارير ستكون متاحة قريباً");
    }

    private async Task ScheduleReport()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة جدولة التقارير ستكون متاحة قريباً");
    }

    private void ViewExamReport(int examId)
    {
        Navigation.NavigateTo($"/teacher/exams/{examId}/results");
    }

    private async Task ExportExamReport(int examId)
    {
        await JSRuntime.InvokeVoidAsync("alert", $"ميزة تصدير تقرير الامتحان {examId} ستكون متاحة قريباً");
    }

    private string GetGradeColor(string grade)
    {
        return grade switch
        {
            var g when g.Contains("ممتاز") => "bg-success",
            var g when g.Contains("جيد جداً") => "bg-info",
            var g when g.Contains("جيد") => "bg-primary",
            var g when g.Contains("مقبول") => "bg-warning",
            var g when g.Contains("ضعيف") => "bg-secondary",
            var g when g.Contains("راسب") => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetScoreColor(double score)
    {
        return score switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-info",
            >= 70 => "bg-primary",
            >= 60 => "bg-warning",
            >= 50 => "bg-secondary",
            _ => "bg-danger"
        };
    }

    private string GetMetricColor(string value)
    {
        if (double.TryParse(value.Replace("%", ""), out var numValue))
        {
            return numValue switch
            {
                >= 80 => "text-success",
                >= 60 => "text-info",
                >= 40 => "text-warning",
                _ => "text-danger"
            };
        }
        return "text-primary";
    }
}
