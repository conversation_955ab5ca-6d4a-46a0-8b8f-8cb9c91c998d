namespace Schools.Client.Models
{
    public class UserNotificationModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string Message { get; set; } = "";
        public string Type { get; set; } = "";
        public string Priority { get; set; } = "";
        public DateTime CreatedAt { get; set; }
        public DateTime? ReadAt { get; set; }
        public bool IsRead { get; set; }
        public string SenderId { get; set; } = "";
        public string SenderName { get; set; } = "";
        public string RecipientId { get; set; } = "";
        public string Category { get; set; } = "";
        public string Icon { get; set; } = "";
        public string Color { get; set; } = "";
        public string Link { get; set; } = "";
        public Dictionary<string, object> Data { get; set; } = new();
    }

    public class CreateNotificationModel
    {
        public string Title { get; set; } = "";
        public string Message { get; set; } = "";
        public string Type { get; set; } = "info";
        public string Priority { get; set; } = "normal";
        public string Category { get; set; } = "general";
        public List<string> Recipients { get; set; } = new();
        public string? Link { get; set; }
        public DateTime? ScheduledAt { get; set; }
        public Dictionary<string, object> Data { get; set; } = new();
    }

    public class NotificationFilterModel
    {
        public string? Type { get; set; }
        public string? Priority { get; set; }
        public string? Category { get; set; }
        public bool? IsRead { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class NotificationSettingsModel
    {
        public string UserId { get; set; } = "";
        public bool EmailNotifications { get; set; } = true;
        public bool PushNotifications { get; set; } = true;
        public bool SmsNotifications { get; set; } = false;
        public Dictionary<string, bool> CategorySettings { get; set; } = new();
        public Dictionary<string, bool> TypeSettings { get; set; } = new();
        public string PreferredLanguage { get; set; } = "ar";
        public string TimeZone { get; set; } = "Asia/Riyadh";
        public bool QuietHours { get; set; } = false;
        public TimeSpan QuietStart { get; set; } = new(22, 0, 0);
        public TimeSpan QuietEnd { get; set; } = new(7, 0, 0);
    }

    public class NotificationTemplateModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Type { get; set; } = "";
        public string Category { get; set; } = "";
        public string Subject { get; set; } = "";
        public string Body { get; set; } = "";
        public string Language { get; set; } = "ar";
        public bool IsActive { get; set; } = true;
        public Dictionary<string, object> Variables { get; set; } = new();
    }

    public class NotificationStatisticsModel
    {
        public int TotalSent { get; set; }
        public int TotalRead { get; set; }
        public int TotalUnread { get; set; }
        public double ReadRate { get; set; }
        public Dictionary<string, int> TypeDistribution { get; set; } = new();
        public Dictionary<string, int> CategoryDistribution { get; set; } = new();
        public Dictionary<string, int> DailyStats { get; set; } = new();
    }

    public class BulkNotificationModel
    {
        public string Title { get; set; } = "";
        public string Message { get; set; } = "";
        public string Type { get; set; } = "info";
        public string Priority { get; set; } = "normal";
        public string Category { get; set; } = "announcement";
        public string TargetAudience { get; set; } = ""; // all, students, teachers, parents, admins
        public List<string> SpecificRecipients { get; set; } = new();
        public List<string> ExcludedRecipients { get; set; } = new();
        public DateTime? ScheduledAt { get; set; }
        public bool SendEmail { get; set; } = false;
        public bool SendPush { get; set; } = true;
        public bool SendSms { get; set; } = false;
    }

    public static class NotificationTypes
    {
        public static readonly Dictionary<string, string> Types = new()
        {
            { "info", "معلومات" },
            { "warning", "تحذير" },
            { "error", "خطأ" },
            { "success", "نجاح" },
            { "reminder", "تذكير" },
            { "announcement", "إعلان" },
            { "alert", "تنبيه" }
        };

        public static readonly Dictionary<string, string> Icons = new()
        {
            { "info", "fas fa-info-circle" },
            { "warning", "fas fa-exclamation-triangle" },
            { "error", "fas fa-times-circle" },
            { "success", "fas fa-check-circle" },
            { "reminder", "fas fa-bell" },
            { "announcement", "fas fa-bullhorn" },
            { "alert", "fas fa-exclamation" }
        };

        public static readonly Dictionary<string, string> Colors = new()
        {
            { "info", "info" },
            { "warning", "warning" },
            { "error", "danger" },
            { "success", "success" },
            { "reminder", "primary" },
            { "announcement", "secondary" },
            { "alert", "danger" }
        };
    }

    public static class NotificationPriorities
    {
        public static readonly Dictionary<string, string> Priorities = new()
        {
            { "low", "منخفض" },
            { "normal", "عادي" },
            { "high", "مرتفع" },
            { "urgent", "عاجل" },
            { "critical", "حرج" }
        };

        public static readonly Dictionary<string, string> Colors = new()
        {
            { "low", "secondary" },
            { "normal", "primary" },
            { "high", "warning" },
            { "urgent", "danger" },
            { "critical", "dark" }
        };
    }

    public static class NotificationCategories
    {
        public static readonly Dictionary<string, string> Categories = new()
        {
            { "general", "عام" },
            { "academic", "أكاديمي" },
            { "administrative", "إداري" },
            { "financial", "مالي" },
            { "attendance", "حضور" },
            { "grades", "درجات" },
            { "events", "فعاليات" },
            { "assignments", "واجبات" },
            { "announcements", "إعلانات" },
            { "reminders", "تذكيرات" },
            { "system", "نظام" },
            { "security", "أمان" }
        };

        public static readonly Dictionary<string, string> Icons = new()
        {
            { "general", "fas fa-info" },
            { "academic", "fas fa-graduation-cap" },
            { "administrative", "fas fa-cog" },
            { "financial", "fas fa-dollar-sign" },
            { "attendance", "fas fa-calendar-check" },
            { "grades", "fas fa-chart-line" },
            { "events", "fas fa-calendar-alt" },
            { "assignments", "fas fa-tasks" },
            { "announcements", "fas fa-bullhorn" },
            { "reminders", "fas fa-bell" },
            { "system", "fas fa-server" },
            { "security", "fas fa-shield-alt" }
        };
    }

    public static class TargetAudiences
    {
        public static readonly Dictionary<string, string> Audiences = new()
        {
            { "all", "الجميع" },
            { "students", "الطلاب" },
            { "teachers", "المعلمون" },
            { "parents", "أولياء الأمور" },
            { "admins", "الإداريون" },
            { "staff", "الموظفون" },
            { "specific", "محدد" }
        };
    }

    public class NotificationChannelModel
    {
        public string Type { get; set; } = ""; // email, push, sms, in-app
        public bool IsEnabled { get; set; } = true;
        public Dictionary<string, object> Settings { get; set; } = new();
        public string Provider { get; set; } = "";
        public DateTime LastUsed { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
    }

    public class NotificationQueueModel
    {
        public int Id { get; set; }
        public string NotificationId { get; set; } = "";
        public string RecipientId { get; set; } = "";
        public string Channel { get; set; } = "";
        public string Status { get; set; } = ""; // pending, sent, failed, cancelled
        public DateTime ScheduledAt { get; set; }
        public DateTime? SentAt { get; set; }
        public string? ErrorMessage { get; set; }
        public int RetryCount { get; set; }
    }
}
