@page "/admin/reports"
@using Schools.Shared.Models
@using Schools.Shared.DTOs
@using Schools.Client.Services
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@attribute [Authorize(Roles = "Admin")]

<PageTitle>التقارير والإحصائيات</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير والإحصائيات
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Report Types -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="btn-group" role="group">
                                <input type="radio" class="btn-check" name="reportType" id="overview" @onchange="@(() => SetReportType("overview"))" checked>
                                <label class="btn btn-outline-primary" for="overview">نظرة عامة</label>

                                <input type="radio" class="btn-check" name="reportType" id="users" @onchange="@(() => SetReportType("users"))">
                                <label class="btn btn-outline-success" for="users">المستخدمين</label>

                                <input type="radio" class="btn-check" name="reportType" id="academic" @onchange="@(() => SetReportType("academic"))">
                                <label class="btn btn-outline-info" for="academic">الأكاديمي</label>

                                <input type="radio" class="btn-check" name="reportType" id="attendance" @onchange="@(() => SetReportType("attendance"))">
                                <label class="btn btn-outline-warning" for="attendance">الحضور</label>
                            </div>
                        </div>
                    </div>

                    @if (currentReportType == "overview")
                    {
                        <!-- Overview Report -->
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>@totalUsers</h4>
                                                <p class="mb-0">إجمالي المستخدمين</p>
                                            </div>
                                            <i class="fas fa-users fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>@totalStudents</h4>
                                                <p class="mb-0">عدد الطلاب</p>
                                            </div>
                                            <i class="fas fa-user-graduate fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-4">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>@totalTeachers</h4>
                                                <p class="mb-0">عدد المعلمين</p>
                                            </div>
                                            <i class="fas fa-chalkboard-teacher fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-4">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>@totalClasses</h4>
                                                <p class="mb-0">عدد الصفوف</p>
                                            </div>
                                            <i class="fas fa-school fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Charts Section -->
                        <div class="row">
                            <div class="col-lg-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>توزيع المستخدمين حسب الدور</h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="userRolesChart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>توزيع الطلاب حسب المرحلة</h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="studentsGradeChart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    else if (currentReportType == "users")
                    {
                        <!-- Users Report -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>تقرير المستخدمين المفصل</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>الدور</th>
                                                        <th>العدد</th>
                                                        <th>نشط</th>
                                                        <th>غير نشط</th>
                                                        <th>النسبة</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td><span class="badge bg-primary">الطلاب</span></td>
                                                        <td>@totalStudents</td>
                                                        <td>@(totalStudents - 15)</td>
                                                        <td>15</td>
                                                        <td>@(Math.Round((double)totalStudents / totalUsers * 100, 1))%</td>
                                                    </tr>
                                                    <tr>
                                                        <td><span class="badge bg-success">المعلمين</span></td>
                                                        <td>@totalTeachers</td>
                                                        <td>@(totalTeachers - 2)</td>
                                                        <td>2</td>
                                                        <td>@(Math.Round((double)totalTeachers / totalUsers * 100, 1))%</td>
                                                    </tr>
                                                    <tr>
                                                        <td><span class="badge bg-info">أولياء الأمور</span></td>
                                                        <td>@(totalUsers - totalStudents - totalTeachers - 10)</td>
                                                        <td>@(totalUsers - totalStudents - totalTeachers - 15)</td>
                                                        <td>5</td>
                                                        <td>@(Math.Round((double)(totalUsers - totalStudents - totalTeachers - 10) / totalUsers * 100, 1))%</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    else if (currentReportType == "academic")
                    {
                        <!-- Academic Report -->
                        <div class="row">
                            <div class="col-lg-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>المراحل الدراسية</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="list-group">
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                المرحلة الابتدائية
                                                <span class="badge bg-primary rounded-pill">6 صفوف</span>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                المرحلة المتوسطة
                                                <span class="badge bg-success rounded-pill">3 صفوف</span>
                                            </div>
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                المرحلة الثانوية
                                                <span class="badge bg-info rounded-pill">3 صفوف</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>المواد الدراسية</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h3 class="text-primary">24</h3>
                                                    <p class="mb-0">إجمالي المواد</p>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-center">
                                                    <h3 class="text-success">156</h3>
                                                    <p class="mb-0">إجمالي الساعات</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    else if (currentReportType == "attendance")
                    {
                        <!-- Attendance Report -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>تقرير الحضور (قريباً)</h5>
                                    </div>
                                    <div class="card-body text-center py-5">
                                        <i class="fas fa-calendar-check fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">تقرير الحضور</h5>
                                        <p class="text-muted">سيتم إضافة تقارير الحضور المفصلة قريباً</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Export Options -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>تصدير التقارير</h5>
                                </div>
                                <div class="card-body">
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-outline-success" @onclick="ExportToPDF">
                                            <i class="fas fa-file-pdf me-2"></i>
                                            تصدير PDF
                                        </button>
                                        <button class="btn btn-outline-primary" @onclick="ExportToExcel">
                                            <i class="fas fa-file-excel me-2"></i>
                                            تصدير Excel
                                        </button>
                                        <button class="btn btn-outline-info" @onclick="PrintReport">
                                            <i class="fas fa-print me-2"></i>
                                            طباعة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string currentReportType = "overview";
    private int totalUsers = 1250;
    private int totalStudents = 980;
    private int totalTeachers = 85;
    private int totalClasses = 45;

    protected override async Task OnInitializedAsync()
    {
        await LoadReportData();
    }

    private async Task LoadReportData()
    {
        try
        {
            // Load actual data from API
            // This is mock data for demonstration
            await Task.Delay(100);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
    }

    private void SetReportType(string reportType)
    {
        currentReportType = reportType;
        StateHasChanged();
    }

    private async Task ExportToPDF()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم إضافة تصدير PDF قريباً");
    }

    private async Task ExportToExcel()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم إضافة تصدير Excel قريباً");
    }

    private async Task PrintReport()
    {
        await JSRuntime.InvokeVoidAsync("window.print");
    }
}
