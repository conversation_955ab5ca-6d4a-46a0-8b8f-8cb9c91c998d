using System.ComponentModel.DataAnnotations;

namespace Schools.Shared.DTOs
{
    // Exam DTOs
    public class ExamDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public int ClassId { get; set; }
        public string ClassName { get; set; } = string.Empty;
        public string GradeName { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int DurationMinutes { get; set; }
        public int TotalMarks { get; set; }
        public int PassingMarks { get; set; }
        public int MaxAttempts { get; set; }
        public bool ShowResults { get; set; }
        public bool ShuffleQuestions { get; set; }
        public bool IsActive { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public int TotalQuestions { get; set; }
        public string Status { get; set; } = string.Empty;
        public List<ExamQuestionDto> Questions { get; set; } = new();
    }

    public class CreateExamDto
    {
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        public string? Description { get; set; }

        [Required]
        public int SubjectId { get; set; }

        [Required]
        public int ClassId { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        [Required]
        public int DurationMinutes { get; set; }

        [Required]
        public int TotalMarks { get; set; }

        [Required]
        public int PassingMarks { get; set; }

        public int MaxAttempts { get; set; } = 1;
        public bool ShowResults { get; set; } = true;
        public bool ShuffleQuestions { get; set; } = false;
    }

    public class UpdateExamDto
    {
        [StringLength(200)]
        public string? Title { get; set; }

        public string? Description { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? DurationMinutes { get; set; }
        public int? TotalMarks { get; set; }
        public int? PassingMarks { get; set; }
        public int? MaxAttempts { get; set; }
        public bool? ShowResults { get; set; }
        public bool? ShuffleQuestions { get; set; }
    }

    // Exam Question DTOs
    public class ExamQuestionDto
    {
        public int Id { get; set; }
        public string QuestionText { get; set; } = string.Empty;
        public string QuestionType { get; set; } = string.Empty;
        public int Points { get; set; }
        public int OrderIndex { get; set; }
        public List<QuestionOptionDto> Options { get; set; } = new();
    }

    public class CreateExamQuestionDto
    {
        [Required]
        [StringLength(1000)]
        public string QuestionText { get; set; } = string.Empty;

        [Required]
        public string QuestionType { get; set; } = string.Empty;

        [Required]
        public int Points { get; set; }

        public int OrderIndex { get; set; }

        public List<CreateQuestionOptionDto> Options { get; set; } = new();
    }

    public class UpdateExamQuestionDto
    {
        [StringLength(1000)]
        public string? QuestionText { get; set; }

        public int? Points { get; set; }
        public int? OrderIndex { get; set; }
    }

    // Question Option DTOs
    public class QuestionOptionDto
    {
        public int Id { get; set; }
        public string OptionText { get; set; } = string.Empty;
        public bool IsCorrect { get; set; }
        public int OrderIndex { get; set; }
    }

    public class CreateQuestionOptionDto
    {
        [Required]
        [StringLength(500)]
        public string OptionText { get; set; } = string.Empty;

        public bool IsCorrect { get; set; }
        public int OrderIndex { get; set; }
    }

    // Exam Attempt DTOs
    public class ExamAttemptDto
    {
        public int Id { get; set; }
        public int ExamId { get; set; }
        public string ExamTitle { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public int DurationMinutes { get; set; }
        public int TotalMarks { get; set; }
        public List<ExamQuestionDto> Questions { get; set; } = new();
    }

    public class SubmitExamDto
    {
        public List<StudentAnswerDto> Answers { get; set; } = new();
    }

    public class StudentAnswerDto
    {
        public int QuestionId { get; set; }
        public int? SelectedOptionId { get; set; }
        public string? TextAnswer { get; set; }
    }

    // Exam Result DTOs
    public class ExamResultDto
    {
        public int AttemptId { get; set; }
        public string ExamTitle { get; set; } = string.Empty;
        public string SubjectName { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public decimal Score { get; set; }
        public int TotalMarks { get; set; }
        public decimal Percentage { get; set; }
        public bool IsPassed { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? CompletedAt { get; set; }
        public double TimeTaken { get; set; }
        public int CorrectAnswers { get; set; }
        public int TotalQuestions { get; set; }
        public string Grade { get; set; } = string.Empty;
    }

    // Available Exam DTOs
    public class AvailableExamDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int DurationMinutes { get; set; }
        public int TotalMarks { get; set; }
        public int TotalQuestions { get; set; }
        public int MaxAttempts { get; set; }
        public int AttemptsUsed { get; set; }
        public bool IsAvailable { get; set; }
        public bool CanTakeExam { get; set; }
        public DateTime? LastAttemptDate { get; set; }
        public double? BestScore { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    // Student Grade DTOs
    public class StudentGradeDto
    {
        public int Id { get; set; }
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public string ExamType { get; set; } = string.Empty;
        public decimal Score { get; set; }
        public decimal MaxScore { get; set; }
        public decimal Percentage { get; set; }
        public string Grade { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public string Semester { get; set; } = string.Empty;
        public string AcademicYearName { get; set; } = string.Empty;
        public string? Notes { get; set; }
    }

    // Create Grade DTO
    public class CreateGradeDto
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int SubjectId { get; set; }

        [Required]
        public int AcademicYearId { get; set; }

        [Required]
        public string ExamType { get; set; } = string.Empty;

        [Required]
        public decimal Score { get; set; }

        [Required]
        public decimal MaxScore { get; set; }

        [Required]
        public DateTime Date { get; set; }

        [Required]
        public string Semester { get; set; } = string.Empty;

        public string? Notes { get; set; }
    }

    // Transfer Student DTO
    public class TransferStudentDto
    {
        public int? NewClassId { get; set; }
        public int? NewSectionId { get; set; }
        public int? NewAcademicGradeId { get; set; }
        public string? TransferReason { get; set; }
    }

    // Withdraw Student DTO
    public class WithdrawStudentDto
    {
        [Required]
        public DateTime WithdrawalDate { get; set; }

        [Required]
        public string WithdrawalReason { get; set; } = string.Empty;
    }

    // Class Student DTO
    public class ClassStudentDto
    {
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public DateTime EnrollmentDate { get; set; }
    }
}
