@page "/accounting/dashboard"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Accountant,Admin")]

<PageTitle>لوحة تحكم المحاسبة - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-calculator text-primary me-2"></i>
                        لوحة تحكم المحاسبة
                    </h2>
                    <p class="text-muted mb-0">نظرة شاملة على الوضع المالي للمدرسة</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary" @onclick="RefreshData">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث البيانات
                    </button>
                    <button class="btn btn-outline-success" @onclick="ExportReports">
                        <i class="fas fa-download me-2"></i>
                        تصدير التقارير
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل البيانات المالية...</p>
        </div>
    }
    else
    {
        <!-- Financial Summary Cards -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100 bg-gradient-primary text-white">
                    <div class="card-body text-center">
                        <div class="avatar-lg bg-white bg-opacity-25 text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-chart-line fa-lg"></i>
                        </div>
                        <h3 class="mb-1">@dashboardData.TotalAssets.ToString("C")</h3>
                        <p class="mb-0 opacity-75">إجمالي الأصول</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100 bg-gradient-danger text-white">
                    <div class="card-body text-center">
                        <div class="avatar-lg bg-white bg-opacity-25 text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-credit-card fa-lg"></i>
                        </div>
                        <h3 class="mb-1">@dashboardData.TotalLiabilities.ToString("C")</h3>
                        <p class="mb-0 opacity-75">إجمالي الخصوم</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100 bg-gradient-success text-white">
                    <div class="card-body text-center">
                        <div class="avatar-lg bg-white bg-opacity-25 text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-piggy-bank fa-lg"></i>
                        </div>
                        <h3 class="mb-1">@dashboardData.TotalEquity.ToString("C")</h3>
                        <p class="mb-0 opacity-75">حقوق الملكية</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100 bg-gradient-info text-white">
                    <div class="card-body text-center">
                        <div class="avatar-lg bg-white bg-opacity-25 text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-coins fa-lg"></i>
                        </div>
                        <h3 class="mb-1">@dashboardData.NetIncome.ToString("C")</h3>
                        <p class="mb-0 opacity-75">صافي الدخل</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Performance -->
        <div class="row g-3 mb-4">
            <div class="col-md-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-arrow-up text-success me-2"></i>
                            الإيرادات الشهرية
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <h2 class="text-success mb-2">@dashboardData.MonthlyRevenue.ToString("C")</h2>
                        <div class="progress mb-3" style="height: 10px;">
                            <div class="progress-bar bg-success" style="width: @GetRevenuePercentage()%"></div>
                        </div>
                        <small class="text-muted">مقارنة بالشهر الماضي</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-arrow-down text-danger me-2"></i>
                            المصروفات الشهرية
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <h2 class="text-danger mb-2">@dashboardData.MonthlyExpenses.ToString("C")</h2>
                        <div class="progress mb-3" style="height: 10px;">
                            <div class="progress-bar bg-danger" style="width: @GetExpensePercentage()%"></div>
                        </div>
                        <small class="text-muted">مقارنة بالشهر الماضي</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-wallet text-primary me-2"></i>
                            الأرصدة النقدية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>الخزينة:</span>
                            <strong class="text-primary">@dashboardData.CashBalance.ToString("C")</strong>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>البنك:</span>
                            <strong class="text-info">@dashboardData.BankBalance.ToString("C")</strong>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between align-items-center">
                            <span><strong>الإجمالي:</strong></span>
                            <strong class="text-success">@((dashboardData.CashBalance + dashboardData.BankBalance).ToString("C"))</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Student Fees Summary -->
        <div class="row g-3 mb-4">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-graduation-cap me-2"></i>
                            رسوم الطلاب
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-6">
                                <div class="text-center p-2 bg-light rounded">
                                    <h6 class="mb-1 text-primary">@dashboardData.StudentFeesCollected.ToString("C")</h6>
                                    <small class="text-muted">رسوم محصلة</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-2 bg-light rounded">
                                    <h6 class="mb-1 text-danger">@dashboardData.StudentFeesOutstanding.ToString("C")</h6>
                                    <small class="text-muted">رسوم مستحقة</small>
                                </div>
                            </div>
                            <div class="col-12 mt-2">
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" role="progressbar"
                                         style="width: @GetCollectionPercentage()%"
                                         aria-valuenow="@GetCollectionPercentage()" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <small class="text-muted">نسبة التحصيل: @GetCollectionPercentage().ToString("F1")%</small>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-sm btn-warning w-100" @onclick="@(() => Navigation.NavigateTo("/accounting/fees"))">
                                <i class="fas fa-eye me-1"></i>
                                عرض التفاصيل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            إحصائيات الطلاب
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-6">
                                <div class="text-center p-2 bg-light rounded">
                                    <h6 class="mb-1 text-success">@dashboardData.StudentsWithFullPayment</h6>
                                    <small class="text-muted">مدفوع بالكامل</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-2 bg-light rounded">
                                    <h6 class="mb-1 text-warning">@dashboardData.StudentsWithPartialPayment</h6>
                                    <small class="text-muted">مدفوع جزئياً</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-2 bg-light rounded">
                                    <h6 class="mb-1 text-secondary">@dashboardData.StudentsWithNoPayment</h6>
                                    <small class="text-muted">غير مدفوع</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-2 bg-light rounded">
                                    <h6 class="mb-1 text-danger">@dashboardData.StudentsOverdue</h6>
                                    <small class="text-muted">متأخر</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions & Pending Items -->
        <div class="row g-4 mb-4">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-6">
                                <button class="btn btn-outline-primary w-100" @onclick="@(() => Navigation.NavigateTo("/accounting/receipt-vouchers/new"))">
                                    <i class="fas fa-plus-circle me-2"></i>
                                    سند قبض
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-danger w-100" @onclick="@(() => Navigation.NavigateTo("/accounting/payment-vouchers/new"))">
                                    <i class="fas fa-minus-circle me-2"></i>
                                    سند صرف
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-success w-100" @onclick="@(() => Navigation.NavigateTo("/accounting/journal-entries/new"))">
                                    <i class="fas fa-book me-2"></i>
                                    قيد يومي
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-info w-100" @onclick="@(() => Navigation.NavigateTo("/accounting/reports"))">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    التقارير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            العمليات المعلقة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>سندات معلقة:</span>
                            <span class="badge bg-warning text-dark fs-6">@dashboardData.PendingVouchers</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>قيود غير مرحلة:</span>
                            <span class="badge bg-danger fs-6">@dashboardData.UnpostedEntries</span>
                        </div>
                        <button class="btn btn-warning w-100" @onclick="ViewPendingItems">
                            <i class="fas fa-eye me-2"></i>
                            عرض العمليات المعلقة
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Transactions & Top Expenses -->
        <div class="row g-4">
            <div class="col-md-8">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-history me-2 text-primary"></i>
                                آخر العمليات
                            </h5>
                            <button class="btn btn-sm btn-outline-primary" @onclick="@(() => Navigation.NavigateTo("/accounting/transactions"))">
                                عرض الكل
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (dashboardData.RecentTransactions?.Any() == true)
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>الوصف</th>
                                            <th>النوع</th>
                                            <th>المبلغ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var transaction in dashboardData.RecentTransactions.Take(5))
                                        {
                                            <tr>
                                                <td>@transaction.Date.ToString("dd/MM/yyyy")</td>
                                                <td>@transaction.Description</td>
                                                <td>
                                                    <span class="badge @GetTransactionTypeBadge(transaction.Type)">
                                                        @GetTransactionTypeText(transaction.Type)
                                                    </span>
                                                </td>
                                                <td class="@GetAmountClass(transaction.Type)">
                                                    @transaction.Amount.ToString("C")
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد عمليات حديثة</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie me-2 text-danger"></i>
                            أكبر المصروفات
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (dashboardData.TopExpenses?.Any() == true)
                        {
                            @foreach (var expense in dashboardData.TopExpenses.Take(5))
                            {
                                <div class="expense-item mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span class="fw-bold">@expense.AccountName</span>
                                        <span class="text-danger">@expense.Amount.ToString("C")</span>
                                    </div>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-danger" style="width: @expense.Percentage%"></div>
                                    </div>
                                    <small class="text-muted">@expense.Percentage.ToString("F1")% من إجمالي المصروفات</small>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد بيانات مصروفات</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .avatar-lg {
        width: 64px;
        height: 64px;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }

    .bg-gradient-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }

    .bg-gradient-info {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    }

    .bg-gradient-danger {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    }

    .expense-item {
        padding: 10px;
        border-radius: 6px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }

    .expense-item:hover {
        background-color: #e9ecef;
        transform: translateX(5px);
    }

    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }
</style>

@code {
    private AccountingDashboardDto dashboardData = new();
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            dashboardData = await ApiService.GetAccountingDashboardAsync() ?? new AccountingDashboardDto();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshData()
    {
        await LoadDashboardData();
        await JSRuntime.InvokeVoidAsync("alert", "تم تحديث البيانات بنجاح");
    }

    private async Task ExportReports()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة تصدير التقارير ستكون متاحة قريباً");
    }

    private void ViewPendingItems()
    {
        Navigation.NavigateTo("/accounting/pending-items");
    }

    private double GetRevenuePercentage()
    {
        // حساب نسبة الإيرادات مقارنة بالهدف أو الشهر الماضي
        return Math.Min(100, (double)(dashboardData.MonthlyRevenue / Math.Max(1, dashboardData.MonthlyExpenses)) * 50);
    }

    private double GetExpensePercentage()
    {
        // حساب نسبة المصروفات مقارنة بالإيرادات
        return Math.Min(100, (double)(dashboardData.MonthlyExpenses / Math.Max(1, dashboardData.MonthlyRevenue)) * 100);
    }

    private string GetTransactionTypeBadge(string type)
    {
        return type.ToLower() switch
        {
            "receipt" => "bg-success",
            "payment" => "bg-danger",
            "journal" => "bg-info",
            _ => "bg-secondary"
        };
    }

    private string GetTransactionTypeText(string type)
    {
        return type.ToLower() switch
        {
            "receipt" => "قبض",
            "payment" => "صرف",
            "journal" => "قيد",
            _ => "أخرى"
        };
    }

    private string GetAmountClass(string type)
    {
        return type.ToLower() switch
        {
            "receipt" => "text-success fw-bold",
            "payment" => "text-danger fw-bold",
            _ => "fw-bold"
        };
    }

    private double GetCollectionPercentage()
    {
        var totalFees = dashboardData.StudentFeesCollected + dashboardData.StudentFeesOutstanding;
        if (totalFees == 0) return 0;
        return (double)(dashboardData.StudentFeesCollected / totalFees) * 100;
    }
}
