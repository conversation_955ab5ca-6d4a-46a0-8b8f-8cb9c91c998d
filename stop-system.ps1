# PowerShell script to stop the Schools Management System

Write-Host "🛑 إيقاف نظام إدارة المدارس..." -ForegroundColor Red
Write-Host "================================================" -ForegroundColor Cyan

# Function to kill processes by name
function Stop-ProcessByName {
    param([string]$ProcessName)
    
    $processes = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue
    if ($processes) {
        Write-Host "🔄 إيقاف عمليات $ProcessName..." -ForegroundColor Yellow
        $processes | Stop-Process -Force
        Write-Host "✅ تم إيقاف عمليات $ProcessName" -ForegroundColor Green
    } else {
        Write-Host "ℹ️ لا توجد عمليات $ProcessName قيد التشغيل" -ForegroundColor Gray
    }
}

# Stop dotnet processes
Write-Host "🔍 البحث عن عمليات .NET..." -ForegroundColor Yellow

# Kill dotnet processes that might be running our applications
$dotnetProcesses = Get-Process -Name "dotnet" -ErrorAction SilentlyContinue | Where-Object {
    $_.CommandLine -like "*Schools.API*" -or $_.CommandLine -like "*Schools.Client*"
}

if ($dotnetProcesses) {
    Write-Host "🔄 إيقاف عمليات النظام..." -ForegroundColor Yellow
    $dotnetProcesses | Stop-Process -Force
    Write-Host "✅ تم إيقاف عمليات النظام" -ForegroundColor Green
} else {
    Write-Host "ℹ️ لا توجد عمليات للنظام قيد التشغيل" -ForegroundColor Gray
}

# Also try to kill any remaining dotnet processes
Stop-ProcessByName "dotnet"

# Kill any cmd windows that might be running our applications
$cmdProcesses = Get-Process -Name "cmd" -ErrorAction SilentlyContinue | Where-Object {
    $_.MainWindowTitle -like "*Schools*"
}

if ($cmdProcesses) {
    Write-Host "🔄 إيقاف نوافذ الأوامر..." -ForegroundColor Yellow
    $cmdProcesses | Stop-Process -Force
    Write-Host "✅ تم إيقاف نوافذ الأوامر" -ForegroundColor Green
}

# Kill any PowerShell windows that might be running our applications
$psProcesses = Get-Process -Name "powershell" -ErrorAction SilentlyContinue | Where-Object {
    $_.MainWindowTitle -like "*Schools*"
}

if ($psProcesses) {
    Write-Host "🔄 إيقاف نوافذ PowerShell..." -ForegroundColor Yellow
    $psProcesses | Stop-Process -Force
    Write-Host "✅ تم إيقاف نوافذ PowerShell" -ForegroundColor Green
}

Write-Host ""
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "✅ تم إيقاف نظام إدارة المدارس بنجاح!" -ForegroundColor Green
Write-Host ""
Write-Host "💡 لإعادة تشغيل النظام:" -ForegroundColor Yellow
Write-Host "   • قم بتشغيل start-system.ps1" -ForegroundColor White
Write-Host "   • أو start-system.bat" -ForegroundColor White
Write-Host "================================================" -ForegroundColor Cyan

# Keep the script window open for a moment
Start-Sleep -Seconds 2
