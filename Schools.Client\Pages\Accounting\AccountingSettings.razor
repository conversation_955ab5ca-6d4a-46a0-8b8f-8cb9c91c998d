@page "/accounting/settings"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Admin")]

<PageTitle>إعدادات النظام المحاسبي - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-cogs text-primary me-2"></i>
                        إعدادات النظام المحاسبي
                    </h2>
                    <p class="text-muted mb-0">إعدادات وتكوين النظام المحاسبي والمالي</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-success" @onclick="SaveAllSettings" disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        else
                        {
                            <i class="fas fa-save me-2"></i>
                        }
                        حفظ جميع الإعدادات
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل الإعدادات...</p>
        </div>
    }
    else
    {
        <div class="row">
            <!-- General Settings -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات العامة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">اسم المؤسسة</label>
                            <input @bind="settings.InstitutionName" class="form-control" placeholder="اسم المدرسة أو المؤسسة" />
                        </div>

                        <div class="mb-3">
                            <label class="form-label">العملة الافتراضية</label>
                            <select @bind="settings.DefaultCurrency" class="form-select">
                                <option value="SAR">ريال سعودي (SAR)</option>
                                <option value="USD">دولار أمريكي (USD)</option>
                                <option value="EUR">يورو (EUR)</option>
                                <option value="AED">درهم إماراتي (AED)</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">السنة المالية</label>
                            <select @bind="settings.FinancialYear" class="form-select">
                                @for (int year = DateTime.Now.Year - 2; year <= DateTime.Now.Year + 2; year++)
                                {
                                    <option value="@year">@year - @(year + 1)</option>
                                }
                            </select>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input @bind="settings.EnableAutoBackup" type="checkbox" class="form-check-input" />
                                <label class="form-check-label">تفعيل النسخ الاحتياطي التلقائي</label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input @bind="settings.RequireApprovalForVouchers" type="checkbox" class="form-check-input" />
                                <label class="form-check-label">طلب اعتماد للسندات المالية</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fee Settings -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-dollar-sign me-2"></i>
                            إعدادات الرسوم
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">فترة السماح للدفع (بالأيام)</label>
                            <input @bind="settings.PaymentGracePeriod" type="number" class="form-control" min="0" max="365" />
                            <small class="text-muted">عدد الأيام المسموحة بعد تاريخ الاستحقاق</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">رسوم التأخير (نسبة مئوية)</label>
                            <div class="input-group">
                                <input @bind="settings.LateFeePercentage" type="number" class="form-control" min="0" max="100" step="0.1" />
                                <span class="input-group-text">%</span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input @bind="settings.AllowPartialPayments" type="checkbox" class="form-check-input" />
                                <label class="form-check-label">السماح بالدفعات الجزئية</label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input @bind="settings.AutoSendReminders" type="checkbox" class="form-check-input" />
                                <label class="form-check-label">إرسال تذكيرات تلقائية</label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">تكرار التذكيرات (بالأيام)</label>
                            <input @bind="settings.ReminderFrequency" type="number" class="form-control" min="1" max="30" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <!-- Accounting Settings -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            الإعدادات المحاسبية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">حساب الخزينة الافتراضي</label>
                            <select @bind="settings.DefaultCashAccountId" class="form-select">
                                <option value="0">اختر الحساب</option>
                                @foreach (var account in cashAccounts)
                                {
                                    <option value="@account.Id">@account.AccountCode - @account.AccountName</option>
                                }
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">حساب الإيرادات الافتراضي</label>
                            <select @bind="settings.DefaultRevenueAccountId" class="form-select">
                                <option value="0">اختر الحساب</option>
                                @foreach (var account in revenueAccounts)
                                {
                                    <option value="@account.Id">@account.AccountCode - @account.AccountName</option>
                                }
                            </select>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input @bind="settings.AutoGenerateVoucherNumbers" type="checkbox" class="form-check-input" />
                                <label class="form-check-label">إنشاء أرقام السندات تلقائياً</label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">بادئة أرقام سندات القبض</label>
                            <input @bind="settings.ReceiptVoucherPrefix" class="form-control" placeholder="مثال: RV" />
                        </div>

                        <div class="mb-3">
                            <label class="form-label">بادئة أرقام سندات الصرف</label>
                            <input @bind="settings.PaymentVoucherPrefix" class="form-control" placeholder="مثال: PV" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notification Settings -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-bell me-2"></i>
                            إعدادات التنبيهات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="form-check">
                                <input @bind="settings.EmailNotifications" type="checkbox" class="form-check-input" />
                                <label class="form-check-label">تفعيل التنبيهات عبر البريد الإلكتروني</label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input @bind="settings.SmsNotifications" type="checkbox" class="form-check-input" />
                                <label class="form-check-label">تفعيل التنبيهات عبر الرسائل النصية</label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">بريد إلكتروني للإشعارات المالية</label>
                            <input @bind="settings.FinanceNotificationEmail" type="email" class="form-control" placeholder="<EMAIL>" />
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input @bind="settings.NotifyOnOverduePayments" type="checkbox" class="form-check-input" />
                                <label class="form-check-label">تنبيه عند تأخر المدفوعات</label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input @bind="settings.NotifyOnLowBalance" type="checkbox" class="form-check-input" />
                                <label class="form-check-label">تنبيه عند انخفاض الرصيد</label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">حد الرصيد المنخفض</label>
                            <div class="input-group">
                                <input @bind="settings.LowBalanceThreshold" type="number" class="form-control" min="0" step="100" />
                                <span class="input-group-text">@settings.DefaultCurrency</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <h6 class="text-primary mb-1">@systemInfo.TotalStudents</h6>
                                    <small class="text-muted">إجمالي الطلاب</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <h6 class="text-success mb-1">@systemInfo.TotalVouchers</h6>
                                    <small class="text-muted">إجمالي السندات</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <h6 class="text-info mb-1">@systemInfo.TotalAccounts</h6>
                                    <small class="text-muted">إجمالي الحسابات</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <h6 class="text-warning mb-1">@systemInfo.LastBackup</h6>
                                    <small class="text-muted">آخر نسخة احتياطية</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private AccountingSettingsDto settings = new();
    private List<AccountDto> cashAccounts = new();
    private List<AccountDto> revenueAccounts = new();
    private SystemInfoDto systemInfo = new();

    private bool isLoading = true;
    private bool isSaving = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // Load settings
            settings = await ApiService.GetAccountingSettingsAsync() ?? new AccountingSettingsDto();

            // Load accounts
            var allAccounts = await ApiService.GetAccountsAsync() ?? new List<AccountDto>();
            cashAccounts = allAccounts.Where(a => a.AccountType == AccountType.Asset && a.IsActive).ToList();
            revenueAccounts = allAccounts.Where(a => a.AccountType == AccountType.Revenue && a.IsActive).ToList();

            // Load system info
            systemInfo = await ApiService.GetSystemInfoAsync() ?? new SystemInfoDto();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task SaveAllSettings()
    {
        try
        {
            isSaving = true;
            StateHasChanged();

            var success = await ApiService.SaveAccountingSettingsAsync(settings);
            if (success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "تم حفظ الإعدادات بنجاح");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ أثناء حفظ الإعدادات");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ الإعدادات: {ex.Message}");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    // DTOs for settings
    public class AccountingSettingsDto
    {
        public string InstitutionName { get; set; } = "مدرسة النموذجية";
        public string DefaultCurrency { get; set; } = "SAR";
        public int FinancialYear { get; set; } = DateTime.Now.Year;
        public bool EnableAutoBackup { get; set; } = true;
        public bool RequireApprovalForVouchers { get; set; } = true;

        // Fee Settings
        public int PaymentGracePeriod { get; set; } = 30;
        public decimal LateFeePercentage { get; set; } = 5.0m;
        public bool AllowPartialPayments { get; set; } = true;
        public bool AutoSendReminders { get; set; } = true;
        public int ReminderFrequency { get; set; } = 7;

        // Accounting Settings
        public int DefaultCashAccountId { get; set; }
        public int DefaultRevenueAccountId { get; set; }
        public bool AutoGenerateVoucherNumbers { get; set; } = true;
        public string ReceiptVoucherPrefix { get; set; } = "RV";
        public string PaymentVoucherPrefix { get; set; } = "PV";

        // Notification Settings
        public bool EmailNotifications { get; set; } = true;
        public bool SmsNotifications { get; set; } = false;
        public string FinanceNotificationEmail { get; set; } = "";
        public bool NotifyOnOverduePayments { get; set; } = true;
        public bool NotifyOnLowBalance { get; set; } = true;
        public decimal LowBalanceThreshold { get; set; } = 10000;
    }

    public class SystemInfoDto
    {
        public int TotalStudents { get; set; } = 0;
        public int TotalVouchers { get; set; } = 0;
        public int TotalAccounts { get; set; } = 0;
        public string LastBackup { get; set; } = "لم يتم";
    }
}
