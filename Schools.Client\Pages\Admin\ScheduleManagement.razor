@page "/admin/schedule"
@using Schools.Shared.Models
@using Schools.Shared.DTOs
@using Schools.Client.Services
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Admin")]

<PageTitle>إدارة الجداول الدراسية</PageTitle>

<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-success text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="text-white mb-1">
                                <i class="fas fa-calendar-alt me-2"></i>
                                إدارة الجداول الدراسية
                            </h2>
                            <p class="text-white-75 mb-0">إنشاء وإدارة الجداول الدراسية للصفوف والمعلمين</p>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-table fa-3x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Controls -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر وإعدادات الجدول
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label class="form-label">الصف</label>
                            <select class="form-select" @bind="selectedClass" @bind:after="LoadSchedule">
                                <option value="">اختر الصف</option>
                                @foreach (var classItem in availableClasses)
                                {
                                    <option value="@classItem.Id">@classItem.Name</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">المعلم</label>
                            <select class="form-select" @bind="selectedTeacher" @bind:after="LoadSchedule">
                                <option value="">اختر المعلم</option>
                                @foreach (var teacher in availableTeachers)
                                {
                                    <option value="@teacher.Id">@teacher.Name</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">الأسبوع</label>
                            <input type="week" class="form-control" @bind="selectedWeek" @bind:after="LoadSchedule">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button class="btn btn-primary" @onclick="ShowAddScheduleModal">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة حصة جديدة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Schedule Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-week me-2"></i>
                        الجدول الأسبوعي
                    </h5>
                    <div class="btn-group">
                        <button class="btn btn-outline-success btn-sm" @onclick="ExportSchedule">
                            <i class="fas fa-file-excel me-2"></i>
                            تصدير
                        </button>
                        <button class="btn btn-outline-primary btn-sm" @onclick="PrintSchedule">
                            <i class="fas fa-print me-2"></i>
                            طباعة
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-4">
                            <div class="spinner-border text-success" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-bordered schedule-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="time-column">الوقت</th>
                                        <th>الأحد</th>
                                        <th>الاثنين</th>
                                        <th>الثلاثاء</th>
                                        <th>الأربعاء</th>
                                        <th>الخميس</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var timeSlot in timeSlots)
                                    {
                                        <tr>
                                            <td class="time-column fw-bold">@timeSlot</td>
                                            @for (int day = 0; day < 5; day++)
                                            {
                                                var schedule = GetScheduleForSlot(timeSlot, day);
                                                <td class="schedule-cell" @onclick="() => EditScheduleSlot(timeSlot, day)">
                                                    @if (schedule != null)
                                                    {
                                                        <div class="schedule-item bg-@GetSubjectColor(schedule.Subject)">
                                                            <div class="fw-bold">@schedule.Subject</div>
                                                            <div class="small">@schedule.Teacher</div>
                                                            <div class="small text-muted">@schedule.Room</div>
                                                        </div>
                                                    }
                                                    else
                                                    {
                                                        <div class="schedule-empty">
                                                            <i class="fas fa-plus text-muted"></i>
                                                        </div>
                                                    }
                                                </td>
                                            }
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Schedule Modal -->
<div class="modal fade" id="scheduleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@(editingSchedule?.Id > 0 ? "تعديل الحصة" : "إضافة حصة جديدة")</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <EditForm Model="editingSchedule" OnValidSubmit="SaveSchedule">
                    <DataAnnotationsValidator />
                    <div class="mb-3">
                        <label class="form-label">المادة</label>
                        <select class="form-select" @bind="editingSchedule!.Subject" required>
                            <option value="">اختر المادة</option>
                            <option value="الرياضيات">الرياضيات</option>
                            <option value="العلوم">العلوم</option>
                            <option value="اللغة العربية">اللغة العربية</option>
                            <option value="اللغة الإنجليزية">اللغة الإنجليزية</option>
                            <option value="التاريخ">التاريخ</option>
                            <option value="الجغرافيا">الجغرافيا</option>
                            <option value="التربية الإسلامية">التربية الإسلامية</option>
                            <option value="التربية البدنية">التربية البدنية</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">المعلم</label>
                        <select class="form-select" @bind="editingSchedule!.Teacher" required>
                            <option value="">اختر المعلم</option>
                            @foreach (var teacher in availableTeachers)
                            {
                                <option value="@teacher.Name">@teacher.Name</option>
                            }
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">القاعة</label>
                        <input type="text" class="form-control" @bind="editingSchedule!.Room" placeholder="رقم القاعة" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">اليوم</label>
                        <select class="form-select" @bind="editingSchedule!.DayOfWeek" required>
                            <option value="0">الأحد</option>
                            <option value="1">الاثنين</option>
                            <option value="2">الثلاثاء</option>
                            <option value="3">الأربعاء</option>
                            <option value="4">الخميس</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوقت</label>
                        <select class="form-select" @bind="editingSchedule!.TimeSlot" required>
                            @foreach (var slot in timeSlots)
                            {
                                <option value="@slot">@slot</option>
                            }
                        </select>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                        @if (editingSchedule?.Id > 0)
                        {
                            <button type="button" class="btn btn-danger" @onclick="DeleteSchedule">حذف</button>
                        }
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>

<style>
    .schedule-table {
        font-size: 0.9rem;
    }

    .time-column {
        width: 120px;
        background-color: #f8f9fa;
    }

    .schedule-cell {
        height: 80px;
        vertical-align: middle;
        cursor: pointer;
        position: relative;
    }

    .schedule-cell:hover {
        background-color: #f8f9fa;
    }

    .schedule-item {
        padding: 8px;
        border-radius: 4px;
        text-align: center;
        color: white;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .schedule-empty {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0.3;
    }

    .schedule-empty:hover {
        opacity: 0.7;
    }
</style>

@code {
    private List<ClassDto> availableClasses = new();
    private List<TeacherDto> availableTeachers = new();
    private List<LocalScheduleDto> schedules = new();
    private bool isLoading = true;

    private string selectedClass = string.Empty;
    private string selectedTeacher = string.Empty;
    private string selectedWeek = DateTime.Now.ToString("yyyy-\\WW");

    private LocalScheduleDto editingSchedule = new();

    private readonly string[] timeSlots = {
        "08:00 - 08:45",
        "09:00 - 09:45",
        "10:15 - 11:00",
        "11:15 - 12:00",
        "12:15 - 13:00",
        "14:00 - 14:45",
        "15:00 - 15:45"
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            // Load data from API
            var apiClasses = await ApiService.GetClassesAsync();
            availableClasses = apiClasses.Select(c => new ClassDto
            {
                Id = c.Id,
                Name = c.Name
            }).ToList();

            var apiTeachers = await ApiService.GetTeachersAsync();
            availableTeachers = apiTeachers.Select(t => new TeacherDto
            {
                Id = t.Id,
                Name = t.FirstName + " " + t.LastName
            }).ToList();

            await LoadSchedule();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
            // Fallback to mock data if API fails
            availableClasses = GenerateMockClasses();
            availableTeachers = GenerateMockTeachers();
            await LoadSchedule();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadSchedule()
    {
        try
        {
            if (!string.IsNullOrEmpty(selectedClass))
            {
                var apiSchedules = await ApiService.GetSchedulesAsync(int.Parse(selectedClass));
                schedules = apiSchedules.Select(s => new LocalScheduleDto
                {
                    Id = s.Id,
                    Subject = s.SubjectName,
                    Teacher = s.TeacherName,
                    Room = s.Room,
                    DayOfWeek = (int)s.DayOfWeek,
                    TimeSlot = s.TimeSlot,
                    ClassId = s.ClassId
                }).ToList();
            }
            else
            {
                schedules = GenerateMockSchedule();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الجدول: {ex.Message}");
            schedules = GenerateMockSchedule();
        }
        StateHasChanged();
    }

    private List<ClassDto> GenerateMockClasses()
    {
        return new List<ClassDto>
        {
            new ClassDto { Id = 1, Name = "الصف الأول أ" },
            new ClassDto { Id = 2, Name = "الصف الثاني أ" },
            new ClassDto { Id = 3, Name = "الصف الثالث أ" },
            new ClassDto { Id = 4, Name = "الصف الرابع أ" },
            new ClassDto { Id = 5, Name = "الصف الخامس أ" },
            new ClassDto { Id = 6, Name = "الصف السادس أ" }
        };
    }

    private List<TeacherDto> GenerateMockTeachers()
    {
        return new List<TeacherDto>
        {
            new TeacherDto { Id = "1", Name = "أ. محمد أحمد" },
            new TeacherDto { Id = "2", Name = "أ. فاطمة علي" },
            new TeacherDto { Id = "3", Name = "أ. عبدالله محمد" },
            new TeacherDto { Id = "4", Name = "أ. مريم أحمد" },
            new TeacherDto { Id = "5", Name = "أ. يوسف علي" }
        };
    }

    private List<LocalScheduleDto> GenerateMockSchedule()
    {
        var mockSchedule = new List<LocalScheduleDto>();
        var subjects = new[] { "الرياضيات", "العلوم", "اللغة العربية", "اللغة الإنجليزية", "التاريخ" };
        var teachers = availableTeachers.Select(t => t.Name).ToArray();
        var random = new Random();

        for (int day = 0; day < 5; day++)
        {
            for (int slot = 0; slot < Math.Min(5, timeSlots.Length); slot++)
            {
                if (random.Next(10) > 2) // 70% chance of having a class
                {
                    mockSchedule.Add(new LocalScheduleDto
                    {
                        Id = mockSchedule.Count + 1,
                        Subject = subjects[random.Next(subjects.Length)],
                        Teacher = teachers[random.Next(teachers.Length)],
                        Room = $"قاعة {random.Next(101, 120)}",
                        DayOfWeek = day,
                        TimeSlot = timeSlots[slot],
                        ClassId = !string.IsNullOrEmpty(selectedClass) ? int.Parse(selectedClass) : 1
                    });
                }
            }
        }

        return mockSchedule;
    }

    private LocalScheduleDto? GetScheduleForSlot(string timeSlot, int dayOfWeek)
    {
        return schedules.FirstOrDefault(s => s.TimeSlot == timeSlot && s.DayOfWeek == dayOfWeek);
    }

    private string GetSubjectColor(string subject)
    {
        return subject switch
        {
            "الرياضيات" => "primary",
            "العلوم" => "success",
            "اللغة العربية" => "warning",
            "اللغة الإنجليزية" => "info",
            "التاريخ" => "secondary",
            "الجغرافيا" => "danger",
            "التربية الإسلامية" => "dark",
            "التربية البدنية" => "success",
            _ => "secondary"
        };
    }

    private async Task ShowAddScheduleModal()
    {
        editingSchedule = new LocalScheduleDto();
        await JSRuntime.InvokeVoidAsync("eval", "new bootstrap.Modal(document.getElementById('scheduleModal')).show()");
    }

    private async Task EditScheduleSlot(string timeSlot, int dayOfWeek)
    {
        var existing = GetScheduleForSlot(timeSlot, dayOfWeek);
        editingSchedule = existing ?? new LocalScheduleDto
        {
            TimeSlot = timeSlot,
            DayOfWeek = dayOfWeek,
            ClassId = !string.IsNullOrEmpty(selectedClass) ? int.Parse(selectedClass) : 1
        };

        await JSRuntime.InvokeVoidAsync("eval", "new bootstrap.Modal(document.getElementById('scheduleModal')).show()");
    }

    private async Task SaveSchedule()
    {
        try
        {
            if (editingSchedule.Id == 0)
            {
                // Create new schedule
                var createDto = new CreateScheduleDto
                {
                    ClassId = editingSchedule.ClassId,
                    SubjectId = 1, // You'll need to get this from a subject dropdown
                    TeacherId = "1", // You'll need to get this from teacher dropdown
                    Date = DateTime.Today.AddDays(editingSchedule.DayOfWeek),
                    StartTime = TimeSpan.Parse(editingSchedule.TimeSlot.Split(" - ")[0]),
                    EndTime = TimeSpan.Parse(editingSchedule.TimeSlot.Split(" - ")[1]),
                    Room = editingSchedule.Room
                };

                var result = await ApiService.CreateScheduleAsync(createDto);
                if (result != null)
                {
                    editingSchedule.Id = result.Id;
                    schedules.Add(editingSchedule);
                    await JSRuntime.InvokeVoidAsync("alert", "تم إضافة الحصة بنجاح");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "فشل في إضافة الحصة");
                    return;
                }
            }
            else
            {
                // Update existing schedule
                var updateDto = new UpdateScheduleDto
                {
                    ClassId = editingSchedule.ClassId,
                    SubjectId = 1, // You'll need to get this from a subject dropdown
                    TeacherId = "1", // You'll need to get this from teacher dropdown
                    Date = DateTime.Today.AddDays(editingSchedule.DayOfWeek),
                    StartTime = TimeSpan.Parse(editingSchedule.TimeSlot.Split(" - ")[0]),
                    EndTime = TimeSpan.Parse(editingSchedule.TimeSlot.Split(" - ")[1]),
                    Room = editingSchedule.Room
                };

                var success = await ApiService.UpdateScheduleAsync(editingSchedule.Id, updateDto);
                if (success)
                {
                    var index = schedules.FindIndex(s => s.Id == editingSchedule.Id);
                    if (index >= 0)
                    {
                        schedules[index] = editingSchedule;
                    }
                    await JSRuntime.InvokeVoidAsync("alert", "تم تحديث الحصة بنجاح");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "فشل في تحديث الحصة");
                    return;
                }
            }

            await JSRuntime.InvokeVoidAsync("eval", "bootstrap.Modal.getInstance(document.getElementById('scheduleModal')).hide()");
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ الحصة: {ex.Message}");
        }
    }

    private async Task DeleteSchedule()
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذه الحصة؟"))
        {
            try
            {
                var success = await ApiService.DeleteScheduleAsync(editingSchedule.Id);
                if (success)
                {
                    schedules.RemoveAll(s => s.Id == editingSchedule.Id);
                    await JSRuntime.InvokeVoidAsync("eval", "bootstrap.Modal.getInstance(document.getElementById('scheduleModal')).hide()");
                    await JSRuntime.InvokeVoidAsync("alert", "تم حذف الحصة بنجاح");
                    StateHasChanged();
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "فشل في حذف الحصة");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف الحصة: {ex.Message}");
            }
        }
    }

    private async Task ExportSchedule()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم تصدير الجدول إلى Excel");
    }

    private async Task PrintSchedule()
    {
        await JSRuntime.InvokeVoidAsync("window.print");
    }

    public class LocalScheduleDto
    {
        public int Id { get; set; }
        public string Subject { get; set; } = string.Empty;
        public string Teacher { get; set; } = string.Empty;
        public string Room { get; set; } = string.Empty;
        public int DayOfWeek { get; set; }
        public string TimeSlot { get; set; } = string.Empty;
        public int ClassId { get; set; }
    }

    public class TeacherDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }
}
