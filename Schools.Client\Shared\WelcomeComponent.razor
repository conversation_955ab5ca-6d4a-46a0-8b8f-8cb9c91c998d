@inject NavigationManager Navigation

<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center min-vh-100">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="display-4 fw-bold mb-4">
                        مرحباً بك في نظام إدارة المدارس
                    </h1>
                    <p class="lead mb-4">
                        نظام شامل ومتطور لإدارة جميع جوانب المدرسة من طلاب ومعلمين وموظفين وأولياء أمور
                    </p>
                    <div class="d-flex gap-3 flex-wrap">
                        <button class="btn btn-light btn-lg px-4" @onclick='() => Navigation.NavigateTo("/login")'>
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </button>
                        <button class="btn btn-outline-light btn-lg px-4" @onclick='() => Navigation.NavigateTo("/register")'>
                            <i class="fas fa-user-plus me-2"></i>
                            إنشاء حساب جديد
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <div class="hero-image">
                    <i class="fas fa-school" style="font-size: 15rem; opacity: 0.8;"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="display-5 fw-bold mb-3">مميزات النظام</h2>
                <p class="lead text-muted">نظام متكامل يوفر جميع الأدوات اللازمة لإدارة المدرسة بكفاءة</p>
            </div>
        </div>

        <div class="row g-4">
            <!-- Admin Features -->
            <div class="col-lg-3 col-md-6">
                <div class="feature-card card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-user-shield fa-3x text-primary"></i>
                        </div>
                        <h5 class="card-title">لوحة الإدارة</h5>
                        <p class="card-text text-muted">
                            إدارة شاملة للأعوام الدراسية، المراحل، الصفوف، والمواد الدراسية
                        </p>
                    </div>
                </div>
            </div>

            <!-- Teacher Features -->
            <div class="col-lg-3 col-md-6">
                <div class="feature-card card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-chalkboard-teacher fa-3x text-success"></i>
                        </div>
                        <h5 class="card-title">لوحة المعلمين</h5>
                        <p class="card-text text-muted">
                            إدارة الحصص، الحضور والغياب، الامتحانات والواجبات
                        </p>
                    </div>
                </div>
            </div>

            <!-- Student Features -->
            <div class="col-lg-3 col-md-6">
                <div class="feature-card card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-user-graduate fa-3x text-info"></i>
                        </div>
                        <h5 class="card-title">لوحة الطلاب</h5>
                        <p class="card-text text-muted">
                            متابعة الحصص، الامتحانات، الواجبات والدرجات
                        </p>
                    </div>
                </div>
            </div>

            <!-- Parent Features -->
            <div class="col-lg-3 col-md-6">
                <div class="feature-card card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-users fa-3x text-warning"></i>
                        </div>
                        <h5 class="card-title">لوحة أولياء الأمور</h5>
                        <p class="card-text text-muted">
                            متابعة أداء الأبناء والتواصل مع المدرسة
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="services-section py-5 bg-light">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="display-5 fw-bold mb-3">الخدمات المتاحة</h2>
                <p class="lead text-muted">مجموعة شاملة من الخدمات التعليمية والإدارية</p>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="service-item d-flex align-items-start">
                    <div class="service-icon me-3">
                        <i class="fas fa-calendar-alt fa-2x text-primary"></i>
                    </div>
                    <div>
                        <h5>إدارة الجداول الدراسية</h5>
                        <p class="text-muted">تنظيم وإدارة الجداول الأسبوعية للحصص والمواد</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="service-item d-flex align-items-start">
                    <div class="service-icon me-3">
                        <i class="fas fa-clipboard-check fa-2x text-success"></i>
                    </div>
                    <div>
                        <h5>نظام الحضور والغياب</h5>
                        <p class="text-muted">تسجيل ومتابعة حضور الطلاب والموظفين</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="service-item d-flex align-items-start">
                    <div class="service-icon me-3">
                        <i class="fas fa-file-alt fa-2x text-info"></i>
                    </div>
                    <div>
                        <h5>الامتحانات الإلكترونية</h5>
                        <p class="text-muted">إنشاء وإدارة الامتحانات الإلكترونية</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="service-item d-flex align-items-start">
                    <div class="service-icon me-3">
                        <i class="fas fa-chart-line fa-2x text-warning"></i>
                    </div>
                    <div>
                        <h5>التقارير والإحصائيات</h5>
                        <p class="text-muted">تقارير شاملة عن الأداء والإحصائيات</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="service-item d-flex align-items-start">
                    <div class="service-icon me-3">
                        <i class="fas fa-dollar-sign fa-2x text-danger"></i>
                    </div>
                    <div>
                        <h5>إدارة الرسوم المالية</h5>
                        <p class="text-muted">نظام متكامل لإدارة الرسوم والمدفوعات</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="service-item d-flex align-items-start">
                    <div class="service-icon me-3">
                        <i class="fas fa-bell fa-2x text-secondary"></i>
                    </div>
                    <div>
                        <h5>نظام الإشعارات</h5>
                        <p class="text-muted">إشعارات فورية للأحداث المهمة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section py-5 bg-primary text-white">
    <div class="container">
        <div class="row text-center">
            <div class="col-12">
                <h2 class="display-5 fw-bold mb-3">ابدأ رحلتك التعليمية معنا</h2>
                <p class="lead mb-4">انضم إلى آلاف المستخدمين الذين يثقون في نظامنا</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <button class="btn btn-light btn-lg px-4" @onclick='() => Navigation.NavigateTo("/register")'>
                        <i class="fas fa-rocket me-2"></i>
                        ابدأ الآن
                    </button>
                    <button class="btn btn-outline-light btn-lg px-4" @onclick='() => Navigation.NavigateTo("/login")'>
                        <i class="fas fa-sign-in-alt me-2"></i>
                        لديك حساب؟ سجل دخولك
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .feature-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
    }

    .service-item {
        padding: 1.5rem;
        border-radius: 10px;
        transition: background-color 0.3s ease;
    }

    .service-item:hover {
        background-color: white;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .cta-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
</style>
