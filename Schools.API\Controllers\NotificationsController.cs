using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Schools.API.Services;
using Schools.Shared.DTOs;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class NotificationsController : ControllerBase
    {
        private readonly INotificationService _notificationService;
        private readonly ILogger<NotificationsController> _logger;

        public NotificationsController(
            INotificationService notificationService,
            ILogger<NotificationsController> logger)
        {
            _notificationService = notificationService;
            _logger = logger;
        }

        /// <summary>
        /// Get user notifications with pagination
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<List<Schools.API.Services.NotificationDto>>> GetNotifications(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var userId = User.FindFirst("sub")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                var notifications = await _notificationService.GetUserNotificationsAsync(userId, page, pageSize);
                return Ok(notifications);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notifications");
                return StatusCode(500, "An error occurred while retrieving notifications");
            }
        }

        /// <summary>
        /// Get unread notification count
        /// </summary>
        [HttpGet("unread-count")]
        public async Task<ActionResult<int>> GetUnreadCount()
        {
            try
            {
                var userId = User.FindFirst("sub")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                var count = await _notificationService.GetUnreadNotificationCountAsync(userId);
                return Ok(count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unread notification count");
                return StatusCode(500, "An error occurred while retrieving unread count");
            }
        }

        /// <summary>
        /// Mark notification as read
        /// </summary>
        [HttpPut("{id}/read")]
        public async Task<IActionResult> MarkAsRead(int id)
        {
            try
            {
                var userId = User.FindFirst("sub")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                await _notificationService.MarkNotificationAsReadAsync(id, userId);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking notification {NotificationId} as read", id);
                return StatusCode(500, "An error occurred while marking notification as read");
            }
        }

        /// <summary>
        /// Mark all notifications as read
        /// </summary>
        [HttpPut("read-all")]
        public async Task<IActionResult> MarkAllAsRead()
        {
            try
            {
                var userId = User.FindFirst("sub")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found");
                }

                await _notificationService.MarkAllNotificationsAsReadAsync(userId);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking all notifications as read");
                return StatusCode(500, "An error occurred while marking all notifications as read");
            }
        }

        /// <summary>
        /// Send notification to user (Admin only)
        /// </summary>
        [HttpPost("send")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> SendNotification([FromBody] SendNotificationDto request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.UserId))
                {
                    return BadRequest("User ID is required");
                }

                await _notificationService.SendNotificationAsync(
                    request.UserId,
                    request.Title,
                    request.Message,
                    request.Type ?? "info"
                );

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notification");
                return StatusCode(500, "An error occurred while sending notification");
            }
        }

        /// <summary>
        /// Send notification to role (Admin only)
        /// </summary>
        [HttpPost("send-to-role")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> SendNotificationToRole([FromBody] SendNotificationToRoleDto request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Role))
                {
                    return BadRequest("Role is required");
                }

                await _notificationService.SendNotificationToRoleAsync(
                    request.Role,
                    request.Title,
                    request.Message,
                    request.Type ?? "info"
                );

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notification to role");
                return StatusCode(500, "An error occurred while sending notification to role");
            }
        }

        /// <summary>
        /// Send notification to class (Admin/Teacher only)
        /// </summary>
        [HttpPost("send-to-class")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<IActionResult> SendNotificationToClass([FromBody] SendNotificationToClassDto request)
        {
            try
            {
                if (request.ClassId <= 0)
                {
                    return BadRequest("Valid Class ID is required");
                }

                await _notificationService.SendNotificationToClassAsync(
                    request.ClassId,
                    request.Title,
                    request.Message,
                    request.Type ?? "info"
                );

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notification to class");
                return StatusCode(500, "An error occurred while sending notification to class");
            }
        }

        /// <summary>
        /// Send exam reminder (Admin/Teacher only)
        /// </summary>
        [HttpPost("exam-reminder/{examId}")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<IActionResult> SendExamReminder(int examId)
        {
            try
            {
                await _notificationService.SendExamReminderAsync(examId);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending exam reminder for exam {ExamId}", examId);
                return StatusCode(500, "An error occurred while sending exam reminder");
            }
        }

        /// <summary>
        /// Send fee reminder (Admin/Accountant only)
        /// </summary>
        [HttpPost("fee-reminder/{studentId}")]
        [Authorize(Roles = "Admin,Accountant")]
        public async Task<IActionResult> SendFeeReminder(string studentId)
        {
            try
            {
                if (string.IsNullOrEmpty(studentId))
                {
                    return BadRequest("Student ID is required");
                }

                await _notificationService.SendFeeReminderAsync(studentId);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending fee reminder for student {StudentId}", studentId);
                return StatusCode(500, "An error occurred while sending fee reminder");
            }
        }
    }

    // DTOs for notification requests
    public class SendNotificationDto
    {
        public string UserId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? Type { get; set; }
    }

    public class SendNotificationToRoleDto
    {
        public string Role { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? Type { get; set; }
    }

    public class SendNotificationToClassDto
    {
        public int ClassId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? Type { get; set; }
    }
}
