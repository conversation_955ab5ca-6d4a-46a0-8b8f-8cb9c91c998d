using System.ComponentModel.DataAnnotations;
using Schools.Shared.Models;

namespace Schools.Shared.DTOs
{
    // Schedule DTOs
    public class ScheduleDto
    {
        public int Id { get; set; }
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public string SubjectCode { get; set; } = string.Empty;
        public int ClassId { get; set; }
        public string ClassName { get; set; } = string.Empty;
        public int? SectionId { get; set; }
        public string? SectionName { get; set; }
        public string TeacherId { get; set; } = string.Empty;
        public string TeacherName { get; set; } = string.Empty;
        public string? SubstituteTeacherId { get; set; }
        public string? SubstituteTeacherName { get; set; }
        public int AcademicYearId { get; set; }
        public string AcademicYearName { get; set; } = string.Empty;
        public DayOfWeek DayOfWeek { get; set; }
        public string DayName { get; set; } = string.Empty;
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public string TimeSlot { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
        public string? Room { get; set; }
        public DateTime Date { get; set; }
        public ScheduleType Type { get; set; }
        public string TypeName { get; set; } = string.Empty;
        public ScheduleStatus Status { get; set; }
        public string StatusName { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public string? Notes { get; set; }
        public string? CancellationReason { get; set; }
        public DateTime? CancelledAt { get; set; }
        public string? CancelledBy { get; set; }
        public bool IsToday { get; set; }
        public bool IsUpcoming { get; set; }
        public bool IsPast { get; set; }
    }

    public class CreateScheduleDto
    {
        [Required]
        public int SubjectId { get; set; }

        [Required]
        public int ClassId { get; set; }

        public int? SectionId { get; set; }

        [Required]
        public string TeacherId { get; set; } = string.Empty;

        [Required]
        public int AcademicYearId { get; set; }

        [Required]
        public DayOfWeek DayOfWeek { get; set; }

        [Required]
        public TimeSpan StartTime { get; set; }

        [Required]
        public TimeSpan EndTime { get; set; }

        public string? Room { get; set; }

        [Required]
        public DateTime Date { get; set; }

        public ScheduleType Type { get; set; } = ScheduleType.Regular;

        public string? Notes { get; set; }
    }

    public class UpdateScheduleDto
    {
        public int? SubjectId { get; set; }
        public int? ClassId { get; set; }
        public int? SectionId { get; set; }
        public string? TeacherId { get; set; }
        public DayOfWeek? DayOfWeek { get; set; }
        public TimeSpan? StartTime { get; set; }
        public TimeSpan? EndTime { get; set; }
        public string? Room { get; set; }
        public DateTime? Date { get; set; }
        public ScheduleType? Type { get; set; }
        public ScheduleStatus? Status { get; set; }
        public string? Notes { get; set; }
        public string? SubstituteTeacherId { get; set; }
        public string? CancellationReason { get; set; }
    }

    // Enhanced Weekly Schedule DTOs
    public class WeeklyScheduleDto
    {
        public DateTime WeekStart { get; set; }
        public DateTime WeekEnd { get; set; }
        public int? ClassId { get; set; }
        public string? ClassName { get; set; }
        public string? TeacherId { get; set; }
        public string? TeacherName { get; set; }
        public int TotalPeriods { get; set; }
        public double TotalHours { get; set; }
        public List<DayScheduleDto> Days { get; set; } = new();
    }

    public class DayScheduleDto
    {
        public DateTime Date { get; set; }
        public DayOfWeek DayOfWeek { get; set; }
        public string DayName { get; set; } = string.Empty;
        public bool IsToday { get; set; }
        public bool IsWeekend { get; set; }
        public bool IsHoliday { get; set; }
        public string? HolidayName { get; set; }
        public int TotalPeriods { get; set; }
        public double TotalHours { get; set; }
        public List<SchedulePeriodDto> Periods { get; set; } = new();
    }

    public class SchedulePeriodDto
    {
        public int Id { get; set; }
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public string? SubjectNameAr { get; set; }
        public string? SubjectCode { get; set; }
        public int ClassId { get; set; }
        public string ClassName { get; set; } = string.Empty;
        public int? SectionId { get; set; }
        public string? SectionName { get; set; }
        public string TeacherId { get; set; } = string.Empty;
        public string TeacherName { get; set; } = string.Empty;
        public string? SubstituteTeacherId { get; set; }
        public string? SubstituteTeacherName { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public string TimeSlot { get; set; } = string.Empty;
        public string? Room { get; set; }
        public ScheduleType Type { get; set; }
        public ScheduleStatus Status { get; set; }
        public string? Notes { get; set; }
        public bool IsSubstitute { get; set; }
        public bool IsCancelled { get; set; }
        public bool IsCompleted { get; set; }
        public bool IsBreak { get; set; }
        public string? BreakType { get; set; }
    }

    public class TeacherScheduleDto
    {
        public string TeacherId { get; set; } = string.Empty;
        public string TeacherName { get; set; } = string.Empty;
        public string? TeacherEmail { get; set; }
        public string? Department { get; set; }
        public DateTime WeekStart { get; set; }
        public DateTime WeekEnd { get; set; }
        public int TotalClasses { get; set; }
        public double TotalHours { get; set; }
        public int SubjectCount { get; set; }
        public int ClassCount { get; set; }
        public WeeklyScheduleDto WeeklySchedule { get; set; } = new();
        public List<TeacherWorkloadDto> DailyWorkload { get; set; } = new();
    }

    public class TeacherWorkloadDto
    {
        public DateTime Date { get; set; }
        public string DayName { get; set; } = string.Empty;
        public int PeriodCount { get; set; }
        public double HoursCount { get; set; }
        public int SubstituteCount { get; set; }
        public bool IsOverloaded { get; set; }
        public List<string> Subjects { get; set; } = new();
        public List<string> Classes { get; set; } = new();
    }

    public class ClassScheduleDto
    {
        public int ClassId { get; set; }
        public string ClassName { get; set; } = string.Empty;
        public int? SectionId { get; set; }
        public string? SectionName { get; set; }
        public int GradeId { get; set; }
        public string GradeName { get; set; } = string.Empty;
        public int StudentCount { get; set; }
        public DateTime WeekStart { get; set; }
        public DateTime WeekEnd { get; set; }
        public int TotalPeriods { get; set; }
        public double TotalHours { get; set; }
        public int TeacherCount { get; set; }
        public int SubjectCount { get; set; }
        public WeeklyScheduleDto WeeklySchedule { get; set; } = new();
        public List<SubjectScheduleSummaryDto> SubjectSummary { get; set; } = new();
    }

    public class SubjectScheduleSummaryDto
    {
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public string? SubjectNameAr { get; set; }
        public string TeacherId { get; set; } = string.Empty;
        public string TeacherName { get; set; } = string.Empty;
        public int WeeklyPeriods { get; set; }
        public double WeeklyHours { get; set; }
        public List<DayOfWeek> ScheduledDays { get; set; } = new();
    }

    // Schedule Statistics DTOs
    public class ScheduleStatisticsDto
    {
        public int TotalSchedules { get; set; }
        public int TodaySchedules { get; set; }
        public int UpcomingSchedules { get; set; }
        public int CompletedSchedules { get; set; }
        public int CancelledSchedules { get; set; }
        public int ActiveTeachers { get; set; }
        public int ActiveClasses { get; set; }
        public int TotalStudents { get; set; }
        public double AverageClassSize { get; set; }
        public Dictionary<string, int> SchedulesByType { get; set; } = new();
        public Dictionary<string, int> SchedulesByStatus { get; set; } = new();
    }

    // Schedule Conflict DTO
    public class ScheduleConflictDto
    {
        public string TeacherId { get; set; } = string.Empty;
        public string TeacherName { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public List<ScheduleDto> ConflictingSchedules { get; set; } = new();
    }
}
