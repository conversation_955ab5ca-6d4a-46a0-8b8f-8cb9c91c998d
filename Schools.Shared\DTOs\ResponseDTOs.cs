namespace Schools.Shared.DTOs
{
    /// <summary>
    /// Standard API Response DTOs for the School Management System
    /// </summary>

    // Base Response
    public class BaseResponseDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? MessageAr { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string? RequestId { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }

    // Generic Response with Data
    public class ApiResponseDto<T> : BaseResponseDto
    {
        public T? Data { get; set; }
        public object? Metadata { get; set; }
    }

    // Paginated Response
    public class PaginatedResponseDto<T> : BaseResponseDto
    {
        public List<T> Data { get; set; } = new();
        public PaginationMetadata Pagination { get; set; } = new();
    }

    public class PaginationMetadata
    {
        public int CurrentPage { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public bool HasPrevious { get; set; }
        public bool HasNext { get; set; }
        public int? PreviousPage { get; set; }
        public int? NextPage { get; set; }
    }

    // Authentication Responses
    public class LoginResponseDto : BaseResponseDto
    {
        public string? AccessToken { get; set; }
        public string? RefreshToken { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public UserProfileDto? User { get; set; }
        public List<string> Roles { get; set; } = new();
        public List<string> Permissions { get; set; } = new();
    }

    public class RefreshTokenResponseDto : BaseResponseDto
    {
        public string? AccessToken { get; set; }
        public string? RefreshToken { get; set; }
        public DateTime? ExpiresAt { get; set; }
    }

    // User Profile Response
    public class UserProfileDto
    {
        public string Id { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string FullName => $"{FirstName} {LastName}";
        public string? PhoneNumber { get; set; }
        public string? ProfilePicture { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public List<string> Roles { get; set; } = new();
        public Dictionary<string, object> AdditionalInfo { get; set; } = new();
    }

    // File Upload Response
    public class FileUploadResponseDto : BaseResponseDto
    {
        public string? FileName { get; set; }
        public string? FilePath { get; set; }
        public string? FileUrl { get; set; }
        public long FileSize { get; set; }
        public string? ContentType { get; set; }
        public string? FileId { get; set; }
    }

    // Bulk Operation Response
    public class BulkOperationResponseDto : BaseResponseDto
    {
        public int TotalItems { get; set; }
        public int SuccessfulItems { get; set; }
        public int FailedItems { get; set; }
        public List<BulkOperationItemResult> Results { get; set; } = new();
    }

    public class BulkOperationItemResult
    {
        public string ItemId { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? Error { get; set; }
        public object? Data { get; set; }
    }

    // Dashboard Response
    public class DashboardResponseDto : BaseResponseDto
    {
        public Dictionary<string, object> Statistics { get; set; } = new();
        public List<NotificationDto> Notifications { get; set; } = new();
        public List<RecentActivityDto> RecentActivities { get; set; } = new();
        public List<UpcomingEventDto> UpcomingEvents { get; set; } = new();
        public Dictionary<string, List<object>> Widgets { get; set; } = new();
    }

    // NotificationDto defined in other files

    // Recent Activity DTO
    public class RecentActivityDto
    {
        public int Id { get; set; }
        public string Action { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string EntityType { get; set; } = string.Empty;
        public string EntityId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string? Icon { get; set; }
        public string? Color { get; set; }
    }

    // UpcomingEventDto defined in other files

    // Search Response
    public class SearchResponseDto<T> : PaginatedResponseDto<T>
    {
        public string SearchTerm { get; set; } = string.Empty;
        public Dictionary<string, object> Filters { get; set; } = new();
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; }
        public int SearchDurationMs { get; set; }
        public Dictionary<string, int> Facets { get; set; } = new();
    }

    // Export Response
    public class ExportResponseDto : BaseResponseDto
    {
        public string? FileName { get; set; }
        public string? FilePath { get; set; }
        public string? DownloadUrl { get; set; }
        public string Format { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public int RecordCount { get; set; }
        public DateTime ExportedAt { get; set; }
        public string ExportedBy { get; set; } = string.Empty;
    }

    // Import Response
    public class ImportResponseDto : BaseResponseDto
    {
        public int TotalRecords { get; set; }
        public int SuccessfulRecords { get; set; }
        public int FailedRecords { get; set; }
        public int SkippedRecords { get; set; }
        public new List<ImportErrorDto> Errors { get; set; } = new();
        public string? ImportId { get; set; }
        public DateTime ImportedAt { get; set; }
        public string ImportedBy { get; set; } = string.Empty;
    }

    public class ImportErrorDto
    {
        public int RowNumber { get; set; }
        public string Field { get; set; } = string.Empty;
        public string Error { get; set; } = string.Empty;
        public string? Value { get; set; }
    }

    // Validation Response
    public class ValidationResponseDto : BaseResponseDto
    {
        public bool IsValid { get; set; }
        public Dictionary<string, List<string>> FieldErrors { get; set; } = new();
        public List<string> GeneralErrors { get; set; } = new();
    }

    // Health Check Response
    public class HealthCheckResponseDto
    {
        public string Status { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string Version { get; set; } = string.Empty;
        public Dictionary<string, HealthCheckItemDto> Checks { get; set; } = new();
        public TimeSpan Duration { get; set; }
    }

    public class HealthCheckItemDto
    {
        public string Status { get; set; } = string.Empty;
        public string? Description { get; set; }
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> Data { get; set; } = new();
    }

    // Statistics Response
    public class StatisticsResponseDto : BaseResponseDto
    {
        public Dictionary<string, object> Statistics { get; set; } = new();
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public string Period { get; set; } = string.Empty;
        public Dictionary<string, List<object>> Charts { get; set; } = new();
        public Dictionary<string, object> Comparisons { get; set; } = new();
    }

    // Audit Log Response
    public class AuditLogDto
    {
        public int Id { get; set; }
        public string Action { get; set; } = string.Empty;
        public string EntityType { get; set; } = string.Empty;
        public string EntityId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string? OldValues { get; set; }
        public string? NewValues { get; set; }
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; } = new();
    }

    // Configuration Response
    public class ConfigurationResponseDto : BaseResponseDto
    {
        public Dictionary<string, object> Settings { get; set; } = new();
        public string Version { get; set; } = string.Empty;
        public DateTime LastUpdated { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;
    }

    // Backup Response
    public class BackupResponseDto : BaseResponseDto
    {
        public string? BackupId { get; set; }
        public string? FileName { get; set; }
        public string? FilePath { get; set; }
        public long FileSize { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public string BackupType { get; set; } = string.Empty;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
}
