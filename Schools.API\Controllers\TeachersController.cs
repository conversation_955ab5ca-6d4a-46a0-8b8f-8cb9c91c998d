using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;
using System.Security.Claims;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TeachersController : ControllerBase
    {
        private readonly SchoolsDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public TeachersController(SchoolsDbContext context, UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        #region Teachers Management

        [HttpGet]
        public async Task<ActionResult<IEnumerable<TeacherDto>>> GetTeachers()
        {
            try
            {
                var teacherRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == "Teacher");
                if (teacherRole == null)
                {
                    return Ok(new List<TeacherDto>());
                }

                var teachers = await _context.Users
                    .Where(u => u.UserRoles.Any(ur => ur.RoleId == teacherRole.Id) && u.IsActive)
                    .Include(u => u.TeacherAssignments)
                    .ThenInclude(ta => ta.Subject)
                    .Include(u => u.TeacherAssignments)
                    .ThenInclude(ta => ta.Class)
                    .ThenInclude(c => c.AcademicGrade)
                    .Select(u => new TeacherDto
                    {
                        Id = u.Id,
                        FirstName = u.FirstName,
                        LastName = u.LastName,
                        FullName = $"{u.FirstName} {u.LastName}",
                        Email = u.Email ?? "",
                        PhoneNumber = u.PhoneNumber,
                        NationalId = u.NationalId,
                        DateOfBirth = u.DateOfBirth,
                        Address = u.Address,
                        ProfilePicture = u.ProfilePicture,
                        IsActive = u.IsActive,
                        CreatedAt = u.CreatedAt,
                        EmployeeNumber = u.UserName ?? "",
                        Specialization = u.Specialization,
                        QualificationLevel = u.QualificationLevel,
                        YearsOfExperience = u.YearsOfExperience,
                        HireDate = u.HireDate,
                        Salary = u.Salary,
                        Assignments = u.TeacherAssignments
                            .Where(ta => ta.IsActive)
                            .Select(ta => new TeacherAssignmentDto
                            {
                                Id = ta.Id,
                                SubjectId = ta.SubjectId,
                                SubjectName = ta.Subject.Name,
                                ClassId = ta.ClassId,
                                ClassName = ta.Class.Name,
                                GradeName = ta.Class.AcademicGrade.Name,
                                IsClassTeacher = ta.IsClassTeacher,
                                AssignedDate = ta.AssignedDate
                            }).ToList()
                    })
                    .ToListAsync();

                return Ok(teachers);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب المعلمين", error = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<TeacherDto>> GetTeacher(string id)
        {
            try
            {
                var teacher = await _context.Users
                    .Include(u => u.TeacherAssignments)
                    .ThenInclude(ta => ta.Subject)
                    .Include(u => u.TeacherAssignments)
                    .ThenInclude(ta => ta.Class)
                    .ThenInclude(c => c.AcademicGrade)
                    .FirstOrDefaultAsync(u => u.Id == id);

                if (teacher == null)
                {
                    return NotFound(new { message = "المعلم غير موجود" });
                }

                // Check if user is a teacher
                var isTeacher = await _userManager.IsInRoleAsync(teacher, "Teacher");
                if (!isTeacher)
                {
                    return NotFound(new { message = "المستخدم ليس معلماً" });
                }

                var teacherDto = new TeacherDto
                {
                    Id = teacher.Id,
                    FirstName = teacher.FirstName,
                    LastName = teacher.LastName,
                    FullName = $"{teacher.FirstName} {teacher.LastName}",
                    Email = teacher.Email ?? "",
                    PhoneNumber = teacher.PhoneNumber,
                    NationalId = teacher.NationalId,
                    DateOfBirth = teacher.DateOfBirth,
                    Address = teacher.Address,
                    ProfilePicture = teacher.ProfilePicture,
                    IsActive = teacher.IsActive,
                    CreatedAt = teacher.CreatedAt,
                    EmployeeNumber = teacher.UserName ?? "",
                    Specialization = teacher.Specialization,
                    QualificationLevel = teacher.QualificationLevel,
                    YearsOfExperience = teacher.YearsOfExperience,
                    HireDate = teacher.HireDate,
                    Salary = teacher.Salary,
                    Assignments = teacher.TeacherAssignments
                        .Select(ta => new TeacherAssignmentDto
                        {
                            Id = ta.Id,
                            SubjectId = ta.SubjectId,
                            SubjectName = ta.Subject.Name,
                            ClassId = ta.ClassId,
                            ClassName = ta.Class.Name,
                            GradeName = ta.Class.AcademicGrade.Name,
                            IsClassTeacher = ta.IsClassTeacher,
                            AssignedDate = ta.AssignedDate,
                            IsActive = ta.IsActive
                        }).ToList()
                };

                return Ok(teacherDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب المعلم", error = ex.Message });
            }
        }

        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<TeacherDto>> CreateTeacher(CreateTeacherDto model)
        {
            try
            {
                // Check if user already exists
                var existingUser = await _userManager.FindByEmailAsync(model.Email);
                if (existingUser != null)
                {
                    return BadRequest(new { message = "البريد الإلكتروني مستخدم بالفعل" });
                }

                // Create user
                var user = new ApplicationUser
                {
                    UserName = model.EmployeeNumber,
                    Email = model.Email,
                    FirstName = model.FirstName,
                    LastName = model.LastName,
                    PhoneNumber = model.PhoneNumber,
                    NationalId = model.NationalId,
                    DateOfBirth = model.DateOfBirth,
                    Address = model.Address,
                    Specialization = model.Specialization,
                    QualificationLevel = model.QualificationLevel,
                    YearsOfExperience = model.YearsOfExperience,
                    HireDate = model.HireDate,
                    Salary = model.Salary,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                };

                var result = await _userManager.CreateAsync(user, model.Password ?? "Teacher123!");
                if (!result.Succeeded)
                {
                    return BadRequest(new { message = string.Join(", ", result.Errors.Select(e => e.Description)) });
                }

                // Assign Teacher role
                await _userManager.AddToRoleAsync(user, "Teacher");

                return CreatedAtAction(nameof(GetTeacher), new { id = user.Id }, new { id = user.Id });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في إنشاء المعلم", error = ex.Message });
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UpdateTeacher(string id, UpdateTeacherDto model)
        {
            try
            {
                var teacher = await _userManager.FindByIdAsync(id);
                if (teacher == null)
                {
                    return NotFound(new { message = "المعلم غير موجود" });
                }

                // Update teacher information
                if (!string.IsNullOrEmpty(model.FirstName))
                    teacher.FirstName = model.FirstName;
                if (!string.IsNullOrEmpty(model.LastName))
                    teacher.LastName = model.LastName;
                if (!string.IsNullOrEmpty(model.PhoneNumber))
                    teacher.PhoneNumber = model.PhoneNumber;
                if (!string.IsNullOrEmpty(model.Address))
                    teacher.Address = model.Address;
                if (!string.IsNullOrEmpty(model.Specialization))
                    teacher.Specialization = model.Specialization;
                if (!string.IsNullOrEmpty(model.QualificationLevel))
                    teacher.QualificationLevel = model.QualificationLevel;
                if (model.YearsOfExperience.HasValue)
                    teacher.YearsOfExperience = model.YearsOfExperience;
                if (model.Salary.HasValue)
                    teacher.Salary = model.Salary;
                if (model.DateOfBirth.HasValue)
                    teacher.DateOfBirth = model.DateOfBirth;
                if (model.IsActive.HasValue)
                    teacher.IsActive = model.IsActive.Value;

                teacher.UpdatedAt = DateTime.UtcNow;

                var result = await _userManager.UpdateAsync(teacher);
                if (!result.Succeeded)
                {
                    return BadRequest(new { message = string.Join(", ", result.Errors.Select(e => e.Description)) });
                }

                return Ok(new { message = "تم تحديث المعلم بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحديث المعلم", error = ex.Message });
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteTeacher(string id)
        {
            try
            {
                var teacher = await _userManager.FindByIdAsync(id);
                if (teacher == null)
                {
                    return NotFound(new { message = "المعلم غير موجود" });
                }

                // Deactivate instead of delete
                teacher.IsActive = false;
                teacher.UpdatedAt = DateTime.UtcNow;

                var result = await _userManager.UpdateAsync(teacher);
                if (!result.Succeeded)
                {
                    return BadRequest(new { message = string.Join(", ", result.Errors.Select(e => e.Description)) });
                }

                // Deactivate assignments
                var assignments = await _context.TeacherAssignments
                    .Where(ta => ta.TeacherId == id)
                    .ToListAsync();

                foreach (var assignment in assignments)
                {
                    assignment.IsActive = false;
                    assignment.UpdatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم إلغاء تفعيل المعلم بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في حذف المعلم", error = ex.Message });
            }
        }

        #endregion

        #region Teacher Assignments

        [HttpPost("{id}/assignments")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> AssignTeacher(string id, CreateTeacherAssignmentDto model)
        {
            try
            {
                var teacher = await _userManager.FindByIdAsync(id);
                if (teacher == null)
                {
                    return NotFound(new { message = "المعلم غير موجود" });
                }

                // Check if assignment already exists
                var existingAssignment = await _context.TeacherAssignments
                    .FirstOrDefaultAsync(ta => ta.TeacherId == id &&
                                             ta.ClassId == model.ClassId &&
                                             ta.SubjectId == model.SubjectId &&
                                             ta.IsActive);

                if (existingAssignment != null)
                {
                    return BadRequest(new { message = "المعلم مُعيَّن بالفعل لهذا الصف والمادة" });
                }

                var assignment = new TeacherAssignment
                {
                    TeacherId = id,
                    ClassId = model.ClassId,
                    SubjectId = model.SubjectId,
                    IsClassTeacher = model.IsClassTeacher,
                    AssignedDate = model.AssignedDate,
                    IsActive = true,
                    CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System",
                    CreatedAt = DateTime.UtcNow
                };

                _context.TeacherAssignments.Add(assignment);
                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تعيين المعلم بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تعيين المعلم", error = ex.Message });
            }
        }

        [HttpDelete("{id}/assignments/{assignmentId}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> RemoveAssignment(string id, int assignmentId)
        {
            try
            {
                var assignment = await _context.TeacherAssignments
                    .FirstOrDefaultAsync(ta => ta.Id == assignmentId && ta.TeacherId == id);

                if (assignment == null)
                {
                    return NotFound(new { message = "التعيين غير موجود" });
                }

                assignment.IsActive = false;
                assignment.UpdatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System";
                assignment.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم إلغاء التعيين بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في إلغاء التعيين", error = ex.Message });
            }
        }

        [HttpGet("{id}/schedule")]
        public async Task<ActionResult<IEnumerable<TeacherScheduleDto>>> GetTeacherSchedule(string id)
        {
            try
            {
                var schedule = await _context.Schedules
                    .Include(s => s.Subject)
                    .Include(s => s.Class)
                    .ThenInclude(c => c.AcademicGrade)
                    .Where(s => s.TeacherId == id && s.IsActive)
                    .Select(s => new TeacherScheduleDto
                    {
                        Id = s.Id,
                        SubjectName = s.Subject.Name,
                        ClassName = s.Class.Name,
                        GradeName = s.Class.AcademicGrade.Name,
                        DayOfWeek = s.DayOfWeek,
                        StartTime = s.StartTime,
                        EndTime = s.EndTime,
                        Room = s.Room,
                        Notes = s.Notes
                    })
                    .OrderBy(s => s.DayOfWeek)
                    .ThenBy(s => s.StartTime)
                    .ToListAsync();

                return Ok(schedule);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب جدول المعلم", error = ex.Message });
            }
        }

        #endregion

        #region Teacher Performance

        [HttpGet("{id}/classes")]
        public async Task<ActionResult<IEnumerable<TeacherClassDto>>> GetTeacherClasses(string id)
        {
            try
            {
                var classes = await _context.TeacherAssignments
                    .Include(ta => ta.Class)
                    .ThenInclude(c => c.AcademicGrade)
                    .Include(ta => ta.Subject)
                    .Where(ta => ta.TeacherId == id && ta.IsActive)
                    .Select(ta => new TeacherClassDto
                    {
                        ClassId = ta.ClassId,
                        ClassName = ta.Class.Name,
                        GradeName = ta.Class.AcademicGrade.Name,
                        SubjectName = ta.Subject.Name,
                        IsClassTeacher = ta.IsClassTeacher,
                        TotalStudents = ta.Class.StudentEnrollments.Count(se => se.IsActive)
                    })
                    .ToListAsync();

                return Ok(classes);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب صفوف المعلم", error = ex.Message });
            }
        }

        [HttpGet("{id}/dashboard")]
        public async Task<ActionResult<TeacherDashboardDto>> GetTeacherDashboard(string id)
        {
            try
            {
                var teacher = await _userManager.FindByIdAsync(id);
                if (teacher == null)
                {
                    return NotFound(new { message = "المعلم غير موجود" });
                }

                // Get teacher assignments
                var assignments = await _context.TeacherAssignments
                    .Include(ta => ta.Class)
                    .ThenInclude(c => c.AcademicGrade)
                    .Include(ta => ta.Subject)
                    .Where(ta => ta.TeacherId == id && ta.IsActive)
                    .ToListAsync();

                // Get total students
                var totalStudents = await _context.StudentEnrollments
                    .Where(se => assignments.Select(a => a.ClassId).Contains(se.ClassId) && se.IsActive)
                    .CountAsync();

                // Get today's schedule
                var today = DateTime.Now.DayOfWeek;
                var todaySchedule = await _context.Schedules
                    .Include(s => s.Subject)
                    .Include(s => s.Class)
                    .Where(s => s.TeacherId == id && s.DayOfWeek == today && s.IsActive)
                    .Select(s => new TodayScheduleDto
                    {
                        SubjectName = s.Subject.Name,
                        ClassName = s.Class.Name,
                        StartTime = s.StartTime,
                        EndTime = s.EndTime,
                        Room = s.Room
                    })
                    .OrderBy(s => s.StartTime)
                    .ToListAsync();

                // Get pending exams
                var pendingExams = await _context.Exams
                    .Include(e => e.Subject)
                    .Include(e => e.Class)
                    .Where(e => assignments.Select(a => a.ClassId).Contains(e.ClassId) &&
                               e.StartDate > DateTime.Now &&
                               e.IsActive)
                    .Select(e => new PendingExamDto
                    {
                        Id = e.Id,
                        Title = e.Title,
                        SubjectName = e.Subject.Name,
                        ClassName = e.Class.Name,
                        StartDate = e.StartDate,
                        DurationMinutes = e.DurationMinutes
                    })
                    .OrderBy(e => e.StartDate)
                    .Take(5)
                    .ToListAsync();

                var dashboard = new TeacherDashboardDto
                {
                    TeacherInfo = new TeacherInfoDto
                    {
                        Id = teacher.Id,
                        FirstName = teacher.FirstName,
                        LastName = teacher.LastName,
                        Specialization = teacher.Specialization ?? "",
                        YearsOfExperience = teacher.YearsOfExperience ?? 0
                    },
                    TotalClasses = assignments.Count,
                    TotalStudents = totalStudents,
                    TotalSubjects = assignments.Select(a => a.SubjectId).Distinct().Count(),
                    TodaySchedule = todaySchedule,
                    PendingExams = pendingExams
                };

                return Ok(dashboard);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب لوحة المعلم", error = ex.Message });
            }
        }

        #endregion

        #region Grade Management

        [HttpPost("{id}/grades")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<IActionResult> AddGrade(string id, CreateGradeDto model)
        {
            try
            {
                // Verify teacher has permission to grade this student
                var hasPermission = await _context.TeacherAssignments
                    .AnyAsync(ta => ta.TeacherId == id &&
                                   ta.SubjectId == model.SubjectId &&
                                   ta.IsActive);

                if (!hasPermission && !User.IsInRole("Admin"))
                {
                    return Forbid("ليس لديك صلاحية لإدخال درجات هذه المادة");
                }

                var grade = new Grade
                {
                    StudentId = model.StudentId,
                    SubjectId = model.SubjectId,
                    AcademicYearId = model.AcademicYearId,
                    ExamType = model.ExamType,
                    Score = model.Score,
                    MaxScore = model.MaxScore,
                    Percentage = (model.Score / model.MaxScore) * 100,
                    Date = model.Date,
                    Semester = model.Semester,
                    Notes = model.Notes,
                    CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System",
                    CreatedAt = DateTime.UtcNow
                };

                _context.Grades.Add(grade);
                await _context.SaveChangesAsync();

                return Ok(new { message = "تم إضافة الدرجة بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في إضافة الدرجة", error = ex.Message });
            }
        }

        [HttpGet("{id}/students/{classId}")]
        public async Task<ActionResult<IEnumerable<ClassStudentDto>>> GetClassStudents(string id, int classId)
        {
            try
            {
                // Verify teacher has access to this class
                var hasAccess = await _context.TeacherAssignments
                    .AnyAsync(ta => ta.TeacherId == id && ta.ClassId == classId && ta.IsActive);

                if (!hasAccess && !User.IsInRole("Admin"))
                {
                    return Forbid("ليس لديك صلاحية للوصول لهذا الصف");
                }

                var students = await _context.StudentEnrollments
                    .Include(se => se.Student)
                    .Where(se => se.ClassId == classId && se.IsActive)
                    .Select(se => new ClassStudentDto
                    {
                        StudentId = se.StudentId,
                        StudentName = $"{se.Student.FirstName} {se.Student.LastName}",
                        StudentNumber = se.Student.UserName ?? "",
                        EnrollmentDate = se.EnrollmentDate
                    })
                    .OrderBy(s => s.StudentName)
                    .ToListAsync();

                return Ok(students);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب طلاب الصف", error = ex.Message });
            }
        }

        #endregion
    }
}
