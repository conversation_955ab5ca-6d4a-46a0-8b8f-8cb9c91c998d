﻿using System.ComponentModel.DataAnnotations;

namespace Schools.Shared.Models
{
    // Enums
    public enum UserRole
    {
        Ad<PERSON>,
        Teacher,
        Student,
        Parent,
        Employee,
        Accountant
    }

    public enum GradeLevel
    {
        Elementary,
        Middle,
        High
    }

    public enum AttendanceStatus
    {
        Present,
        Absent,
        Late,
        Excused
    }

    public enum BehaviorType
    {
        Excellent,
        Good,
        Fair,
        Poor
    }

    public enum ExamStatus
    {
        Draft,
        Published,
        Approved,
        Completed
    }

    public enum RequestStatus
    {
        Pending,
        Approved,
        Rejected
    }

    public enum PaymentStatus
    {
        Pending,
        Paid,
        Overdue,
        Cancelled
    }

    // Base Entity
    public abstract class BaseEntity
    {
        public int Id { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }
        public string? CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }
        public bool IsDeleted { get; set; } = false;
    }

    // Academic Year
    public class AcademicYear : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        public bool IsActive { get; set; } = false;

        // Navigation properties
        public virtual ICollection<StudentEnrollment> StudentEnrollments { get; set; } = new List<StudentEnrollment>();
        public virtual ICollection<TeacherAssignment> TeacherAssignments { get; set; } = new List<TeacherAssignment>();
    }

    // Grade Level
    public class Grade : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public GradeLevel Level { get; set; }

        public string? Description { get; set; }

        // Navigation properties
        public virtual ICollection<Class> Classes { get; set; } = new List<Class>();
    }

    // Class
    public class Class : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public int GradeId { get; set; }

        public int Capacity { get; set; } = 30;

        public string? Description { get; set; }

        // Navigation properties
        public virtual Grade Grade { get; set; } = null!;
        public virtual ICollection<Section> Sections { get; set; } = new List<Section>();
        public virtual ICollection<StudentEnrollment> StudentEnrollments { get; set; } = new List<StudentEnrollment>();
    }

    // Section
    public class Section : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public int ClassId { get; set; }

        public int Capacity { get; set; } = 30;

        // Navigation properties
        public virtual Class Class { get; set; } = null!;
        public virtual ICollection<StudentEnrollment> StudentEnrollments { get; set; } = new List<StudentEnrollment>();
        public virtual ICollection<Schedule> Schedules { get; set; } = new List<Schedule>();
    }

    // Subject
    public class Subject : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NameAr { get; set; }

        [Required]
        [StringLength(10)]
        public string Code { get; set; } = string.Empty;

        public string? Description { get; set; }

        public int CreditHours { get; set; } = 1;

        // Navigation properties
        public virtual ICollection<TeacherAssignment> TeacherAssignments { get; set; } = new List<TeacherAssignment>();
        public virtual ICollection<Schedule> Schedules { get; set; } = new List<Schedule>();
        public virtual ICollection<Exam> Exams { get; set; } = new List<Exam>();
        public virtual ICollection<Assignment> Assignments { get; set; } = new List<Assignment>();
    }
}
