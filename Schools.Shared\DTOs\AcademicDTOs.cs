using System.ComponentModel.DataAnnotations;

namespace Schools.Shared.DTOs
{
    // Academic Grade/Level DTOs
    public class AcademicGradeDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string GradeName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int SortOrder { get; set; }
        public int Level { get; set; } = 1;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }
        public List<ClassDto> Classes { get; set; } = new();
        public int TotalStudents { get; set; }
        public int TotalClasses { get; set; }
    }

    // Class DTOs
    public class ClassDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
        public int GradeId { get; set; }
        public string GradeName { get; set; } = string.Empty;
        public int Capacity { get; set; } = 30;
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }
        public List<SectionDto> Sections { get; set; } = new();
        public int TotalStudents { get; set; }
        public int TotalSections { get; set; }
        public string? ClassTeacher { get; set; }
        public string? ClassTeacherId { get; set; }
    }

    // Section DTOs
    public class SectionDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SectionName { get; set; } = string.Empty;
        public int ClassId { get; set; }
        public string ClassName { get; set; } = string.Empty;
        public int Capacity { get; set; } = 30;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }
        public int TotalStudents { get; set; }
        public string? SectionTeacher { get; set; }
        public string? SectionTeacherId { get; set; }
    }

    // Subject DTOs
    public class SubjectDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string SubjectName { get; set; } = string.Empty;
        public string? NameAr { get; set; }
        public string Code { get; set; } = string.Empty;
        public string SubjectCode { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int CreditHours { get; set; } = 1;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }
        public List<TeacherSubjectDto> Teachers { get; set; } = new();
        public int TotalTeachers { get; set; }
        public int TotalClasses { get; set; }
    }

    // Academic Year DTOs
    public class AcademicYearDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string AcademicYearName { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsActive { get; set; } = true;
        public string? Description { get; set; }
        public DateTime CreatedAt { get; set; }
        public int TotalStudents { get; set; }
        public int TotalTeachers { get; set; }
        public bool IsCurrent { get; set; }
    }

    // Create DTOs
    public class CreateAcademicGradeDto
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        public string? Description { get; set; }
        public int SortOrder { get; set; }
        public int Level { get; set; } = 1;
        public bool IsActive { get; set; } = true;
    }

    public class CreateClassDto
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public int GradeId { get; set; }

        public int Capacity { get; set; } = 30;
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public class CreateSectionDto
    {
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public int ClassId { get; set; }

        public int Capacity { get; set; } = 30;
        public bool IsActive { get; set; } = true;
    }

    public class CreateSubjectDto
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NameAr { get; set; }

        [Required]
        [StringLength(10)]
        public string Code { get; set; } = string.Empty;

        public string? Description { get; set; }
        public int CreditHours { get; set; } = 1;
        public bool IsActive { get; set; } = true;
    }

    public class CreateAcademicYearDto
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
    }

    // Update DTOs
    public class UpdateAcademicGradeDto
    {
        [StringLength(100)]
        public string? Name { get; set; }

        public string? Description { get; set; }
        public int? SortOrder { get; set; }
        public bool? IsActive { get; set; }
    }

    public class UpdateClassDto
    {
        [StringLength(100)]
        public string? Name { get; set; }

        public int? Capacity { get; set; }
        public string? Description { get; set; }
        public bool? IsActive { get; set; }
    }

    public class UpdateSectionDto
    {
        [StringLength(50)]
        public string? Name { get; set; }

        public int? Capacity { get; set; }
        public bool? IsActive { get; set; }
    }

    public class UpdateSubjectDto
    {
        [StringLength(100)]
        public string? Name { get; set; }

        [StringLength(100)]
        public string? NameAr { get; set; }

        public string? Description { get; set; }
        public int? CreditHours { get; set; }
        public bool? IsActive { get; set; }
    }

    public class UpdateAcademicYearDto
    {
        [StringLength(100)]
        public string? Name { get; set; }

        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Description { get; set; }
        public bool? IsActive { get; set; }
    }

    // Search DTOs
    public class AcademicSearchDto
    {
        public string? SearchTerm { get; set; }
        public bool? IsActive { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; } = false;
    }

    // Statistics DTOs
    public class AcademicStatisticsDto
    {
        public int TotalGrades { get; set; }
        public int TotalClasses { get; set; }
        public int TotalSections { get; set; }
        public int TotalSubjects { get; set; }
        public int TotalStudents { get; set; }
        public int TotalTeachers { get; set; }
        public int ActiveAcademicYears { get; set; }
        public double AverageClassSize { get; set; }
        public double AverageSectionSize { get; set; }
        public Dictionary<string, int> GradeDistribution { get; set; } = new();
        public Dictionary<string, int> SubjectDistribution { get; set; } = new();
    }

    // Assignment DTOs
    public class TeacherClassAssignmentDto
    {
        public int Id { get; set; }
        public string TeacherId { get; set; } = string.Empty;
        public string TeacherName { get; set; } = string.Empty;
        public int ClassId { get; set; }
        public string ClassName { get; set; } = string.Empty;
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public bool IsClassTeacher { get; set; }
        public DateTime AssignedDate { get; set; }
        public bool IsActive { get; set; }
    }

    public class CreateTeacherAssignmentDto
    {
        [Required]
        public string TeacherId { get; set; } = string.Empty;

        [Required]
        public int ClassId { get; set; }

        [Required]
        public int SubjectId { get; set; }

        public bool IsClassTeacher { get; set; } = false;

        [Required]
        public DateTime AssignedDate { get; set; }
    }

    // Parent DTOs
    public class ParentDto
    {
        public string Id { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string FullName => $"{FirstName} {LastName}";
        public string Email { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? NationalId { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public string? ProfilePicture { get; set; }
        public string? Occupation { get; set; }
        public string? WorkPlace { get; set; }
        public string? WorkPhone { get; set; }
        public string? EmergencyContact { get; set; }
        public string Relationship { get; set; } = string.Empty;
        public bool IsEmergencyContact { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public List<StudentDto> Children { get; set; } = new();
        public int TotalChildren { get; set; }
    }

    // Recent Grade DTO for dashboard
    public class RecentGradeDto
    {
        public int Id { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public string ExamType { get; set; } = string.Empty;
        public decimal Score { get; set; }
        public decimal MaxScore { get; set; }
        public decimal Percentage { get; set; }
        public string Grade { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public bool IsPassed { get; set; }
    }

    // Attendance Record DTO for dashboard
    public class AttendanceRecordDto
    {
        public int Id { get; set; }
        public DateTime Date { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public bool IsPresent { get; set; }
        public string Status { get; set; } = string.Empty;
        public TimeSpan? CheckInTime { get; set; }
        public TimeSpan? CheckOutTime { get; set; }
        public string? Reason { get; set; }
    }

    // Available Exam DTO for dashboard
    public class AvailableExamDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string SubjectName { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int DurationMinutes { get; set; }
        public int TotalMarks { get; set; }
        public int TotalQuestions { get; set; }
        public bool IsAvailable { get; set; }
        public bool IsCompleted { get; set; }
        public bool CanTakeExam { get; set; }
        public int AttemptsUsed { get; set; }
        public int MaxAttempts { get; set; }
        public DateTime? LastAttemptDate { get; set; }
        public double? BestScore { get; set; }
        public string Status { get; set; } = string.Empty;
    }
}
