using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using Schools.Shared.DTOs;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text.Json;

namespace Schools.Client.Services
{
    public class CustomAuthStateProvider : AuthenticationStateProvider
    {
        private readonly IJSRuntime _jsRuntime;
        private readonly string _tokenKey = "authToken";
        private readonly string _userKey = "currentUser";

        public CustomAuthStateProvider(IJSRuntime jsRuntime)
        {
            _jsRuntime = jsRuntime;
        }

        public override async Task<AuthenticationState> GetAuthenticationStateAsync()
        {
            try
            {
                var token = await GetTokenAsync();
                
                if (string.IsNullOrEmpty(token))
                {
                    return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
                }

                var claims = ParseClaimsFromJwt(token);
                var user = new ClaimsPrincipal(new ClaimsIdentity(claims, "jwt"));

                return new AuthenticationState(user);
            }
            catch
            {
                return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
            }
        }

        public async Task MarkUserAsAuthenticated(string token, UserDto user)
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("localStorage.setItem", _tokenKey, token);
                await _jsRuntime.InvokeVoidAsync("localStorage.setItem", _userKey, JsonSerializer.Serialize(user));

                var claims = ParseClaimsFromJwt(token);
                var authenticatedUser = new ClaimsPrincipal(new ClaimsIdentity(claims, "jwt"));

                NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(authenticatedUser)));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in MarkUserAsAuthenticated: {ex.Message}");
            }
        }

        public async Task MarkUserAsLoggedOut()
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", _tokenKey);
                await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", _userKey);

                var anonymousUser = new ClaimsPrincipal(new ClaimsIdentity());
                NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(anonymousUser)));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in MarkUserAsLoggedOut: {ex.Message}");
            }
        }

        public async Task<string?> GetTokenAsync()
        {
            try
            {
                return await _jsRuntime.InvokeAsync<string>("localStorage.getItem", _tokenKey);
            }
            catch
            {
                return null;
            }
        }

        public async Task<UserDto?> GetCurrentUserAsync()
        {
            try
            {
                var userJson = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", _userKey);
                if (string.IsNullOrEmpty(userJson))
                    return null;

                return JsonSerializer.Deserialize<UserDto>(userJson);
            }
            catch
            {
                return null;
            }
        }

        private IEnumerable<Claim> ParseClaimsFromJwt(string jwt)
        {
            var claims = new List<Claim>();
            
            try
            {
                var handler = new JwtSecurityTokenHandler();
                var token = handler.ReadJwtToken(jwt);

                claims.AddRange(token.Claims);

                // Add expiration claim
                if (token.ValidTo != DateTime.MinValue)
                {
                    claims.Add(new Claim("exp", new DateTimeOffset(token.ValidTo).ToUnixTimeSeconds().ToString()));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error parsing JWT: {ex.Message}");
            }

            return claims;
        }

        private bool IsTokenExpired(string token)
        {
            try
            {
                var handler = new JwtSecurityTokenHandler();
                var jwtToken = handler.ReadJwtToken(token);
                return jwtToken.ValidTo <= DateTime.UtcNow;
            }
            catch
            {
                return true;
            }
        }
    }
}
