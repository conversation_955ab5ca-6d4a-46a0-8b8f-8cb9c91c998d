namespace Schools.Shared.Models
{
    // Schedule Enums
    public enum ScheduleType
    {
        Regular = 1,
        Makeup = 2,
        Extra = 3,
        Exam = 4,
        Activity = 5,
        Assembly = 6,
        Break = 7,
        Lunch = 8,
        Study = 9,
        Lab = 10,
        Library = 11,
        Sports = 12,
        Prayer = 13,
        Meeting = 14,
        Event = 15
    }

    public enum ScheduleStatus
    {
        Scheduled = 1,
        InProgress = 2,
        Completed = 3,
        Cancelled = 4,
        Postponed = 5,
        Rescheduled = 6,
        Substituted = 7
    }

    public enum ScheduleChangeType
    {
        TimeChange = 1,
        DateChange = 2,
        RoomChange = 3,
        TeacherChange = 4,
        Cancellation = 5,
        Addition = 6,
        Substitution = 7
    }

    public enum ScheduleChangeStatus
    {
        Pending = 1,
        Approved = 2,
        Rejected = 3,
        Applied = 4,
        Cancelled = 5
    }

    public enum EnrollmentStatus
    {
        Active = 1,
        Inactive = 2,
        Withdrawn = 3,
        Transferred = 4,
        Graduated = 5,
        Suspended = 6,
        Expelled = 7
    }

    public enum HolidayType
    {
        National = 1,
        Religious = 2,
        School = 3,
        Emergency = 4,
        Vacation = 5,
        Exam = 6
    }

    public enum SlotType
    {
        Class = 1,
        Break = 2,
        Lunch = 3,
        Assembly = 4,
        Sports = 5,
        Library = 6,
        Lab = 7,
        Study = 8,
        Prayer = 9,
        Activity = 10
    }

    // Library Enums
    public enum BookStatus
    {
        Available = 1,
        Borrowed = 2,
        Reserved = 3,
        Maintenance = 4,
        Lost = 5,
        Damaged = 6
    }

    public enum BorrowStatus
    {
        Active = 1,
        Returned = 2,
        Overdue = 3,
        Lost = 4,
        Damaged = 5,
        Renewed = 6
    }

    public enum ReservationStatus
    {
        Active = 1,
        Fulfilled = 2,
        Expired = 3,
        Cancelled = 4
    }

    public enum LibraryCardStatus
    {
        Active = 1,
        Suspended = 2,
        Expired = 3,
        Blocked = 4,
        Lost = 5
    }

    public enum DigitalResourceType
    {
        EBook = 1,
        Video = 2,
        Audio = 3,
        Document = 4,
        Presentation = 5,
        Image = 6,
        Software = 7,
        Game = 8,
        Course = 9,
        Reference = 10
    }

    public enum AccessType
    {
        View = 1,
        Download = 2,
        Stream = 3,
        Print = 4
    }

    // Activity Enums
    public enum ActivityStatus
    {
        Planned = 1,
        Active = 2,
        Completed = 3,
        Cancelled = 4,
        Postponed = 5
    }

    public enum ActivityResourceType
    {
        Equipment = 1,
        Material = 2,
        Document = 3,
        Video = 4,
        Audio = 5,
        Software = 6,
        Venue = 7,
        Transportation = 8,
        Food = 9,
        Prize = 10
    }

    public enum EventType
    {
        Academic = 1,
        Sports = 2,
        Cultural = 3,
        Social = 4,
        Religious = 5,
        Scientific = 6,
        Arts = 7,
        Competition = 8,
        Workshop = 9,
        Seminar = 10,
        Conference = 11,
        Exhibition = 12,
        Trip = 13,
        Ceremony = 14,
        Meeting = 15
    }

    public enum EventStatus
    {
        Planned = 1,
        Published = 2,
        RegistrationOpen = 3,
        RegistrationClosed = 4,
        InProgress = 5,
        Completed = 6,
        Cancelled = 7,
        Postponed = 8
    }

    public enum RegistrationStatus
    {
        Pending = 1,
        Confirmed = 2,
        Cancelled = 3,
        WaitingList = 4,
        Rejected = 5
    }

    // Exam Enums
    public enum QuestionType
    {
        MultipleChoice = 1,
        TrueFalse = 2,
        ShortAnswer = 3,
        Essay = 4,
        FillInTheBlank = 5,
        Matching = 6,
        Numerical = 7,
        Ordering = 8
    }

    public enum AttemptStatus
    {
        NotStarted = 1,
        InProgress = 2,
        Completed = 3,
        Submitted = 4,
        TimedOut = 5,
        Cancelled = 6,
        Graded = 7
    }

}
