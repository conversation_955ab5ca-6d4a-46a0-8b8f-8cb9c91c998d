using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;
using System.Security.Claims;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AcademicGradesController : ControllerBase
    {
        private readonly SchoolsDbContext _context;

        public AcademicGradesController(SchoolsDbContext context)
        {
            _context = context;
        }

        #region Academic Grades Management

        [HttpGet]
        public async Task<ActionResult<IEnumerable<AcademicGradeDto>>> GetAcademicGrades()
        {
            try
            {
                var grades = await _context.AcademicGrades
                    .Include(g => g.Classes)
                    .ThenInclude(c => c.StudentEnrollments.Where(se => se.IsActive))
                    .Where(g => g.IsActive)
                    .Select(g => new AcademicGradeDto
                    {
                        Id = g.Id,
                        Name = g.Name,
                        GradeName = g.Name,
                        Description = g.Description,
                        SortOrder = g.SortOrder,
                        Level = g.Level,
                        IsActive = g.IsActive,
                        CreatedAt = g.CreatedAt,
                        TotalClasses = g.Classes.Count(c => c.IsActive),
                        TotalStudents = g.Classes
                            .Where(c => c.IsActive)
                            .SelectMany(c => c.StudentEnrollments)
                            .Count(se => se.IsActive),
                        Classes = g.Classes
                            .Where(c => c.IsActive)
                            .Select(c => new ClassDto
                            {
                                Id = c.Id,
                                Name = c.Name,
                                ClassName = c.Name,
                                GradeId = c.AcademicGradeId,
                                GradeName = g.Name,
                                Capacity = c.Capacity,
                                IsActive = c.IsActive,
                                CreatedAt = c.CreatedAt,
                                TotalStudents = c.StudentEnrollments.Count(se => se.IsActive)
                            }).ToList()
                    })
                    .OrderBy(g => g.SortOrder)
                    .ToListAsync();

                return Ok(grades);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب المراحل الدراسية", error = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<AcademicGradeDto>> GetAcademicGrade(int id)
        {
            try
            {
                var grade = await _context.AcademicGrades
                    .Include(g => g.Classes)
                    .ThenInclude(c => c.StudentEnrollments.Where(se => se.IsActive))
                    .ThenInclude(se => se.Student)
                    .FirstOrDefaultAsync(g => g.Id == id && g.IsActive);

                if (grade == null)
                {
                    return NotFound(new { message = "المرحلة الدراسية غير موجودة" });
                }

                var gradeDto = new AcademicGradeDto
                {
                    Id = grade.Id,
                    Name = grade.Name,
                    GradeName = grade.Name,
                    Description = grade.Description,
                    SortOrder = grade.SortOrder,
                    Level = grade.Level,
                    IsActive = grade.IsActive,
                    CreatedAt = grade.CreatedAt,
                    TotalClasses = grade.Classes.Count(c => c.IsActive),
                    TotalStudents = grade.Classes
                        .Where(c => c.IsActive)
                        .SelectMany(c => c.StudentEnrollments)
                        .Count(se => se.IsActive),
                    Classes = grade.Classes
                        .Where(c => c.IsActive)
                        .Select(c => new ClassDto
                        {
                            Id = c.Id,
                            Name = c.Name,
                            ClassName = c.Name,
                            GradeId = c.AcademicGradeId,
                            GradeName = grade.Name,
                            Capacity = c.Capacity,
                            Description = c.Description,
                            IsActive = c.IsActive,
                            CreatedAt = c.CreatedAt,
                            TotalStudents = c.StudentEnrollments.Count(se => se.IsActive)
                        }).ToList()
                };

                return Ok(gradeDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب المرحلة الدراسية", error = ex.Message });
            }
        }

        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<AcademicGradeDto>> CreateAcademicGrade(CreateAcademicGradeDto model)
        {
            try
            {
                // Check if grade name already exists
                var existingGrade = await _context.AcademicGrades
                    .FirstOrDefaultAsync(g => g.Name == model.Name && g.IsActive);

                if (existingGrade != null)
                {
                    return BadRequest(new { message = "اسم المرحلة الدراسية موجود بالفعل" });
                }

                var grade = new AcademicGrade
                {
                    Name = model.Name,
                    Description = model.Description,
                    SortOrder = model.SortOrder,
                    Level = model.Level,
                    IsActive = model.IsActive,
                    CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System",
                    CreatedAt = DateTime.UtcNow
                };

                _context.AcademicGrades.Add(grade);
                await _context.SaveChangesAsync();

                var gradeDto = new AcademicGradeDto
                {
                    Id = grade.Id,
                    Name = grade.Name,
                    GradeName = grade.Name,
                    Description = grade.Description,
                    SortOrder = grade.SortOrder,
                    Level = grade.Level,
                    IsActive = grade.IsActive,
                    CreatedAt = grade.CreatedAt,
                    TotalClasses = 0,
                    TotalStudents = 0,
                    Classes = new List<ClassDto>()
                };

                return CreatedAtAction(nameof(GetAcademicGrade), new { id = grade.Id }, gradeDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في إنشاء المرحلة الدراسية", error = ex.Message });
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UpdateAcademicGrade(int id, UpdateAcademicGradeDto model)
        {
            try
            {
                var grade = await _context.AcademicGrades.FindAsync(id);
                if (grade == null || !grade.IsActive)
                {
                    return NotFound(new { message = "المرحلة الدراسية غير موجودة" });
                }

                // Check if new name already exists (excluding current grade)
                if (!string.IsNullOrEmpty(model.Name) && model.Name != grade.Name)
                {
                    var existingGrade = await _context.AcademicGrades
                        .FirstOrDefaultAsync(g => g.Name == model.Name && g.Id != id && g.IsActive);

                    if (existingGrade != null)
                    {
                        return BadRequest(new { message = "اسم المرحلة الدراسية موجود بالفعل" });
                    }
                    grade.Name = model.Name;
                }

                if (!string.IsNullOrEmpty(model.Description))
                    grade.Description = model.Description;
                if (model.SortOrder.HasValue)
                    grade.SortOrder = model.SortOrder.Value;
                if (model.IsActive.HasValue)
                    grade.IsActive = model.IsActive.Value;

                grade.UpdatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System";
                grade.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تحديث المرحلة الدراسية بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحديث المرحلة الدراسية", error = ex.Message });
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteAcademicGrade(int id)
        {
            try
            {
                var grade = await _context.AcademicGrades
                    .Include(g => g.Classes)
                    .FirstOrDefaultAsync(g => g.Id == id && g.IsActive);

                if (grade == null)
                {
                    return NotFound(new { message = "المرحلة الدراسية غير موجودة" });
                }

                // Check if grade has active classes
                var hasActiveClasses = grade.Classes.Any(c => c.IsActive);
                if (hasActiveClasses)
                {
                    return BadRequest(new { message = "لا يمكن حذف مرحلة دراسية تحتوي على صفوف نشطة" });
                }

                grade.IsActive = false;
                grade.DeletedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System";
                grade.DeletedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم حذف المرحلة الدراسية بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في حذف المرحلة الدراسية", error = ex.Message });
            }
        }

        #endregion

        #region Statistics

        [HttpGet("statistics")]
        public async Task<ActionResult<AcademicStatisticsDto>> GetAcademicStatistics()
        {
            try
            {
                var statistics = new AcademicStatisticsDto
                {
                    TotalGrades = await _context.AcademicGrades.CountAsync(g => g.IsActive),
                    TotalClasses = await _context.Classes.CountAsync(c => c.IsActive),
                    TotalSections = await _context.Sections.CountAsync(s => s.IsActive),
                    TotalSubjects = await _context.Subjects.CountAsync(s => s.IsActive),
                    TotalStudents = await _context.StudentEnrollments.CountAsync(se => se.IsActive),
                    TotalTeachers = await _context.TeacherAssignments.CountAsync(ta => ta.IsActive),
                    ActiveAcademicYears = await _context.AcademicYears.CountAsync(ay => ay.IsActive)
                };

                // Calculate averages
                var totalClassCapacity = await _context.Classes
                    .Where(c => c.IsActive)
                    .SumAsync(c => c.Capacity);

                var totalSectionCapacity = await _context.Sections
                    .Where(s => s.IsActive)
                    .SumAsync(s => s.Capacity);

                statistics.AverageClassSize = statistics.TotalClasses > 0 ? 
                    (double)totalClassCapacity / statistics.TotalClasses : 0;

                statistics.AverageSectionSize = statistics.TotalSections > 0 ? 
                    (double)totalSectionCapacity / statistics.TotalSections : 0;

                // Grade distribution
                var gradeDistribution = await _context.AcademicGrades
                    .Where(g => g.IsActive)
                    .Select(g => new { 
                        GradeName = g.Name, 
                        StudentCount = g.Classes
                            .Where(c => c.IsActive)
                            .SelectMany(c => c.StudentEnrollments)
                            .Count(se => se.IsActive)
                    })
                    .ToDictionaryAsync(x => x.GradeName, x => x.StudentCount);

                statistics.GradeDistribution = gradeDistribution;

                // Subject distribution
                var subjectDistribution = await _context.Subjects
                    .Where(s => s.IsActive)
                    .Select(s => new { 
                        SubjectName = s.Name, 
                        TeacherCount = s.TeacherAssignments.Count(ta => ta.IsActive)
                    })
                    .ToDictionaryAsync(x => x.SubjectName, x => x.TeacherCount);

                statistics.SubjectDistribution = subjectDistribution;

                return Ok(statistics);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب الإحصائيات", error = ex.Message });
            }
        }

        #endregion
    }
}
