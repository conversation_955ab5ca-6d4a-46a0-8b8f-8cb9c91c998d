using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;

namespace Schools.API.Services
{
    public interface IAttendanceSummaryService
    {
        Task<List<EnhancedAttendanceSummaryDto>> GetAttendanceSummariesAsync(int? academicYearId = null, int? classId = null, int? semesterId = null);
        Task<EnhancedAttendanceSummaryDto?> GetStudentAttendanceSummaryAsync(string studentId, int academicYearId, int? semesterId = null);
        Task<List<EnhancedAttendanceSummaryDto>> GetClassAttendanceSummariesAsync(int classId, int academicYearId, int? semesterId = null);
        Task<bool> RecalculateAttendanceSummariesAsync(int? academicYearId = null, string? studentId = null);
        Task<bool> RecalculateStudentSummaryAsync(string studentId, int academicYearId, int? semesterId = null);
        Task<AttendanceComparisonDto> GetAttendanceComparisonAsync(int academicYearId, int? previousYearId = null);
        Task<List<AttendanceTrendDto>> GetAttendanceTrendsAsync(string studentId, int months = 6);
        Task<List<EnhancedAttendanceSummaryDto>> GetLowAttendanceStudentsAsync(decimal threshold = 80m, int? academicYearId = null);
        Task<AttendanceRankingDto> GetAttendanceRankingsAsync(int? classId = null, int? academicYearId = null);
        Task<bool> GenerateMonthlyAttendanceReportAsync(int month, int year, int? classId = null);
    }

    public class AttendanceSummaryService : IAttendanceSummaryService
    {
        private readonly SchoolsDbContext _context;
        private readonly ILogger<AttendanceSummaryService> _logger;

        public AttendanceSummaryService(SchoolsDbContext context, ILogger<AttendanceSummaryService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<EnhancedAttendanceSummaryDto>> GetAttendanceSummariesAsync(int? academicYearId = null, int? classId = null, int? semesterId = null)
        {
            try
            {
                var query = _context.AttendanceSummaries
                    .Include(s => s.Student)
                    .Include(s => s.AcademicYear)
                    .Include(s => s.Class)
                    .AsQueryable();

                if (academicYearId.HasValue)
                    query = query.Where(s => s.AcademicYearId == academicYearId.Value);

                if (classId.HasValue)
                    query = query.Where(s => s.ClassId == classId.Value);

                if (semesterId.HasValue)
                    query = query.Where(s => s.SemesterId == semesterId.Value);

                var summaries = await query
                    .OrderBy(s => s.Class.Name)
                    .ThenBy(s => s.Student.FirstName)
                    .ThenBy(s => s.Student.LastName)
                    .ToListAsync();

                return summaries.Select(s => new EnhancedAttendanceSummaryDto
                {
                    Id = s.Id,
                    StudentId = s.StudentId,
                    StudentName = $"{s.Student.FirstName} {s.Student.LastName}",
                    StudentNumber = s.Student.UserName ?? "",
                    AcademicYearId = s.AcademicYearId,
                    AcademicYearName = s.AcademicYear.Name,
                    SemesterId = s.SemesterId,
                    ClassId = s.ClassId,
                    ClassName = s.Class?.Name,
                    TotalDays = s.TotalDays,
                    PresentDays = s.PresentDays,
                    AbsentDays = s.AbsentDays,
                    LateDays = s.LateDays,
                    ExcusedDays = s.ExcusedDays,
                    AttendanceRate = s.AttendanceRate,
                    LateRate = s.LateRate,
                    LastUpdated = s.LastUpdated
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting attendance summaries");
                throw;
            }
        }

        public async Task<EnhancedAttendanceSummaryDto?> GetStudentAttendanceSummaryAsync(string studentId, int academicYearId, int? semesterId = null)
        {
            try
            {
                var summary = await _context.AttendanceSummaries
                    .Include(s => s.Student)
                    .Include(s => s.AcademicYear)
                    .Include(s => s.Class)
                    .FirstOrDefaultAsync(s => s.StudentId == studentId && 
                                            s.AcademicYearId == academicYearId &&
                                            (semesterId == null || s.SemesterId == semesterId));

                if (summary == null)
                {
                    // Try to calculate from attendance records if summary doesn't exist
                    return await CalculateStudentSummaryFromRecordsAsync(studentId, academicYearId, semesterId);
                }

                return new EnhancedAttendanceSummaryDto
                {
                    Id = summary.Id,
                    StudentId = summary.StudentId,
                    StudentName = $"{summary.Student.FirstName} {summary.Student.LastName}",
                    StudentNumber = summary.Student.UserName ?? "",
                    AcademicYearId = summary.AcademicYearId,
                    AcademicYearName = summary.AcademicYear.Name,
                    SemesterId = summary.SemesterId,
                    ClassId = summary.ClassId,
                    ClassName = summary.Class?.Name,
                    TotalDays = summary.TotalDays,
                    PresentDays = summary.PresentDays,
                    AbsentDays = summary.AbsentDays,
                    LateDays = summary.LateDays,
                    ExcusedDays = summary.ExcusedDays,
                    AttendanceRate = summary.AttendanceRate,
                    LateRate = summary.LateRate,
                    LastUpdated = summary.LastUpdated
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting student attendance summary for {StudentId}", studentId);
                throw;
            }
        }

        public async Task<List<EnhancedAttendanceSummaryDto>> GetClassAttendanceSummariesAsync(int classId, int academicYearId, int? semesterId = null)
        {
            try
            {
                var summaries = await _context.AttendanceSummaries
                    .Include(s => s.Student)
                    .Include(s => s.AcademicYear)
                    .Include(s => s.Class)
                    .Where(s => s.ClassId == classId && 
                              s.AcademicYearId == academicYearId &&
                              (semesterId == null || s.SemesterId == semesterId))
                    .OrderBy(s => s.Student.FirstName)
                    .ThenBy(s => s.Student.LastName)
                    .ToListAsync();

                return summaries.Select(s => new EnhancedAttendanceSummaryDto
                {
                    Id = s.Id,
                    StudentId = s.StudentId,
                    StudentName = $"{s.Student.FirstName} {s.Student.LastName}",
                    StudentNumber = s.Student.UserName ?? "",
                    AcademicYearId = s.AcademicYearId,
                    AcademicYearName = s.AcademicYear.Name,
                    SemesterId = s.SemesterId,
                    ClassId = s.ClassId,
                    ClassName = s.Class?.Name,
                    TotalDays = s.TotalDays,
                    PresentDays = s.PresentDays,
                    AbsentDays = s.AbsentDays,
                    LateDays = s.LateDays,
                    ExcusedDays = s.ExcusedDays,
                    AttendanceRate = s.AttendanceRate,
                    LateRate = s.LateRate,
                    LastUpdated = s.LastUpdated
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting class attendance summaries for class {ClassId}", classId);
                throw;
            }
        }

        public async Task<bool> RecalculateAttendanceSummariesAsync(int? academicYearId = null, string? studentId = null)
        {
            try
            {
                var query = _context.Users.AsQueryable();

                if (!string.IsNullOrEmpty(studentId))
                    query = query.Where(u => u.Id == studentId);

                var students = await query.ToListAsync();

                foreach (var student in students)
                {
                    if (academicYearId.HasValue)
                    {
                        await RecalculateStudentSummaryAsync(student.Id, academicYearId.Value);
                    }
                    else
                    {
                        // Recalculate for all academic years
                        var academicYears = await _context.AcademicYears.ToListAsync();
                        foreach (var year in academicYears)
                        {
                            await RecalculateStudentSummaryAsync(student.Id, year.Id);
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recalculating attendance summaries");
                throw;
            }
        }

        public async Task<bool> RecalculateStudentSummaryAsync(string studentId, int academicYearId, int? semesterId = null)
        {
            try
            {
                // Get or create summary
                var summary = await _context.AttendanceSummaries
                    .FirstOrDefaultAsync(s => s.StudentId == studentId && 
                                            s.AcademicYearId == academicYearId &&
                                            (semesterId == null || s.SemesterId == semesterId));

                if (summary == null)
                {
                    summary = new AttendanceSummary
                    {
                        StudentId = studentId,
                        AcademicYearId = academicYearId,
                        SemesterId = semesterId,
                        CreatedAt = DateTime.UtcNow
                    };
                    _context.AttendanceSummaries.Add(summary);
                }

                // Get attendance records
                var recordsQuery = _context.AttendanceRecords
                    .Where(ar => ar.StudentId == studentId && ar.AcademicYearId == academicYearId);

                if (semesterId.HasValue)
                    recordsQuery = recordsQuery.Where(ar => ar.SemesterId == semesterId);

                var records = await recordsQuery.ToListAsync();

                // Calculate statistics
                summary.TotalDays = records.Count;
                summary.PresentDays = records.Count(r => r.IsPresent);
                summary.AbsentDays = records.Count(r => !r.IsPresent);
                summary.LateDays = records.Count(r => r.IsLate);
                summary.ExcusedDays = records.Count(r => r.IsExcused);
                summary.AttendanceRate = summary.TotalDays > 0 ? (decimal)summary.PresentDays / summary.TotalDays * 100 : 0;
                summary.LateRate = summary.TotalDays > 0 ? (decimal)summary.LateDays / summary.TotalDays * 100 : 0;
                summary.LastUpdated = DateTime.UtcNow;
                summary.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recalculating student summary for {StudentId}", studentId);
                throw;
            }
        }

        // Placeholder methods for additional functionality
        public async Task<AttendanceComparisonDto> GetAttendanceComparisonAsync(int academicYearId, int? previousYearId = null)
        {
            // Implementation will be added
            return new AttendanceComparisonDto();
        }

        public async Task<List<AttendanceTrendDto>> GetAttendanceTrendsAsync(string studentId, int months = 6)
        {
            // Implementation will be added
            return new List<AttendanceTrendDto>();
        }

        public async Task<List<EnhancedAttendanceSummaryDto>> GetLowAttendanceStudentsAsync(decimal threshold = 80m, int? academicYearId = null)
        {
            // Implementation will be added
            return new List<EnhancedAttendanceSummaryDto>();
        }

        public async Task<AttendanceRankingDto> GetAttendanceRankingsAsync(int? classId = null, int? academicYearId = null)
        {
            // Implementation will be added
            return new AttendanceRankingDto();
        }

        public async Task<bool> GenerateMonthlyAttendanceReportAsync(int month, int year, int? classId = null)
        {
            // Implementation will be added
            return true;
        }

        // Helper method
        private async Task<EnhancedAttendanceSummaryDto?> CalculateStudentSummaryFromRecordsAsync(string studentId, int academicYearId, int? semesterId = null)
        {
            var recordsQuery = _context.AttendanceRecords
                .Where(ar => ar.StudentId == studentId && ar.AcademicYearId == academicYearId);

            if (semesterId.HasValue)
                recordsQuery = recordsQuery.Where(ar => ar.SemesterId == semesterId);

            var records = await recordsQuery.ToListAsync();

            if (!records.Any())
                return null;

            var student = await _context.Users.FirstOrDefaultAsync(u => u.Id == studentId);
            var academicYear = await _context.AcademicYears.FirstOrDefaultAsync(ay => ay.Id == academicYearId);

            var totalDays = records.Count;
            var presentDays = records.Count(r => r.IsPresent);
            var absentDays = records.Count(r => !r.IsPresent);
            var lateDays = records.Count(r => r.IsLate);
            var excusedDays = records.Count(r => r.IsExcused);

            return new EnhancedAttendanceSummaryDto
            {
                StudentId = studentId,
                StudentName = student != null ? $"{student.FirstName} {student.LastName}" : "",
                StudentNumber = student?.UserName ?? "",
                AcademicYearId = academicYearId,
                AcademicYearName = academicYear?.Name ?? "",
                SemesterId = semesterId,
                TotalDays = totalDays,
                PresentDays = presentDays,
                AbsentDays = absentDays,
                LateDays = lateDays,
                ExcusedDays = excusedDays,
                AttendanceRate = totalDays > 0 ? (decimal)presentDays / totalDays * 100 : 0,
                LateRate = totalDays > 0 ? (decimal)lateDays / totalDays * 100 : 0,
                LastUpdated = DateTime.UtcNow
            };
        }
    }

    // Additional DTOs for attendance summary features
    public class AttendanceComparisonDto
    {
        public int CurrentYearId { get; set; }
        public int? PreviousYearId { get; set; }
        public decimal CurrentYearAttendanceRate { get; set; }
        public decimal? PreviousYearAttendanceRate { get; set; }
        public decimal ChangePercentage { get; set; }
        public string Trend { get; set; } = string.Empty; // "Improving", "Declining", "Stable"
        public List<MonthlyComparisonDto> MonthlyComparisons { get; set; } = new();
    }

    public class MonthlyComparisonDto
    {
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public decimal CurrentYearRate { get; set; }
        public decimal? PreviousYearRate { get; set; }
    }

    public class AttendanceTrendDto
    {
        public DateTime Date { get; set; }
        public decimal AttendanceRate { get; set; }
        public int TotalDays { get; set; }
        public int PresentDays { get; set; }
        public int AbsentDays { get; set; }
        public int LateDays { get; set; }
    }

    public class AttendanceRankingDto
    {
        public List<StudentRankingDto> TopPerformers { get; set; } = new();
        public List<StudentRankingDto> LowPerformers { get; set; } = new();
        public decimal ClassAverage { get; set; }
        public decimal SchoolAverage { get; set; }
    }

    public class StudentRankingDto
    {
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public string? ClassName { get; set; }
        public decimal AttendanceRate { get; set; }
        public int Rank { get; set; }
    }
}
