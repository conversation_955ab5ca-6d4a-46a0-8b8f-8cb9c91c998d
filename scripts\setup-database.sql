-- إعد<PERSON> قاعدة البيانات الأولي
-- Initial Database Setup

USE master;
GO

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'SchoolsDB')
BEGIN
    CREATE DATABASE SchoolsDB
    COLLATE Arabic_CI_AS;
END
GO

USE SchoolsDB;
GO

-- إعداد الإعدادات الأساسية
ALTER DATABASE SchoolsDB SET RECOVERY SIMPLE;
ALTER DATABASE SchoolsDB SET AUTO_CLOSE OFF;
ALTER DATABASE SchoolsDB SET AUTO_SHRINK OFF;
GO

-- إ<PERSON><PERSON><PERSON><PERSON> مخطط للبيانات الأكاديمية
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Academic')
BEGIN
    EXEC('CREATE SCHEMA Academic');
END
GO

-- إنشاء مخطط للبيانات المالية
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Finance')
BEGIN
    EXEC('CREATE SCHEMA Finance');
END
GO

-- إنشاء مخطط للامتحانات
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Exams')
BEGIN
    EXEC('CREATE SCHEMA Exams');
END
GO

-- إنشاء مخطط للتقارير
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Reports')
BEGIN
    EXEC('CREATE SCHEMA Reports');
END
GO

-- إنشاء فهارس للأداء
-- Performance Indexes

-- فهرس للبحث في المستخدمين
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_Email')
BEGIN
    CREATE NONCLUSTERED INDEX IX_Users_Email 
    ON AspNetUsers (Email);
END
GO

-- فهرس للبحث في الطلاب
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Students_StudentNumber')
BEGIN
    CREATE NONCLUSTERED INDEX IX_Students_StudentNumber 
    ON Students (StudentNumber) WHERE StudentNumber IS NOT NULL;
END
GO

-- إنشاء دوال مساعدة
-- Helper Functions

-- دالة لحساب العمر
CREATE OR ALTER FUNCTION dbo.CalculateAge(@BirthDate DATE)
RETURNS INT
AS
BEGIN
    DECLARE @Age INT;
    SET @Age = DATEDIFF(YEAR, @BirthDate, GETDATE());
    
    IF (MONTH(@BirthDate) > MONTH(GETDATE())) 
        OR (MONTH(@BirthDate) = MONTH(GETDATE()) AND DAY(@BirthDate) > DAY(GETDATE()))
    BEGIN
        SET @Age = @Age - 1;
    END
    
    RETURN @Age;
END
GO

-- دالة لحساب المعدل
CREATE OR ALTER FUNCTION dbo.CalculateGPA(@StudentId NVARCHAR(450))
RETURNS DECIMAL(3,2)
AS
BEGIN
    DECLARE @GPA DECIMAL(3,2);
    
    SELECT @GPA = AVG(CAST(Score AS DECIMAL(5,2)) / CAST(MaxScore AS DECIMAL(5,2)) * 4.0)
    FROM Grades 
    WHERE StudentId = @StudentId 
    AND IsActive = 1;
    
    RETURN ISNULL(@GPA, 0.00);
END
GO

-- إنشاء إجراءات مخزنة للتقارير
-- Stored Procedures for Reports

-- إجراء لتقرير أداء الطلاب
CREATE OR ALTER PROCEDURE Reports.GetStudentPerformanceReport
    @ClassId INT = NULL,
    @SubjectId INT = NULL,
    @StartDate DATE = NULL,
    @EndDate DATE = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        s.Id AS StudentId,
        s.FirstName + ' ' + s.LastName AS StudentName,
        s.StudentNumber,
        c.Name AS ClassName,
        sub.Name AS SubjectName,
        AVG(CAST(g.Score AS DECIMAL(5,2))) AS AverageScore,
        COUNT(g.Id) AS TotalGrades,
        MIN(g.Date) AS FirstGradeDate,
        MAX(g.Date) AS LastGradeDate
    FROM Students s
    INNER JOIN StudentEnrollments se ON s.Id = se.StudentId
    INNER JOIN Classes c ON se.ClassId = c.Id
    LEFT JOIN Grades g ON s.Id = g.StudentId
    LEFT JOIN Subjects sub ON g.SubjectId = sub.Id
    WHERE 
        (@ClassId IS NULL OR c.Id = @ClassId)
        AND (@SubjectId IS NULL OR sub.Id = @SubjectId)
        AND (@StartDate IS NULL OR g.Date >= @StartDate)
        AND (@EndDate IS NULL OR g.Date <= @EndDate)
        AND se.IsActive = 1
        AND s.IsActive = 1
    GROUP BY 
        s.Id, s.FirstName, s.LastName, s.StudentNumber, 
        c.Name, sub.Name
    ORDER BY 
        c.Name, s.FirstName, s.LastName;
END
GO

-- إجراء لتقرير الحضور
CREATE OR ALTER PROCEDURE Reports.GetAttendanceReport
    @ClassId INT = NULL,
    @StartDate DATE = NULL,
    @EndDate DATE = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        s.Id AS StudentId,
        s.FirstName + ' ' + s.LastName AS StudentName,
        s.StudentNumber,
        c.Name AS ClassName,
        COUNT(a.Id) AS TotalDays,
        SUM(CASE WHEN a.IsPresent = 1 THEN 1 ELSE 0 END) AS PresentDays,
        SUM(CASE WHEN a.IsPresent = 0 THEN 1 ELSE 0 END) AS AbsentDays,
        SUM(CASE WHEN a.IsLate = 1 THEN 1 ELSE 0 END) AS LateDays,
        CAST(SUM(CASE WHEN a.IsPresent = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(a.Id) AS DECIMAL(5,2)) AS AttendanceRate
    FROM Students s
    INNER JOIN StudentEnrollments se ON s.Id = se.StudentId
    INNER JOIN Classes c ON se.ClassId = c.Id
    LEFT JOIN AttendanceRecords a ON s.Id = a.StudentId
    WHERE 
        (@ClassId IS NULL OR c.Id = @ClassId)
        AND (@StartDate IS NULL OR a.Date >= @StartDate)
        AND (@EndDate IS NULL OR a.Date <= @EndDate)
        AND se.IsActive = 1
        AND s.IsActive = 1
    GROUP BY 
        s.Id, s.FirstName, s.LastName, s.StudentNumber, c.Name
    ORDER BY 
        c.Name, s.FirstName, s.LastName;
END
GO

-- إنشاء مشاهد للتقارير السريعة
-- Views for Quick Reports

-- مشهد لإحصائيات الطلاب
CREATE OR ALTER VIEW Reports.StudentStatistics AS
SELECT 
    c.Name AS ClassName,
    COUNT(DISTINCT s.Id) AS TotalStudents,
    COUNT(DISTINCT CASE WHEN s.Gender = 'Male' THEN s.Id END) AS MaleStudents,
    COUNT(DISTINCT CASE WHEN s.Gender = 'Female' THEN s.Id END) AS FemaleStudents,
    AVG(dbo.CalculateAge(s.DateOfBirth)) AS AverageAge
FROM Students s
INNER JOIN StudentEnrollments se ON s.Id = se.StudentId
INNER JOIN Classes c ON se.ClassId = c.Id
WHERE se.IsActive = 1 AND s.IsActive = 1
GROUP BY c.Id, c.Name;
GO

-- مشهد لإحصائيات المعلمين
CREATE OR ALTER VIEW Reports.TeacherStatistics AS
SELECT 
    t.Id AS TeacherId,
    t.FirstName + ' ' + t.LastName AS TeacherName,
    COUNT(DISTINCT tc.ClassId) AS TotalClasses,
    COUNT(DISTINCT ts.SubjectId) AS TotalSubjects,
    COUNT(DISTINCT se.StudentId) AS TotalStudents
FROM Teachers t
LEFT JOIN TeacherClasses tc ON t.Id = tc.TeacherId
LEFT JOIN TeacherSubjects ts ON t.Id = ts.TeacherId
LEFT JOIN StudentEnrollments se ON tc.ClassId = se.ClassId
WHERE t.IsActive = 1 AND tc.IsActive = 1 AND ts.IsActive = 1 AND se.IsActive = 1
GROUP BY t.Id, t.FirstName, t.LastName;
GO

-- إنشاء مشغلات للتدقيق
-- Audit Triggers

-- مشغل لتسجيل تغييرات الدرجات
CREATE OR ALTER TRIGGER tr_Grades_Audit
ON Grades
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- تسجيل العمليات في جدول التدقيق
    INSERT INTO AuditLog (TableName, Operation, RecordId, UserId, Timestamp, OldValues, NewValues)
    SELECT 
        'Grades',
        CASE 
            WHEN EXISTS(SELECT * FROM inserted) AND EXISTS(SELECT * FROM deleted) THEN 'UPDATE'
            WHEN EXISTS(SELECT * FROM inserted) THEN 'INSERT'
            ELSE 'DELETE'
        END,
        COALESCE(i.Id, d.Id),
        SYSTEM_USER,
        GETDATE(),
        (SELECT * FROM deleted d2 WHERE d2.Id = COALESCE(i.Id, d.Id) FOR JSON AUTO),
        (SELECT * FROM inserted i2 WHERE i2.Id = COALESCE(i.Id, d.Id) FOR JSON AUTO)
    FROM inserted i
    FULL OUTER JOIN deleted d ON i.Id = d.Id;
END
GO

PRINT 'تم إعداد قاعدة البيانات بنجاح';
PRINT 'Database setup completed successfully';
GO
