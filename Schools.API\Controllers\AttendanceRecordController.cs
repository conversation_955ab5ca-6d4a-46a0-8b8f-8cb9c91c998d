using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Schools.API.Services;
using Schools.Shared.DTOs;
using System.Security.Claims;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AttendanceRecordController : ControllerBase
    {
        private readonly IAttendanceRecordService _attendanceRecordService;
        private readonly IAttendanceSummaryService _attendanceSummaryService;
        private readonly ILogger<AttendanceRecordController> _logger;

        public AttendanceRecordController(
            IAttendanceRecordService attendanceRecordService,
            IAttendanceSummaryService attendanceSummaryService,
            ILogger<AttendanceRecordController> logger)
        {
            _attendanceRecordService = attendanceRecordService;
            _attendanceSummaryService = attendanceSummaryService;
            _logger = logger;
        }

        private string GetCurrentUserId()
        {
            return User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "";
        }

        [HttpPost]
        [Authorize(Roles = "Admin,Teacher,Staff")]
        public async Task<ActionResult<AttendanceRecordDto>> CreateAttendanceRecord([FromBody] CreateAttendanceRecordDto createDto)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                var result = await _attendanceRecordService.CreateAttendanceRecordAsync(createDto, currentUserId);
                return Ok(result);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating attendance record");
                return StatusCode(500, "An error occurred while creating the attendance record");
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,Teacher,Staff")]
        public async Task<ActionResult<AttendanceRecordDto>> UpdateAttendanceRecord(int id, [FromBody] UpdateAttendanceRecordDto updateDto)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                var result = await _attendanceRecordService.UpdateAttendanceRecordAsync(id, updateDto, currentUserId);
                
                if (result == null)
                    return NotFound("Attendance record not found");

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating attendance record {Id}", id);
                return StatusCode(500, "An error occurred while updating the attendance record");
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<ActionResult> DeleteAttendanceRecord(int id)
        {
            try
            {
                var result = await _attendanceRecordService.DeleteAttendanceRecordAsync(id);
                
                if (!result)
                    return NotFound("Attendance record not found");

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting attendance record {Id}", id);
                return StatusCode(500, "An error occurred while deleting the attendance record");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<AttendanceRecordDto>> GetAttendanceRecord(int id)
        {
            try
            {
                var result = await _attendanceRecordService.GetAttendanceRecordByIdAsync(id);
                
                if (result == null)
                    return NotFound("Attendance record not found");

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting attendance record {Id}", id);
                return StatusCode(500, "An error occurred while retrieving the attendance record");
            }
        }

        [HttpPost("search")]
        public async Task<ActionResult<List<AttendanceRecordDto>>> SearchAttendanceRecords([FromBody] AttendanceSearchDto searchDto)
        {
            try
            {
                var result = await _attendanceRecordService.GetAttendanceRecordsAsync(searchDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching attendance records");
                return StatusCode(500, "An error occurred while searching attendance records");
            }
        }

        [HttpGet("student/{studentId}")]
        public async Task<ActionResult<List<AttendanceRecordDto>>> GetStudentAttendance(
            string studentId, 
            [FromQuery] DateTime? startDate = null, 
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var result = await _attendanceRecordService.GetStudentAttendanceAsync(studentId, startDate, endDate);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting student attendance for {StudentId}", studentId);
                return StatusCode(500, "An error occurred while retrieving student attendance");
            }
        }

        [HttpGet("class/{classId}/date/{date}")]
        public async Task<ActionResult<List<AttendanceRecordDto>>> GetClassAttendance(int classId, DateTime date)
        {
            try
            {
                var result = await _attendanceRecordService.GetClassAttendanceAsync(classId, date);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting class attendance for class {ClassId} on {Date}", classId, date);
                return StatusCode(500, "An error occurred while retrieving class attendance");
            }
        }

        [HttpGet("daily/{date}")]
        public async Task<ActionResult<List<AttendanceRecordDto>>> GetDailyAttendance(DateTime date)
        {
            try
            {
                var result = await _attendanceRecordService.GetDailyAttendanceAsync(date);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting daily attendance for {Date}", date);
                return StatusCode(500, "An error occurred while retrieving daily attendance");
            }
        }

        [HttpPost("bulk")]
        [Authorize(Roles = "Admin,Teacher,Staff")]
        public async Task<ActionResult> BulkCreateAttendance([FromBody] BulkAttendanceDto bulkDto)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                var result = await _attendanceRecordService.BulkCreateAttendanceAsync(bulkDto, currentUserId);
                
                if (!result)
                    return BadRequest("Failed to create bulk attendance records");

                return Ok("Bulk attendance records created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating bulk attendance");
                return StatusCode(500, "An error occurred while creating bulk attendance records");
            }
        }

        [HttpPut("{id}/verify")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<ActionResult> VerifyAttendanceRecord(int id)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                var result = await _attendanceRecordService.VerifyAttendanceRecordAsync(id, currentUserId);
                
                if (!result)
                    return NotFound("Attendance record not found");

                return Ok("Attendance record verified successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying attendance record {Id}", id);
                return StatusCode(500, "An error occurred while verifying the attendance record");
            }
        }

        [HttpGet("statistics")]
        public async Task<ActionResult<EnhancedAttendanceStatisticsDto>> GetAttendanceStatistics(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int? classId = null)
        {
            try
            {
                var result = await _attendanceRecordService.GetAttendanceStatisticsAsync(startDate, endDate, classId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting attendance statistics");
                return StatusCode(500, "An error occurred while retrieving attendance statistics");
            }
        }

        [HttpGet("alerts")]
        [Authorize(Roles = "Admin,Teacher,Staff")]
        public async Task<ActionResult<List<AttendanceAlertDto>>> GetAttendanceAlerts([FromQuery] bool includeResolved = false)
        {
            try
            {
                var result = await _attendanceRecordService.GetAttendanceAlertsAsync(includeResolved);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting attendance alerts");
                return StatusCode(500, "An error occurred while retrieving attendance alerts");
            }
        }

        [HttpPut("alerts/{alertId}/resolve")]
        [Authorize(Roles = "Admin,Teacher,Staff")]
        public async Task<ActionResult> ResolveAttendanceAlert(int alertId, [FromBody] string? resolutionNotes = null)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                var result = await _attendanceRecordService.ResolveAttendanceAlertAsync(alertId, currentUserId, resolutionNotes);
                
                if (!result)
                    return NotFound("Attendance alert not found");

                return Ok("Attendance alert resolved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resolving attendance alert {AlertId}", alertId);
                return StatusCode(500, "An error occurred while resolving the attendance alert");
            }
        }

        [HttpPost("{id}/notify-parent")]
        [Authorize(Roles = "Admin,Teacher,Staff")]
        public async Task<ActionResult> SendParentNotification(int id)
        {
            try
            {
                var result = await _attendanceRecordService.SendParentNotificationAsync(id);
                
                if (!result)
                    return NotFound("Attendance record not found");

                return Ok("Parent notification sent successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending parent notification for attendance record {Id}", id);
                return StatusCode(500, "An error occurred while sending parent notification");
            }
        }

        [HttpPost("update-summaries")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> UpdateAttendanceSummaries([FromQuery] string? studentId = null)
        {
            try
            {
                var result = await _attendanceRecordService.UpdateAttendanceSummariesAsync(studentId);
                
                if (!result)
                    return BadRequest("Failed to update attendance summaries");

                return Ok("Attendance summaries updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating attendance summaries");
                return StatusCode(500, "An error occurred while updating attendance summaries");
            }
        }

        [HttpGet("follow-ups/pending")]
        [Authorize(Roles = "Admin,Teacher,Staff")]
        public async Task<ActionResult<List<AttendanceRecordDto>>> GetPendingFollowUps()
        {
            try
            {
                var result = await _attendanceRecordService.GetPendingFollowUpsAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending follow-ups");
                return StatusCode(500, "An error occurred while retrieving pending follow-ups");
            }
        }

        [HttpPut("{id}/complete-follow-up")]
        [Authorize(Roles = "Admin,Teacher,Staff")]
        public async Task<ActionResult> MarkFollowUpComplete(int id, [FromBody] string? notes = null)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                var result = await _attendanceRecordService.MarkFollowUpCompleteAsync(id, currentUserId, notes);
                
                if (!result)
                    return NotFound("Attendance record not found");

                return Ok("Follow-up marked as complete");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking follow-up complete for attendance record {Id}", id);
                return StatusCode(500, "An error occurred while marking follow-up as complete");
            }
        }

        // Attendance Summary endpoints
        [HttpGet("summaries")]
        public async Task<ActionResult<List<EnhancedAttendanceSummaryDto>>> GetAttendanceSummaries(
            [FromQuery] int? academicYearId = null,
            [FromQuery] int? classId = null,
            [FromQuery] int? semesterId = null)
        {
            try
            {
                var result = await _attendanceSummaryService.GetAttendanceSummariesAsync(academicYearId, classId, semesterId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting attendance summaries");
                return StatusCode(500, "An error occurred while retrieving attendance summaries");
            }
        }

        [HttpGet("summaries/student/{studentId}")]
        public async Task<ActionResult<EnhancedAttendanceSummaryDto>> GetStudentAttendanceSummary(
            string studentId,
            [FromQuery] int academicYearId,
            [FromQuery] int? semesterId = null)
        {
            try
            {
                var result = await _attendanceSummaryService.GetStudentAttendanceSummaryAsync(studentId, academicYearId, semesterId);
                
                if (result == null)
                    return NotFound("Attendance summary not found");

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting student attendance summary for {StudentId}", studentId);
                return StatusCode(500, "An error occurred while retrieving student attendance summary");
            }
        }

        [HttpPost("summaries/recalculate")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> RecalculateAttendanceSummaries(
            [FromQuery] int? academicYearId = null,
            [FromQuery] string? studentId = null)
        {
            try
            {
                var result = await _attendanceSummaryService.RecalculateAttendanceSummariesAsync(academicYearId, studentId);
                
                if (!result)
                    return BadRequest("Failed to recalculate attendance summaries");

                return Ok("Attendance summaries recalculated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recalculating attendance summaries");
                return StatusCode(500, "An error occurred while recalculating attendance summaries");
            }
        }
    }
}
