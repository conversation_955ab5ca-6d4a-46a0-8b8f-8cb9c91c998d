# Use the official .NET 8 runtime as base image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Use the official .NET 8 SDK for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY ["Schools.API/Schools.API.csproj", "Schools.API/"]
COPY ["Schools.Shared/Schools.Shared.csproj", "Schools.Shared/"]
COPY ["Schools.Data/Schools.Data.csproj", "Schools.Data/"]
COPY ["Schools.Client/Schools.Client.csproj", "Schools.Client/"]

# Restore dependencies
RUN dotnet restore "Schools.API/Schools.API.csproj"

# Copy all source code
COPY . .

# Build the API project
WORKDIR "/src/Schools.API"
RUN dotnet build "Schools.API.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "Schools.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Build the Blazor client
WORKDIR "/src/Schools.Client"
RUN dotnet publish "Schools.Client.csproj" -c Release -o /app/publish/wwwroot

# Final stage
FROM base AS final
WORKDIR /app

# Install necessary packages for Arabic support
RUN apt-get update && apt-get install -y \
    fonts-dejavu-core \
    fontconfig \
    && rm -rf /var/lib/apt/lists/*

# Copy published application
COPY --from=publish /app/publish .

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:80

# Create uploads directory
RUN mkdir -p /app/wwwroot/uploads

# Set proper permissions
RUN chmod -R 755 /app/wwwroot

ENTRYPOINT ["dotnet", "Schools.API.dll"]
