using System.ComponentModel.DataAnnotations;

namespace Schools.Shared.DTOs
{
    // General Report DTOs
    public class ReportParametersDto
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? GradeId { get; set; }
        public int? ClassId { get; set; }
        public int? SectionId { get; set; }
        public int? SubjectId { get; set; }
        public string? TeacherId { get; set; }
        public string? StudentId { get; set; }
        public int? AcademicYearId { get; set; }
        public string? ReportType { get; set; }
        public string? Format { get; set; } = "PDF"; // PDF, Excel, CSV
        public Dictionary<string, object> CustomParameters { get; set; } = new();
    }

    public class ReportResultDto
    {
        public string ReportId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime GeneratedAt { get; set; }
        public string GeneratedBy { get; set; } = string.Empty;
        public string Format { get; set; } = string.Empty;
        public byte[]? Data { get; set; }
        public string? FilePath { get; set; }
        public string? DownloadUrl { get; set; }
        public long FileSize { get; set; }
        public ReportParametersDto Parameters { get; set; } = new();
    }

    // Academic Reports
    public class StudentAcademicReportDto
    {
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
        public string GradeName { get; set; } = string.Empty;
        public string AcademicYear { get; set; } = string.Empty;
        public double OverallGPA { get; set; }
        public double AttendanceRate { get; set; }
        public string AcademicStatus { get; set; } = string.Empty;
        public int TotalSubjects { get; set; }
        public int PassedSubjects { get; set; }
        public int FailedSubjects { get; set; }
        public List<SubjectGradeReportDto> SubjectGrades { get; set; } = new();
        public List<AttendanceSummaryDto> AttendanceSummary { get; set; } = new();
        public List<ExamResultSummaryDto> ExamResults { get; set; } = new();
        public string Remarks { get; set; } = string.Empty;
    }

    public class SubjectGradeReportDto
    {
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public string SubjectCode { get; set; } = string.Empty;
        public string TeacherName { get; set; } = string.Empty;
        public double AverageGrade { get; set; }
        public string LetterGrade { get; set; } = string.Empty;
        public double GradePoints { get; set; }
        public int CreditHours { get; set; }
        public List<ExamGradeDto> ExamGrades { get; set; } = new();
        public string Status { get; set; } = string.Empty; // Pass, Fail, Incomplete
    }

    public class ExamGradeDto
    {
        public int ExamId { get; set; }
        public string ExamTitle { get; set; } = string.Empty;
        public string ExamType { get; set; } = string.Empty;
        public DateTime ExamDate { get; set; }
        public double Score { get; set; }
        public double MaxScore { get; set; }
        public double Percentage { get; set; }
        public string Grade { get; set; } = string.Empty;
    }

    public class AttendanceSummaryDto
    {
        public DateTime Date { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public bool IsPresent { get; set; }
        public string? Reason { get; set; }
        public TimeSpan? CheckInTime { get; set; }
        public TimeSpan? CheckOutTime { get; set; }
    }

    public class ExamResultSummaryDto
    {
        public int ExamId { get; set; }
        public string ExamTitle { get; set; } = string.Empty;
        public string SubjectName { get; set; } = string.Empty;
        public DateTime ExamDate { get; set; }
        public double Score { get; set; }
        public double MaxScore { get; set; }
        public double Percentage { get; set; }
        public string Grade { get; set; } = string.Empty;
        public bool IsPassed { get; set; }
        public int Rank { get; set; }
        public int TotalStudents { get; set; }
    }

    // Class Reports
    public class ClassAcademicReportDto
    {
        public int ClassId { get; set; }
        public string ClassName { get; set; } = string.Empty;
        public string GradeName { get; set; } = string.Empty;
        public string ClassTeacher { get; set; } = string.Empty;
        public int TotalStudents { get; set; }
        public double ClassAverageGPA { get; set; }
        public double ClassAttendanceRate { get; set; }
        public int PassedStudents { get; set; }
        public int FailedStudents { get; set; }
        public double PassRate { get; set; }
        public List<StudentPerformanceSummaryDto> StudentPerformances { get; set; } = new();
        public List<SubjectClassPerformanceDto> SubjectPerformances { get; set; } = new();
        public ClassStatisticsDto Statistics { get; set; } = new();
    }

    public class StudentPerformanceSummaryDto
    {
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public double GPA { get; set; }
        public double AttendanceRate { get; set; }
        public int ClassRank { get; set; }
        public string Status { get; set; } = string.Empty;
        public string PerformanceTrend { get; set; } = string.Empty;
    }

    public class SubjectClassPerformanceDto
    {
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public string TeacherName { get; set; } = string.Empty;
        public double ClassAverage { get; set; }
        public double HighestScore { get; set; }
        public double LowestScore { get; set; }
        public double PassRate { get; set; }
        public int TotalStudents { get; set; }
        public int PassedStudents { get; set; }
        public int FailedStudents { get; set; }
    }

    public class ClassStatisticsDto
    {
        public double AverageGPA { get; set; }
        public double MedianGPA { get; set; }
        public double StandardDeviation { get; set; }
        public double AttendanceRate { get; set; }
        public int TotalExams { get; set; }
        public int CompletedExams { get; set; }
        public int PendingExams { get; set; }
        public Dictionary<string, int> GradeDistribution { get; set; } = new();
    }

    // Teacher Reports - using existing TeacherPerformanceDto from TeacherDTOs

    // Financial Reports
    public class FinancialSummaryReportDto
    {
        public DateTime ReportDate { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetIncome { get; set; }
        public decimal StudentFeesCollected { get; set; }
        public decimal StudentFeesOutstanding { get; set; }
        public decimal CollectionRate { get; set; }
        public List<RevenueBreakdownDto> RevenueBreakdown { get; set; } = new();
        public List<ExpenseBreakdownDto> ExpenseBreakdown { get; set; } = new();
        public List<MonthlyFinancialDto> MonthlyData { get; set; } = new();
    }

    public class RevenueBreakdownDto
    {
        public string Category { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    public class ExpenseBreakdownDto
    {
        public string Category { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    public class MonthlyFinancialDto
    {
        public string Month { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public decimal Expenses { get; set; }
        public decimal NetIncome { get; set; }
        public decimal StudentFees { get; set; }
        public decimal CollectionRate { get; set; }
    }

    // Attendance Reports - using existing DTOs from other files

    // Exam Reports
    public class ExamAnalysisReportDto
    {
        public int ExamId { get; set; }
        public string ExamTitle { get; set; } = string.Empty;
        public string SubjectName { get; set; } = string.Empty;
        public string ClassName { get; set; } = string.Empty;
        public DateTime ExamDate { get; set; }
        public int TotalStudents { get; set; }
        public int StudentsAppeared { get; set; }
        public double AverageScore { get; set; }
        public double HighestScore { get; set; }
        public double LowestScore { get; set; }
        public double PassRate { get; set; }
        public List<ExamResultSummaryDto> StudentResults { get; set; } = new();
        public List<QuestionAnalysisDto> QuestionAnalysis { get; set; } = new();
        public ExamStatisticsDto Statistics { get; set; } = new();
    }

    public class QuestionAnalysisDto
    {
        public int QuestionId { get; set; }
        public string QuestionText { get; set; } = string.Empty;
        public string QuestionType { get; set; } = string.Empty;
        public int TotalMarks { get; set; }
        public double AverageScore { get; set; }
        public double DifficultyLevel { get; set; }
        public int CorrectAnswers { get; set; }
        public int IncorrectAnswers { get; set; }
        public double SuccessRate { get; set; }
    }
}
