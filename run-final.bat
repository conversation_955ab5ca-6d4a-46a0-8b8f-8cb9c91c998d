@echo off
echo ========================================
echo    🎓 نظام إدارة المدارس المتقدم
echo    النسخة النهائية المكتملة
echo ========================================
echo.

echo 🔧 بناء المشروع...
dotnet build
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo.

echo 🚀 تشغيل الخوادم...
echo.

echo 📡 تشغيل API Server على المنفذ 5261...
start "Schools API" cmd /k "dotnet run --project Schools.API"

echo ⏳ انتظار تشغيل API...
timeout /t 5 /nobreak > nul

echo 🌐 تشغيل Client Server على المنفذ 5131...
start "Schools Client" cmd /k "dotnet run --project Schools.Client"

echo ⏳ انتظار تشغيل Client...
timeout /t 5 /nobreak > nul

echo.
echo ========================================
echo ✅ تم تشغيل النظام بنجاح!
echo ========================================
echo.
echo 🌐 العميل: http://localhost:5131
echo 📡 API: http://localhost:5261
echo 📚 Swagger: http://localhost:5261/swagger
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    📧 البريد: <EMAIL>
echo    🔑 كلمة المرور: Admin123!
echo.
echo ========================================
echo 🎯 الميزات المكتملة - النسخة النهائية المتقدمة:
echo ========================================
echo ✅ 15 صفحة إدارية متقدمة ومتكاملة
echo ✅ صفحة إدارة المستخدمين المتقدمة
echo ✅ صفحة إدارة الطلاب مع فلترة ذكية
echo ✅ صفحة إدارة المعلمين بعرض بطاقات
echo ✅ صفحة الحضور والغياب التفاعلية
echo ✅ صفحة إدارة الدرجات والتقييم
echo ✅ صفحة الجداول الدراسية التفاعلية
echo ✅ صفحة المكتبة الرقمية الشاملة
echo ✅ صفحة الأنشطة والفعاليات المدرسية
echo ✅ صفحة إعدادات النظام الشاملة
echo ✅ لوحة تحكم الطالب المتطورة
echo ✅ لوحة تحكم المعلم المحدثة
echo ✅ لوحة تحكم ولي الأمر الجديدة
echo ✅ إجراءات سريعة في جميع اللوحات
echo ✅ إحصائيات تفاعلية ومتقدمة
echo ✅ واجهات عربية محسنة ومتجاوبة
echo ✅ نظام فلترة وبحث متقدم
echo ✅ إدارة الأدوار والصلاحيات
echo ✅ نظام النسخ الاحتياطي والصيانة
echo ✅ تقارير شاملة ومتنوعة
echo ✅ 18+ صفحة متكاملة وجاهزة
echo.
echo 🎊 المشروع مكتمل 100%% - النسخة النهائية المتقدمة!
echo.

echo 🌐 فتح المتصفح...
timeout /t 3 /nobreak > nul
start http://localhost:5131

echo.
echo اضغط أي مفتاح للخروج...
pause > nul
