using System.ComponentModel.DataAnnotations;

namespace Schools.Data.Entities
{
    public class Event
    {
        public int Id { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = "";
        
        [MaxLength(1000)]
        public string Description { get; set; } = "";
        
        [Required]
        [MaxLength(50)]
        public string Category { get; set; } = "";
        
        public DateTime StartDate { get; set; }
        
        public DateTime EndDate { get; set; }
        
        [MaxLength(200)]
        public string Location { get; set; } = "";
        
        public int MaxParticipants { get; set; }
        
        public int CurrentParticipants { get; set; } = 0;
        
        [Required]
        [MaxLength(20)]
        public string Status { get; set; } = "scheduled";
        
        [Required]
        [MaxLength(100)]
        public string CreatedBy { get; set; } = "";
        
        public DateTime CreatedDate { get; set; }
        
        public bool IsPublic { get; set; } = true;
        
        public bool RequiresApproval { get; set; } = false;
        
        // Navigation properties
        public virtual ICollection<EventParticipant> Participants { get; set; } = new List<EventParticipant>();
    }
}
