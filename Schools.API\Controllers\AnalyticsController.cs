using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Schools.Shared.DTOs;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")]
    public class AnalyticsController : ControllerBase
    {
        private readonly ILogger<AnalyticsController> _logger;

        public AnalyticsController(ILogger<AnalyticsController> logger)
        {
            _logger = logger;
        }

        [HttpGet("overview")]
        public async Task<ActionResult<AnalyticsOverviewDto>> GetAnalyticsOverview()
        {
            try
            {
                var overview = new AnalyticsOverviewDto
                {
                    TotalStudents = 450,
                    TotalTeachers = 35,
                    TotalClasses = 18,
                    TotalSubjects = 12,
                    AverageGPA = 3.42,
                    AttendanceRate = 92.8,
                    StudentGrowthRate = 15.2,
                    SatisfactionScore = 87.5,
                    GPAImprovement = 8.7
                };

                return Ok(overview);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving analytics overview");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("performance-trends")]
        public async Task<ActionResult<PerformanceTrendsDto>> GetPerformanceTrends(
            [FromQuery] string timeRange = "6months")
        {
            try
            {
                var trends = new List<MonthlyPerformanceDto>();

                var months = timeRange switch
                {
                    "1year" => 12,
                    "2years" => 24,
                    _ => 6
                };

                for (int i = months; i >= 0; i--)
                {
                    var monthStart = DateTime.UtcNow.AddMonths(-i);
                    trends.Add(new MonthlyPerformanceDto
                    {
                        Month = monthStart.ToString("yyyy-MM"),
                        MonthName = monthStart.ToString("MMMM yyyy"),
                        AverageGPA = Math.Round(3.2 + (i * 0.05), 2),
                        AttendanceRate = Math.Round(89 + (i * 0.5), 1)
                    });
                }

                var trendsDto = new PerformanceTrendsDto
                {
                    TimeRange = timeRange,
                    MonthlyData = trends
                };

                return Ok(trendsDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving performance trends");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("subject-performance")]
        public async Task<ActionResult<SubjectPerformanceDto>> GetSubjectPerformance()
        {
            try
            {
                var subjectPerformance = new List<SubjectPerformanceItemDto>
                {
                    new() { SubjectName = "الرياضيات", AverageScore = 85.2, TotalStudents = 120, PassingRate = 92.5 },
                    new() { SubjectName = "العلوم", AverageScore = 78.8, TotalStudents = 115, PassingRate = 88.7 },
                    new() { SubjectName = "اللغة العربية", AverageScore = 92.1, TotalStudents = 130, PassingRate = 96.2 },
                    new() { SubjectName = "الإنجليزية", AverageScore = 88.5, TotalStudents = 125, PassingRate = 94.4 },
                    new() { SubjectName = "التاريخ", AverageScore = 90.3, TotalStudents = 110, PassingRate = 95.5 },
                    new() { SubjectName = "الجغرافيا", AverageScore = 86.7, TotalStudents = 108, PassingRate = 93.5 }
                };

                var performanceDto = new SubjectPerformanceDto
                {
                    Subjects = subjectPerformance
                };

                return Ok(performanceDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving subject performance");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("grade-distribution")]
        public async Task<ActionResult<GradeDistributionDto>> GetGradeDistribution()
        {
            try
            {
                var distribution = new Dictionary<string, int>
                {
                    { "A+", 45 },
                    { "A", 78 },
                    { "B+", 92 },
                    { "B", 67 },
                    { "C+", 34 },
                    { "C", 23 },
                    { "D", 12 },
                    { "F", 5 }
                };

                var distributionDto = new GradeDistributionDto
                {
                    Distribution = distribution,
                    TotalGrades = distribution.Values.Sum()
                };

                return Ok(distributionDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving grade distribution");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("attendance-heatmap")]
        public async Task<ActionResult<AttendanceHeatmapDto>> GetAttendanceHeatmap()
        {
            try
            {
                var endDate = DateTime.UtcNow.Date;
                var startDate = endDate.AddDays(-28);
                var heatmapData = new List<DailyAttendanceDto>();
                var random = new Random();

                for (var date = startDate; date <= endDate; date = date.AddDays(1))
                {
                    var attendanceRate = random.Next(75, 100);
                    var totalStudents = 450;
                    var presentStudents = (int)(totalStudents * attendanceRate / 100.0);

                    heatmapData.Add(new DailyAttendanceDto
                    {
                        Date = date,
                        AttendanceRate = attendanceRate,
                        TotalStudents = totalStudents,
                        PresentStudents = presentStudents
                    });
                }

                var heatmapDto = new AttendanceHeatmapDto
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    DailyData = heatmapData
                };

                return Ok(heatmapDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving attendance heatmap");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("predictions")]
        public async Task<ActionResult<PredictiveAnalyticsDto>> GetPredictiveAnalytics()
        {
            try
            {
                var predictions = new PredictiveAnalyticsDto
                {
                    PredictedStudentGrowth = 8.5,
                    PredictedPerformanceImprovement = 12.3,
                    PredictedAttendanceRate = 94.2,
                    PredictedGPA = 3.58,
                    ConfidenceLevel = 85.2,
                    PredictionDate = DateTime.UtcNow.AddMonths(3)
                };

                return Ok(predictions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving predictive analytics");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("recommendations")]
        public async Task<ActionResult<IEnumerable<RecommendationDto>>> GetRecommendations()
        {
            try
            {
                var recommendations = new List<RecommendationDto>
                {
                    new() { Title = "تحسين الرياضيات", Description = "يُنصح بإضافة حصص تقوية في الرياضيات للصف الثالث", Priority = "high", Category = "academic", EstimatedImpact = "high" },
                    new() { Title = "تفاعل الطلاب", Description = "زيادة الأنشطة التفاعلية لتحسين مشاركة الطلاب", Priority = "medium", Category = "engagement", EstimatedImpact = "medium" },
                    new() { Title = "إدارة الوقت", Description = "تحسين جدولة الحصص لزيادة معدل الحضور", Priority = "medium", Category = "attendance", EstimatedImpact = "medium" },
                    new() { Title = "برامج التميز", Description = "إنشاء برامج خاصة للطلاب المتفوقين", Priority = "low", Category = "academic", EstimatedImpact = "high" },
                    new() { Title = "تحديث المعدات", Description = "الحاجة لتحديث أجهزة الكمبيوتر في المختبر", Priority = "medium", Category = "resources", EstimatedImpact = "medium" }
                };

                return Ok(recommendations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving recommendations");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("risk-analysis")]
        public async Task<ActionResult<IEnumerable<RiskAnalysisDto>>> GetRiskAnalysis()
        {
            try
            {
                var risks = new List<RiskAnalysisDto>
                {
                    new() 
                    { 
                        Title = "انخفاض الحضور", 
                        Description = "ملاحظة انخفاض طفيف في معدل الحضور", 
                        Severity = "warning", 
                        RiskLevel = 25, 
                        Category = "attendance",
                        RecommendedActions = new[] { "تحسين التواصل مع أولياء الأمور", "مراجعة أسباب الغياب", "تطبيق برامج تحفيزية" }
                    },
                    new() 
                    { 
                        Title = "ضعف في العلوم", 
                        Description = "أداء أقل من المتوقع في مادة العلوم", 
                        Severity = "danger", 
                        RiskLevel = 40, 
                        Category = "academic",
                        RecommendedActions = new[] { "برامج تقوية إضافية", "تحسين طرق التدريس", "دعم فردي للطلاب" }
                    },
                    new() 
                    { 
                        Title = "نقص الموارد", 
                        Description = "الحاجة لتحديث المعدات التقنية", 
                        Severity = "info", 
                        RiskLevel = 60, 
                        Category = "resources",
                        RecommendedActions = new[] { "وضع خطة للتحديث", "البحث عن مصادر تمويل", "تحديد الأولويات" }
                    }
                };

                return Ok(risks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving risk analysis");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
