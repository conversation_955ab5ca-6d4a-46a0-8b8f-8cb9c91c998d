@page "/accounting/fee-structures/new"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Accountant,Admin")]

<PageTitle>إنشاء هيكل رسوم جديد - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-plus-circle text-warning me-2"></i>
                        إنشاء هيكل رسوم جديد
                    </h2>
                    <p class="text-muted mb-0">إنشاء هيكل رسوم جديد للصفوف الدراسية</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-outline-secondary" @onclick="GoBack">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل البيانات...</p>
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            بيانات هيكل الرسوم
                        </h5>
                    </div>
                    <div class="card-body">
                        <EditForm Model="feeStructure" OnValidSubmit="SaveFeeStructure">
                            <DataAnnotationsValidator />

                            <!-- Basic Information -->
                            <div class="row g-3 mb-4">
                                <div class="col-md-6">
                                    <label class="form-label">اسم هيكل الرسوم <span class="text-danger">*</span></label>
                                    <InputText @bind-Value="feeStructure.Name" class="form-control" placeholder="مثال: رسوم الصف الأول الابتدائي" />
                                    <ValidationMessage For="() => feeStructure.Name" />
                                </div>

                                <div class="col-md-6">
                                    <label class="form-label">الصف الدراسي <span class="text-danger">*</span></label>
                                    <select @bind="feeStructure.GradeId" class="form-select">
                                        <option value="0">اختر الصف</option>
                                        @foreach (var grade in grades)
                                        {
                                            <option value="@grade.Id">@grade.GradeName</option>
                                        }
                                    </select>
                                </div>

                                <div class="col-12">
                                    <label class="form-label">الوصف</label>
                                    <InputTextArea @bind-Value="feeStructure.Description" class="form-control" rows="3" placeholder="وصف تفصيلي لهيكل الرسوم..." />
                                </div>
                            </div>

                            <!-- Fee Components -->
                            <div class="card border-light mb-4">
                                <div class="card-header bg-light">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">
                                            <i class="fas fa-list me-2"></i>
                                            مكونات الرسوم
                                        </h6>
                                        <button type="button" class="btn btn-sm btn-primary" @onclick="AddFeeComponent">
                                            <i class="fas fa-plus me-1"></i>
                                            إضافة مكون
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    @if (feeStructure.Components?.Any() == true)
                                    {
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>اسم المكون</th>
                                                        <th>الوصف</th>
                                                        <th>المبلغ</th>
                                                        <th>إجباري</th>
                                                        <th>تاريخ الاستحقاق</th>
                                                        <th>الحساب المحاسبي</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @for (int i = 0; i < feeStructure.Components.Count; i++)
                                                    {
                                                        var index = i;
                                                        var component = feeStructure.Components[index];
                                                        <tr>
                                                            <td>
                                                                <input @bind="component.Name" class="form-control form-control-sm" placeholder="اسم المكون" />
                                                            </td>
                                                            <td>
                                                                <input @bind="component.Description" class="form-control form-control-sm" placeholder="الوصف" />
                                                            </td>
                                                            <td>
                                                                <div class="input-group input-group-sm">
                                                                    <span class="input-group-text">ر.س</span>
                                                                    <input @bind="component.Amount" type="number" class="form-control" min="0" step="0.01" />
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="form-check">
                                                                    <input @bind="component.IsMandatory" type="checkbox" class="form-check-input" />
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <input @bind="component.DueDate" type="date" class="form-control form-control-sm" />
                                                            </td>
                                                            <td>
                                                                <select @bind="component.AccountId" class="form-select form-select-sm">
                                                                    <option value="0">اختر الحساب</option>
                                                                    @foreach (var account in revenueAccounts)
                                                                    {
                                                                        <option value="@account.Id">@account.AccountCode - @account.AccountName</option>
                                                                    }
                                                                </select>
                                                            </td>
                                                            <td>
                                                                <button type="button" class="btn btn-sm btn-outline-danger" @onclick="() => RemoveFeeComponent(index)">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    }
                                                </tbody>
                                                <tfoot class="table-secondary">
                                                    <tr>
                                                        <th colspan="2">الإجمالي</th>
                                                        <th>
                                                            <strong class="text-primary">@GetTotalAmount().ToString("C")</strong>
                                                        </th>
                                                        <th colspan="4"></th>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="text-center py-4">
                                            <i class="fas fa-list fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">لا توجد مكونات رسوم</p>
                                            <button type="button" class="btn btn-primary" @onclick="AddFeeComponent">
                                                <i class="fas fa-plus me-2"></i>
                                                إضافة أول مكون
                                            </button>
                                        </div>
                                    }
                                </div>
                            </div>

                            <!-- Summary -->
                            <div class="row g-3 mb-4">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h5 class="text-primary mb-1">@GetTotalAmount().ToString("C")</h5>
                                            <small class="text-muted">إجمالي الرسوم</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h5 class="text-info mb-1">@feeStructure.Components.Count</h5>
                                            <small class="text-muted">عدد المكونات</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex justify-content-end gap-2">
                                        <button type="button" class="btn btn-outline-secondary" @onclick="GoBack">
                                            <i class="fas fa-times me-2"></i>
                                            إلغاء
                                        </button>
                                        <button type="submit" class="btn btn-warning" disabled="@isSaving">
                                            @if (isSaving)
                                            {
                                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                            }
                                            else
                                            {
                                                <i class="fas fa-save me-2"></i>
                                            }
                                            حفظ هيكل الرسوم
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </EditForm>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private FeeStructureDto feeStructure = new()
    {
        Components = new List<FeeComponentDto>(),
        IsActive = true
    };

    private List<GradeDto> grades = new();
    private List<AccountDto> revenueAccounts = new();

    private bool isLoading = true;
    private bool isSaving = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // Load grades
            grades = await ApiService.GetGradesAsync() ?? new List<GradeDto>();

            // Load revenue accounts
            var allAccounts = await ApiService.GetAccountsAsync() ?? new List<AccountDto>();
            revenueAccounts = allAccounts.Where(a =>
                a.AccountType == AccountType.Revenue &&
                a.IsActive
            ).ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task SaveFeeStructure()
    {
        if (!ValidateFeeStructure())
            return;

        try
        {
            isSaving = true;
            StateHasChanged();

            // Update total amount
            feeStructure.TotalAmount = GetTotalAmount();

            var success = await ApiService.CreateFeeStructureAsync(feeStructure);
            if (success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "تم حفظ هيكل الرسوم بنجاح");
                Navigation.NavigateTo("/accounting/fee-structures");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ أثناء حفظ هيكل الرسوم");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ هيكل الرسوم: {ex.Message}");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private bool ValidateFeeStructure()
    {
        if (string.IsNullOrWhiteSpace(feeStructure.Name))
        {
            JSRuntime.InvokeVoidAsync("alert", "يرجى إدخال اسم هيكل الرسوم");
            return false;
        }

        if (feeStructure.GradeId == 0)
        {
            JSRuntime.InvokeVoidAsync("alert", "يرجى اختيار الصف الدراسي");
            return false;
        }

        if (!feeStructure.Components.Any())
        {
            JSRuntime.InvokeVoidAsync("alert", "يرجى إضافة مكون واحد على الأقل");
            return false;
        }

        foreach (var component in feeStructure.Components)
        {
            if (string.IsNullOrWhiteSpace(component.Name))
            {
                JSRuntime.InvokeVoidAsync("alert", "يرجى إدخال اسم جميع المكونات");
                return false;
            }

            if (component.Amount <= 0)
            {
                JSRuntime.InvokeVoidAsync("alert", "يرجى إدخال مبلغ صحيح لجميع المكونات");
                return false;
            }

            if (component.AccountId == 0)
            {
                JSRuntime.InvokeVoidAsync("alert", "يرجى اختيار الحساب المحاسبي لجميع المكونات");
                return false;
            }
        }

        return true;
    }

    private void AddFeeComponent()
    {
        feeStructure.Components.Add(new FeeComponentDto
        {
            IsMandatory = true,
            DueDate = DateTime.Now.AddMonths(1)
        });
        StateHasChanged();
    }

    private void RemoveFeeComponent(int index)
    {
        if (index >= 0 && index < feeStructure.Components.Count)
        {
            feeStructure.Components.RemoveAt(index);
            StateHasChanged();
        }
    }

    private decimal GetTotalAmount()
    {
        return feeStructure.Components.Sum(c => c.Amount);
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/accounting/fee-structures");
    }
}
