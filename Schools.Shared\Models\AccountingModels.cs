using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Schools.Shared.Models
{
    // Account Model
    public class Account : BaseEntity
    {
        [Required]
        [StringLength(20)]
        public string AccountCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string AccountName { get; set; } = string.Empty;

        [StringLength(200)]
        public string AccountNameEn { get; set; } = string.Empty;

        [Required]
        public AccountType AccountType { get; set; }

        public int? ParentAccountId { get; set; }

        public int Level { get; set; } = 1;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Balance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal DebitBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditBalance { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        public string? Description { get; set; }

        // Navigation properties
        public virtual Account? ParentAccount { get; set; }
        public virtual ICollection<Account> SubAccounts { get; set; } = new List<Account>();
        public virtual ICollection<JournalEntryDetail> JournalEntryDetails { get; set; } = new List<JournalEntryDetail>();
        public virtual ICollection<ReceiptVoucherDetail> ReceiptVoucherDetails { get; set; } = new List<ReceiptVoucherDetail>();
        public virtual ICollection<PaymentVoucherDetail> PaymentVoucherDetails { get; set; } = new List<PaymentVoucherDetail>();
        public virtual ICollection<FeeComponent> FeeComponents { get; set; } = new List<FeeComponent>();
    }

    // Receipt Voucher Model
    public class ReceiptVoucher : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string VoucherNumber { get; set; } = string.Empty;

        [Required]
        public DateTime VoucherDate { get; set; }

        [Required]
        [StringLength(200)]
        public string ReceivedFrom { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        public string AmountInWords { get; set; } = string.Empty;

        [Required]
        public string Description { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string PaymentMethod { get; set; } = string.Empty; // Cash, Bank, Check, Card, Online

        public string? CheckNumber { get; set; }

        public string? BankName { get; set; }

        public string? AccountNumber { get; set; }

        public DateTime? CheckDate { get; set; }

        [Required]
        public VoucherStatus Status { get; set; } = VoucherStatus.Draft;

        public string? ApprovedBy { get; set; }

        public DateTime? ApprovedDate { get; set; }

        public string? Reference { get; set; }

        public string? Notes { get; set; }

        // Navigation properties
        public virtual ICollection<ReceiptVoucherDetail> Details { get; set; } = new List<ReceiptVoucherDetail>();
        public virtual ICollection<JournalEntry> JournalEntries { get; set; } = new List<JournalEntry>();
        public virtual ICollection<StudentPayment> StudentPayments { get; set; } = new List<StudentPayment>();
    }

    // Receipt Voucher Detail Model
    public class ReceiptVoucherDetail : BaseEntity
    {
        [Required]
        public int ReceiptVoucherId { get; set; }

        [Required]
        public int AccountId { get; set; }

        [Required]
        public string Description { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        public string? Reference { get; set; }

        // Navigation properties
        public virtual ReceiptVoucher ReceiptVoucher { get; set; } = null!;
        public virtual Account Account { get; set; } = null!;
    }

    // Payment Voucher Model
    public class PaymentVoucher : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string VoucherNumber { get; set; } = string.Empty;

        [Required]
        public DateTime VoucherDate { get; set; }

        [Required]
        [StringLength(200)]
        public string PaidTo { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        public string AmountInWords { get; set; } = string.Empty;

        [Required]
        public string Description { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string PaymentMethod { get; set; } = string.Empty;

        public string? CheckNumber { get; set; }

        public string? BankName { get; set; }

        public string? AccountNumber { get; set; }

        public DateTime? CheckDate { get; set; }

        [Required]
        public VoucherStatus Status { get; set; } = VoucherStatus.Draft;

        public string? ApprovedBy { get; set; }

        public DateTime? ApprovedDate { get; set; }

        public string? Reference { get; set; }

        public string? Notes { get; set; }

        // Navigation properties
        public virtual ICollection<PaymentVoucherDetail> Details { get; set; } = new List<PaymentVoucherDetail>();
        public virtual ICollection<JournalEntry> JournalEntries { get; set; } = new List<JournalEntry>();
    }

    // Payment Voucher Detail Model
    public class PaymentVoucherDetail : BaseEntity
    {
        [Required]
        public int PaymentVoucherId { get; set; }

        [Required]
        public int AccountId { get; set; }

        [Required]
        public string Description { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        public string? Reference { get; set; }

        // Navigation properties
        public virtual PaymentVoucher PaymentVoucher { get; set; } = null!;
        public virtual Account Account { get; set; } = null!;
    }

    // Journal Entry Model
    public class JournalEntry : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string EntryNumber { get; set; } = string.Empty;

        [Required]
        public DateTime EntryDate { get; set; }

        [Required]
        public string Description { get; set; } = string.Empty;

        public string? Reference { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalDebit { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCredit { get; set; }

        [Required]
        public JournalEntryStatus Status { get; set; } = JournalEntryStatus.Draft;

        public string? ApprovedBy { get; set; }

        public DateTime? ApprovedDate { get; set; }

        public int? ReceiptVoucherId { get; set; }

        public int? PaymentVoucherId { get; set; }

        public string? Notes { get; set; }

        // Navigation properties
        public virtual ICollection<JournalEntryDetail> Details { get; set; } = new List<JournalEntryDetail>();
        public virtual ReceiptVoucher? ReceiptVoucher { get; set; }
        public virtual PaymentVoucher? PaymentVoucher { get; set; }
    }

    // Journal Entry Detail Model
    public class JournalEntryDetail : BaseEntity
    {
        [Required]
        public int JournalEntryId { get; set; }

        [Required]
        public int AccountId { get; set; }

        [Required]
        public string Description { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal DebitAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditAmount { get; set; } = 0;

        public string? Reference { get; set; }

        // Navigation properties
        public virtual JournalEntry JournalEntry { get; set; } = null!;
        public virtual Account Account { get; set; } = null!;
    }

    // Fee Structure Model
    public class FeeStructure : BaseEntity
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        public string? Description { get; set; }

        [Required]
        public int AcademicGradeId { get; set; }

        [Required]
        public int AcademicYearId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime? EffectiveDate { get; set; }

        public DateTime? ExpiryDate { get; set; }

        // Navigation properties
        public virtual AcademicGrade AcademicGrade { get; set; } = null!;
        public virtual AcademicYear AcademicYear { get; set; } = null!;
        public virtual ICollection<FeeComponent> Components { get; set; } = new List<FeeComponent>();
        public virtual ICollection<StudentFee> StudentFees { get; set; } = new List<StudentFee>();
    }

    // Fee Component Model
    public class FeeComponent : BaseEntity
    {
        [Required]
        public int FeeStructureId { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        public string? Description { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        public bool IsMandatory { get; set; } = true;

        public DateTime? DueDate { get; set; }

        [Required]
        public int AccountId { get; set; }

        public int SortOrder { get; set; }

        // Navigation properties
        public virtual FeeStructure FeeStructure { get; set; } = null!;
        public virtual Account Account { get; set; } = null!;
        public virtual ICollection<StudentFeeItem> StudentFeeItems { get; set; } = new List<StudentFeeItem>();
    }

    // Student Fee Model
    public class StudentFee : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int StudentEnrollmentId { get; set; }

        [Required]
        public int FeeStructureId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalFees { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal OutstandingAmount => TotalFees - PaidAmount;

        [Required]
        [StringLength(50)]
        public string PaymentStatus { get; set; } = "Unpaid"; // Paid, Partial, Unpaid, Overdue

        public DateTime? LastPaymentDate { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal LastPaymentAmount { get; set; } = 0;

        public DateTime? DueDate { get; set; }

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual StudentEnrollment StudentEnrollment { get; set; } = null!;
        public virtual FeeStructure FeeStructure { get; set; } = null!;
        public virtual ICollection<StudentFeeItem> FeeItems { get; set; } = new List<StudentFeeItem>();
        public virtual ICollection<StudentPayment> Payments { get; set; } = new List<StudentPayment>();
    }

    // Student Fee Item Model
    public class StudentFeeItem : BaseEntity
    {
        [Required]
        public int StudentFeeId { get; set; }

        [Required]
        public int FeeComponentId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        public bool IsPaid { get; set; } = false;

        public DateTime? PaidDate { get; set; }

        public DateTime? DueDate { get; set; }

        // Navigation properties
        public virtual StudentFee StudentFee { get; set; } = null!;
        public virtual FeeComponent FeeComponent { get; set; } = null!;
    }

    // Student Payment Model
    public class StudentPayment : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int StudentFeeId { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required]
        public DateTime PaymentDate { get; set; }

        [Required]
        [StringLength(50)]
        public string PaymentMethod { get; set; } = string.Empty;

        public string? Reference { get; set; }

        public string? Notes { get; set; }

        public int? ReceiptVoucherId { get; set; }

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual StudentFee StudentFee { get; set; } = null!;
        public virtual ReceiptVoucher? ReceiptVoucher { get; set; }
    }

    // Enums
    public enum AccountType
    {
        Asset = 1,
        Liability = 2,
        Equity = 3,
        Revenue = 4,
        Expense = 5
    }

    public enum VoucherStatus
    {
        Draft = 1,
        Approved = 2,
        Posted = 3,
        Cancelled = 4
    }

    public enum JournalEntryStatus
    {
        Draft = 1,
        Posted = 2,
        Cancelled = 3
    }

    public enum PaymentMethod
    {
        Cash = 1,
        Bank = 2,
        Check = 3,
        Card = 4,
        Online = 5
    }
}
