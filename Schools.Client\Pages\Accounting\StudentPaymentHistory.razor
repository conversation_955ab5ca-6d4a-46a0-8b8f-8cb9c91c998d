@page "/accounting/students/{StudentId:int}/payment-history"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Accountant,Admin")]

<PageTitle>تاريخ مدفوعات الطالب - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-history text-info me-2"></i>
                        تاريخ مدفوعات الطالب
                    </h2>
                    <p class="text-muted mb-0">سجل كامل لجميع مدفوعات الطالب @studentFee?.StudentName</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-outline-secondary" @onclick="GoBack">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة
                    </button>
                    <button class="btn btn-outline-success" @onclick="ExportHistory">
                        <i class="fas fa-download me-2"></i>
                        تصدير
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل تاريخ المدفوعات...</p>
        </div>
    }
    else if (studentFee == null)
    {
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            لم يتم العثور على بيانات الطالب
        </div>
    }
    else
    {
        <!-- Student Summary -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            ملخص مالي للطالب
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="avatar-lg bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2">
                                        <i class="fas fa-user fa-2x"></i>
                                    </div>
                                    <h5>@studentFee.StudentName</h5>
                                    <p class="text-muted">@studentFee.StudentNumber</p>
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <div class="text-center p-3 bg-light rounded">
                                            <h6 class="text-primary mb-1">@studentFee.TotalFees.ToString("C")</h6>
                                            <small class="text-muted">إجمالي الرسوم</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center p-3 bg-light rounded">
                                            <h6 class="text-success mb-1">@studentFee.PaidAmount.ToString("C")</h6>
                                            <small class="text-muted">المدفوع</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center p-3 bg-light rounded">
                                            <h6 class="text-danger mb-1">@studentFee.OutstandingAmount.ToString("C")</h6>
                                            <small class="text-muted">المتبقي</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center p-3 bg-light rounded">
                                            <h6 class="mb-1">@studentFee.PaymentHistory.Count</h6>
                                            <small class="text-muted">عدد الدفعات</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="progress" style="height: 10px;">
                                        <div class="progress-bar bg-success" style="width: @GetPaymentPercentage()%"></div>
                                    </div>
                                    <small class="text-muted">نسبة الدفع: @GetPaymentPercentage().ToString("F1")%</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment History -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2 text-info"></i>
                                تاريخ المدفوعات
                            </h5>
                            <button class="btn btn-success btn-sm" @onclick="AddNewPayment">
                                <i class="fas fa-plus me-1"></i>
                                دفعة جديدة
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (studentFee.PaymentHistory?.Any() == true)
                        {
                            <div class="timeline">
                                @foreach (var payment in studentFee.PaymentHistory.OrderByDescending(p => p.PaymentDate))
                                {
                                    <div class="timeline-item">
                                        <div class="timeline-marker bg-success">
                                            <i class="fas fa-money-bill-wave text-white"></i>
                                        </div>
                                        <div class="timeline-content">
                                            <div class="card border-left-success">
                                                <div class="card-body">
                                                    <div class="row align-items-center">
                                                        <div class="col-md-8">
                                                            <h6 class="mb-1">
                                                                <strong class="text-success">@payment.Amount.ToString("C")</strong>
                                                                <span class="badge @GetPaymentMethodBadge(payment.PaymentMethod) ms-2">
                                                                    @GetPaymentMethodText(payment.PaymentMethod)
                                                                </span>
                                                            </h6>
                                                            <p class="text-muted mb-1">
                                                                <i class="fas fa-calendar me-1"></i>
                                                                @payment.PaymentDate.ToString("dd/MM/yyyy hh:mm tt")
                                                            </p>
                                                            @if (!string.IsNullOrEmpty(payment.Reference))
                                                            {
                                                                <p class="text-muted mb-1">
                                                                    <i class="fas fa-hashtag me-1"></i>
                                                                    مرجع: @payment.Reference
                                                                </p>
                                                            }
                                                            @if (!string.IsNullOrEmpty(payment.Notes))
                                                            {
                                                                <p class="text-muted mb-0">
                                                                    <i class="fas fa-sticky-note me-1"></i>
                                                                    @payment.Notes
                                                                </p>
                                                            }
                                                        </div>
                                                        <div class="col-md-4 text-end">
                                                            @if (payment.ReceiptVoucherId.HasValue)
                                                            {
                                                                <button class="btn btn-outline-primary btn-sm mb-2"
                                                                        @onclick="() => ViewReceipt(payment.ReceiptVoucherId.Value)">
                                                                    <i class="fas fa-receipt me-1"></i>
                                                                    عرض السند
                                                                </button>
                                                                <br>
                                                            }
                                                            <small class="text-muted">
                                                                ID: @payment.Id
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا يوجد تاريخ مدفوعات</h5>
                                <p class="text-muted">لم يتم تسجيل أي دفعات لهذا الطالب بعد</p>
                                <button class="btn btn-success" @onclick="AddNewPayment">
                                    <i class="fas fa-plus me-2"></i>
                                    تسجيل أول دفعة
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Fee Items Status -->
        @if (studentFee.FeeItems?.Any() == true)
        {
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-bottom">
                            <h5 class="mb-0">
                                <i class="fas fa-list-check me-2 text-warning"></i>
                                حالة بنود الرسوم
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>البند</th>
                                            <th>المبلغ</th>
                                            <th>تاريخ الاستحقاق</th>
                                            <th>الحالة</th>
                                            <th>تاريخ الدفع</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var item in studentFee.FeeItems)
                                        {
                                            <tr class="@(item.IsPaid ? "table-success" : (item.DueDate < DateTime.Now ? "table-danger" : ""))">
                                                <td>
                                                    <strong>@item.FeeType</strong>
                                                    @if (!string.IsNullOrEmpty(item.Description))
                                                    {
                                                        <br>
                                                        <small class="text-muted">@item.Description</small>
                                                    }
                                                </td>
                                                <td>
                                                    <strong class="text-primary">@item.Amount.ToString("C")</strong>
                                                </td>
                                                <td>
                                                    @item.DueDate.ToString("dd/MM/yyyy")
                                                    @if (item.DueDate < DateTime.Now && !item.IsPaid)
                                                    {
                                                        <br>
                                                        <small class="text-danger">متأخر @((DateTime.Now - item.DueDate).Days) يوم</small>
                                                    }
                                                </td>
                                                <td>
                                                    <span class="badge @(item.IsPaid ? "bg-success" : "bg-warning")">
                                                        @(item.IsPaid ? "مدفوع" : "مستحق")
                                                    </span>
                                                </td>
                                                <td>
                                                    @if (item.PaidDate.HasValue)
                                                    {
                                                        @item.PaidDate.Value.ToString("dd/MM/yyyy")
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    }
</div>

<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 30px;
    }

    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 10px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3px solid #fff;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .timeline-content {
        margin-left: 20px;
    }

    .border-left-success {
        border-left: 4px solid #28a745 !important;
    }

    .avatar-lg {
        width: 80px;
        height: 80px;
    }
</style>

@code {
    [Parameter] public int StudentId { get; set; }

    private StudentFeeDto? studentFee;
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadStudentData();
    }

    private async Task LoadStudentData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            studentFee = await ApiService.GetStudentFeeAsync(StudentId);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل بيانات الطالب: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/accounting/fees");
    }

    private void AddNewPayment()
    {
        Navigation.NavigateTo($"/accounting/students/{StudentId}/payment");
    }

    private void ViewReceipt(int receiptId)
    {
        Navigation.NavigateTo($"/accounting/receipt-vouchers/{receiptId}");
    }

    private async Task ExportHistory()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة تصدير تاريخ المدفوعات ستكون متاحة قريباً");
    }

    private double GetPaymentPercentage()
    {
        if (studentFee?.TotalFees == 0) return 0;
        return (double)(studentFee?.PaidAmount ?? 0) / (double)(studentFee?.TotalFees ?? 1) * 100;
    }

    private string GetPaymentMethodBadge(string method)
    {
        return method.ToLower() switch
        {
            "cash" => "bg-success",
            "bank" => "bg-primary",
            "check" => "bg-info",
            "card" => "bg-warning",
            _ => "bg-secondary"
        };
    }

    private string GetPaymentMethodText(string method)
    {
        return method.ToLower() switch
        {
            "cash" => "نقدي",
            "bank" => "بنكي",
            "check" => "شيك",
            "card" => "بطاقة",
            _ => "أخرى"
        };
    }
}
