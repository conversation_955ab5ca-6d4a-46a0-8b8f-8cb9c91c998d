@page "/admin/grades"
@using Schools.Shared.Models
@using Schools.Shared.DTOs
@using Schools.Client.Services
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@attribute [Authorize(Roles = "Admin")]

<PageTitle>إدارة المراحل الدراسية</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-layer-group me-2"></i>
                        إدارة المراحل الدراسية
                    </h4>
                    <button class="btn btn-light" @onclick="ShowAddModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مرحلة دراسية
                    </button>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-4">
                            <div class="spinner-border text-success" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else if (grades?.Any() == true)
                    {
                        <div class="row">
                            @foreach (var grade in grades)
                            {
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card h-100 border-start border-4 @GetGradeBorderClass((GradeLevel)grade.Level)">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <h5 class="card-title text-@GetGradeColorClass((GradeLevel)grade.Level)">
                                                    @grade.Name
                                                </h5>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="#" @onclick="() => EditGrade(grade)">
                                                            <i class="fas fa-edit me-2"></i>تعديل
                                                        </a></li>
                                                        <li><a class="dropdown-item text-danger" href="#" @onclick="() => DeleteGrade(grade.Id)">
                                                            <i class="fas fa-trash me-2"></i>حذف
                                                        </a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <p class="card-text">
                                                <span class="badge bg-@GetGradeColorClass((GradeLevel)grade.Level) mb-2">
                                                    @GetGradeLevelText((GradeLevel)grade.Level)
                                                </span>
                                            </p>
                                            <small class="text-muted">
                                                تم الإنشاء: @grade.CreatedAt.ToString("dd/MM/yyyy")
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد مراحل دراسية</h5>
                            <p class="text-muted">ابدأ بإضافة المراحل الدراسية</p>
                            <button class="btn btn-success" @onclick="ShowAddModal">
                                <i class="fas fa-plus me-2"></i>
                                إضافة مرحلة دراسية
                            </button>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Modal -->
@if (showModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        @if (editingGrade == null)
                        {
                            <text>إضافة مرحلة دراسية جديدة</text>
                        }
                        else
                        {
                            <text>تعديل المرحلة الدراسية</text>
                        }
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseModal"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="gradeModel" OnValidSubmit="SaveGrade">
                        <DataAnnotationsValidator />
                        <div class="mb-3">
                            <label class="form-label">اسم المرحلة الدراسية</label>
                            <InputText class="form-control" @bind-Value="gradeModel.Name" placeholder="مثال: الصف الأول الابتدائي" />
                            <ValidationMessage For="() => gradeModel.Name" class="text-danger" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نوع المرحلة</label>
                            <InputSelect class="form-select" @bind-Value="gradeModel.Level">
                                <option value="">اختر نوع المرحلة</option>
                                <option value="@GradeLevel.PreKindergarten">ما قبل الروضة</option>
                                <option value="@GradeLevel.Kindergarten">روضة</option>
                                <option value="@GradeLevel.Grade1">الصف الأول</option>
                                <option value="@GradeLevel.Grade2">الصف الثاني</option>
                                <option value="@GradeLevel.Grade3">الصف الثالث</option>
                                <option value="@GradeLevel.Grade4">الصف الرابع</option>
                                <option value="@GradeLevel.Grade5">الصف الخامس</option>
                                <option value="@GradeLevel.Grade6">الصف السادس</option>
                                <option value="@GradeLevel.Grade7">الصف السابع</option>
                                <option value="@GradeLevel.Grade8">الصف الثامن</option>
                                <option value="@GradeLevel.Grade9">الصف التاسع</option>
                                <option value="@GradeLevel.Grade10">الصف العاشر</option>
                                <option value="@GradeLevel.Grade11">الصف الحادي عشر</option>
                                <option value="@GradeLevel.Grade12">الصف الثاني عشر</option>
                            </InputSelect>
                            <ValidationMessage For="() => gradeModel.Level" class="text-danger" />
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" @onclick="CloseModal">إلغاء</button>
                            <button type="submit" class="btn btn-success" disabled="@isSaving">
                                @if (isSaving)
                                {
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                }
                                حفظ
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<AcademicGradeDto>? grades;
    private bool isLoading = true;
    private bool showModal = false;
    private bool isSaving = false;
    private AcademicGradeDto? editingGrade;
    private CreateAcademicGradeDto gradeModel = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadGrades();
    }

    private async Task LoadGrades()
    {
        try
        {
            isLoading = true;
            var gradeDtos = await ApiService.GetAcademicGradesAsync();
            grades = gradeDtos?.ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void ShowAddModal()
    {
        editingGrade = null;
        gradeModel = new CreateAcademicGradeDto();
        showModal = true;
    }

    private void EditGrade(AcademicGradeDto grade)
    {
        editingGrade = grade;
        gradeModel = new CreateAcademicGradeDto
        {
            Name = grade.Name,
            Level = grade.Level
        };
        showModal = true;
    }

    private void CloseModal()
    {
        showModal = false;
        editingGrade = null;
        gradeModel = new();
    }

    private async Task SaveGrade()
    {
        try
        {
            isSaving = true;

            if (editingGrade == null)
            {
                await ApiService.CreateGradeAsync(gradeModel);
                await JSRuntime.InvokeVoidAsync("alert", "تم إضافة المرحلة الدراسية بنجاح");
            }
            else
            {
                await ApiService.UpdateGradeAsync(editingGrade.Id, gradeModel);
                await JSRuntime.InvokeVoidAsync("alert", "تم تحديث المرحلة الدراسية بنجاح");
            }

            CloseModal();
            await LoadGrades();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ البيانات: {ex.Message}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task DeleteGrade(int gradeId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذه المرحلة الدراسية؟"))
        {
            try
            {
                await ApiService.DeleteGradeAsync(gradeId);
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف المرحلة الدراسية بنجاح");
                await LoadGrades();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف المرحلة الدراسية: {ex.Message}");
            }
        }
    }

    private string GetGradeLevelText(GradeLevel level)
    {
        return level switch
        {
            GradeLevel.PreKindergarten => "ما قبل الروضة",
            GradeLevel.Kindergarten => "روضة",
            GradeLevel.Grade1 => "الصف الأول",
            GradeLevel.Grade2 => "الصف الثاني",
            GradeLevel.Grade3 => "الصف الثالث",
            GradeLevel.Grade4 => "الصف الرابع",
            GradeLevel.Grade5 => "الصف الخامس",
            GradeLevel.Grade6 => "الصف السادس",
            GradeLevel.Grade7 => "الصف السابع",
            GradeLevel.Grade8 => "الصف الثامن",
            GradeLevel.Grade9 => "الصف التاسع",
            GradeLevel.Grade10 => "الصف العاشر",
            GradeLevel.Grade11 => "الصف الحادي عشر",
            GradeLevel.Grade12 => "الصف الثاني عشر",
            _ => "غير محدد"
        };
    }

    private string GetGradeColorClass(GradeLevel level)
    {
        return level switch
        {
            GradeLevel.PreKindergarten or GradeLevel.Kindergarten => "info",
            GradeLevel.Grade1 or GradeLevel.Grade2 or GradeLevel.Grade3 or GradeLevel.Grade4 or GradeLevel.Grade5 or GradeLevel.Grade6 => "primary",
            GradeLevel.Grade7 or GradeLevel.Grade8 or GradeLevel.Grade9 => "success",
            GradeLevel.Grade10 or GradeLevel.Grade11 or GradeLevel.Grade12 => "warning",
            _ => "secondary"
        };
    }

    private string GetGradeBorderClass(GradeLevel level)
    {
        return level switch
        {
            GradeLevel.PreKindergarten or GradeLevel.Kindergarten => "border-info",
            GradeLevel.Grade1 or GradeLevel.Grade2 or GradeLevel.Grade3 or GradeLevel.Grade4 or GradeLevel.Grade5 or GradeLevel.Grade6 => "border-primary",
            GradeLevel.Grade7 or GradeLevel.Grade8 or GradeLevel.Grade9 => "border-success",
            GradeLevel.Grade10 or GradeLevel.Grade11 or GradeLevel.Grade12 => "border-warning",
            _ => "border-secondary"
        };
    }
}
