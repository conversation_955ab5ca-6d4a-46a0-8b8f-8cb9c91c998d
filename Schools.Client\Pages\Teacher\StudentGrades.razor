@page "/teacher/student-grades"
@using Schools.Shared.DTOs
@using Schools.Client.Services
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            إدارة درجات الطلاب
                        </h4>
                        <button class="btn btn-light" @onclick="ShowAddModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة درجة
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <label class="form-label">الطالب</label>
                            <select class="form-select" @bind="selectedStudentId">
                                <option value="">جميع الطلاب</option>
                                @if (students != null)
                                {
                                    @foreach (var student in students)
                                    {
                                        <option value="@student.Id">@student.FirstName @student.LastName</option>
                                    }
                                }
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">المادة</label>
                            <select class="form-select" @bind="selectedSubjectId">
                                <option value="">جميع المواد</option>
                                @if (subjects != null)
                                {
                                    @foreach (var subject in subjects)
                                    {
                                        <option value="@subject.Id">@subject.Name</option>
                                    }
                                }
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">نوع الامتحان</label>
                            <select class="form-select" @bind="selectedExamType">
                                <option value="">جميع الأنواع</option>
                                <option value="Quiz">اختبار قصير</option>
                                <option value="Midterm">امتحان نصفي</option>
                                <option value="Final">امتحان نهائي</option>
                                <option value="Assignment">واجب</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">الفصل</label>
                            <select class="form-select" @bind="selectedSemester">
                                <option value="">جميع الفصول</option>
                                <option value="First">الأول</option>
                                <option value="Second">الثاني</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-primary d-block w-100" @onclick="LoadGrades">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    @if (statistics != null)
                    {
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h5>@statistics.TotalStudents</h5>
                                        <p class="mb-0">إجمالي الطلاب</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h5>@statistics.AverageScore.ToString("F1")%</h5>
                                        <p class="mb-0">المتوسط العام</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h5>@statistics.HighestScore.ToString("F1")%</h5>
                                        <p class="mb-0">أعلى درجة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h5>@statistics.PassRate.ToString("F1")%</h5>
                                        <p class="mb-0">معدل النجاح</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Grades Table -->
                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else if (grades?.Any() == true)
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الطالب</th>
                                        <th>المادة</th>
                                        <th>نوع الامتحان</th>
                                        <th>الدرجة</th>
                                        <th>النسبة المئوية</th>
                                        <th>التاريخ</th>
                                        <th>الفصل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var grade in grades)
                                    {
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong>@grade.StudentName</strong>
                                                    <br>
                                                    <small class="text-muted">@grade.StudentEmail</small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@grade.SubjectName</span>
                                            </td>
                                            <td>@GetExamTypeText(grade.ExamType)</td>
                                            <td>
                                                <strong>@grade.Score / @grade.MaxScore</strong>
                                            </td>
                                            <td>
                                                <span class="badge @GetPercentageClass(grade.Percentage)">
                                                    @grade.Percentage.ToString("F1")%
                                                </span>
                                            </td>
                                            <td>@grade.Date.ToString("yyyy-MM-dd")</td>
                                            <td>@GetSemesterText(grade.Semester)</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-primary" @onclick="() => EditGrade(grade)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteGrade(grade.Id)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                <li class="page-item @(currentPage <= 1 ? "disabled" : "")">
                                    <button class="page-link" @onclick="() => ChangePage(currentPage - 1)">السابق</button>
                                </li>
                                @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                                {
                                    <li class="page-item @(i == currentPage ? "active" : "")">
                                        <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                                    </li>
                                }
                                <li class="page-item @(currentPage >= totalPages ? "disabled" : "")">
                                    <button class="page-link" @onclick="() => ChangePage(currentPage + 1)">التالي</button>
                                </li>
                            </ul>
                        </nav>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد درجات</h5>
                            <p class="text-muted">لم يتم العثور على أي درجات بالمعايير المحددة</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Grade Modal -->
@if (showModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        @(editingGrade == null ? "إضافة درجة جديدة" : "تعديل الدرجة")
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseModal"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="gradeModel" OnValidSubmit="SaveGrade">
                        <DataAnnotationsValidator />
                        <ValidationSummary class="text-danger" />

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الطالب *</label>
                                    <InputSelect class="form-select" @bind-Value="gradeModel.StudentId">
                                        <option value="">اختر الطالب</option>
                                        @if (students != null)
                                        {
                                            @foreach (var student in students)
                                            {
                                                <option value="@student.Id">@student.FirstName @student.LastName</option>
                                            }
                                        }
                                    </InputSelect>
                                    <ValidationMessage For="@(() => gradeModel.StudentId)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المادة *</label>
                                    <InputSelect class="form-select" @bind-Value="gradeModel.SubjectId">
                                        <option value="">اختر المادة</option>
                                        @if (subjects != null)
                                        {
                                            @foreach (var subject in subjects)
                                            {
                                                <option value="@subject.Id">@subject.Name</option>
                                            }
                                        }
                                    </InputSelect>
                                    <ValidationMessage For="@(() => gradeModel.SubjectId)" />
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">نوع الامتحان *</label>
                                    <InputSelect class="form-select" @bind-Value="gradeModel.ExamType">
                                        <option value="">اختر النوع</option>
                                        <option value="Quiz">اختبار قصير</option>
                                        <option value="Midterm">امتحان نصفي</option>
                                        <option value="Final">امتحان نهائي</option>
                                        <option value="Assignment">واجب</option>
                                    </InputSelect>
                                    <ValidationMessage For="@(() => gradeModel.ExamType)" />
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الدرجة المحصلة *</label>
                                    <InputNumber class="form-control" @bind-Value="gradeModel.Score" />
                                    <ValidationMessage For="@(() => gradeModel.Score)" />
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الدرجة الكاملة *</label>
                                    <InputNumber class="form-control" @bind-Value="gradeModel.MaxScore" />
                                    <ValidationMessage For="@(() => gradeModel.MaxScore)" />
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">التاريخ *</label>
                                    <InputDate class="form-control" @bind-Value="gradeModel.Date" />
                                    <ValidationMessage For="@(() => gradeModel.Date)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الفصل الدراسي *</label>
                                    <InputSelect class="form-select" @bind-Value="gradeModel.Semester">
                                        <option value="">اختر الفصل</option>
                                        <option value="First">الفصل الأول</option>
                                        <option value="Second">الفصل الثاني</option>
                                    </InputSelect>
                                    <ValidationMessage For="@(() => gradeModel.Semester)" />
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <InputTextArea class="form-control" @bind-Value="gradeModel.Notes" rows="3" />
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" @onclick="CloseModal">إلغاء</button>
                            <button type="submit" class="btn btn-primary" disabled="@isSaving">
                                @if (isSaving)
                                {
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                }
                                @(editingGrade == null ? "إضافة" : "تحديث")
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<GradeDto>? grades;
    private List<UserDto>? students;
    private List<SubjectDto>? subjects;
    private List<AcademicYearDto>? academicYears;
    private GradeStatisticsDto? statistics;

    private bool isLoading = true;
    private bool showModal = false;
    private bool isSaving = false;
    private GradeDto? editingGrade;
    private CreateGradeDto gradeModel = new();

    // Filters
    private string selectedStudentId = "";
    private int? selectedSubjectId;
    private string selectedExamType = "";
    private string selectedSemester = "";
    private int? selectedAcademicYearId;

    // Pagination
    private int currentPage = 1;
    private int pageSize = 20;
    private int totalPages = 1;

    protected override async Task OnInitializedAsync()
    {
        await LoadInitialData();
        await LoadGrades();
    }

    private async Task LoadInitialData()
    {
        try
        {
            var studentsTask = ApiService.GetUsersAsync();
            var subjectsTask = ApiService.GetSubjectsAsync();
            var academicYearsTask = ApiService.GetAcademicYearsAsync();

            await Task.WhenAll(studentsTask, subjectsTask, academicYearsTask);

            students = (await studentsTask).Where(u => u.Role == "Student").ToList();
            subjects = (await subjectsTask).ToList();
            academicYears = (await academicYearsTask).ToList();

            // Set default academic year
            selectedAcademicYearId = academicYears.FirstOrDefault(y => y.IsActive)?.Id;
            gradeModel.AcademicYearId = selectedAcademicYearId ?? 0;
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
    }

    private async Task LoadGrades()
    {
        try
        {
            isLoading = true;

            var gradesTask = ApiService.GetStudentGradesAsync(
                selectedStudentId,
                selectedSubjectId,
                selectedExamType,
                selectedSemester,
                selectedAcademicYearId,
                currentPage,
                pageSize);

            var statisticsTask = ApiService.GetGradeStatisticsAsync(
                selectedSubjectId,
                selectedExamType,
                selectedSemester,
                selectedAcademicYearId);

            await Task.WhenAll(gradesTask, statisticsTask);

            grades = (await gradesTask).ToList();
            statistics = await statisticsTask;

            // Calculate total pages (this would normally come from API headers)
            totalPages = Math.Max(1, (int)Math.Ceiling(grades.Count / (double)pageSize));
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الدرجات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void ShowAddModal()
    {
        editingGrade = null;
        gradeModel = new CreateGradeDto
        {
            Date = DateTime.Today,
            AcademicYearId = selectedAcademicYearId ?? 0
        };
        showModal = true;
    }

    private void EditGrade(GradeDto grade)
    {
        editingGrade = grade;
        gradeModel = new CreateGradeDto
        {
            StudentId = grade.StudentId,
            SubjectId = grade.SubjectId,
            AcademicYearId = grade.AcademicYearId,
            ExamType = grade.ExamType,
            Score = grade.Score,
            MaxScore = grade.MaxScore,
            Date = grade.Date,
            Semester = grade.Semester,
            Notes = grade.Notes
        };
        showModal = true;
    }

    private void CloseModal()
    {
        showModal = false;
        editingGrade = null;
        gradeModel = new();
    }

    private async Task SaveGrade()
    {
        try
        {
            isSaving = true;

            if (editingGrade == null)
            {
                await ApiService.CreateStudentGradeAsync(gradeModel);
                await JSRuntime.InvokeVoidAsync("alert", "تم إضافة الدرجة بنجاح");
            }
            else
            {
                var updateDto = new UpdateGradeDto
                {
                    Score = gradeModel.Score,
                    MaxScore = gradeModel.MaxScore,
                    ExamType = gradeModel.ExamType,
                    Date = gradeModel.Date,
                    Semester = gradeModel.Semester,
                    Notes = gradeModel.Notes
                };

                await ApiService.UpdateStudentGradeAsync(editingGrade.Id, updateDto);
                await JSRuntime.InvokeVoidAsync("alert", "تم تحديث الدرجة بنجاح");
            }

            CloseModal();
            await LoadGrades();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ الدرجة: {ex.Message}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task DeleteGrade(int gradeId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذه الدرجة؟"))
        {
            try
            {
                await ApiService.DeleteStudentGradeAsync(gradeId);
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف الدرجة بنجاح");
                await LoadGrades();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف الدرجة: {ex.Message}");
            }
        }
    }

    private async Task ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages && page != currentPage)
        {
            currentPage = page;
            await LoadGrades();
        }
    }

    private string GetExamTypeText(string examType)
    {
        return examType switch
        {
            "Quiz" => "اختبار قصير",
            "Midterm" => "امتحان نصفي",
            "Final" => "امتحان نهائي",
            "Assignment" => "واجب",
            _ => examType
        };
    }

    private string GetSemesterText(string semester)
    {
        return semester switch
        {
            "First" => "الأول",
            "Second" => "الثاني",
            _ => semester
        };
    }

    private string GetPercentageClass(decimal percentage)
    {
        return percentage switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-info",
            >= 70 => "bg-warning",
            >= 60 => "bg-secondary",
            _ => "bg-danger"
        };
    }
}
