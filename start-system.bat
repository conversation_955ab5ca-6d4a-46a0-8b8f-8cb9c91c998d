@echo off
chcp 65001 >nul
title نظام إدارة المدارس - Schools Management System

echo.
echo ================================================
echo 🏫 بدء تشغيل نظام إدارة المدارس
echo ================================================
echo.

REM Check if .NET is installed
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ .NET SDK غير مثبت. يرجى تثبيت .NET 9 SDK أولاً
    pause
    exit /b 1
)

echo ✅ .NET SDK متوفر
echo.

REM Build the solution
echo 🔨 بناء المشروع...
dotnet build
if errorlevel 1 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo.

REM Start API
echo 🚀 تشغيل API...
start "Schools API" cmd /k "dotnet run --project Schools.API"

REM Wait for API to start
timeout /t 5 /nobreak >nul

REM Start Client
echo 🌐 تشغيل العميل...
start "Schools Client" cmd /k "dotnet run --project Schools.Client"

REM Wait for Client to start
timeout /t 8 /nobreak >nul

echo.
echo ================================================
echo 🎉 تم تشغيل النظام بنجاح!
echo ================================================
echo.
echo 📍 الروابط المتاحة:
echo    🌐 العميل: http://localhost:5131
echo    🔧 API: http://localhost:5261
echo    📚 Swagger: http://localhost:5261/swagger
echo.
echo 🔐 بيانات تسجيل الدخول للمدير:
echo    📧 البريد: <EMAIL>
echo    🔑 كلمة المرور: Admin123!
echo.
echo 💡 نصائح:
echo    • سجل دخولك كمدير أولاً
echo    • أنشئ حسابات للمستخدمين الآخرين
echo    • وافق على طلبات المستخدمين الجدد
echo    • استكشف جميع لوحات التحكم
echo.

REM Open browser
echo 🌐 فتح المتصفح...
start http://localhost:5131

echo.
echo ✨ استمتع باستخدام نظام إدارة المدارس!
echo ================================================
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
