# 🔧 دليل حل المشاكل - نظام إدارة المدارس

## 🚨 المشاكل الشائعة وحلولها

### 1. خطأ المصادقة (Authorization Error)

**المشكلة:**
```
Authorization requires a cascading parameter of type Task<AuthenticationState>
```

**الحل:**
✅ **تم إصلاحه** - تأكد من وجود `CascadingAuthenticationState` في `App.razor`

### 2. خطأ الثقافة (Culture Error)

**المشكلة:**
```
<PERSON><PERSON><PERSON> detected a change in the application's culture that is not supported
```

**الحل:**
✅ **تم إصلاحه** - أضف في ملف المشروع:
```xml
<BlazorWebAssemblyLoadAllGlobalizationData>true</BlazorWebAssemblyLoadAllGlobalizationData>
```

### 3. خطأ الاتصال بـ API

**المشكلة:**
```
Failed to fetch data from API
```

**الحل:**
1. تأكد من تشغيل API على http://localhost:5261
2. تحقق من `HttpClient` في `Program.cs`:
```csharp
builder.Services.AddScoped(sp => new HttpClient { BaseAddress = new Uri("http://localhost:5261/") });
```

### 4. خطأ قاعدة البيانات

**المشكلة:**
```
Cannot open database / Connection string error
```

**الحل:**
```bash
# إعادة إنشاء قاعدة البيانات
dotnet ef database drop --project Schools.Data --startup-project Schools.API
dotnet ef database update --project Schools.Data --startup-project Schools.API
```

### 5. خطأ في البناء (Build Error)

**المشكلة:**
```
Build failed with compilation errors
```

**الحل:**
```bash
# تنظيف وإعادة البناء
dotnet clean
dotnet restore
dotnet build
```

### 6. خطأ في تشغيل العميل

**المشكلة:**
```
Client fails to start or shows blank page
```

**الحل:**
1. تحقق من Console في المتصفح للأخطاء
2. تأكد من تشغيل API أولاً
3. امسح cache المتصفح (Ctrl+Shift+Delete)

### 7. خطأ CORS

**المشكلة:**
```
Access to fetch at 'http://localhost:5261/api/auth/login' from origin 'http://localhost:5131' has been blocked by CORS policy
```

**الحل:**
✅ **تم إصلاحه** - تأكد من إعداد CORS في `Program.cs` للـ API:
```csharp
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowBlazorClient", policy =>
    {
        policy.WithOrigins("http://localhost:5131", "https://localhost:5131")
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

app.UseCors("AllowBlazorClient");
```

### 8. خطأ JWT Token

**المشكلة:**
```
JWT token validation failed
```

**الحل:**
1. تحقق من JWT settings في `appsettings.json`
2. تأكد من تطابق المفاتيح بين API والعميل
3. تحقق من انتهاء صلاحية Token

### 9. خطأ في الصفحات

**المشكلة:**
```
Page not found or routing error
```

**الحل:**
1. تحقق من `@page` directive في أعلى الصفحة
2. تأكد من إضافة الصفحة في Navigation
3. تحقق من Authorization requirements

### 10. خطأ في قاعدة البيانات Migration

**المشكلة:**
```
Migration failed or database schema error
```

**الحل:**
```bash
# حذف آخر migration
dotnet ef migrations remove --project Schools.Data --startup-project Schools.API

# إنشاء migration جديد
dotnet ef migrations add NewMigration --project Schools.Data --startup-project Schools.API

# تطبيق migration
dotnet ef database update --project Schools.Data --startup-project Schools.API
```

## 🔍 أدوات التشخيص

### فحص حالة النظام:

#### 1. فحص API:
```bash
curl http://localhost:5261/api/health
```

#### 2. فحص Swagger:
افتح: http://localhost:5261/swagger

#### 3. فحص العميل:
افتح: http://localhost:5131

#### 4. فحص قاعدة البيانات:
```bash
dotnet ef dbcontext info --project Schools.Data --startup-project Schools.API
```

### فحص Logs:

#### API Logs:
```bash
# في terminal الخاص بـ API
dotnet run --project Schools.API --verbosity detailed
```

#### Client Logs:
- افتح Developer Tools في المتصفح (F12)
- تحقق من Console tab للأخطاء
- تحقق من Network tab لطلبات API

## 🚀 خطوات إعادة التشغيل الكامل

إذا واجهت مشاكل متعددة، اتبع هذه الخطوات:

### 1. إيقاف كل شيء:
```bash
# أوقف جميع العمليات
Ctrl+C في كل terminal
```

### 2. تنظيف المشروع:
```bash
dotnet clean
dotnet restore
```

### 3. إعادة إنشاء قاعدة البيانات:
```bash
dotnet ef database drop --project Schools.Data --startup-project Schools.API --force
dotnet ef database update --project Schools.Data --startup-project Schools.API
```

### 4. إعادة البناء:
```bash
dotnet build
```

### 5. تشغيل API:
```bash
dotnet run --project Schools.API
```

### 6. تشغيل العميل:
```bash
dotnet run --project Schools.Client
```

### 7. اختبار النظام:
- افتح http://localhost:5131
- سجل دخول بحساب المدير
- اختبر الوظائف الأساسية

## 📞 الحصول على المساعدة

### معلومات مفيدة عند طلب المساعدة:

1. **نسخة .NET:**
```bash
dotnet --version
```

2. **نظام التشغيل:**
```bash
systeminfo | findstr "OS Name"
```

3. **رسالة الخطأ الكاملة** من Console أو Terminal

4. **خطوات إعادة إنتاج المشكلة**

5. **ما كان يعمل قبل ظهور المشكلة**

### نصائح عامة:

- ✅ **احفظ عملك** قبل تجربة الحلول
- ✅ **اختبر خطوة واحدة في كل مرة**
- ✅ **راجع الـ logs** دائماً للحصول على تفاصيل أكثر
- ✅ **استخدم Git** لحفظ النسخ المستقرة

---

**معظم المشاكل يمكن حلها بإعادة التشغيل الكامل للنظام 🔄**
