using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;
using System.Security.Claims;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Policy = "AdminOnly")]
    public class SubjectsController : ControllerBase
    {
        private readonly SchoolsDbContext _context;

        public SubjectsController(SchoolsDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<SubjectDto>>> GetSubjects()
        {
            var subjects = await _context.Subjects
                .Where(s => !s.IsDeleted)
                .Select(s => new SubjectDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    NameAr = s.NameAr,
                    Code = s.Code,
                    Description = s.Description,
                    CreditHours = s.CreditHours
                })
                .ToListAsync();

            return Ok(subjects);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<SubjectDto>> GetSubject(int id)
        {
            var subject = await _context.Subjects
                .Where(s => s.Id == id && !s.IsDeleted)
                .Select(s => new SubjectDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Code = s.Code,
                    Description = s.Description,
                    CreditHours = s.CreditHours
                })
                .FirstOrDefaultAsync();

            if (subject == null)
            {
                return NotFound(new { message = "المادة الدراسية غير موجودة" });
            }

            return Ok(subject);
        }

        [HttpPost]
        public async Task<ActionResult<SubjectDto>> CreateSubject(CreateSubjectDto model)
        {
            try
            {
                // Check if subject code already exists
                var existingSubject = await _context.Subjects
                    .Where(s => s.Code == model.Code && !s.IsDeleted)
                    .FirstOrDefaultAsync();

                if (existingSubject != null)
                {
                    return BadRequest(new { message = "رمز المادة موجود بالفعل" });
                }

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                var subject = new Subject
                {
                    Name = model.Name,
                    NameAr = model.NameAr,
                    Code = model.Code,
                    Description = model.Description,
                    CreditHours = model.CreditHours,
                    CreatedBy = userId
                };

                _context.Subjects.Add(subject);
                await _context.SaveChangesAsync();

                var result = new SubjectDto
                {
                    Id = subject.Id,
                    Name = subject.Name,
                    Code = subject.Code,
                    Description = subject.Description,
                    CreditHours = subject.CreditHours
                };

                return CreatedAtAction(nameof(GetSubject), new { id = subject.Id }, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء إنشاء المادة الدراسية" });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateSubject(int id, CreateSubjectDto model)
        {
            try
            {
                var subject = await _context.Subjects.FindAsync(id);
                if (subject == null || subject.IsDeleted)
                {
                    return NotFound(new { message = "المادة الدراسية غير موجودة" });
                }

                // Check if subject code already exists (excluding current subject)
                var existingSubject = await _context.Subjects
                    .Where(s => s.Code == model.Code && s.Id != id && !s.IsDeleted)
                    .FirstOrDefaultAsync();

                if (existingSubject != null)
                {
                    return BadRequest(new { message = "رمز المادة موجود بالفعل" });
                }

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                subject.Name = model.Name;
                subject.Code = model.Code;
                subject.Description = model.Description;
                subject.CreditHours = model.CreditHours;
                subject.UpdatedAt = DateTime.UtcNow;
                subject.UpdatedBy = userId;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تحديث المادة الدراسية بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء تحديث المادة الدراسية" });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteSubject(int id)
        {
            try
            {
                var subject = await _context.Subjects.FindAsync(id);
                if (subject == null || subject.IsDeleted)
                {
                    return NotFound(new { message = "المادة الدراسية غير موجودة" });
                }

                // Check if subject is assigned to any teacher
                var hasAssignments = await _context.TeacherAssignments
                    .AnyAsync(ta => ta.SubjectId == id && ta.IsActive);

                if (hasAssignments)
                {
                    return BadRequest(new { message = "لا يمكن حذف المادة لأنها مسندة لمعلمين" });
                }

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                subject.IsDeleted = true;
                subject.UpdatedAt = DateTime.UtcNow;
                subject.UpdatedBy = userId;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم حذف المادة الدراسية بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء حذف المادة الدراسية" });
            }
        }

        [HttpGet("{id}/teachers")]
        public async Task<ActionResult<IEnumerable<object>>> GetSubjectTeachers(int id)
        {
            var teachers = await _context.TeacherAssignments
                .Include(ta => ta.Teacher)
                .Include(ta => ta.AcademicYear)
                .Include(ta => ta.Class)
                .Include(ta => ta.Section)
                .Where(ta => ta.SubjectId == id && ta.IsActive)
                .Select(ta => new
                {
                    AssignmentId = ta.Id,
                    TeacherId = ta.TeacherId,
                    TeacherName = ta.Teacher.FirstName + " " + ta.Teacher.LastName,
                    TeacherEmail = ta.Teacher.Email,
                    AcademicYear = ta.AcademicYear.Name,
                    Class = ta.Class != null ? ta.Class.Name : null,
                    Section = ta.Section != null ? ta.Section.Name : null
                })
                .ToListAsync();

            return Ok(teachers);
        }

        [HttpPost("{id}/assign-teacher")]
        public async Task<IActionResult> AssignTeacher(int id, [FromBody] AssignTeacherDto model)
        {
            try
            {
                // Check if subject exists
                var subject = await _context.Subjects.FindAsync(id);
                if (subject == null || subject.IsDeleted)
                {
                    return NotFound(new { message = "المادة الدراسية غير موجودة" });
                }

                // Check if teacher exists and has Teacher role
                var teacher = await _context.Users.FindAsync(model.TeacherId);
                if (teacher == null || !teacher.IsActive)
                {
                    return BadRequest(new { message = "المعلم غير موجود أو غير مفعل" });
                }

                // Check if academic year exists
                var academicYear = await _context.AcademicYears.FindAsync(model.AcademicYearId);
                if (academicYear == null || academicYear.IsDeleted)
                {
                    return BadRequest(new { message = "العام الدراسي غير موجود" });
                }

                // Check if assignment already exists
                var existingAssignment = await _context.TeacherAssignments
                    .Where(ta => ta.TeacherId == model.TeacherId &&
                                ta.SubjectId == id &&
                                ta.AcademicYearId == model.AcademicYearId &&
                                ta.ClassId == model.ClassId &&
                                ta.SectionId == model.SectionId &&
                                ta.IsActive)
                    .FirstOrDefaultAsync();

                if (existingAssignment != null)
                {
                    return BadRequest(new { message = "المعلم مسند بالفعل لهذه المادة" });
                }

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                var assignment = new TeacherAssignment
                {
                    TeacherId = model.TeacherId,
                    SubjectId = id,
                    AcademicYearId = model.AcademicYearId,
                    ClassId = model.ClassId ?? 0,
                    SectionId = model.SectionId,
                    CreatedBy = userId
                };

                _context.TeacherAssignments.Add(assignment);
                await _context.SaveChangesAsync();

                return Ok(new { message = "تم إسناد المادة للمعلم بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء إسناد المادة للمعلم" });
            }
        }

        [HttpDelete("assignments/{assignmentId}")]
        public async Task<IActionResult> RemoveTeacherAssignment(int assignmentId)
        {
            try
            {
                var assignment = await _context.TeacherAssignments.FindAsync(assignmentId);
                if (assignment == null)
                {
                    return NotFound(new { message = "الإسناد غير موجود" });
                }

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                assignment.IsActive = false;
                assignment.UpdatedAt = DateTime.UtcNow;
                assignment.UpdatedBy = userId;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم إلغاء إسناد المادة بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء إلغاء إسناد المادة" });
            }
        }
    }

    public class AssignTeacherDto
    {
        public string TeacherId { get; set; } = string.Empty;
        public int AcademicYearId { get; set; }
        public int? ClassId { get; set; }
        public int? SectionId { get; set; }
    }
}
