using System.ComponentModel.DataAnnotations;

namespace Schools.Shared.Models
{
    // Activity
    public class Activity : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Description { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string Type { get; set; } = string.Empty; // Sports, Cultural, Scientific, Social, Religious

        [Required]
        public ActivityStatus Status { get; set; } = ActivityStatus.Planned;

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        [Required]
        public TimeSpan StartTime { get; set; }

        [Required]
        public TimeSpan EndTime { get; set; }

        [Required]
        [StringLength(100)]
        public string Location { get; set; } = string.Empty;

        [Required]
        public string OrganizerId { get; set; } = string.Empty;

        public int MaxParticipants { get; set; } = 0;

        public int MinParticipants { get; set; } = 0;

        public decimal? RegistrationFee { get; set; }

        public DateTime? RegistrationDeadline { get; set; }

        public string? Requirements { get; set; }

        public string? Notes { get; set; }

        public string? ImageUrl { get; set; }

        public bool IsPublic { get; set; } = true;

        public bool RequiresApproval { get; set; } = false;

        public string? TargetGrades { get; set; } // JSON array of grade IDs

        public string? Equipment { get; set; }

        public string? ContactInfo { get; set; }

        // Computed properties
        public int ParticipantsCount => Participants?.Count(p => p.Status == "Registered" || p.Status == "Attended") ?? 0;

        public bool IsRegistrationOpen =>
            DateTime.Now <= (RegistrationDeadline ?? StartDate) &&
            Status == ActivityStatus.Planned &&
            (MaxParticipants == 0 || ParticipantsCount < MaxParticipants);

        // Navigation properties
        public virtual ApplicationUser Organizer { get; set; } = null!;
        public virtual ICollection<ActivityParticipant> Participants { get; set; } = new List<ActivityParticipant>();
        public virtual ICollection<ActivityResource> ActivityResources { get; set; } = new List<ActivityResource>();
        public virtual ICollection<ActivityFeedback> ActivityFeedbacks { get; set; } = new List<ActivityFeedback>();
    }

    // Activity Participant
    public class ActivityParticipant : BaseEntity
    {
        [Required]
        public int ActivityId { get; set; }

        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public DateTime RegistrationDate { get; set; }

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = string.Empty; // Registered, Attended, Absent, Cancelled

        public string? Notes { get; set; }

        public DateTime? AttendanceMarkedAt { get; set; }

        public string? AttendanceMarkedBy { get; set; }

        public decimal? FeePaid { get; set; }

        public DateTime? PaymentDate { get; set; }

        public string? PaymentMethod { get; set; }

        public bool IsApproved { get; set; } = true;

        public string? ApprovedBy { get; set; }

        public DateTime? ApprovedAt { get; set; }

        public string? RejectionReason { get; set; }

        // Navigation properties
        public virtual Activity Activity { get; set; } = null!;
        public virtual ApplicationUser Student { get; set; } = null!;
    }

    // Activity Resource
    public class ActivityResource : BaseEntity
    {
        [Required]
        public int ActivityId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public ActivityResourceType Type { get; set; }

        public string? Description { get; set; }

        public string? FileUrl { get; set; }

        public string? ExternalUrl { get; set; }

        public int? Quantity { get; set; }

        public decimal? Cost { get; set; }

        public string? Supplier { get; set; }

        public bool IsRequired { get; set; } = false;

        public bool IsAvailable { get; set; } = true;

        // Navigation properties
        public virtual Activity Activity { get; set; } = null!;
    }

    // Activity Feedback
    public class ActivityFeedback : BaseEntity
    {
        [Required]
        public int ActivityId { get; set; }

        [Required]
        public string ParticipantId { get; set; } = string.Empty;

        [Required]
        [Range(1, 5)]
        public int Rating { get; set; }

        public string? Comments { get; set; }

        public DateTime FeedbackDate { get; set; } = DateTime.UtcNow;

        public bool IsAnonymous { get; set; } = false;

        public string? Suggestions { get; set; }

        public bool WouldRecommend { get; set; } = true;

        // Navigation properties
        public virtual Activity Activity { get; set; } = null!;
        public virtual ApplicationUser Participant { get; set; } = null!;
    }

    // Event (Special type of Activity)
    public class Event : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Description { get; set; } = string.Empty;

        [Required]
        public EventType Type { get; set; }

        [Required]
        public DateTime StartDateTime { get; set; }

        [Required]
        public DateTime EndDateTime { get; set; }

        [Required]
        [StringLength(100)]
        public string Venue { get; set; } = string.Empty;

        public string? OrganizerId { get; set; }

        public int? Capacity { get; set; }

        public decimal? TicketPrice { get; set; }

        public bool IsPublic { get; set; } = true;

        public bool RequiresRegistration { get; set; } = false;

        public DateTime? RegistrationDeadline { get; set; }

        public string? ImageUrl { get; set; }

        public string? ContactEmail { get; set; }

        public string? ContactPhone { get; set; }

        public EventStatus Status { get; set; } = EventStatus.Planned;

        public string? Notes { get; set; }

        // Navigation properties
        public virtual ApplicationUser? Organizer { get; set; }
        public virtual ICollection<EventRegistration> EventRegistrations { get; set; } = new List<EventRegistration>();
    }

    // Event Registration
    public class EventRegistration : BaseEntity
    {
        [Required]
        public int EventId { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        public DateTime RegistrationDate { get; set; }

        public RegistrationStatus Status { get; set; } = RegistrationStatus.Confirmed;

        public decimal? AmountPaid { get; set; }

        public DateTime? PaymentDate { get; set; }

        public string? PaymentMethod { get; set; }

        public string? Notes { get; set; }

        public bool IsAttended { get; set; } = false;

        public DateTime? AttendanceTime { get; set; }

        // Navigation properties
        public virtual Event Event { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
    }

}
