{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=SchoolsDB;Trusted_Connection=true;MultipleActiveResultSets=true"}, "JwtSettings": {"Secret": "YourSecretKeyHere123456789SchoolsApp2024ExtendedForSecurity", "Issuer": "SchoolsAPI", "Audience": "SchoolsClient", "ExpiryInHours": 24, "RefreshTokenExpiryInDays": 7, "ClockSkewInMinutes": 5, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "RequireExpirationTime": true, "RequireSignedTokens": true}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*", "SchoolSettings": {"SchoolName": "مدرسة المستقبل النموذجية", "SchoolNameEn": "Future Model School", "SchoolCode": "FMS2024", "Address": "الرياض، المملكة العربية السعودية", "Phone": "+966-11-1234567", "Email": "<EMAIL>", "Website": "https://www.futureschool.edu.sa", "Logo": "/images/school-logo.png", "EstablishedYear": 2010, "Currency": "SAR", "Language": "ar", "TimeZone": "Asia/Riyadh", "AcademicYearStart": "09-01", "AcademicYearEnd": "06-30"}, "LibrarySettings": {"MaxBooksPerStudent": 3, "BorrowDurationDays": 14, "MaxRenewalCount": 2, "FinePerDay": 1.0, "MaxFineAmount": 50.0, "ReservationExpiryDays": 3, "DigitalResourcesEnabled": true, "AllowOnlineReading": true, "RequireLibraryCard": true}, "AttendanceSettings": {"LateThresholdMinutes": 15, "AbsentThresholdMinutes": 60, "AllowSelfCheckIn": false, "RequireLocationVerification": true, "AttendanceGracePeriodMinutes": 5, "NotifyParentsOnAbsence": true, "MaxConsecutiveAbsences": 3}, "GradingSettings": {"PassingGrade": 60.0, "MaxGrade": 100.0, "GradeScale": "Percentage", "AllowGradeModification": true, "RequireApprovalForChanges": true, "ShowGradesToParents": true, "ShowGradesToStudents": true, "GradeCalculationMethod": "Weighted"}, "ActivitySettings": {"MaxParticipantsDefault": 30, "RegistrationDeadlineDays": 3, "AllowLateRegistration": false, "RequireParentApproval": true, "AllowActivityFeedback": true, "ActivityPhotoUpload": true, "MaxActivityDurationHours": 8}, "NotificationSettings": {"EmailEnabled": true, "SMSEnabled": true, "PushNotificationsEnabled": true, "NotifyOnGradeUpdate": true, "NotifyOnAttendance": true, "NotifyOnActivityRegistration": true, "NotifyOnLibraryDueDate": true, "NotifyOnFeesDue": true}, "FileUploadSettings": {"MaxFileSizeMB": 10, "AllowedImageTypes": [".jpg", ".jpeg", ".png", ".gif"], "AllowedDocumentTypes": [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"], "AllowedVideoTypes": [".mp4", ".avi", ".mov", ".wmv"], "UploadPath": "wwwroot/uploads", "ProfilePicturePath": "wwwroot/uploads/profiles", "DocumentPath": "wwwroot/uploads/documents", "ActivityPhotoPath": "wwwroot/uploads/activities"}, "SecuritySettings": {"PasswordMinLength": 8, "RequireDigit": true, "RequireLowercase": true, "RequireUppercase": true, "RequireNonAlphanumeric": true, "MaxFailedAccessAttempts": 5, "LockoutTimeSpanMinutes": 30, "RequireConfirmedEmail": false, "RequireConfirmedPhoneNumber": false, "AllowedForNewUsers": true}, "ApiSettings": {"Version": "v1", "Title": "Schools Management API", "Description": "Comprehensive School Management System API", "ContactName": "Schools API Support", "ContactEmail": "<EMAIL>", "LicenseName": "MIT", "EnableSwagger": true, "EnableCors": true, "RateLimitingEnabled": true, "RequestsPerMinute": 100}, "DatabaseSettings": {"CommandTimeout": 30, "EnableSensitiveDataLogging": false, "EnableDetailedErrors": false, "MaxRetryCount": 3, "MaxRetryDelay": "00:00:30", "EnableServiceProviderCaching": true, "EnableQuerySplitting": true}, "CacheSettings": {"DefaultExpirationMinutes": 60, "SlidingExpirationMinutes": 30, "UserCacheExpirationMinutes": 15, "StaticDataCacheHours": 24, "EnableDistributedCache": false, "CacheProvider": "Memory"}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SmtpUsername": "<EMAIL>", "SmtpPassword": "your-email-password", "EnableSsl": true, "FromName": "مدرسة المستقبل النموذجية", "FromEmail": "<EMAIL>", "ReplyToEmail": "<EMAIL>"}, "SmsSettings": {"Provider": "<PERSON><PERSON><PERSON>", "AccountSid": "your-twilio-account-sid", "AuthToken": "your-twilio-auth-token", "FromNumber": "+************", "EnableSms": false}, "ReportSettings": {"DefaultPageSize": 50, "MaxPageSize": 1000, "AllowExportToExcel": true, "AllowExportToPdf": true, "ReportCacheMinutes": 30, "EnableReportScheduling": true, "ReportStoragePath": "wwwroot/reports"}}