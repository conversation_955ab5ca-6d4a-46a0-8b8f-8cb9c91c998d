@page "/admin/users"
@using Schools.Shared.Models
@using Schools.Shared.DTOs
@using Schools.Client.Services
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Admin")]

<PageTitle>إدارة المستخدمين</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        إدارة المستخدمين
                    </h4>
                    <div class="btn-group">
                        <button class="btn btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-2"></i>
                            فلترة: @GetRoleDisplayName(selectedRole)
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" @onclick="@(() => FilterByRole(string.Empty))">جميع المستخدمين</a></li>
                            <li><a class="dropdown-item" href="#" @onclick="@(() => FilterByRole("Student"))">الطلاب</a></li>
                            <li><a class="dropdown-item" href="#" @onclick="@(() => FilterByRole("Teacher"))">المعلمين</a></li>
                            <li><a class="dropdown-item" href="#" @onclick="@(() => FilterByRole("Parent"))">أولياء الأمور</a></li>
                            <li><a class="dropdown-item" href="#" @onclick="@(() => FilterByRole("Staff"))">الموظفين</a></li>
                            <li><a class="dropdown-item" href="#" @onclick="@(() => FilterByRole("Accountant"))">المحاسبين</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else if (filteredUsers?.Any() == true)
                    {
                        <!-- Statistics Cards -->
                        <div class="row mb-4">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>@activeUsersCount</h4>
                                                <p class="mb-0">مستخدمين نشطين</p>
                                            </div>
                                            <i class="fas fa-user-check fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>@pendingUsersCount</h4>
                                                <p class="mb-0">في انتظار الموافقة</p>
                                            </div>
                                            <i class="fas fa-user-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>@inactiveUsersCount</h4>
                                                <p class="mb-0">مستخدمين غير نشطين</p>
                                            </div>
                                            <i class="fas fa-user-times fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>@totalUsersCount</h4>
                                                <p class="mb-0">إجمالي المستخدمين</p>
                                            </div>
                                            <i class="fas fa-users fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Users Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الدور</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var user in filteredUsers)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-@GetRoleColor(user.Role) rounded-circle d-flex align-items-center justify-content-center me-3">
                                                        <i class="fas @GetRoleIcon(user.Role) text-white"></i>
                                                    </div>
                                                    <div>
                                                        <strong>@user.FirstName @user.LastName</strong>
                                                        @if (!string.IsNullOrEmpty(user.PhoneNumber))
                                                        {
                                                            <br><small class="text-muted">@user.PhoneNumber</small>
                                                        }
                                                    </div>
                                                </div>
                                            </td>
                                            <td>@user.Email</td>
                                            <td>
                                                <span class="badge bg-@GetRoleColor(user.Role)">
                                                    @GetRoleDisplayName(user.Role)
                                                </span>
                                            </td>
                                            <td>
                                                @if (user.IsApproved && user.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else if (!user.IsApproved)
                                                {
                                                    <span class="badge bg-warning">في انتظار الموافقة</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غير نشط</span>
                                                }
                                            </td>
                                            <td>@user.CreatedAt.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    @if (!user.IsApproved)
                                                    {
                                                        <button class="btn btn-sm btn-success" @onclick="() => ApproveUser(user.Id)" title="موافقة">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    }
                                                    @if (user.IsActive)
                                                    {
                                                        <button class="btn btn-sm btn-warning" @onclick="() => DeactivateUser(user.Id)" title="إلغاء تفعيل">
                                                            <i class="fas fa-pause"></i>
                                                        </button>
                                                    }
                                                    else
                                                    {
                                                        <button class="btn btn-sm btn-info" @onclick="() => ActivateUser(user.Id)" title="تفعيل">
                                                            <i class="fas fa-play"></i>
                                                        </button>
                                                    }
                                                    <button class="btn btn-sm btn-primary" @onclick="() => ViewUserDetails(user.Id)" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-danger" @onclick="() => DeleteUser(user.Id)" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد مستخدمين</h5>
                            <p class="text-muted">لم يتم العثور على مستخدمين بالفلتر المحدد</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .avatar-sm {
        width: 40px;
        height: 40px;
    }
</style>

@code {
    private List<UserDto>? users;
    private List<UserDto> filteredUsers = new();
    private bool isLoading = true;
    private string selectedRole = string.Empty;

    private int activeUsersCount => users?.Count(u => u.IsApproved && u.IsActive) ?? 0;
    private int pendingUsersCount => users?.Count(u => !u.IsApproved) ?? 0;
    private int inactiveUsersCount => users?.Count(u => u.IsApproved && !u.IsActive) ?? 0;
    private int totalUsersCount => users?.Count ?? 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
    }

    private async Task LoadUsers()
    {
        try
        {
            isLoading = true;
            var usersList = await ApiService.GetUsersAsync();
            users = usersList.ToList();
            FilterByRole(selectedRole);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void FilterByRole(string role)
    {
        selectedRole = role;
        if (string.IsNullOrEmpty(role))
        {
            filteredUsers = users?.ToList() ?? new List<UserDto>();
        }
        else
        {
            filteredUsers = users?.Where(u => u.Role == role).ToList() ?? new List<UserDto>();
        }
        StateHasChanged();
    }

    private async Task ApproveUser(string userId)
    {
        try
        {
            await ApiService.ApproveUserAsync(userId);
            await JSRuntime.InvokeVoidAsync("alert", "تم قبول المستخدم بنجاح");
            await LoadUsers();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في قبول المستخدم: {ex.Message}");
        }
    }

    private async Task ActivateUser(string userId)
    {
        try
        {
            await ApiService.ActivateUserAsync(userId);
            await JSRuntime.InvokeVoidAsync("alert", "تم تفعيل المستخدم بنجاح");
            await LoadUsers();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تفعيل المستخدم: {ex.Message}");
        }
    }

    private async Task DeactivateUser(string userId)
    {
        try
        {
            await ApiService.DeactivateUserAsync(userId);
            await JSRuntime.InvokeVoidAsync("alert", "تم إلغاء تفعيل المستخدم بنجاح");
            await LoadUsers();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في إلغاء تفعيل المستخدم: {ex.Message}");
        }
    }

    private async Task DeleteUser(string userId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا المستخدم؟"))
        {
            try
            {
                await ApiService.DeleteUserAsync(userId);
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف المستخدم بنجاح");
                await LoadUsers();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف المستخدم: {ex.Message}");
            }
        }
    }

    private async Task ViewUserDetails(string userId)
    {
        Navigation.NavigateTo($"/admin/users/{userId}");
    }

    private string GetRoleDisplayName(string role)
    {
        return role switch
        {
            "Student" => "طالب",
            "Teacher" => "معلم",
            "Parent" => "ولي أمر",
            "Staff" => "موظف",
            "Accountant" => "محاسب",
            "Admin" => "مدير",
            _ => "جميع الأدوار"
        };
    }

    private string GetRoleColor(string role)
    {
        return role switch
        {
            "Student" => "primary",
            "Teacher" => "success",
            "Parent" => "info",
            "Staff" => "warning",
            "Accountant" => "secondary",
            "Admin" => "danger",
            _ => "dark"
        };
    }

    private string GetRoleIcon(string role)
    {
        return role switch
        {
            "Student" => "fa-user-graduate",
            "Teacher" => "fa-chalkboard-teacher",
            "Parent" => "fa-users",
            "Staff" => "fa-user-tie",
            "Accountant" => "fa-calculator",
            "Admin" => "fa-user-shield",
            _ => "fa-user"
        };
    }
}
