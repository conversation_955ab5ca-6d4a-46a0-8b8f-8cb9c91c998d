@inject IJSRuntime JSRuntime

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4><i class="fas fa-user-graduate me-2"></i>Student Dashboard</h4>
                </div>
                <div class="card-body">
                    <p>Welcome to Student Dashboard</p>
                    <div class="row g-3">
                        <div class="col-md-3">
                            <button class="btn btn-primary w-100" @onclick='() => ShowAlert("Schedule")'>
                                <i class="fas fa-calendar"></i><br>Schedule
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-success w-100" @onclick='() => ShowAlert("Exams")'>
                                <i class="fas fa-file-alt"></i><br>Exams
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-info w-100" @onclick='() => ShowAlert("Assignments")'>
                                <i class="fas fa-tasks"></i><br>Assignments
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-warning w-100" @onclick='() => ShowAlert("Grades")'>
                                <i class="fas fa-star"></i><br>Grades
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private void ShowAlert(string feature)
    {
        JSRuntime.InvokeVoidAsync("alert", $"Feature: {feature}");
    }
}
