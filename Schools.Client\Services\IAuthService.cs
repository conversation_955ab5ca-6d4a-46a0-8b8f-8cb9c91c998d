using Schools.Shared.DTOs;

namespace Schools.Client.Services
{
    public interface IAuthService
    {
        Task<AuthResponseDto> LoginAsync(LoginDto loginDto);
        Task<AuthResponseDto> RegisterAsync(RegisterDto registerDto);
        Task LogoutAsync();
        Task<bool> IsAuthenticatedAsync();
        Task<UserDto?> GetCurrentUserAsync();
        Task<string?> GetTokenAsync();
        Task<bool> ChangePasswordAsync(ChangePasswordDto changePasswordDto);
        Task<bool> UpdateProfileAsync(UpdateProfileDto updateProfileDto);
    }
}
