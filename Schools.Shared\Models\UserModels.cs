using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity;

namespace Schools.Shared.Models
{
    // Application User extending Identity User
    public class ApplicationUser : IdentityUser
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [StringLength(20)]
        public string? NationalId { get; set; }

        public DateTime? DateOfBirth { get; set; }

        public string? Address { get; set; }

        public string? ProfilePicture { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual ICollection<StudentEnrollment> StudentEnrollments { get; set; } = new List<StudentEnrollment>();
        public virtual ICollection<TeacherAssignment> TeacherAssignments { get; set; } = new List<TeacherAssignment>();
        public virtual ICollection<ParentStudent> ParentStudents { get; set; } = new List<ParentStudent>();
        public virtual ICollection<ParentStudent> StudentParents { get; set; } = new List<ParentStudent>();
        public virtual ICollection<Request> Requests { get; set; } = new List<Request>();
        public virtual ICollection<Attendance> Attendances { get; set; } = new List<Attendance>();
    }



    // Parent-Student Relationship
    public class ParentStudent : BaseEntity
    {
        [Required]
        public string ParentId { get; set; } = string.Empty;

        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Relationship { get; set; } = string.Empty; // Father, Mother, Guardian

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ApplicationUser Parent { get; set; } = null!;
        public virtual ApplicationUser Student { get; set; } = null!;
    }


}
