using Schools.Shared.DTOs;
using Schools.Shared.Models;

namespace Schools.API.Services;

public interface IExamService
{
    // Exam management
    Task<PagedResult<ExamDto>> GetExamsAsync(
        string? search = null,
        int? subjectId = null,
        int? classId = null,
        string? examType = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int page = 1,
        int pageSize = 20);

    Task<ExamDto?> GetExamByIdAsync(int id);
    Task<ExamDto> CreateExamAsync(CreateExamDto createDto);
    Task<ExamDto?> UpdateExamAsync(int id, UpdateExamDto updateDto);
    Task<bool> DeleteExamAsync(int id);
    Task<bool> PublishExamAsync(int id);
    Task<bool> UnpublishExamAsync(int id);
    
    // Question management
    Task<List<ExamQuestionDto>> GetExamQuestionsAsync(int examId);
    Task<ExamQuestionDto> AddQuestionToExamAsync(int examId, CreateExamQuestionDto questionDto);
    Task<ExamQuestionDto?> UpdateExamQuestionAsync(int questionId, UpdateExamQuestionDto updateDto);
    Task<bool> RemoveQuestionFromExamAsync(int questionId);
    Task<bool> ReorderExamQuestionsAsync(int examId, List<int> questionIds);
    
    // Student exam attempts
    Task<List<ExamAttemptDto>> GetExamAttemptsAsync(int examId);
    Task<ExamAttemptDto?> GetExamAttemptAsync(int attemptId);
    Task<ExamAttemptDto> StartExamAttemptAsync(int examId, string studentId);
    Task<ExamAttemptDto?> SubmitExamAttemptAsync(int attemptId, List<ExamAnswerDto> answers);
    Task<ExamAttemptDto?> SaveExamProgressAsync(int attemptId, List<ExamAnswerDto> answers);
    
    // Grading and results
    Task<ExamResultDto?> GetExamResultAsync(int attemptId);
    Task<List<ExamResultDto>> GetExamResultsAsync(int examId);
    Task<ExamResultDto?> GradeExamAttemptAsync(int attemptId, List<QuestionGradeDto> grades);
    Task<bool> PublishExamResultsAsync(int examId);
    Task<bool> UnpublishExamResultsAsync(int examId);
    
    // Analytics and reports
    Task<ExamAnalyticsDto> GetExamAnalyticsAsync(int examId);
    Task<ExamStatisticsDto> GetExamStatisticsAsync(int examId);
    Task<List<ExamPerformanceDto>> GetStudentExamPerformanceAsync(string studentId, DateTime? startDate = null, DateTime? endDate = null);
    Task<List<ExamPerformanceDto>> GetClassExamPerformanceAsync(int classId, DateTime? startDate = null, DateTime? endDate = null);
    
    // Question bank
    Task<PagedResult<QuestionBankDto>> GetQuestionBankAsync(
        string? search = null,
        int? subjectId = null,
        string? questionType = null,
        string? difficulty = null,
        int page = 1,
        int pageSize = 20);
    
    Task<QuestionBankDto?> GetQuestionBankItemAsync(int id);
    Task<QuestionBankDto> CreateQuestionBankItemAsync(CreateQuestionBankDto createDto);
    Task<QuestionBankDto?> UpdateQuestionBankItemAsync(int id, UpdateQuestionBankDto updateDto);
    Task<bool> DeleteQuestionBankItemAsync(int id);
    
    // Import/Export
    Task<string> ExportExamAsync(int examId, string format = "json");
    Task<ExamDto> ImportExamAsync(string examData, string format = "json");
    Task<string> ExportExamResultsAsync(int examId, string format = "csv");
}
