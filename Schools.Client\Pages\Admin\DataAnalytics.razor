@page "/admin/data-analytics"
@using Schools.Shared.DTOs
@using Schools.Client.Services
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-dark text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-1">
                                <i class="fas fa-chart-bar me-2"></i>
                                تحليل البيانات المتقدم
                            </h2>
                            <p class="mb-0">رؤى عميقة وتحليلات ذكية لأداء المؤسسة التعليمية</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-light me-2" @onclick="RefreshData">
                                <i class="fas fa-sync-alt me-1"></i>
                                تحديث البيانات
                            </button>
                            <button class="btn btn-outline-light" @onclick="ExportAnalytics">
                                <i class="fas fa-download me-1"></i>
                                تصدير التحليلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="visually-hidden">جاري تحليل البيانات...</span>
            </div>
            <p class="mt-3">جاري معالجة وتحليل البيانات...</p>
        </div>
    }
    else
    {
        <!-- Key Metrics Overview -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="metric-icon bg-primary mb-3">
                            <i class="fas fa-users fa-2x text-white"></i>
                        </div>
                        <h3 class="text-primary">@totalStudents</h3>
                        <p class="text-muted mb-1">إجمالي الطلاب</p>
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar bg-primary" style="width: @studentGrowthRate%"></div>
                        </div>
                        <small class="text-success">
                            <i class="fas fa-arrow-up"></i> +@studentGrowthRate.ToString("F1")% نمو
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="metric-icon bg-success mb-3">
                            <i class="fas fa-graduation-cap fa-2x text-white"></i>
                        </div>
                        <h3 class="text-success">@averageGPA.ToString("F2")</h3>
                        <p class="text-muted mb-1">المعدل العام</p>
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar bg-success" style="width: @(averageGPA * 25)%"></div>
                        </div>
                        <small class="text-success">
                            <i class="fas fa-arrow-up"></i> +@gpaImprovement.ToString("F1")% تحسن
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="metric-icon bg-info mb-3">
                            <i class="fas fa-calendar-check fa-2x text-white"></i>
                        </div>
                        <h3 class="text-info">@attendanceRate.ToString("F1")%</h3>
                        <p class="text-muted mb-1">معدل الحضور</p>
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar bg-info" style="width: @attendanceRate%"></div>
                        </div>
                        <small class="text-success">
                            <i class="fas fa-check"></i> ممتاز
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="metric-icon bg-warning mb-3">
                            <i class="fas fa-star fa-2x text-white"></i>
                        </div>
                        <h3 class="text-warning">@satisfactionScore.ToString("F1")%</h3>
                        <p class="text-muted mb-1">رضا الطلاب</p>
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar bg-warning" style="width: @satisfactionScore%"></div>
                        </div>
                        <small class="text-success">
                            <i class="fas fa-thumbs-up"></i> جيد جداً
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Charts Row -->
        <div class="row mb-4">
            <!-- Performance Trends -->
            <div class="col-md-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                اتجاهات الأداء الأكاديمي
                            </h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary active" @onclick="@(() => SetTimeRange("6months"))">6 أشهر</button>
                                <button class="btn btn-outline-primary" @onclick="@(() => SetTimeRange("1year"))">سنة</button>
                                <button class="btn btn-outline-primary" @onclick="@(() => SetTimeRange("2years"))">سنتان</button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="performanceTrendsChart" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- Subject Performance Radar -->
            <div class="col-md-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-radar me-2"></i>
                            أداء المواد
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="subjectRadarChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Analytics -->
        <div class="row mb-4">
            <!-- Grade Distribution -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>
                            توزيع الدرجات
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="gradeDistributionChart" height="300"></canvas>
                        <div class="mt-3">
                            <div class="row text-center">
                                @foreach (var grade in gradeDistribution)
                                {
                                    <div class="col-3 mb-2">
                                        <div class="border rounded p-2">
                                            <h6 class="@GetGradeColor(grade.Key)">@grade.Value</h6>
                                            <small class="text-muted">@grade.Key</small>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Attendance Heatmap -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>
                            خريطة الحضور الحرارية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="attendance-heatmap">
                            @for (int week = 0; week < 4; week++)
                            {
                                <div class="week-row d-flex mb-1">
                                    @for (int day = 0; day < 7; day++)
                                    {
                                        var attendance = GetAttendanceForDay(week, day);
                                        <div class="day-cell @GetAttendanceClass(attendance)"
                                             title="@GetDayTitle(week, day, attendance)">
                                            <small>@attendance%</small>
                                        </div>
                                    }
                                </div>
                            }
                        </div>
                        <div class="mt-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">منخفض</small>
                                <div class="heatmap-legend d-flex">
                                    <div class="legend-item bg-light"></div>
                                    <div class="legend-item bg-info"></div>
                                    <div class="legend-item bg-primary"></div>
                                    <div class="legend-item bg-success"></div>
                                    <div class="legend-item bg-warning"></div>
                                </div>
                                <small class="text-muted">مرتفع</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Predictive Analytics -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0">
                            <i class="fas fa-crystal-ball me-2"></i>
                            التحليل التنبؤي والتوصيات الذكية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Predictions -->
                            <div class="col-md-8">
                                <h6 class="text-primary mb-3">التوقعات للفصل القادم:</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="prediction-card bg-light p-3 rounded mb-3">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-trending-up fa-2x text-success me-3"></i>
                                                <div>
                                                    <h6 class="mb-1">نمو الطلاب</h6>
                                                    <p class="mb-0 text-success">+@predictedGrowth.ToString("F1")%</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="prediction-card bg-light p-3 rounded mb-3">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-chart-line fa-2x text-info me-3"></i>
                                                <div>
                                                    <h6 class="mb-1">تحسن الأداء</h6>
                                                    <p class="mb-0 text-info">+@predictedPerformance.ToString("F1")%</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="prediction-card bg-light p-3 rounded mb-3">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-user-check fa-2x text-warning me-3"></i>
                                                <div>
                                                    <h6 class="mb-1">معدل الحضور</h6>
                                                    <p class="mb-0 text-warning">@predictedAttendance.ToString("F1")%</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- AI Recommendations -->
                            <div class="col-md-4">
                                <h6 class="text-warning mb-3">توصيات ذكية:</h6>
                                <div class="recommendations">
                                    @foreach (var recommendation in aiRecommendations.Take(4))
                                    {
                                        <div class="recommendation-item d-flex align-items-start mb-3">
                                            <i class="@recommendation.Icon <EMAIL> me-2 mt-1"></i>
                                            <div>
                                                <small class="fw-bold">@recommendation.Title</small>
                                                <p class="mb-0 small text-muted">@recommendation.Description</p>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Risk Analysis -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تحليل المخاطر والتنبيهات المبكرة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach (var risk in riskAnalysis)
                            {
                                <div class="col-md-4 mb-3">
                                    <div class="risk-card border-start <EMAIL> border-4 p-3 bg-light">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="<EMAIL>">@risk.Title</h6>
                                                <p class="mb-2 small">@risk.Description</p>
                                                <div class="progress" style="height: 6px;">
                                                    <div class="progress-bar <EMAIL>" style="width: @risk.RiskLevel%"></div>
                                                </div>
                                                <small class="text-muted">مستوى المخاطرة: @risk.RiskLevel%</small>
                                            </div>
                                            <i class="@risk.Icon fa-2x <EMAIL>"></i>
                                        </div>
                                        <div class="mt-2">
                                            <button class="btn <EMAIL> btn-sm" @onclick="@(() => ViewRiskDetails(risk))">
                                                عرض التفاصيل
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .bg-gradient-dark {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }

    .metric-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
    }

    .attendance-heatmap {
        max-width: 300px;
        margin: 0 auto;
    }

    .week-row {
        gap: 2px;
    }

    .day-cell {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;
    }

    .day-cell:hover {
        transform: scale(1.1);
    }

    .attendance-low { background-color: #f8f9fa; }
    .attendance-medium { background-color: #17a2b8; color: white; }
    .attendance-high { background-color: #007bff; color: white; }
    .attendance-excellent { background-color: #28a745; color: white; }

    .heatmap-legend {
        gap: 2px;
    }

    .legend-item {
        width: 20px;
        height: 10px;
        border-radius: 2px;
    }

    .prediction-card {
        transition: transform 0.2s;
    }

    .prediction-card:hover {
        transform: translateY(-2px);
    }

    .risk-card {
        transition: all 0.2s;
    }

    .risk-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .recommendation-item {
        padding: 8px;
        border-radius: 6px;
        background-color: #f8f9fa;
        transition: background-color 0.2s;
    }

    .recommendation-item:hover {
        background-color: #e9ecef;
    }
</style>

@code {
    private bool isLoading = true;
    private string selectedTimeRange = "6months";

    // Key Metrics
    private int totalStudents = 0;
    private double averageGPA = 0;
    private double attendanceRate = 0;
    private double satisfactionScore = 0;
    private double studentGrowthRate = 0;
    private double gpaImprovement = 0;

    // Predictions
    private double predictedGrowth = 8.5;
    private double predictedPerformance = 12.3;
    private double predictedAttendance = 94.2;

    // Data Collections
    private Dictionary<string, int> gradeDistribution = new();
    private List<AIRecommendation> aiRecommendations = new();
    private List<RiskAnalysis> riskAnalysis = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadAnalyticsData();
        await InitializeCharts();
    }

    private async Task LoadAnalyticsData()
    {
        try
        {
            isLoading = true;

            // Load basic data
            var users = await ApiService.GetUsersAsync();
            totalStudents = users.Count(u => u.Role == "Student");

            // Mock advanced analytics data
            averageGPA = 3.42;
            attendanceRate = 92.8;
            satisfactionScore = 87.5;
            studentGrowthRate = 15.2;
            gpaImprovement = 8.7;

            // Grade distribution
            gradeDistribution = new Dictionary<string, int>
            {
                { "A+", 45 }, { "A", 78 }, { "B+", 92 }, { "B", 67 },
                { "C+", 34 }, { "C", 23 }, { "D", 12 }, { "F", 5 }
            };

            GenerateAIRecommendations();
            GenerateRiskAnalysis();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void GenerateAIRecommendations()
    {
        aiRecommendations = new List<AIRecommendation>
        {
            new() { Icon = "fas fa-lightbulb", Color = "warning", Title = "تحسين الرياضيات", Description = "يُنصح بإضافة حصص تقوية في الرياضيات للصف الثالث" },
            new() { Icon = "fas fa-users", Color = "info", Title = "تفاعل الطلاب", Description = "زيادة الأنشطة التفاعلية لتحسين مشاركة الطلاب" },
            new() { Icon = "fas fa-clock", Color = "success", Title = "إدارة الوقت", Description = "تحسين جدولة الحصص لزيادة معدل الحضور" },
            new() { Icon = "fas fa-star", Color = "primary", Title = "برامج التميز", Description = "إنشاء برامج خاصة للطلاب المتفوقين" }
        };
    }

    private void GenerateRiskAnalysis()
    {
        riskAnalysis = new List<RiskAnalysis>
        {
            new() { Title = "انخفاض الحضور", Description = "ملاحظة انخفاض طفيف في معدل الحضور", Severity = "warning", Icon = "fas fa-calendar-times", RiskLevel = 25 },
            new() { Title = "ضعف في العلوم", Description = "أداء أقل من المتوقع في مادة العلوم", Severity = "danger", Icon = "fas fa-flask", RiskLevel = 40 },
            new() { Title = "نقص الموارد", Description = "الحاجة لتحديث المعدات التقنية", Severity = "info", Icon = "fas fa-tools", RiskLevel = 60 }
        };
    }

    private async Task InitializeCharts()
    {
        try
        {
            // Performance Trends Chart
            var performanceData = new
            {
                labels = new[] { "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو" },
                datasets = new[]
                {
                    new
                    {
                        label = "المعدل العام",
                        data = new[] { 3.2, 3.25, 3.3, 3.35, 3.4, 3.42 },
                        borderColor = "#007bff",
                        backgroundColor = "rgba(0, 123, 255, 0.1)",
                        tension = 0.4,
                        fill = true
                    },
                    new
                    {
                        label = "معدل الحضور",
                        data = new[] { 89, 90, 91, 92, 93, 92.8 },
                        borderColor = "#28a745",
                        backgroundColor = "rgba(40, 167, 69, 0.1)",
                        tension = 0.4,
                        fill = true
                    }
                }
            };

            await JSRuntime.InvokeVoidAsync("createChart", "performanceTrendsChart", "line", performanceData, new
            {
                responsive = true,
                plugins = new { legend = new { display = true } },
                scales = new { y = new { beginAtZero = true } }
            });

            // Subject Radar Chart
            var subjectData = new
            {
                labels = new[] { "الرياضيات", "العلوم", "اللغة العربية", "الإنجليزية", "التاريخ", "الجغرافيا" },
                datasets = new[]
                {
                    new
                    {
                        label = "الأداء الحالي",
                        data = new[] { 85, 78, 92, 88, 90, 86 },
                        backgroundColor = "rgba(54, 162, 235, 0.2)",
                        borderColor = "rgba(54, 162, 235, 1)",
                        borderWidth = 2
                    }
                }
            };

            await JSRuntime.InvokeVoidAsync("createChart", "subjectRadarChart", "radar", subjectData, new
            {
                responsive = true,
                plugins = new { legend = new { display = false } },
                scales = new { r = new { beginAtZero = true, max = 100 } }
            });

            // Grade Distribution Chart
            var gradeData = new
            {
                labels = gradeDistribution.Keys.ToArray(),
                datasets = new[]
                {
                    new
                    {
                        data = gradeDistribution.Values.ToArray(),
                        backgroundColor = new[] { "#28a745", "#20c997", "#17a2b8", "#007bff", "#6f42c1", "#e83e8c", "#fd7e14", "#dc3545" }
                    }
                }
            };

            await JSRuntime.InvokeVoidAsync("createChart", "gradeDistributionChart", "doughnut", gradeData, new
            {
                responsive = true,
                plugins = new { legend = new { position = "bottom" } }
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error initializing charts: {ex.Message}");
        }
    }

    private async Task RefreshData()
    {
        await JSRuntime.InvokeVoidAsync("showNotification", "جاري تحديث البيانات...", "info");
        await LoadAnalyticsData();
        await InitializeCharts();
        await JSRuntime.InvokeVoidAsync("showNotification", "تم تحديث البيانات بنجاح", "success");
    }

    private async Task ExportAnalytics()
    {
        await JSRuntime.InvokeVoidAsync("showNotification", "جاري تصدير التحليلات...", "info");
        await Task.Delay(2000);
        await JSRuntime.InvokeVoidAsync("showNotification", "تم تصدير التحليلات بنجاح", "success");
    }

    private async Task SetTimeRange(string range)
    {
        selectedTimeRange = range;
        await InitializeCharts();
        StateHasChanged();
    }

    private int GetAttendanceForDay(int week, int day)
    {
        // Mock attendance data for heatmap
        var random = new Random(week * 7 + day);
        return random.Next(75, 100);
    }

    private string GetAttendanceClass(int attendance)
    {
        if (attendance < 80) return "attendance-low";
        if (attendance < 90) return "attendance-medium";
        if (attendance < 95) return "attendance-high";
        return "attendance-excellent";
    }

    private string GetDayTitle(int week, int day, int attendance)
    {
        var date = DateTime.Now.AddDays(-(28 - (week * 7 + day)));
        return $"{date:yyyy-MM-dd}: {attendance}% حضور";
    }

    private string GetGradeColor(string grade)
    {
        return grade switch
        {
            "A+" or "A" => "text-success",
            "B+" or "B" => "text-info",
            "C+" or "C" => "text-warning",
            _ => "text-danger"
        };
    }

    private async Task ViewRiskDetails(RiskAnalysis risk)
    {
        await JSRuntime.InvokeVoidAsync("alert", $"تفاصيل المخاطرة: {risk.Title}\n{risk.Description}");
    }

    public class AIRecommendation
    {
        public string Icon { get; set; } = "";
        public string Color { get; set; } = "";
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
    }

    public class RiskAnalysis
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Severity { get; set; } = "";
        public string Icon { get; set; } = "";
        public int RiskLevel { get; set; }
    }
}
