namespace Schools.Client.Models
{
    public class EventModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Category { get; set; } = "";
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Location { get; set; } = "";
        public int MaxParticipants { get; set; }
        public int CurrentParticipants { get; set; }
        public string Status { get; set; } = "";
        public string CreatedBy { get; set; } = "";
        public DateTime CreatedDate { get; set; }
        public bool IsPublic { get; set; }
        public bool RequiresApproval { get; set; }
        public List<EventParticipantModel> Participants { get; set; } = new();
    }

    public class CreateEventModel
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Category { get; set; } = "";
        public DateTime StartDate { get; set; } = DateTime.Now.AddDays(1);
        public DateTime EndDate { get; set; } = DateTime.Now.AddDays(1).AddHours(2);
        public string Location { get; set; } = "";
        public int MaxParticipants { get; set; } = 50;
        public bool IsPublic { get; set; } = true;
        public bool RequiresApproval { get; set; } = false;
    }

    public class EventParticipantModel
    {
        public int Id { get; set; }
        public int EventId { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; } = "";
        public DateTime RegistrationDate { get; set; }
        public string Status { get; set; } = "";
    }

    public class EventFilterModel
    {
        public string? Category { get; set; }
        public string? Status { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Search { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class EventCalendarModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public DateTime Start { get; set; }
        public DateTime End { get; set; }
        public string Category { get; set; } = "";
        public string Status { get; set; } = "";
        public string Location { get; set; } = "";
        public string Color { get; set; } = "";
    }

    public class EventStatisticsModel
    {
        public int TotalEvents { get; set; }
        public int ActiveEvents { get; set; }
        public int ScheduledEvents { get; set; }
        public int CompletedEvents { get; set; }
        public int TotalParticipants { get; set; }
        public Dictionary<string, int> CategoryDistribution { get; set; } = new();
        public Dictionary<string, int> MonthlyEventCounts { get; set; } = new();
    }

    public static class EventCategories
    {
        public static readonly Dictionary<string, string> Categories = new()
        {
            { "academic", "أكاديمي" },
            { "sports", "رياضي" },
            { "cultural", "ثقافي" },
            { "social", "اجتماعي" },
            { "administrative", "إداري" },
            { "meeting", "اجتماع" },
            { "workshop", "ورشة عمل" },
            { "conference", "مؤتمر" }
        };

        public static readonly Dictionary<string, string> Icons = new()
        {
            { "academic", "fas fa-graduation-cap" },
            { "sports", "fas fa-futbol" },
            { "cultural", "fas fa-theater-masks" },
            { "social", "fas fa-users" },
            { "administrative", "fas fa-cog" },
            { "meeting", "fas fa-handshake" },
            { "workshop", "fas fa-tools" },
            { "conference", "fas fa-microphone" }
        };

        public static readonly Dictionary<string, string> Colors = new()
        {
            { "academic", "#007bff" },
            { "sports", "#28a745" },
            { "cultural", "#ffc107" },
            { "social", "#17a2b8" },
            { "administrative", "#6c757d" },
            { "meeting", "#fd7e14" },
            { "workshop", "#e83e8c" },
            { "conference", "#6f42c1" }
        };
    }

    public static class EventStatuses
    {
        public static readonly Dictionary<string, string> Statuses = new()
        {
            { "scheduled", "مجدول" },
            { "active", "نشط" },
            { "completed", "مكتمل" },
            { "cancelled", "ملغي" },
            { "postponed", "مؤجل" }
        };

        public static readonly Dictionary<string, string> BadgeClasses = new()
        {
            { "scheduled", "bg-warning" },
            { "active", "bg-success" },
            { "completed", "bg-info" },
            { "cancelled", "bg-danger" },
            { "postponed", "bg-secondary" }
        };
    }

    public static class ParticipantStatuses
    {
        public static readonly Dictionary<string, string> Statuses = new()
        {
            { "confirmed", "مؤكد" },
            { "pending", "في الانتظار" },
            { "cancelled", "ملغي" },
            { "attended", "حضر" },
            { "absent", "غائب" }
        };

        public static readonly Dictionary<string, string> BadgeClasses = new()
        {
            { "confirmed", "bg-success" },
            { "pending", "bg-warning" },
            { "cancelled", "bg-danger" },
            { "attended", "bg-primary" },
            { "absent", "bg-secondary" }
        };
    }
}
