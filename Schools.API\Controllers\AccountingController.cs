using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;
using System.Security.Claims;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AccountingController : ControllerBase
    {
        private readonly SchoolsDbContext _context;

        public AccountingController(SchoolsDbContext context)
        {
            _context = context;
        }

        #region Accounts Management

        [HttpGet("accounts")]
        public async Task<ActionResult<IEnumerable<AccountDto>>> GetAccounts()
        {
            try
            {
                var accounts = await _context.Accounts
                    .Include(a => a.SubAccounts)
                    .Where(a => !a.IsDeleted)
                    .Select(a => new AccountDto
                    {
                        Id = a.Id,
                        AccountCode = a.AccountCode,
                        AccountName = a.AccountName,
                        AccountNameEn = a.AccountNameEn,
                        AccountType = a.AccountType,
                        ParentAccountId = a.ParentAccountId,
                        Level = a.Level,
                        Balance = a.Balance,
                        DebitBalance = a.DebitBalance,
                        CreditBalance = a.CreditBalance,
                        IsActive = a.IsActive,
                        CreatedDate = a.CreatedAt,
                        CreatedBy = a.CreatedBy,
                        SubAccounts = a.SubAccounts.Select(sa => new AccountDto
                        {
                            Id = sa.Id,
                            AccountCode = sa.AccountCode,
                            AccountName = sa.AccountName,
                            AccountType = sa.AccountType,
                            Balance = sa.Balance,
                            IsActive = sa.IsActive
                        }).ToList()
                    })
                    .ToListAsync();

                return Ok(accounts);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب الحسابات", error = ex.Message });
            }
        }

        [HttpPost("accounts")]
        [Authorize(Roles = "Admin,Accountant")]
        public async Task<ActionResult<AccountDto>> CreateAccount(CreateAccountDto model)
        {
            try
            {
                var account = new Account
                {
                    AccountCode = model.AccountCode,
                    AccountName = model.AccountName,
                    AccountNameEn = model.AccountNameEn,
                    AccountType = model.AccountType,
                    ParentAccountId = model.ParentAccountId,
                    Level = model.Level,
                    IsActive = model.IsActive,
                    CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System",
                    CreatedAt = DateTime.UtcNow
                };

                _context.Accounts.Add(account);
                await _context.SaveChangesAsync();

                var accountDto = new AccountDto
                {
                    Id = account.Id,
                    AccountCode = account.AccountCode,
                    AccountName = account.AccountName,
                    AccountNameEn = account.AccountNameEn,
                    AccountType = account.AccountType,
                    ParentAccountId = account.ParentAccountId,
                    Level = account.Level,
                    Balance = account.Balance,
                    IsActive = account.IsActive,
                    CreatedDate = account.CreatedAt,
                    CreatedBy = account.CreatedBy
                };

                return CreatedAtAction(nameof(GetAccount), new { id = account.Id }, accountDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في إنشاء الحساب", error = ex.Message });
            }
        }

        [HttpGet("accounts/{id}")]
        public async Task<ActionResult<AccountDto>> GetAccount(int id)
        {
            try
            {
                var account = await _context.Accounts
                    .Include(a => a.SubAccounts)
                    .FirstOrDefaultAsync(a => a.Id == id && !a.IsDeleted);

                if (account == null)
                {
                    return NotFound(new { message = "الحساب غير موجود" });
                }

                var accountDto = new AccountDto
                {
                    Id = account.Id,
                    AccountCode = account.AccountCode,
                    AccountName = account.AccountName,
                    AccountNameEn = account.AccountNameEn,
                    AccountType = account.AccountType,
                    ParentAccountId = account.ParentAccountId,
                    Level = account.Level,
                    Balance = account.Balance,
                    DebitBalance = account.DebitBalance,
                    CreditBalance = account.CreditBalance,
                    IsActive = account.IsActive,
                    CreatedDate = account.CreatedAt,
                    CreatedBy = account.CreatedBy,
                    SubAccounts = account.SubAccounts.Select(sa => new AccountDto
                    {
                        Id = sa.Id,
                        AccountCode = sa.AccountCode,
                        AccountName = sa.AccountName,
                        AccountType = sa.AccountType,
                        Balance = sa.Balance,
                        IsActive = sa.IsActive
                    }).ToList()
                };

                return Ok(accountDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب الحساب", error = ex.Message });
            }
        }

        [HttpPut("accounts/{id}")]
        [Authorize(Roles = "Admin,Accountant")]
        public async Task<IActionResult> UpdateAccount(int id, UpdateAccountDto model)
        {
            try
            {
                var account = await _context.Accounts.FindAsync(id);
                if (account == null || account.IsDeleted)
                {
                    return NotFound(new { message = "الحساب غير موجود" });
                }

                if (!string.IsNullOrEmpty(model.AccountName))
                    account.AccountName = model.AccountName;
                if (!string.IsNullOrEmpty(model.AccountNameEn))
                    account.AccountNameEn = model.AccountNameEn;
                if (model.IsActive.HasValue)
                    account.IsActive = model.IsActive.Value;

                account.UpdatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System";
                account.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تحديث الحساب بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحديث الحساب", error = ex.Message });
            }
        }

        [HttpDelete("accounts/{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteAccount(int id)
        {
            try
            {
                var account = await _context.Accounts.FindAsync(id);
                if (account == null || account.IsDeleted)
                {
                    return NotFound(new { message = "الحساب غير موجود" });
                }

                account.IsDeleted = true;
                account.DeletedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System";
                account.DeletedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم حذف الحساب بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في حذف الحساب", error = ex.Message });
            }
        }

        #endregion

        #region Receipt Vouchers

        [HttpGet("receipt-vouchers")]
        public async Task<ActionResult<IEnumerable<ReceiptVoucherDto>>> GetReceiptVouchers()
        {
            try
            {
                var vouchers = await _context.ReceiptVouchers
                    .Include(v => v.Details)
                    .ThenInclude(d => d.Account)
                    .Where(v => !v.IsDeleted)
                    .Select(v => new ReceiptVoucherDto
                    {
                        Id = v.Id,
                        VoucherNumber = v.VoucherNumber,
                        VoucherDate = v.VoucherDate,
                        ReceivedFrom = v.ReceivedFrom,
                        TotalAmount = v.TotalAmount,
                        AmountInWords = v.AmountInWords,
                        Description = v.Description,
                        PaymentMethod = v.PaymentMethod,
                        CheckNumber = v.CheckNumber,
                        BankName = v.BankName,
                        AccountNumber = v.AccountNumber,
                        CheckDate = v.CheckDate,
                        Status = v.Status,
                        CreatedBy = v.CreatedBy,
                        CreatedDate = v.CreatedAt,
                        ApprovedBy = v.ApprovedBy,
                        ApprovedDate = v.ApprovedDate,
                        Reference = v.Reference,
                        Notes = v.Notes,
                        Details = v.Details.Select(d => new ReceiptVoucherDetailDto
                        {
                            Id = d.Id,
                            ReceiptVoucherId = d.ReceiptVoucherId,
                            AccountId = d.AccountId,
                            AccountName = d.Account.AccountName,
                            Description = d.Description,
                            Amount = d.Amount
                        }).ToList()
                    })
                    .OrderByDescending(v => v.CreatedDate)
                    .ToListAsync();

                return Ok(vouchers);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب سندات القبض", error = ex.Message });
            }
        }

        [HttpPost("receipt-vouchers")]
        [Authorize(Roles = "Admin,Accountant")]
        public async Task<ActionResult<ReceiptVoucherDto>> CreateReceiptVoucher(CreateReceiptVoucherDto model)
        {
            try
            {
                var voucher = new ReceiptVoucher
                {
                    VoucherNumber = await GenerateVoucherNumber("RV"),
                    VoucherDate = model.VoucherDate,
                    ReceivedFrom = model.ReceivedFrom,
                    TotalAmount = model.TotalAmount,
                    AmountInWords = model.AmountInWords,
                    Description = model.Description,
                    PaymentMethod = model.PaymentMethod,
                    CheckNumber = model.CheckNumber,
                    BankName = model.BankName,
                    AccountNumber = model.AccountNumber,
                    CheckDate = model.CheckDate,
                    Status = VoucherStatus.Draft,
                    CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System",
                    CreatedAt = DateTime.UtcNow,
                    Reference = model.Reference,
                    Notes = model.Notes
                };

                _context.ReceiptVouchers.Add(voucher);
                await _context.SaveChangesAsync();

                // Add details
                foreach (var detail in model.Details)
                {
                    var voucherDetail = new ReceiptVoucherDetail
                    {
                        ReceiptVoucherId = voucher.Id,
                        AccountId = detail.AccountId,
                        Description = detail.Description,
                        Amount = detail.Amount
                    };
                    _context.ReceiptVoucherDetails.Add(voucherDetail);
                }

                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetReceiptVoucher), new { id = voucher.Id }, voucher.Id);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في إنشاء سند القبض", error = ex.Message });
            }
        }

        [HttpGet("receipt-vouchers/{id}")]
        public async Task<ActionResult<ReceiptVoucherDto>> GetReceiptVoucher(int id)
        {
            try
            {
                var voucher = await _context.ReceiptVouchers
                    .Include(v => v.Details)
                    .ThenInclude(d => d.Account)
                    .FirstOrDefaultAsync(v => v.Id == id && !v.IsDeleted);

                if (voucher == null)
                {
                    return NotFound(new { message = "سند القبض غير موجود" });
                }

                var voucherDto = new ReceiptVoucherDto
                {
                    Id = voucher.Id,
                    VoucherNumber = voucher.VoucherNumber,
                    VoucherDate = voucher.VoucherDate,
                    ReceivedFrom = voucher.ReceivedFrom,
                    TotalAmount = voucher.TotalAmount,
                    AmountInWords = voucher.AmountInWords,
                    Description = voucher.Description,
                    PaymentMethod = voucher.PaymentMethod,
                    CheckNumber = voucher.CheckNumber,
                    BankName = voucher.BankName,
                    AccountNumber = voucher.AccountNumber,
                    CheckDate = voucher.CheckDate,
                    Status = voucher.Status,
                    CreatedBy = voucher.CreatedBy,
                    CreatedDate = voucher.CreatedAt,
                    ApprovedBy = voucher.ApprovedBy,
                    ApprovedDate = voucher.ApprovedDate,
                    Reference = voucher.Reference,
                    Notes = voucher.Notes,
                    Details = voucher.Details.Select(d => new ReceiptVoucherDetailDto
                    {
                        Id = d.Id,
                        ReceiptVoucherId = d.ReceiptVoucherId,
                        AccountId = d.AccountId,
                        AccountName = d.Account.AccountName,
                        Description = d.Description,
                        Amount = d.Amount
                    }).ToList()
                };

                return Ok(voucherDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب سند القبض", error = ex.Message });
            }
        }

        #endregion

        #region Payment Vouchers

        [HttpGet("payment-vouchers")]
        public async Task<ActionResult<IEnumerable<PaymentVoucherDto>>> GetPaymentVouchers()
        {
            try
            {
                var vouchers = await _context.PaymentVouchers
                    .Include(v => v.Details)
                    .ThenInclude(d => d.Account)
                    .Where(v => !v.IsDeleted)
                    .Select(v => new PaymentVoucherDto
                    {
                        Id = v.Id,
                        VoucherNumber = v.VoucherNumber,
                        VoucherDate = v.VoucherDate,
                        PaidTo = v.PaidTo,
                        TotalAmount = v.TotalAmount,
                        Description = v.Description,
                        PaymentMethod = v.PaymentMethod,
                        CheckNumber = v.CheckNumber,
                        BankName = v.BankName,
                        CheckDate = v.CheckDate,
                        Status = v.Status,
                        CreatedBy = v.CreatedBy,
                        CreatedDate = v.CreatedAt,
                        ApprovedBy = v.ApprovedBy,
                        ApprovedDate = v.ApprovedDate,
                        Details = v.Details.Select(d => new PaymentVoucherDetailDto
                        {
                            Id = d.Id,
                            PaymentVoucherId = d.PaymentVoucherId,
                            AccountId = d.AccountId,
                            AccountName = d.Account.AccountName,
                            Description = d.Description,
                            Amount = d.Amount
                        }).ToList()
                    })
                    .OrderByDescending(v => v.CreatedDate)
                    .ToListAsync();

                return Ok(vouchers);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب سندات الصرف", error = ex.Message });
            }
        }

        [HttpPost("payment-vouchers")]
        [Authorize(Roles = "Admin,Accountant")]
        public async Task<ActionResult<PaymentVoucherDto>> CreatePaymentVoucher(CreatePaymentVoucherDto model)
        {
            try
            {
                var voucher = new PaymentVoucher
                {
                    VoucherNumber = await GeneratePaymentVoucherNumber("PV"),
                    VoucherDate = model.VoucherDate,
                    PaidTo = model.PaidTo,
                    TotalAmount = model.TotalAmount,
                    Description = model.Description,
                    PaymentMethod = model.PaymentMethod,
                    CheckNumber = model.CheckNumber,
                    BankName = model.BankName,
                    CheckDate = model.CheckDate,
                    Status = VoucherStatus.Draft,
                    CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System",
                    CreatedAt = DateTime.UtcNow
                };

                _context.PaymentVouchers.Add(voucher);
                await _context.SaveChangesAsync();

                // Add details
                foreach (var detail in model.Details)
                {
                    var voucherDetail = new PaymentVoucherDetail
                    {
                        PaymentVoucherId = voucher.Id,
                        AccountId = detail.AccountId,
                        Description = detail.Description,
                        Amount = detail.Amount
                    };
                    _context.PaymentVoucherDetails.Add(voucherDetail);
                }

                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetPaymentVoucher), new { id = voucher.Id }, voucher.Id);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في إنشاء سند الصرف", error = ex.Message });
            }
        }

        [HttpGet("payment-vouchers/{id}")]
        public async Task<ActionResult<PaymentVoucherDto>> GetPaymentVoucher(int id)
        {
            try
            {
                var voucher = await _context.PaymentVouchers
                    .Include(v => v.Details)
                    .ThenInclude(d => d.Account)
                    .FirstOrDefaultAsync(v => v.Id == id && !v.IsDeleted);

                if (voucher == null)
                {
                    return NotFound(new { message = "سند الصرف غير موجود" });
                }

                var voucherDto = new PaymentVoucherDto
                {
                    Id = voucher.Id,
                    VoucherNumber = voucher.VoucherNumber,
                    VoucherDate = voucher.VoucherDate,
                    PaidTo = voucher.PaidTo,
                    TotalAmount = voucher.TotalAmount,
                    Description = voucher.Description,
                    PaymentMethod = voucher.PaymentMethod,
                    CheckNumber = voucher.CheckNumber,
                    BankName = voucher.BankName,
                    CheckDate = voucher.CheckDate,
                    Status = voucher.Status,
                    CreatedBy = voucher.CreatedBy,
                    CreatedDate = voucher.CreatedAt,
                    ApprovedBy = voucher.ApprovedBy,
                    ApprovedDate = voucher.ApprovedDate,
                    Details = voucher.Details.Select(d => new PaymentVoucherDetailDto
                    {
                        Id = d.Id,
                        PaymentVoucherId = d.PaymentVoucherId,
                        AccountId = d.AccountId,
                        AccountName = d.Account.AccountName,
                        Description = d.Description,
                        Amount = d.Amount
                    }).ToList()
                };

                return Ok(voucherDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب سند الصرف", error = ex.Message });
            }
        }

        #endregion

        #region Student Fees

        [HttpGet("student-fees")]
        public async Task<ActionResult<IEnumerable<StudentFeeDto>>> GetStudentFees()
        {
            try
            {
                var studentFees = await _context.StudentFees
                    .Include(sf => sf.Student)
                    .Include(sf => sf.StudentEnrollment)
                    .ThenInclude(se => se.AcademicGrade)
                    .Include(sf => sf.StudentEnrollment)
                    .ThenInclude(se => se.Class)
                    .Include(sf => sf.FeeStructure)
                    .Where(sf => !sf.IsDeleted)
                    .Select(sf => new StudentFeeDto
                    {
                        StudentId = sf.StudentId,
                        StudentName = $"{sf.Student.FirstName} {sf.Student.LastName}",
                        StudentNumber = sf.Student.UserName ?? "",
                        GradeId = sf.StudentEnrollment.AcademicGradeId,
                        GradeName = sf.StudentEnrollment.AcademicGrade.Name,
                        ClassId = sf.StudentEnrollment.ClassId,
                        ClassName = sf.StudentEnrollment.Class.Name,
                        TotalFees = sf.TotalAmount,
                        PaidAmount = sf.PaidAmount,
                        OutstandingAmount = sf.TotalAmount - sf.PaidAmount,
                        PaymentStatus = sf.PaymentStatus.ToString(),
                        LastPaymentDate = sf.LastPaymentDate,
                        LastPaymentAmount = sf.LastPaymentAmount
                    })
                    .ToListAsync();

                return Ok(studentFees);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب رسوم الطلاب", error = ex.Message });
            }
        }

        [HttpPost("student-payment")]
        [Authorize(Roles = "Admin,Accountant")]
        public async Task<IActionResult> ProcessStudentPayment(StudentPaymentDto model)
        {
            try
            {
                var studentFee = await _context.StudentFees
                    .FirstOrDefaultAsync(sf => sf.StudentId == model.StudentId.ToString() && !sf.IsDeleted);

                if (studentFee == null)
                {
                    return NotFound(new { message = "رسوم الطالب غير موجودة" });
                }

                // Update student fee
                studentFee.PaidAmount += model.Amount;
                studentFee.LastPaymentDate = model.PaymentDate;
                studentFee.LastPaymentAmount = model.Amount;
                studentFee.UpdatedAt = DateTime.UtcNow;
                studentFee.UpdatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System";

                // Update payment status
                if (studentFee.PaidAmount >= studentFee.TotalAmount)
                {
                    studentFee.PaymentStatus = PaymentStatus.Paid;
                }
                else if (studentFee.PaidAmount > 0)
                {
                    studentFee.PaymentStatus = PaymentStatus.Partial;
                }

                // Create payment history
                var paymentHistory = new PaymentHistory
                {
                    StudentFeeId = studentFee.Id,
                    PaymentDate = model.PaymentDate,
                    Amount = model.Amount,
                    PaymentMethod = model.PaymentMethod,
                    Reference = model.Reference,
                    Notes = model.Notes,
                    CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System",
                    CreatedAt = DateTime.UtcNow
                };

                _context.PaymentHistories.Add(paymentHistory);
                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تسجيل الدفعة بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تسجيل الدفعة", error = ex.Message });
            }
        }

        #endregion

        #region Dashboard & Reports

        [HttpGet("dashboard")]
        public async Task<ActionResult<AccountingDashboardDto>> GetAccountingDashboard()
        {
            try
            {
                var dashboard = new AccountingDashboardDto();

                // Calculate totals
                var assets = await _context.Accounts
                    .Where(a => a.AccountType == AccountType.Asset && a.IsActive && !a.IsDeleted)
                    .SumAsync(a => a.Balance);

                var liabilities = await _context.Accounts
                    .Where(a => a.AccountType == AccountType.Liability && a.IsActive && !a.IsDeleted)
                    .SumAsync(a => a.Balance);

                var equity = await _context.Accounts
                    .Where(a => a.AccountType == AccountType.Equity && a.IsActive && !a.IsDeleted)
                    .SumAsync(a => a.Balance);

                var monthlyRevenue = await _context.Accounts
                    .Where(a => a.AccountType == AccountType.Revenue && a.IsActive && !a.IsDeleted)
                    .SumAsync(a => a.Balance);

                var monthlyExpenses = await _context.Accounts
                    .Where(a => a.AccountType == AccountType.Expense && a.IsActive && !a.IsDeleted)
                    .SumAsync(a => a.Balance);

                dashboard.TotalAssets = assets;
                dashboard.TotalLiabilities = liabilities;
                dashboard.TotalEquity = equity;
                dashboard.MonthlyRevenue = monthlyRevenue;
                dashboard.MonthlyExpenses = monthlyExpenses;
                dashboard.NetIncome = monthlyRevenue - monthlyExpenses;

                // Student fees statistics
                var totalStudentFees = await _context.StudentFees
                    .Where(sf => !sf.IsDeleted)
                    .SumAsync(sf => sf.TotalAmount);

                var collectedFees = await _context.StudentFees
                    .Where(sf => !sf.IsDeleted)
                    .SumAsync(sf => sf.PaidAmount);

                dashboard.StudentFeesCollected = collectedFees;
                dashboard.StudentFeesOutstanding = totalStudentFees - collectedFees;

                // Pending vouchers
                dashboard.PendingVouchers = await _context.ReceiptVouchers
                    .CountAsync(v => v.Status == VoucherStatus.Draft && !v.IsDeleted) +
                    await _context.PaymentVouchers
                    .CountAsync(v => v.Status == VoucherStatus.Draft && !v.IsDeleted);

                return Ok(dashboard);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب لوحة المحاسبة", error = ex.Message });
            }
        }

        #endregion

        #region Helper Methods

        private async Task<string> GenerateVoucherNumber(string prefix)
        {
            var year = DateTime.Now.Year;
            var lastVoucher = await _context.ReceiptVouchers
                .Where(v => v.VoucherNumber.StartsWith($"{prefix}-{year}"))
                .OrderByDescending(v => v.Id)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastVoucher != null)
            {
                var parts = lastVoucher.VoucherNumber.Split('-');
                if (parts.Length == 3 && int.TryParse(parts[2], out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}-{year}-{nextNumber:D6}";
        }

        private async Task<string> GeneratePaymentVoucherNumber(string prefix)
        {
            var year = DateTime.Now.Year;
            var lastVoucher = await _context.PaymentVouchers
                .Where(v => v.VoucherNumber.StartsWith($"{prefix}-{year}"))
                .OrderByDescending(v => v.Id)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastVoucher != null)
            {
                var parts = lastVoucher.VoucherNumber.Split('-');
                if (parts.Length == 3 && int.TryParse(parts[2], out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}-{year}-{nextNumber:D6}";
        }

        #endregion
    }
}
