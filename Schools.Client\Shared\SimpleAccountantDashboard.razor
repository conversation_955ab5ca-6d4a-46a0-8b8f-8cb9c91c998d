@inject IJSRuntime JSRuntime

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4><i class="fas fa-calculator me-2"></i>Accountant Dashboard</h4>
                </div>
                <div class="card-body">
                    <p>Welcome to Accountant Dashboard</p>
                    <div class="row g-3">
                        <div class="col-md-3">
                            <button class="btn btn-primary w-100" @onclick='() => ShowAlert("Fees")'>
                                <i class="fas fa-money-bill-wave"></i><br>Fees
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-success w-100" @onclick='() => ShowAlert("Payments")'>
                                <i class="fas fa-credit-card"></i><br>Payments
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-info w-100" @onclick='() => ShowAlert("Salaries")'>
                                <i class="fas fa-users"></i><br>Salaries
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-warning w-100" @onclick='() => ShowAlert("Reports")'>
                                <i class="fas fa-chart-bar"></i><br>Reports
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private void ShowAlert(string feature)
    {
        JSRuntime.InvokeVoidAsync("alert", $"Feature: {feature}");
    }
}
