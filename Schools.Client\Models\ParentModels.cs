namespace Schools.Client.Models
{
    public class ParentModel
    {
        public string Id { get; set; } = "";
        public string FirstName { get; set; } = "";
        public string LastName { get; set; } = "";
        public string FullName => $"{FirstName} {LastName}";
        public string Email { get; set; } = "";
        public string? PhoneNumber { get; set; }
        public string? NationalId { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public string? ProfilePicture { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public List<ParentChildModel> Children { get; set; } = new();
        public int TotalChildren { get; set; }
        public string? EmergencyContact { get; set; }
        public string? Occupation { get; set; }
        public string? WorkPlace { get; set; }
        public string? WorkPhone { get; set; }
    }

    public class ParentChildModel
    {
        public string StudentId { get; set; } = "";
        public string StudentName { get; set; } = "";
        public string StudentNumber { get; set; } = "";
        public string ClassName { get; set; } = "";
        public string Relationship { get; set; } = "";
        public bool IsActive { get; set; } = true;
        public double? AverageGrade { get; set; }
        public double? AttendanceRate { get; set; }
        public int? PendingAssignments { get; set; }
        public string? ProfilePicture { get; set; }
    }

    public class CreateParentModel
    {
        public string FirstName { get; set; } = "";
        public string LastName { get; set; } = "";
        public string Email { get; set; } = "";
        public string? PhoneNumber { get; set; }
        public string? NationalId { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public string? EmergencyContact { get; set; }
        public string? Occupation { get; set; }
        public string? WorkPlace { get; set; }
        public string? WorkPhone { get; set; }
    }

    public class UpdateParentModel
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? PhoneNumber { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }
        public string? EmergencyContact { get; set; }
        public string? Occupation { get; set; }
        public string? WorkPlace { get; set; }
        public string? WorkPhone { get; set; }
    }

    public class LinkParentStudentModel
    {
        public string ParentId { get; set; } = "";
        public string StudentId { get; set; } = "";
        public string Relationship { get; set; } = "";
    }

    public class ParentStudentModel
    {
        public int Id { get; set; }
        public string ParentId { get; set; } = "";
        public string StudentId { get; set; } = "";
        public string Relationship { get; set; } = "";
        public bool IsActive { get; set; }
        public ParentModel? Parent { get; set; }
        public StudentSummaryModel? Student { get; set; }
    }

    public class StudentSummaryModel
    {
        public string Id { get; set; } = "";
        public string FirstName { get; set; } = "";
        public string LastName { get; set; } = "";
        public string FullName => $"{FirstName} {LastName}";
        public string StudentNumber { get; set; } = "";
        public string ClassName { get; set; } = "";
        public string? ProfilePicture { get; set; }
        public bool IsActive { get; set; }
    }

    public class ParentDashboardModel
    {
        public ParentModel Parent { get; set; } = new();
        public List<ChildPerformanceModel> Children { get; set; } = new();
        public List<ParentNotificationModel> RecentNotifications { get; set; } = new();
        public List<UpcomingEventModel> UpcomingEvents { get; set; } = new();
        public ParentStatisticsModel Statistics { get; set; } = new();
    }

    public class ChildPerformanceModel
    {
        public string StudentId { get; set; } = "";
        public string StudentName { get; set; } = "";
        public string StudentNumber { get; set; } = "";
        public string ClassName { get; set; } = "";
        public string? ProfilePicture { get; set; }
        public double AverageGrade { get; set; }
        public double AttendanceRate { get; set; }
        public int PendingAssignments { get; set; }
        public int TotalAssignments { get; set; }
        public List<RecentGradeModel> RecentGrades { get; set; } = new();
        public List<AttendanceRecordModel> RecentAttendance { get; set; } = new();
        public string PerformanceTrend { get; set; } = "stable"; // improving, declining, stable
    }

    public class RecentGradeModel
    {
        public int Id { get; set; }
        public string SubjectName { get; set; } = "";
        public double Score { get; set; }
        public double MaxScore { get; set; }
        public double Percentage { get; set; }
        public string Grade { get; set; } = "";
        public DateTime Date { get; set; }
        public string ExamType { get; set; } = "";
    }

    public class AttendanceRecordModel
    {
        public DateTime Date { get; set; }
        public bool IsPresent { get; set; }
        public string? Reason { get; set; }
        public TimeSpan? CheckInTime { get; set; }
        public TimeSpan? CheckOutTime { get; set; }
        public string StatusText => IsPresent ? "حاضر" : "غائب";
        public string StatusClass => IsPresent ? "text-success" : "text-danger";
    }

    public class ParentStatisticsModel
    {
        public int TotalChildren { get; set; }
        public double OverallAverageGrade { get; set; }
        public double OverallAttendanceRate { get; set; }
        public int TotalPendingAssignments { get; set; }
        public int UnreadNotifications { get; set; }
        public int UpcomingEvents { get; set; }
    }

    public class ParentNotificationModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string Message { get; set; } = "";
        public string Type { get; set; } = "";
        public DateTime CreatedAt { get; set; }
        public bool IsRead { get; set; }
        public string? StudentName { get; set; }
        public string TypeIcon => GetTypeIcon(Type);
        public string TypeColor => GetTypeColor(Type);

        private static string GetTypeIcon(string type) => type.ToLower() switch
        {
            "grade" => "fas fa-chart-line",
            "assignment" => "fas fa-tasks",
            "attendance" => "fas fa-calendar-check",
            "event" => "fas fa-calendar-alt",
            "meeting" => "fas fa-handshake",
            "announcement" => "fas fa-bullhorn",
            _ => "fas fa-info-circle"
        };

        private static string GetTypeColor(string type) => type.ToLower() switch
        {
            "grade" => "success",
            "assignment" => "warning",
            "attendance" => "danger",
            "event" => "info",
            "meeting" => "primary",
            "announcement" => "secondary",
            _ => "light"
        };
    }

    public class UpcomingEventModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Location { get; set; } = "";
        public string Category { get; set; } = "";
        public string CategoryIcon => GetCategoryIcon(Category);
        public string CategoryColor => GetCategoryColor(Category);

        private static string GetCategoryIcon(string category) => category.ToLower() switch
        {
            "academic" => "fas fa-graduation-cap",
            "sports" => "fas fa-futbol",
            "cultural" => "fas fa-theater-masks",
            "meeting" => "fas fa-handshake",
            "social" => "fas fa-users",
            _ => "fas fa-calendar-alt"
        };

        private static string GetCategoryColor(string category) => category.ToLower() switch
        {
            "academic" => "primary",
            "sports" => "success",
            "cultural" => "warning",
            "meeting" => "info",
            "social" => "secondary",
            _ => "light"
        };
    }

    public class ParentFilterModel
    {
        public string? Search { get; set; }
        public bool? IsActive { get; set; }
        public string? Relationship { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class ParentStatisticsOverviewModel
    {
        public int TotalParents { get; set; }
        public int ActiveParents { get; set; }
        public int InactiveParents { get; set; }
        public int ParentsWithMultipleChildren { get; set; }
        public double AverageChildrenPerParent { get; set; }
        public Dictionary<string, int> ParentsByRelationship { get; set; } = new();
        public RecentRegistrationsModel RecentRegistrations { get; set; } = new();
        public EngagementMetricsModel EngagementMetrics { get; set; } = new();
    }

    public class RecentRegistrationsModel
    {
        public int ThisMonth { get; set; }
        public int LastMonth { get; set; }
        public int ThisYear { get; set; }
    }

    public class EngagementMetricsModel
    {
        public double LoginRate { get; set; }
        public double NotificationReadRate { get; set; }
        public double EventAttendanceRate { get; set; }
    }

    public static class ParentRelationships
    {
        public static readonly Dictionary<string, string> Relationships = new()
        {
            { "Father", "الأب" },
            { "Mother", "الأم" },
            { "Guardian", "الوصي" },
            { "Grandfather", "الجد" },
            { "Grandmother", "الجدة" },
            { "Uncle", "العم" },
            { "Aunt", "العمة" },
            { "Other", "أخرى" }
        };

        public static readonly Dictionary<string, string> Icons = new()
        {
            { "Father", "fas fa-male" },
            { "Mother", "fas fa-female" },
            { "Guardian", "fas fa-user-shield" },
            { "Grandfather", "fas fa-user-tie" },
            { "Grandmother", "fas fa-user-nurse" },
            { "Uncle", "fas fa-user" },
            { "Aunt", "fas fa-user" },
            { "Other", "fas fa-user-friends" }
        };
    }

    public static class PerformanceTrends
    {
        public static readonly Dictionary<string, string> Trends = new()
        {
            { "improving", "تحسن" },
            { "declining", "تراجع" },
            { "stable", "مستقر" }
        };

        public static readonly Dictionary<string, string> Icons = new()
        {
            { "improving", "fas fa-arrow-up" },
            { "declining", "fas fa-arrow-down" },
            { "stable", "fas fa-minus" }
        };

        public static readonly Dictionary<string, string> Colors = new()
        {
            { "improving", "text-success" },
            { "declining", "text-danger" },
            { "stable", "text-warning" }
        };
    }
}
