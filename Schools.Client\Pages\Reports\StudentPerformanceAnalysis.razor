@page "/reports/student-performance"
@using Schools.Shared.DTOs
@using Schools.Client.Services
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-gradient-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        تحليل أداء الطلاب المتقدم
                    </h4>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <label class="form-label">الطالب</label>
                            <select class="form-select" @bind="selectedStudentId" @bind:after="LoadAnalysis">
                                <option value="">اختر الطالب</option>
                                @if (students != null)
                                {
                                    @foreach (var student in students)
                                    {
                                        <option value="@student.Id">@student.FirstName @student.LastName</option>
                                    }
                                }
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">السنة الأكاديمية</label>
                            <select class="form-select" @bind="selectedAcademicYearId" @bind:after="LoadAnalysis">
                                @if (academicYears != null)
                                {
                                    @foreach (var year in academicYears)
                                    {
                                        <option value="@year.Id">@year.Name</option>
                                    }
                                }
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الفصل الدراسي</label>
                            <select class="form-select" @bind="selectedSemester" @bind:after="LoadAnalysis">
                                <option value="">جميع الفصول</option>
                                <option value="First">الفصل الأول</option>
                                <option value="Second">الفصل الثاني</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">نوع التحليل</label>
                            <select class="form-select" @bind="analysisType" @bind:after="LoadAnalysis">
                                <option value="overview">نظرة عامة</option>
                                <option value="subjects">تحليل المواد</option>
                                <option value="trends">الاتجاهات الزمنية</option>
                                <option value="comparison">مقارنة مع الأقران</option>
                            </select>
                        </div>
                    </div>

                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحليل...</span>
                            </div>
                        </div>
                    }
                    else if (!string.IsNullOrEmpty(selectedStudentId) && studentGrades?.Any() == true)
                    {
                        <!-- Student Info -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-md-8">
                                                <h5 class="mb-1">@selectedStudentName</h5>
                                                <p class="text-muted mb-0">
                                                    إجمالي الدرجات: @studentGrades.Count |
                                                    المعدل العام: @GetOverallAverage().ToString("F1")% |
                                                    التقدير: @GetLetterGrade(GetOverallAverage())
                                                </p>
                                            </div>
                                            <div class="col-md-4 text-end">
                                                <div class="progress" style="height: 25px;">
                                                    <div class="progress-bar @GetPerformanceClass(GetOverallAverage())"
                                                         style="width: @GetOverallAverage()%">
                                                        @GetOverallAverage().ToString("F1")%
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if (analysisType == "overview")
                        {
                            <!-- Overview Analysis -->
                            <div class="row mb-4">
                                <!-- Performance Metrics -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">مؤشرات الأداء</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row text-center">
                                                <div class="col-6 mb-3">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-success">@GetHighestScore().ToString("F1")%</h4>
                                                        <small class="text-muted">أعلى درجة</small>
                                                    </div>
                                                </div>
                                                <div class="col-6 mb-3">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-danger">@GetLowestScore().ToString("F1")%</h4>
                                                        <small class="text-muted">أقل درجة</small>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-info">@GetConsistencyScore().ToString("F1")%</h4>
                                                        <small class="text-muted">الثبات</small>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="border rounded p-3">
                                                        <h4 class="text-warning">@GetImprovementTrend()</h4>
                                                        <small class="text-muted">الاتجاه</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Grade Distribution -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">توزيع الدرجات</h6>
                                        </div>
                                        <div class="card-body">
                                            @foreach (var gradeRange in GetGradeDistribution())
                                            {
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span>@gradeRange.Label</span>
                                                    <span class="badge @gradeRange.Class">@gradeRange.Count</span>
                                                </div>
                                                <div class="progress mb-3">
                                                    <div class="progress-bar @gradeRange.Class" style="width: @gradeRange.Percentage%"></div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        @if (analysisType == "subjects")
                        {
                            <!-- Subject Analysis -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">تحليل أداء المواد</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                @foreach (var subjectAnalysis in GetSubjectAnalysis())
                                                {
                                                    <div class="col-md-6 col-lg-4 mb-4">
                                                        <div class="card border-start @GetSubjectBorderClass(subjectAnalysis.Average) border-4">
                                                            <div class="card-body">
                                                                <h6 class="card-title">@subjectAnalysis.SubjectName</h6>
                                                                <div class="row">
                                                                    <div class="col-6">
                                                                        <small class="text-muted">المتوسط</small>
                                                                        <h5 class="text-primary">@subjectAnalysis.Average.ToString("F1")%</h5>
                                                                    </div>
                                                                    <div class="col-6">
                                                                        <small class="text-muted">عدد الدرجات</small>
                                                                        <h5 class="text-info">@subjectAnalysis.Count</h5>
                                                                    </div>
                                                                </div>
                                                                <div class="progress mt-2">
                                                                    <div class="progress-bar @GetPerformanceClass(subjectAnalysis.Average)"
                                                                         style="width: @subjectAnalysis.Average%"></div>
                                                                </div>
                                                                <small class="text-muted mt-1 d-block">
                                                                    التقدير: @GetLetterGrade(subjectAnalysis.Average)
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        @if (analysisType == "trends")
                        {
                            <!-- Trends Analysis -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">الاتجاهات الزمنية للأداء</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>التاريخ</th>
                                                            <th>المادة</th>
                                                            <th>نوع الامتحان</th>
                                                            <th>الدرجة</th>
                                                            <th>النسبة المئوية</th>
                                                            <th>الاتجاه</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach (var grade in studentGrades.OrderBy(g => g.Date))
                                                        {
                                                            <tr>
                                                                <td>@grade.Date.ToString("yyyy-MM-dd")</td>
                                                                <td>
                                                                    <span class="badge bg-info">@grade.SubjectName</span>
                                                                </td>
                                                                <td>@GetExamTypeText(grade.ExamType)</td>
                                                                <td>@grade.Score / @grade.MaxScore</td>
                                                                <td>
                                                                    <span class="badge @GetPerformanceClass(grade.Percentage)">
                                                                        @grade.Percentage.ToString("F1")%
                                                                    </span>
                                                                </td>
                                                                <td>
                                                                    @GetTrendIcon(grade.Percentage, GetPreviousGradePercentage(grade))
                                                                </td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        @if (analysisType == "comparison")
                        {
                            <!-- Comparison Analysis -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">مقارنة مع متوسط الصف</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i>
                                                هذه الميزة تتطلب بيانات إضافية عن متوسط الصف وستكون متاحة قريباً.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">اختر طالباً لبدء التحليل</h5>
                            <p class="text-muted">حدد طالباً من القائمة أعلاه لعرض تحليل مفصل لأدائه الأكاديمي</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private List<UserDto>? students;
    private List<AcademicYearDto>? academicYears;
    private List<GradeDto>? studentGrades;

    private bool isLoading = false;

    // Filters
    private string selectedStudentId = "";
    private string selectedStudentName = "";
    private int? selectedAcademicYearId;
    private string selectedSemester = "";
    private string analysisType = "overview";

    protected override async Task OnInitializedAsync()
    {
        await LoadInitialData();
    }

    private async Task LoadInitialData()
    {
        try
        {
            var usersTask = ApiService.GetUsersAsync();
            var academicYearsTask = ApiService.GetAcademicYearsAsync();

            await Task.WhenAll(usersTask, academicYearsTask);

            var users = await usersTask;
            students = users.Where(u => u.Role == "Student").ToList();
            academicYears = (await academicYearsTask).ToList();

            // Set default academic year
            selectedAcademicYearId = academicYears.FirstOrDefault(y => y.IsActive)?.Id;
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
    }

    private async Task LoadAnalysis()
    {
        if (string.IsNullOrEmpty(selectedStudentId)) return;

        try
        {
            isLoading = true;

            selectedStudentName = students?.FirstOrDefault(s => s.Id == selectedStudentId)?.FirstName + " " +
                                 students?.FirstOrDefault(s => s.Id == selectedStudentId)?.LastName ?? "";

            var grades = await ApiService.GetStudentGradesAsync(
                selectedStudentId,
                null,
                null,
                selectedSemester,
                selectedAcademicYearId,
                1,
                1000);

            studentGrades = grades.ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل التحليل: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private decimal GetOverallAverage()
    {
        if (studentGrades?.Any() != true) return 0;
        return studentGrades.Average(g => g.Percentage);
    }

    private decimal GetHighestScore()
    {
        if (studentGrades?.Any() != true) return 0;
        return studentGrades.Max(g => g.Percentage);
    }

    private decimal GetLowestScore()
    {
        if (studentGrades?.Any() != true) return 0;
        return studentGrades.Min(g => g.Percentage);
    }

    private decimal GetConsistencyScore()
    {
        if (studentGrades?.Any() != true) return 0;
        var average = GetOverallAverage();
        var variance = studentGrades.Average(g => Math.Pow((double)(g.Percentage - average), 2));
        var standardDeviation = Math.Sqrt(variance);
        return Math.Max(0, 100 - (decimal)standardDeviation);
    }

    private string GetImprovementTrend()
    {
        if (studentGrades?.Count < 2) return "غير محدد";

        var orderedGrades = studentGrades.OrderBy(g => g.Date).ToList();
        var firstHalf = orderedGrades.Take(orderedGrades.Count / 2).Average(g => g.Percentage);
        var secondHalf = orderedGrades.Skip(orderedGrades.Count / 2).Average(g => g.Percentage);

        if (secondHalf > firstHalf + 5) return "تحسن";
        if (secondHalf < firstHalf - 5) return "تراجع";
        return "ثابت";
    }

    private List<GradeDistributionItem> GetGradeDistribution()
    {
        if (studentGrades?.Any() != true) return new List<GradeDistributionItem>();

        var total = studentGrades.Count;
        return new List<GradeDistributionItem>
        {
            new GradeDistributionItem
            {
                Label = "ممتاز (90-100%)",
                Count = studentGrades.Count(g => g.Percentage >= 90),
                Class = "bg-success",
                Percentage = (double)studentGrades.Count(g => g.Percentage >= 90) / total * 100
            },
            new GradeDistributionItem
            {
                Label = "جيد جداً (80-89%)",
                Count = studentGrades.Count(g => g.Percentage >= 80 && g.Percentage < 90),
                Class = "bg-info",
                Percentage = (double)studentGrades.Count(g => g.Percentage >= 80 && g.Percentage < 90) / total * 100
            },
            new GradeDistributionItem
            {
                Label = "جيد (70-79%)",
                Count = studentGrades.Count(g => g.Percentage >= 70 && g.Percentage < 80),
                Class = "bg-warning",
                Percentage = (double)studentGrades.Count(g => g.Percentage >= 70 && g.Percentage < 80) / total * 100
            },
            new GradeDistributionItem
            {
                Label = "مقبول (60-69%)",
                Count = studentGrades.Count(g => g.Percentage >= 60 && g.Percentage < 70),
                Class = "bg-secondary",
                Percentage = (double)studentGrades.Count(g => g.Percentage >= 60 && g.Percentage < 70) / total * 100
            },
            new GradeDistributionItem
            {
                Label = "ضعيف (أقل من 60%)",
                Count = studentGrades.Count(g => g.Percentage < 60),
                Class = "bg-danger",
                Percentage = (double)studentGrades.Count(g => g.Percentage < 60) / total * 100
            }
        };
    }

    private List<SubjectAnalysisItem> GetSubjectAnalysis()
    {
        if (studentGrades?.Any() != true) return new List<SubjectAnalysisItem>();

        return studentGrades
            .GroupBy(g => new { g.SubjectId, g.SubjectName })
            .Select(group => new SubjectAnalysisItem
            {
                SubjectName = group.Key.SubjectName,
                Average = group.Average(g => g.Percentage),
                Count = group.Count()
            })
            .OrderByDescending(s => s.Average)
            .ToList();
    }

    private string GetPerformanceClass(decimal percentage)
    {
        return percentage switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-info",
            >= 70 => "bg-warning",
            >= 60 => "bg-secondary",
            _ => "bg-danger"
        };
    }

    private string GetSubjectBorderClass(decimal average)
    {
        return average switch
        {
            >= 90 => "border-success",
            >= 80 => "border-info",
            >= 70 => "border-warning",
            >= 60 => "border-secondary",
            _ => "border-danger"
        };
    }

    private string GetLetterGrade(decimal percentage)
    {
        return percentage switch
        {
            >= 95 => "A+",
            >= 90 => "A",
            >= 85 => "B+",
            >= 80 => "B",
            >= 75 => "C+",
            >= 70 => "C",
            >= 65 => "D+",
            >= 60 => "D",
            _ => "F"
        };
    }

    private string GetExamTypeText(string examType)
    {
        return examType switch
        {
            "Quiz" => "اختبار قصير",
            "Midterm" => "امتحان نصفي",
            "Final" => "امتحان نهائي",
            "Assignment" => "واجب",
            _ => examType
        };
    }

    private decimal GetPreviousGradePercentage(GradeDto currentGrade)
    {
        if (studentGrades?.Any() != true) return 0;

        var previousGrade = studentGrades
            .Where(g => g.Date < currentGrade.Date && g.SubjectId == currentGrade.SubjectId)
            .OrderByDescending(g => g.Date)
            .FirstOrDefault();

        return previousGrade?.Percentage ?? 0;
    }

    private string GetTrendIcon(decimal current, decimal previous)
    {
        if (previous == 0) return "<i class='fas fa-minus text-muted'></i>";

        if (current > previous + 5)
            return "<i class='fas fa-arrow-up text-success'></i>";
        else if (current < previous - 5)
            return "<i class='fas fa-arrow-down text-danger'></i>";
        else
            return "<i class='fas fa-arrow-right text-warning'></i>";
    }

    public class GradeDistributionItem
    {
        public string Label { get; set; } = "";
        public int Count { get; set; }
        public string Class { get; set; } = "";
        public double Percentage { get; set; }
    }

    public class SubjectAnalysisItem
    {
        public string SubjectName { get; set; } = "";
        public decimal Average { get; set; }
        public int Count { get; set; }
    }
}
