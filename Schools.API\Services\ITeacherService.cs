using Schools.Shared.DTOs;
using Schools.Shared.Models;

namespace Schools.API.Services;

public interface ITeacherService
{
    Task<PagedResult<TeacherDto>> GetTeachersAsync(
        string? search = null,
        string? department = null,
        string? subject = null,
        bool? isActive = null,
        int page = 1,
        int pageSize = 20);

    Task<TeacherDto?> GetTeacherByIdAsync(string id);
    Task<TeacherDto> CreateTeacherAsync(CreateTeacherDto createDto);
    Task<TeacherDto?> UpdateTeacherAsync(string id, UpdateTeacherDto updateDto);
    Task<bool> DeleteTeacherAsync(string id);
    Task<bool> ActivateTeacherAsync(string id);
    Task<bool> DeactivateTeacherAsync(string id);
    
    // Subject assignments
    Task<List<TeacherSubjectDto>> GetTeacherSubjectsAsync(string teacherId);
    Task<TeacherSubjectDto> AssignSubjectToTeacherAsync(string teacherId, int subjectId);
    Task<bool> UnassignSubjectFromTeacherAsync(string teacherId, int subjectId);
    
    // Class assignments
    Task<List<TeacherClassDto>> GetTeacherClassesAsync(string teacherId);
    Task<TeacherClassDto> AssignClassToTeacherAsync(string teacherId, int classId, int subjectId);
    Task<bool> UnassignClassFromTeacherAsync(string teacherId, int classId, int subjectId);
    
    // Schedule and timetable
    Task<List<TeacherScheduleDto>> GetTeacherScheduleAsync(string teacherId, DateTime? date = null);
    Task<TeacherWorkloadDto> GetTeacherWorkloadAsync(string teacherId);
    
    // Performance and evaluation
    Task<TeacherPerformanceDto> GetTeacherPerformanceAsync(string teacherId, DateTime? startDate = null, DateTime? endDate = null);
    Task<List<TeacherEvaluationDto>> GetTeacherEvaluationsAsync(string teacherId);
    Task<TeacherEvaluationDto> CreateTeacherEvaluationAsync(CreateTeacherEvaluationDto createDto);
    
    // Reports
    Task<List<TeacherDto>> GetTeachersBySubjectAsync(int subjectId);
    Task<List<TeacherDto>> GetTeachersByDepartmentAsync(string department);
    Task<TeacherStatisticsDto> GetTeacherStatisticsAsync(string teacherId);
}
