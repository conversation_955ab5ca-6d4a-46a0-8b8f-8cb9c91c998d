﻿@using Microsoft.AspNetCore.Components.Authorization
@using Schools.Client.Services
@inherits LayoutComponentBase
@inject AuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<div class="page">
    <AuthorizeView>
        <Authorized>
            <!-- Sidebar for authenticated users -->
            <div class="sidebar">
                <NavMenu />
            </div>

            <main>
                <!-- Top navigation bar -->
                <div class="top-row px-4 navbar navbar-expand-lg navbar-light bg-white border-bottom">
                    <div class="container-fluid">
                        <span class="navbar-brand mb-0 h1">
                            <i class="fas fa-graduation-cap text-primary me-2"></i>
                            نظام إدارة المدارس
                        </span>

                        <div class="navbar-nav ms-auto">
                            <!-- Notifications -->
                            <div class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-bell"></i>
                                    <span class="badge bg-danger badge-sm">3</span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><h6 class="dropdown-header">الإشعارات</h6></li>
                                    <li><a class="dropdown-item" href="#">إشعار جديد 1</a></li>
                                    <li><a class="dropdown-item" href="#">إشعار جديد 2</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#">عرض الكل</a></li>
                                </ul>
                            </div>

                            <!-- User menu -->
                            <div class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user-circle me-2"></i>
                                    <span>@(context.User.FindFirst("FullName")?.Value ?? context.User.Identity?.Name ?? "مستخدم")</span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><h6 class="dropdown-header">حسابي</h6></li>
                                    <li><a class="dropdown-item" href="/profile"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                                    <li><a class="dropdown-item" href="/settings"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><button class="dropdown-item" @onclick="Logout"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</button></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <article class="content px-4">
                    @Body
                </article>
            </main>
        </Authorized>
        <NotAuthorized>
            <!-- Full-width layout for non-authenticated users -->
            <main class="w-100">
                @Body
            </main>
        </NotAuthorized>
    </AuthorizeView>
</div>

@code {
    private async Task Logout()
    {
        Navigation.NavigateTo("/logout");
    }
}
