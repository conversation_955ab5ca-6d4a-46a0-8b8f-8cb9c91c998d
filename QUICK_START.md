# 🚀 دليل البدء السريع - نظام إدارة المدارس

## ⚡ التشغيل السريع

### 1. تشغيل النظام
```bash
# تشغيل API (في terminal منفصل)
dotnet run --project Schools.API

# تشغيل العميل (في terminal آخر)
dotnet run --project Schools.Client
```

### 2. الوصول للنظام
- **الموقع:** http://localhost:5131
- **API:** http://localhost:5261
- **التوثيق:** http://localhost:5261/swagger

### 3. تسجيل الدخول كمدير
- **البريد:** <EMAIL>
- **كلمة المرور:** Admin123!

## 🎯 الخطوات الأولى

### كمدير نظام:

1. **سجل دخولك** باستخدام بيانات المدير
2. **أنشئ عام دراسي جديد** من قسم "الأعوام الدراسية"
3. **أضف المراحل الدراسية** (ابتدائي، متوسط، ثانوي)
4. **أنشئ الصفوف والشعب** لكل مرحلة
5. **أضف المواد الدراسية** مع أكوادها
6. **وافق على طلبات المستخدمين الجدد**

### إنشاء حسابات المستخدمين:

#### للمعلمين:
1. اذهب لصفحة التسجيل
2. اختر دور "معلم"
3. أدخل البيانات المطلوبة
4. انتظر موافقة الإدارة

#### للطلاب:
1. اذهب لصفحة التسجيل
2. اختر دور "طالب"
3. أدخل البيانات الشخصية
4. انتظر موافقة الإدارة

#### لأولياء الأمور:
1. اذهب لصفحة التسجيل
2. اختر دور "ولي أمر"
3. أدخل البيانات الشخصية
4. بعد الموافقة، اربط الأبناء بالرقم الوطني

## 🔧 الميزات الأساسية

### لوحة تحكم الإدارة:
- ✅ إدارة الأعوام الدراسية
- ✅ إدارة المراحل والصفوف
- ✅ إدارة المواد الدراسية
- ✅ موافقة المستخدمين
- ✅ عرض الإحصائيات

### لوحة تحكم المعلم:
- ✅ تسجيل الحضور والغياب
- ✅ إنشاء الامتحانات
- ✅ إدارة الواجبات
- ✅ تقييم الطلاب

### لوحة تحكم الطالب:
- ✅ عرض الجدول الدراسي
- ✅ أداء الامتحانات
- ✅ حل الواجبات
- ✅ عرض الدرجات

### لوحة تحكم ولي الأمر:
- ✅ متابعة الأبناء
- ✅ عرض الدرجات
- ✅ متابعة الحضور
- ✅ إدارة الرسوم

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة:

#### خطأ الاتصال بقاعدة البيانات:
```bash
# تحديث قاعدة البيانات
dotnet ef database update --project Schools.Data --startup-project Schools.API
```

#### خطأ في تشغيل العميل:
```bash
# إعادة بناء المشروع
dotnet clean
dotnet build
dotnet run --project Schools.Client
```

#### خطأ في الثقافة/اللغة:
- تأكد من وجود `<BlazorWebAssemblyLoadAllGlobalizationData>true</BlazorWebAssemblyLoadAllGlobalizationData>` في ملف المشروع

### فحص الحالة:
- **API يعمل؟** اذهب إلى http://localhost:5261/swagger
- **العميل يعمل؟** اذهب إلى http://localhost:5131
- **قاعدة البيانات متصلة؟** تحقق من connection string في appsettings.json

## 📱 نصائح للاستخدام

### للحصول على أفضل تجربة:
1. **استخدم متصفح حديث** (Chrome, Firefox, Edge)
2. **فعّل JavaScript** في المتصفح
3. **تأكد من الاتصال بالإنترنت** لتحميل الخطوط والأيقونات
4. **استخدم دقة شاشة مناسبة** للحصول على تجربة متجاوبة

### اختصارات مفيدة:
- **Ctrl+Shift+I** لفتح أدوات المطور
- **F5** لإعادة تحميل الصفحة
- **Ctrl+F5** لإعادة تحميل مع تجاهل الكاش

## 🎓 التعلم والتطوير

### لفهم الكود:
1. **ابدأ بـ Program.cs** في كلا المشروعين
2. **اطلع على Models** في مجلد Shared
3. **تابع Controllers** في مشروع API
4. **استكشف Components** في مشروع Client

### للتطوير:
1. **أضف ميزات جديدة** في Controllers
2. **أنشئ صفحات جديدة** في مجلد Pages
3. **طور مكونات مشتركة** في مجلد Shared
4. **اختبر التغييرات** باستمرار

## 📞 الحصول على المساعدة

### إذا واجهت مشاكل:
1. **تحقق من Console** في المتصفح للأخطاء
2. **راجع logs** في terminal الخاص بـ API
3. **تأكد من تشغيل كلا المشروعين** (API + Client)
4. **أعد تشغيل النظام** إذا لزم الأمر

### موارد مفيدة:
- **Blazor Documentation:** https://docs.microsoft.com/aspnet/core/blazor
- **Entity Framework:** https://docs.microsoft.com/ef/core
- **Bootstrap RTL:** https://getbootstrap.com/docs/5.0/getting-started/rtl/

---

**مبروك! أنت الآن جاهز لاستخدام نظام إدارة المدارس 🎉**
