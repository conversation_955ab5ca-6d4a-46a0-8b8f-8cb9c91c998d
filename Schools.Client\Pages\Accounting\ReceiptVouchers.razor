@page "/accounting/receipt-vouchers"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Accountant,Admin")]

<PageTitle>سندات القبض - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-receipt text-success me-2"></i>
                        سندات القبض
                    </h2>
                    <p class="text-muted mb-0">إدارة وتتبع جميع سندات القبض والإيرادات</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-success" @onclick="CreateNewVoucher">
                        <i class="fas fa-plus me-2"></i>
                        سند قبض جديد
                    </button>
                    <button class="btn btn-outline-primary" @onclick="RefreshData">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث
                    </button>
                    <button class="btn btn-outline-info" @onclick="ExportVouchers">
                        <i class="fas fa-download me-2"></i>
                        تصدير
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل سندات القبض...</p>
        </div>
    }
    else
    {
        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" @bind="fromDate" @bind:after="FilterVouchers" />
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" @bind="toDate" @bind:after="FilterVouchers" />
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الحالة</label>
                                <select @bind="selectedStatus" @bind:after="FilterVouchers" class="form-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="@VoucherStatus.Draft">مسودة</option>
                                    <option value="@VoucherStatus.Approved">معتمد</option>
                                    <option value="@VoucherStatus.Posted">مرحل</option>
                                    <option value="@VoucherStatus.Cancelled">ملغي</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" placeholder="رقم السند أو المستلم منه..."
                                           @bind="searchTerm" @bind:after="FilterVouchers" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm bg-success text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@filteredVouchers.Count</h4>
                        <p class="mb-0">إجمالي السندات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm bg-primary text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@GetTotalAmount().ToString("C")</h4>
                        <p class="mb-0">إجمالي المبلغ</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm bg-warning text-dark">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@GetPendingCount()</h4>
                        <p class="mb-0">سندات معلقة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm bg-info text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@GetPostedCount()</h4>
                        <p class="mb-0">سندات مرحلة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vouchers Table -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2 text-success"></i>
                            قائمة سندات القبض
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (filteredVouchers?.Any() == true)
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>رقم السند</th>
                                            <th>التاريخ</th>
                                            <th>المستلم منه</th>
                                            <th>المبلغ</th>
                                            <th>طريقة الدفع</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var voucher in filteredVouchers.Skip((currentPage - 1) * pageSize).Take(pageSize))
                                        {
                                            <tr>
                                                <td>
                                                    <strong class="text-primary">@voucher.VoucherNumber</strong>
                                                </td>
                                                <td>@voucher.VoucherDate.ToString("dd/MM/yyyy")</td>
                                                <td>
                                                    <div>
                                                        <strong>@voucher.ReceivedFrom</strong>
                                                        @if (!string.IsNullOrEmpty(voucher.Description))
                                                        {
                                                            <br>
                                                            <small class="text-muted">@TruncateText(voucher.Description, 50)</small>
                                                        }
                                                    </div>
                                                </td>
                                                <td>
                                                    <strong class="text-success">@voucher.TotalAmount.ToString("C")</strong>
                                                </td>
                                                <td>
                                                    <span class="badge @GetPaymentMethodBadge(voucher.PaymentMethod)">
                                                        @GetPaymentMethodText(voucher.PaymentMethod)
                                                    </span>
                                                    @if (!string.IsNullOrEmpty(voucher.CheckNumber))
                                                    {
                                                        <br>
                                                        <small class="text-muted">شيك: @voucher.CheckNumber</small>
                                                    }
                                                </td>
                                                <td>
                                                    <span class="badge @GetStatusBadge(voucher.Status)">
                                                        @GetStatusText(voucher.Status)
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewVoucher(voucher.Id)" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        @if (voucher.Status == VoucherStatus.Draft)
                                                        {
                                                            <button class="btn btn-sm btn-outline-secondary" @onclick="() => EditVoucher(voucher.Id)" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-success" @onclick="() => ApproveVoucher(voucher.Id)" title="اعتماد">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        }
                                                        @if (voucher.Status == VoucherStatus.Approved)
                                                        {
                                                            <button class="btn btn-sm btn-outline-info" @onclick="() => PostVoucher(voucher.Id)" title="ترحيل">
                                                                <i class="fas fa-share"></i>
                                                            </button>
                                                        }
                                                        <button class="btn btn-sm btn-outline-warning" @onclick="() => PrintVoucher(voucher.Id)" title="طباعة">
                                                            <i class="fas fa-print"></i>
                                                        </button>
                                                        @if (voucher.Status != VoucherStatus.Posted)
                                                        {
                                                            <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteVoucher(voucher.Id)" title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        }
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            @if (totalPages > 1)
                            {
                                <nav class="mt-4">
                                    <ul class="pagination justify-content-center">
                                        <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                            <button class="page-link" @onclick="() => ChangePage(currentPage - 1)">السابق</button>
                                        </li>
                                        @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                                        {
                                            <li class="page-item @(i == currentPage ? "active" : "")">
                                                <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                                            </li>
                                        }
                                        <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                                            <button class="page-link" @onclick="() => ChangePage(currentPage + 1)">التالي</button>
                                        </li>
                                    </ul>
                                </nav>
                            }
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد سندات قبض</h5>
                                <p class="text-muted">ابدأ بإنشاء سند قبض جديد</p>
                                <button class="btn btn-success" @onclick="CreateNewVoucher">
                                    <i class="fas fa-plus me-2"></i>
                                    إنشاء سند قبض
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private List<ReceiptVoucherDto> allVouchers = new();
    private List<ReceiptVoucherDto> filteredVouchers = new();

    private bool isLoading = true;
    private DateTime fromDate = DateTime.Now.AddMonths(-1);
    private DateTime toDate = DateTime.Now;
    private string selectedStatus = "";
    private string searchTerm = "";

    private int currentPage = 1;
    private int pageSize = 10;
    private int totalPages = 1;

    protected override async Task OnInitializedAsync()
    {
        await LoadVouchers();
    }

    private async Task LoadVouchers()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            allVouchers = await ApiService.GetReceiptVouchersAsync() ?? new List<ReceiptVoucherDto>();
            FilterVouchers();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void FilterVouchers()
    {
        filteredVouchers = allVouchers.Where(v =>
            v.VoucherDate >= fromDate &&
            v.VoucherDate <= toDate &&
            (string.IsNullOrEmpty(selectedStatus) || v.Status.ToString() == selectedStatus) &&
            (string.IsNullOrEmpty(searchTerm) ||
             v.VoucherNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
             v.ReceivedFrom.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
        ).OrderByDescending(v => v.VoucherDate).ToList();

        UpdatePagination();
        StateHasChanged();
    }

    private void UpdatePagination()
    {
        totalPages = (int)Math.Ceiling((double)filteredVouchers.Count / pageSize);
        if (currentPage > totalPages && totalPages > 0)
        {
            currentPage = totalPages;
        }
        else if (currentPage < 1)
        {
            currentPage = 1;
        }
    }

    private void ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            StateHasChanged();
        }
    }

    private async Task RefreshData()
    {
        await LoadVouchers();
    }

    private void CreateNewVoucher()
    {
        Navigation.NavigateTo("/accounting/receipt-vouchers/new");
    }

    private void ViewVoucher(int id)
    {
        Navigation.NavigateTo($"/accounting/receipt-vouchers/{id}");
    }

    private void EditVoucher(int id)
    {
        Navigation.NavigateTo($"/accounting/receipt-vouchers/{id}/edit");
    }

    private async Task ApproveVoucher(int id)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من اعتماد هذا السند؟"))
        {
            try
            {
                var success = await ApiService.ApproveReceiptVoucherAsync(id);
                if (success)
                {
                    await LoadVouchers();
                    await JSRuntime.InvokeVoidAsync("alert", "تم اعتماد السند بنجاح");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في اعتماد السند: {ex.Message}");
            }
        }
    }

    private async Task PostVoucher(int id)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من ترحيل هذا السند؟ لن يمكن التراجع عن هذا الإجراء."))
        {
            try
            {
                var success = await ApiService.PostReceiptVoucherAsync(id);
                if (success)
                {
                    await LoadVouchers();
                    await JSRuntime.InvokeVoidAsync("alert", "تم ترحيل السند بنجاح");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في ترحيل السند: {ex.Message}");
            }
        }
    }

    private async Task DeleteVoucher(int id)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا السند؟"))
        {
            try
            {
                var success = await ApiService.DeleteReceiptVoucherAsync(id);
                if (success)
                {
                    await LoadVouchers();
                    await JSRuntime.InvokeVoidAsync("alert", "تم حذف السند بنجاح");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف السند: {ex.Message}");
            }
        }
    }

    private async Task PrintVoucher(int id)
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة الطباعة ستكون متاحة قريباً");
    }

    private async Task ExportVouchers()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة التصدير ستكون متاحة قريباً");
    }

    private decimal GetTotalAmount()
    {
        return filteredVouchers.Sum(v => v.TotalAmount);
    }

    private int GetPendingCount()
    {
        return filteredVouchers.Count(v => v.Status == VoucherStatus.Draft || v.Status == VoucherStatus.Approved);
    }

    private int GetPostedCount()
    {
        return filteredVouchers.Count(v => v.Status == VoucherStatus.Posted);
    }

    private string TruncateText(string text, int maxLength)
    {
        if (string.IsNullOrEmpty(text) || text.Length <= maxLength)
            return text;

        return text.Substring(0, maxLength) + "...";
    }

    private string GetStatusBadge(VoucherStatus status)
    {
        return status switch
        {
            VoucherStatus.Draft => "bg-secondary",
            VoucherStatus.Approved => "bg-warning",
            VoucherStatus.Posted => "bg-success",
            VoucherStatus.Cancelled => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetStatusText(VoucherStatus status)
    {
        return status switch
        {
            VoucherStatus.Draft => "مسودة",
            VoucherStatus.Approved => "معتمد",
            VoucherStatus.Posted => "مرحل",
            VoucherStatus.Cancelled => "ملغي",
            _ => "غير محدد"
        };
    }

    private string GetPaymentMethodBadge(string method)
    {
        return method.ToLower() switch
        {
            "cash" => "bg-success",
            "bank" => "bg-primary",
            "check" => "bg-info",
            _ => "bg-secondary"
        };
    }

    private string GetPaymentMethodText(string method)
    {
        return method.ToLower() switch
        {
            "cash" => "نقدي",
            "bank" => "بنكي",
            "check" => "شيك",
            _ => "أخرى"
        };
    }
}
