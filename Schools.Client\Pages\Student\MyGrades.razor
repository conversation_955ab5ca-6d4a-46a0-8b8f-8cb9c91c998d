@page "/student/my-grades"
@using Schools.Shared.DTOs
@using Schools.Client.Services
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject AuthService AuthService

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>
                        درجاتي الأكاديمية
                    </h4>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <label class="form-label">السنة الأكاديمية</label>
                            <select class="form-select" @bind="selectedAcademicYearId" @bind:after="LoadGrades">
                                @if (academicYears != null)
                                {
                                    @foreach (var year in academicYears)
                                    {
                                        <option value="@year.Id">@year.Name</option>
                                    }
                                }
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">الفصل الدراسي</label>
                            <select class="form-select" @bind="selectedSemester" @bind:after="LoadGrades">
                                <option value="">جميع الفصول</option>
                                <option value="First">الفصل الأول</option>
                                <option value="Second">الفصل الثاني</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">المادة</label>
                            <select class="form-select" @bind="selectedSubjectId" @bind:after="LoadGrades">
                                <option value="">جميع المواد</option>
                                @if (subjects != null)
                                {
                                    @foreach (var subject in subjects)
                                    {
                                        <option value="@subject.Id">@subject.Name</option>
                                    }
                                }
                            </select>
                        </div>
                    </div>

                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else if (summary != null)
                    {
                        <!-- Overall Summary -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chart-line fa-2x mb-2"></i>
                                        <h4>@summary.TotalGrades</h4>
                                        <p class="mb-0">إجمالي الدرجات</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-percentage fa-2x mb-2"></i>
                                        <h4>@summary.OverallAverage.ToString("F1")%</h4>
                                        <p class="mb-0">المعدل العام</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-medal fa-2x mb-2"></i>
                                        <h4>@summary.OverallGrade</h4>
                                        <p class="mb-0">التقدير العام</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-book fa-2x mb-2"></i>
                                        <h4>@summary.SubjectGrades.Count</h4>
                                        <p class="mb-0">عدد المواد</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Subject Performance -->
                        @if (summary.SubjectGrades?.Any() == true)
                        {
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">أداء المواد</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                @foreach (var subjectGrade in summary.SubjectGrades.OrderByDescending(sg => sg.AverageScore))
                                                {
                                                    <div class="col-md-6 col-lg-4 mb-3">
                                                        <div class="card h-100 @GetSubjectCardClass(subjectGrade.AverageScore)">
                                                            <div class="card-body text-center">
                                                                <h6 class="card-title">@subjectGrade.SubjectName</h6>
                                                                <div class="mb-2">
                                                                    <span class="badge bg-light text-dark">@subjectGrade.GradeCount درجة</span>
                                                                </div>
                                                                <h4 class="text-white">@subjectGrade.AverageScore.ToString("F1")%</h4>
                                                                <div class="progress bg-light">
                                                                    <div class="progress-bar bg-white" style="width: @subjectGrade.AverageScore%"></div>
                                                                </div>
                                                                <small class="text-light mt-2 d-block">@GetLetterGrade(subjectGrade.AverageScore)</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        <!-- Detailed Grades -->
                        @if (grades?.Any() == true)
                        {
                            <div class="row">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">تفاصيل الدرجات</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-striped table-hover">
                                                    <thead class="table-dark">
                                                        <tr>
                                                            <th>المادة</th>
                                                            <th>نوع الامتحان</th>
                                                            <th>الدرجة</th>
                                                            <th>النسبة المئوية</th>
                                                            <th>التقدير</th>
                                                            <th>التاريخ</th>
                                                            <th>الفصل</th>
                                                            <th>ملاحظات</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach (var grade in grades.OrderByDescending(g => g.Date))
                                                        {
                                                            <tr>
                                                                <td>
                                                                    <span class="badge bg-info">@grade.SubjectName</span>
                                                                </td>
                                                                <td>@GetExamTypeText(grade.ExamType)</td>
                                                                <td>
                                                                    <strong>@grade.Score / @grade.MaxScore</strong>
                                                                </td>
                                                                <td>
                                                                    <span class="badge @GetPercentageClass(grade.Percentage)">
                                                                        @grade.Percentage.ToString("F1")%
                                                                    </span>
                                                                </td>
                                                                <td>
                                                                    <span class="badge @GetGradeClass(grade.Percentage)">
                                                                        @GetLetterGrade(grade.Percentage)
                                                                    </span>
                                                                </td>
                                                                <td>@grade.Date.ToString("yyyy-MM-dd")</td>
                                                                <td>@GetSemesterText(grade.Semester)</td>
                                                                <td>
                                                                    @if (!string.IsNullOrEmpty(grade.Notes))
                                                                    {
                                                                        <small class="text-muted">@grade.Notes</small>
                                                                    }
                                                                    else
                                                                    {
                                                                        <span class="text-muted">-</span>
                                                                    }
                                                                </td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد درجات</h5>
                            <p class="text-muted">لم يتم العثور على أي درجات للمعايير المحددة</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private List<GradeDto>? grades;
    private List<SubjectDto>? subjects;
    private List<AcademicYearDto>? academicYears;
    private StudentGradeSummaryDto? summary;

    private bool isLoading = true;
    private string? currentUserId;

    // Filters
    private int? selectedSubjectId;
    private string selectedSemester = "";
    private int? selectedAcademicYearId;

    protected override async Task OnInitializedAsync()
    {
        currentUserId = await AuthService.GetCurrentUserIdAsync();
        if (string.IsNullOrEmpty(currentUserId))
        {
            await JSRuntime.InvokeVoidAsync("alert", "يجب تسجيل الدخول أولاً");
            return;
        }

        await LoadInitialData();
        await LoadGrades();
    }

    private async Task LoadInitialData()
    {
        try
        {
            var subjectsTask = ApiService.GetSubjectsAsync();
            var academicYearsTask = ApiService.GetAcademicYearsAsync();

            await Task.WhenAll(subjectsTask, academicYearsTask);

            subjects = (await subjectsTask).ToList();
            academicYears = (await academicYearsTask).ToList();

            // Set default academic year
            selectedAcademicYearId = academicYears.FirstOrDefault(y => y.IsActive)?.Id;
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
    }

    private async Task LoadGrades()
    {
        if (string.IsNullOrEmpty(currentUserId)) return;

        try
        {
            isLoading = true;

            var gradesTask = ApiService.GetStudentGradesAsync(
                currentUserId,
                selectedSubjectId,
                null,
                selectedSemester,
                selectedAcademicYearId,
                1,
                1000);

            var summaryTask = ApiService.GetStudentGradeSummaryAsync(
                currentUserId,
                selectedSemester,
                selectedAcademicYearId);

            await Task.WhenAll(gradesTask, summaryTask);

            grades = (await gradesTask).ToList();
            summary = await summaryTask;
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الدرجات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private string GetExamTypeText(string examType)
    {
        return examType switch
        {
            "Quiz" => "اختبار قصير",
            "Midterm" => "امتحان نصفي",
            "Final" => "امتحان نهائي",
            "Assignment" => "واجب",
            _ => examType
        };
    }

    private string GetSemesterText(string semester)
    {
        return semester switch
        {
            "First" => "الأول",
            "Second" => "الثاني",
            _ => semester
        };
    }

    private string GetPercentageClass(decimal percentage)
    {
        return percentage switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-info",
            >= 70 => "bg-warning",
            >= 60 => "bg-secondary",
            _ => "bg-danger"
        };
    }

    private string GetGradeClass(decimal percentage)
    {
        return percentage switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-info",
            >= 70 => "bg-warning",
            >= 60 => "bg-secondary",
            _ => "bg-danger"
        };
    }

    private string GetSubjectCardClass(double average)
    {
        return average switch
        {
            >= 90 => "border-success",
            >= 80 => "border-info",
            >= 70 => "border-warning",
            >= 60 => "border-secondary",
            _ => "border-danger"
        };
    }

    private string GetLetterGrade(decimal percentage)
    {
        return percentage switch
        {
            >= 95 => "A+",
            >= 90 => "A",
            >= 85 => "B+",
            >= 80 => "B",
            >= 75 => "C+",
            >= 70 => "C",
            >= 65 => "D+",
            >= 60 => "D",
            _ => "F"
        };
    }

    private string GetLetterGrade(double percentage)
    {
        return GetLetterGrade((decimal)percentage);
    }
}
