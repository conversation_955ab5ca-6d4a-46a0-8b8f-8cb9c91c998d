@page "/parent/child/{StudentId}"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Parent")]

<PageTitle>تفاصيل الطفل - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/parent/dashboard">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active">تفاصيل الطفل</li>
                </ol>
            </nav>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل بيانات الطفل...</p>
        </div>
    }
    else if (childData != null)
    {
        <!-- Student Info Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-lg">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <div class="avatar-xl bg-primary text-white rounded-circle d-flex align-items-center justify-content-center">
                                    @if (!string.IsNullOrEmpty(childData.ProfilePicture))
                                    {
                                        <img src="@childData.ProfilePicture" class="rounded-circle" style="width: 80px; height: 80px; object-fit: cover;" alt="@childData.StudentName" />
                                    }
                                    else
                                    {
                                        <span class="fs-2">@childData.StudentName.Substring(0, 1)</span>
                                    }
                                </div>
                            </div>
                            <div class="col">
                                <h3 class="mb-1">@childData.StudentName</h3>
                                <p class="text-muted mb-2">
                                    <i class="fas fa-id-card me-2"></i>
                                    رقم الطالب: @childData.StudentNumber
                                </p>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-school me-2"></i>
                                    الصف: @childData.ClassName
                                </p>
                            </div>
                            <div class="col-auto">
                                <div class="performance-indicator-large @GetPerformanceClass(childData.PerformanceTrend)">
                                    <i class="fas @GetPerformanceIcon(childData.PerformanceTrend) fa-2x"></i>
                                </div>
                                <p class="text-center mt-2 mb-0">
                                    <small class="text-muted">الأداء العام</small>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="row g-3 mb-4">
            <div class="col-md-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="progress-circle mb-3" data-percentage="@childData.AverageGrade">
                            <div class="progress-circle-inner">
                                <span class="progress-percentage">@childData.AverageGrade.ToString("F1")</span>
                            </div>
                        </div>
                        <h6 class="text-success">المعدل الأكاديمي</h6>
                        <p class="text-muted mb-0">من 100 درجة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="progress-circle mb-3" data-percentage="@childData.AttendanceRate">
                            <div class="progress-circle-inner">
                                <span class="progress-percentage">@childData.AttendanceRate.ToString("F1")%</span>
                            </div>
                        </div>
                        <h6 class="text-info">معدل الحضور</h6>
                        <p class="text-muted mb-0">نسبة الحضور</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="progress-circle mb-3" data-percentage="@GetAssignmentProgress()">
                            <div class="progress-circle-inner">
                                <span class="progress-percentage">@(childData.TotalAssignments - childData.PendingAssignments)/@childData.TotalAssignments</span>
                            </div>
                        </div>
                        <h6 class="text-warning">الواجبات المكتملة</h6>
                        <p class="text-muted mb-0">@childData.PendingAssignments واجب معلق</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity Tabs -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <ul class="nav nav-tabs card-header-tabs" role="tablist">
                            <li class="nav-item">
                                <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#grades-tab" type="button">
                                    <i class="fas fa-star me-2"></i>
                                    الدرجات الأخيرة
                                </button>
                            </li>
                            <li class="nav-item">
                                <button class="nav-link" data-bs-toggle="tab" data-bs-target="#attendance-tab" type="button">
                                    <i class="fas fa-calendar-check me-2"></i>
                                    سجل الحضور
                                </button>
                            </li>
                            <li class="nav-item">
                                <button class="nav-link" data-bs-toggle="tab" data-bs-target="#assignments-tab" type="button">
                                    <i class="fas fa-tasks me-2"></i>
                                    الواجبات
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content">
                            <!-- Grades Tab -->
                            <div class="tab-pane fade show active" id="grades-tab">
                                @if (childData.RecentGrades?.Any() == true)
                                {
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>المادة</th>
                                                    <th>نوع الامتحان</th>
                                                    <th>الدرجة</th>
                                                    <th>النسبة المئوية</th>
                                                    <th>التقدير</th>
                                                    <th>التاريخ</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var grade in childData.RecentGrades)
                                                {
                                                    <tr>
                                                        <td>@grade.SubjectName</td>
                                                        <td>@grade.ExamType</td>
                                                        <td>@grade.Score / @grade.MaxScore</td>
                                                        <td>
                                                            <div class="progress" style="height: 20px;">
                                                                <div class="progress-bar @GetGradeColor((double)grade.Percentage)" style="width: @(grade.Percentage)%">
                                                                    @grade.Percentage.ToString("F1")%
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="badge @GetGradeBadgeColor((double)grade.Percentage)">@grade.Grade</span>
                                                        </td>
                                                        <td>@grade.Date.ToString("dd/MM/yyyy")</td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                }
                                else
                                {
                                    <div class="text-center py-4">
                                        <i class="fas fa-star fa-3x text-muted mb-3"></i>
                                        <h6 class="text-muted">لا توجد درجات حديثة</h6>
                                    </div>
                                }
                            </div>

                            <!-- Attendance Tab -->
                            <div class="tab-pane fade" id="attendance-tab">
                                @if (childData.RecentAttendance?.Any() == true)
                                {
                                    <div class="row g-2">
                                        @foreach (var attendance in childData.RecentAttendance.Take(14))
                                        {
                                            <div class="col-md-3 col-sm-4 col-6">
                                                <div class="card border @(attendance.IsPresent ? "border-success" : "border-danger") h-100">
                                                    <div class="card-body text-center p-2">
                                                        <div class="mb-2">
                                                            <i class="fas @(attendance.IsPresent ? "fa-check-circle text-success" : "fa-times-circle text-danger") fa-2x"></i>
                                                        </div>
                                                        <h6 class="mb-1">@attendance.Date.ToString("dd/MM")</h6>
                                                        <small class="text-muted">@attendance.Date.ToString("dddd", new System.Globalization.CultureInfo("ar-SA"))</small>
                                                        @if (attendance.IsPresent && attendance.CheckInTime.HasValue)
                                                        {
                                                            <div class="mt-2">
                                                                <small class="text-success">@attendance.CheckInTime.Value.ToString(@"hh\:mm")</small>
                                                            </div>
                                                        }
                                                        @if (!attendance.IsPresent && !string.IsNullOrEmpty(attendance.Reason))
                                                        {
                                                            <div class="mt-2">
                                                                <small class="text-danger">@attendance.Reason</small>
                                                            </div>
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                }
                                else
                                {
                                    <div class="text-center py-4">
                                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                        <h6 class="text-muted">لا توجد سجلات حضور حديثة</h6>
                                    </div>
                                }
                            </div>

                            <!-- Assignments Tab -->
                            <div class="tab-pane fade" id="assignments-tab">
                                <div class="text-center py-4">
                                    <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                                    <h6 class="text-muted">سيتم إضافة تفاصيل الواجبات قريباً</h6>
                                    <p class="text-muted">@childData.PendingAssignments واجب معلق من أصل @childData.TotalAssignments</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h5 class="text-muted">لم يتم العثور على بيانات الطفل</h5>
            <p class="text-muted">تأكد من صحة الرابط أو تواصل مع الإدارة</p>
            <a href="/parent/dashboard" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للوحة التحكم
            </a>
        </div>
    }
</div>

<style>
    .avatar-xl {
        width: 80px;
        height: 80px;
    }

    .performance-indicator-large {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }

    .performance-improving {
        background-color: #28a745;
    }

    .performance-declining {
        background-color: #dc3545;
    }

    .performance-stable {
        background-color: #6c757d;
    }

    .progress-circle {
        position: relative;
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background: conic-gradient(#007bff 0deg, #007bff calc(var(--percentage) * 3.6deg), #e9ecef calc(var(--percentage) * 3.6deg), #e9ecef 360deg);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
    }

    .progress-circle-inner {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    .progress-percentage {
        font-weight: bold;
        font-size: 14px;
        color: #333;
    }

    .nav-tabs .nav-link {
        border: none;
        color: #6c757d;
    }

    .nav-tabs .nav-link.active {
        background-color: transparent;
        border-bottom: 2px solid #007bff;
        color: #007bff;
    }
</style>

@code {
    [Parameter] public string StudentId { get; set; } = string.Empty;

    private ChildPerformanceDto? childData;
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadChildData();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (!string.IsNullOrEmpty(StudentId))
        {
            await LoadChildData();
        }
    }

    private async Task LoadChildData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // Get current parent ID
            var currentParentId = await ApiService.GetCurrentUserIdAsync();

            if (!string.IsNullOrEmpty(currentParentId))
            {
                var parentDashboard = await ApiService.GetParentDashboardAsync(currentParentId);
                childData = parentDashboard?.Children?.FirstOrDefault(c => c.StudentId == StudentId);
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading child data: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ في تحميل بيانات الطفل");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private string GetPerformanceClass(string trend)
    {
        return trend.ToLower() switch
        {
            "improving" => "performance-improving",
            "declining" => "performance-declining",
            _ => "performance-stable"
        };
    }

    private string GetPerformanceIcon(string trend)
    {
        return trend.ToLower() switch
        {
            "improving" => "fa-arrow-up",
            "declining" => "fa-arrow-down",
            _ => "fa-minus"
        };
    }

    private double GetAssignmentProgress()
    {
        if (childData == null || childData.TotalAssignments == 0) return 0;
        var completed = childData.TotalAssignments - childData.PendingAssignments;
        return (double)completed / childData.TotalAssignments * 100;
    }

    private string GetGradeColor(double percentage)
    {
        return percentage switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-info",
            >= 70 => "bg-warning",
            >= 60 => "bg-secondary",
            _ => "bg-danger"
        };
    }

    private string GetGradeBadgeColor(double percentage)
    {
        return percentage switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-info",
            >= 70 => "bg-warning",
            >= 60 => "bg-secondary",
            _ => "bg-danger"
        };
    }
}
