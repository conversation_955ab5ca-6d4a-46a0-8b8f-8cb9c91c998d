@page "/teacher/attendance-tracker"
@using Schools.Shared.DTOs
@using Schools.Client.Services
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-gradient-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-check me-2"></i>
                        متتبع الحضور المتقدم
                    </h4>
                </div>

                <div class="card-body">
                    <!-- Date and Class Selection -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <label class="form-label">التاريخ</label>
                            <input type="date" class="form-control" @bind="selectedDate" @bind:after="LoadAttendanceData" />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">الصف</label>
                            <select class="form-select" @bind="selectedClassId" @bind:after="LoadStudents">
                                <option value="">اختر الصف</option>
                                @if (classes != null)
                                {
                                    @foreach (var cls in classes)
                                    {
                                        <option value="@cls.Id">@cls.Name</option>
                                    }
                                }
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">الحصة</label>
                            <select class="form-select" @bind="selectedPeriod">
                                <option value="">اختر الحصة</option>
                                <option value="1">الحصة الأولى</option>
                                <option value="2">الحصة الثانية</option>
                                <option value="3">الحصة الثالثة</option>
                                <option value="4">الحصة الرابعة</option>
                                <option value="5">الحصة الخامسة</option>
                                <option value="6">الحصة السادسة</option>
                            </select>
                        </div>
                    </div>

                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-success" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else if (students?.Any() == true)
                    {
                        <!-- Quick Stats -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h4>@students.Count</h4>
                                        <p class="mb-0">إجمالي الطلاب</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h4>@GetPresentCount()</h4>
                                        <p class="mb-0">حاضر</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h4>@GetAbsentCount()</h4>
                                        <p class="mb-0">غائب</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h4>@GetLateCount()</h4>
                                        <p class="mb-0">متأخر</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="btn-group w-100" role="group">
                                    <button class="btn btn-success" @onclick="MarkAllPresent">
                                        <i class="fas fa-check-circle me-2"></i>
                                        تحديد الكل حاضر
                                    </button>
                                    <button class="btn btn-danger" @onclick="MarkAllAbsent">
                                        <i class="fas fa-times-circle me-2"></i>
                                        تحديد الكل غائب
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" placeholder="البحث عن طالب..."
                                           @bind="searchTerm" @oninput="FilterStudents" />
                                </div>
                            </div>
                        </div>

                        <!-- Students Attendance List -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">قائمة الحضور</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead class="table-dark">
                                                    <tr>
                                                        <th style="width: 5%">#</th>
                                                        <th style="width: 25%">اسم الطالب</th>
                                                        <th style="width: 20%">البريد الإلكتروني</th>
                                                        <th style="width: 20%">الحالة</th>
                                                        <th style="width: 15%">وقت الوصول</th>
                                                        <th style="width: 15%">ملاحظات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @if (filteredStudents?.Any() == true)
                                                    {
                                                        @for (int i = 0; i < filteredStudents.Count; i++)
                                                        {
                                                            var index = i;
                                                            var student = filteredStudents[index];
                                                            var attendance = GetOrCreateAttendance(student.Id);

                                                            <tr class="@GetRowClass(attendance.Status)">
                                                                <td>@(index + 1)</td>
                                                                <td>
                                                                    <div class="d-flex align-items-center">
                                                                        <div class="status-indicator @GetStatusIndicatorClass(attendance.Status) me-2"></div>
                                                                        <div>
                                                                            <strong>@student.FirstName @student.LastName</strong>
                                                                            <br>
                                                                            <small class="text-muted">@student.PhoneNumber</small>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <small>@student.Email</small>
                                                                </td>
                                                                <td>
                                                                    <div class="btn-group btn-group-sm" role="group">
                                                                        <input type="radio" class="btn-check" name="<EMAIL>" id="<EMAIL>"
                                                                               checked="@(attendance.Status == "Present")"
                                                                               @onchange="@((e) => UpdateAttendanceStatus(student.Id, "Present"))" />
                                                                        <label class="btn btn-outline-success" for="<EMAIL>">
                                                                            <i class="fas fa-check"></i> حاضر
                                                                        </label>

                                                                        <input type="radio" class="btn-check" name="<EMAIL>" id="<EMAIL>"
                                                                               checked="@(attendance.Status == "Absent")"
                                                                               @onchange="@((e) => UpdateAttendanceStatus(student.Id, "Absent"))" />
                                                                        <label class="btn btn-outline-danger" for="<EMAIL>">
                                                                            <i class="fas fa-times"></i> غائب
                                                                        </label>

                                                                        <input type="radio" class="btn-check" name="<EMAIL>" id="<EMAIL>"
                                                                               checked="@(attendance.Status == "Late")"
                                                                               @onchange="@((e) => UpdateAttendanceStatus(student.Id, "Late"))" />
                                                                        <label class="btn btn-outline-warning" for="<EMAIL>">
                                                                            <i class="fas fa-clock"></i> متأخر
                                                                        </label>

                                                                        <input type="radio" class="btn-check" name="<EMAIL>" id="<EMAIL>"
                                                                               checked="@(attendance.Status == "Excused")"
                                                                               @onchange="@((e) => UpdateAttendanceStatus(student.Id, "Excused"))" />
                                                                        <label class="btn btn-outline-info" for="<EMAIL>">
                                                                            <i class="fas fa-user-shield"></i> معذور
                                                                        </label>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    @if (attendance.Status == "Late" || attendance.Status == "Present")
                                                                    {
                                                                        <input type="time" class="form-control form-control-sm"
                                                                               value="@attendance.ArrivalTime"
                                                                               @onchange="@((e) => attendance.ArrivalTime = e.Value?.ToString() ?? "")" />
                                                                    }
                                                                </td>
                                                                <td>
                                                                    <input type="text" class="form-control form-control-sm"
                                                                           placeholder="ملاحظات..." @bind="attendance.Notes" />
                                                                </td>
                                                            </tr>
                                                        }
                                                    }
                                                    else
                                                    {
                                                        <tr>
                                                            <td colspan="6" class="text-center py-4">
                                                                <i class="fas fa-users fa-2x text-muted mb-2"></i>
                                                                <p class="text-muted">لا يوجد طلاب</p>
                                                            </td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Save Button -->
                        <div class="row mt-4">
                            <div class="col-12 text-center">
                                <button class="btn btn-success btn-lg" @onclick="SaveAttendance" disabled="@isSaving">
                                    @if (isSaving)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2"></span>
                                    }
                                    <i class="fas fa-save me-2"></i>
                                    حفظ الحضور
                                </button>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-user-check fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">اختر صفاً لبدء تسجيل الحضور</h5>
                            <p class="text-muted">حدد الصف والتاريخ لعرض قائمة الطلاب</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
    }

    .status-present { background-color: #28a745; }
    .status-absent { background-color: #dc3545; }
    .status-late { background-color: #ffc107; }
    .status-excused { background-color: #17a2b8; }
    .status-unknown { background-color: #6c757d; }

    .row-present { background-color: rgba(40, 167, 69, 0.1); }
    .row-absent { background-color: rgba(220, 53, 69, 0.1); }
    .row-late { background-color: rgba(255, 193, 7, 0.1); }
    .row-excused { background-color: rgba(23, 162, 184, 0.1); }
</style>

@code {
    private List<UserDto>? students;
    private List<UserDto>? filteredStudents;
    private List<ClassDto>? classes;
    private List<AttendanceInput> attendanceList = new();

    private bool isLoading = false;
    private bool isSaving = false;

    private DateTime selectedDate = DateTime.Today;
    private int? selectedClassId;
    private string selectedPeriod = "";
    private string searchTerm = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadClasses();
    }

    private async Task LoadClasses()
    {
        try
        {
            classes = (await ApiService.GetClassesAsync()).ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الصفوف: {ex.Message}");
        }
    }

    private async Task LoadStudents()
    {
        if (!selectedClassId.HasValue) return;

        try
        { 
            isLoading = true;

            students = (await ApiService.GetClassStudentsAsync(selectedClassId.Value)).ToList();
            filteredStudents = students.ToList();

            // Initialize attendance list
            attendanceList = students.Select(s => new AttendanceInput
            {
                StudentId = s.Id,
                Status = "Present", // Default to present
                ArrivalTime = DateTime.Now.ToString("HH:mm"),
                Notes = ""
            }).ToList();

            await LoadAttendanceData();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الطلاب: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadAttendanceData()
    {
        if (!selectedClassId.HasValue || students?.Any() != true) return;

        try
        {
            // Load existing attendance for the selected date
            var existingAttendance = await ApiService.GetAttendanceAsync(selectedClassId, selectedDate, null, 1, 1000);

            foreach (var attendance in existingAttendance)
            {
                var existingRecord = attendanceList.FirstOrDefault(a => a.StudentId == attendance.StudentId);
                if (existingRecord != null)
                {
                    existingRecord.Status = attendance.Status;
                    existingRecord.ArrivalTime = attendance.ArrivalTime ?? DateTime.Now.ToString("HH:mm");
                    existingRecord.Notes = attendance.Notes ?? "";
                }
            }
        }
        catch (Exception ex)
        {
            // If no existing attendance found, keep defaults
        }

        StateHasChanged();
    }

    private void FilterStudents()
    {
        if (students == null) return;

        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            filteredStudents = students.ToList();
        }
        else
        {
            filteredStudents = students.Where(s =>
                s.FirstName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                s.LastName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                s.Email.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                s.PhoneNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
            ).ToList();
        }

        StateHasChanged();
    }

    private AttendanceInput GetOrCreateAttendance(string studentId)
    {
        var attendance = attendanceList.FirstOrDefault(a => a.StudentId == studentId);
        if (attendance == null)
        {
            attendance = new AttendanceInput
            {
                StudentId = studentId,
                Status = "Present",
                ArrivalTime = DateTime.Now.ToString("HH:mm"),
                Notes = ""
            };
            attendanceList.Add(attendance);
        }
        return attendance;
    }

    private void UpdateAttendanceStatus(string studentId, string status)
    {
        var attendance = GetOrCreateAttendance(studentId);
        attendance.Status = status;

        // Set default arrival time for present/late students
        if ((status == "Present" || status == "Late") && string.IsNullOrEmpty(attendance.ArrivalTime))
        {
            attendance.ArrivalTime = DateTime.Now.ToString("HH:mm");
        }

        StateHasChanged();
    }

    private void MarkAllPresent()
    {
        foreach (var attendance in attendanceList)
        {
            attendance.Status = "Present";
            if (string.IsNullOrEmpty(attendance.ArrivalTime))
            {
                attendance.ArrivalTime = DateTime.Now.ToString("HH:mm");
            }
        }
        StateHasChanged();
    }

    private void MarkAllAbsent()
    {
        foreach (var attendance in attendanceList)
        {
            attendance.Status = "Absent";
            attendance.ArrivalTime = "";
        }
        StateHasChanged();
    }

    private async Task SaveAttendance()
    {
        if (!selectedClassId.HasValue || !attendanceList.Any())
        {
            await JSRuntime.InvokeVoidAsync("alert", "لا توجد بيانات حضور للحفظ");
            return;
        }

        try
        {
            isSaving = true;

            var bulkAttendance = new BulkAttendanceDto
            {
                ClassId = selectedClassId.Value,
                Date = selectedDate,
                StudentAttendances = attendanceList.Select(a => new StudentAttendanceDto
                {
                    StudentId = a.StudentId,
                    Status = a.Status,
                    ArrivalTime = !string.IsNullOrEmpty(a.ArrivalTime) ? TimeSpan.Parse(a.ArrivalTime) : null,
                    Notes = a.Notes
                }).ToList()
            };

            var success = await ApiService.CreateBulkAttendanceAsync(bulkAttendance);

            if (success)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"تم حفظ حضور {attendanceList.Count} طالب بنجاح");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ أثناء حفظ الحضور");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ الحضور: {ex.Message}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private int GetPresentCount()
    {
        return attendanceList.Count(a => a.Status == "Present");
    }

    private int GetAbsentCount()
    {
        return attendanceList.Count(a => a.Status == "Absent");
    }

    private int GetLateCount()
    {
        return attendanceList.Count(a => a.Status == "Late");
    }

    private string GetRowClass(string status)
    {
        return status switch
        {
            "Present" => "row-present",
            "Absent" => "row-absent",
            "Late" => "row-late",
            "Excused" => "row-excused",
            _ => ""
        };
    }

    private string GetStatusIndicatorClass(string status)
    {
        return status switch
        {
            "Present" => "status-present",
            "Absent" => "status-absent",
            "Late" => "status-late",
            "Excused" => "status-excused",
            _ => "status-unknown"
        };
    }

    public class AttendanceInput
    {
        public string StudentId { get; set; } = "";
        public string Status { get; set; } = "Present";
        public string ArrivalTime { get; set; } = "";
        public string Notes { get; set; } = "";
    }
}
