using Microsoft.Extensions.Options;
using Schools.Shared.Models;

namespace Schools.API.Services
{
    public interface ISettingsService
    {
        JwtSettings JwtSettings { get; }
        SchoolSettings SchoolSettings { get; }
        LibrarySettings LibrarySettings { get; }
        AttendanceSettings AttendanceSettings { get; }
        GradingSettings GradingSettings { get; }
        ActivitySettings ActivitySettings { get; }
        NotificationSettings NotificationSettings { get; }
        FileUploadSettings FileUploadSettings { get; }
        SecuritySettings SecuritySettings { get; }
        ApiSettings ApiSettings { get; }
        DatabaseSettings DatabaseSettings { get; }
        CacheSettings CacheSettings { get; }
        EmailSettings EmailSettings { get; }
        SmsSettings SmsSettings { get; }
        ReportSettings ReportSettings { get; }
    }

    public class SettingsService : ISettingsService
    {
        public SettingsService(
            IOptions<JwtSettings> jwtSettings,
            IOptions<SchoolSettings> schoolSettings,
            IOptions<LibrarySettings> librarySettings,
            IOptions<AttendanceSettings> attendanceSettings,
            IOptions<GradingSettings> gradingSettings,
            IOptions<ActivitySettings> activitySettings,
            IOptions<NotificationSettings> notificationSettings,
            IOptions<FileUploadSettings> fileUploadSettings,
            IOptions<SecuritySettings> securitySettings,
            IOptions<ApiSettings> apiSettings,
            IOptions<DatabaseSettings> databaseSettings,
            IOptions<CacheSettings> cacheSettings,
            IOptions<EmailSettings> emailSettings,
            IOptions<SmsSettings> smsSettings,
            IOptions<ReportSettings> reportSettings)
        {
            JwtSettings = jwtSettings.Value;
            SchoolSettings = schoolSettings.Value;
            LibrarySettings = librarySettings.Value;
            AttendanceSettings = attendanceSettings.Value;
            GradingSettings = gradingSettings.Value;
            ActivitySettings = activitySettings.Value;
            NotificationSettings = notificationSettings.Value;
            FileUploadSettings = fileUploadSettings.Value;
            SecuritySettings = securitySettings.Value;
            ApiSettings = apiSettings.Value;
            DatabaseSettings = databaseSettings.Value;
            CacheSettings = cacheSettings.Value;
            EmailSettings = emailSettings.Value;
            SmsSettings = smsSettings.Value;
            ReportSettings = reportSettings.Value;
        }

        public JwtSettings JwtSettings { get; }
        public SchoolSettings SchoolSettings { get; }
        public LibrarySettings LibrarySettings { get; }
        public AttendanceSettings AttendanceSettings { get; }
        public GradingSettings GradingSettings { get; }
        public ActivitySettings ActivitySettings { get; }
        public NotificationSettings NotificationSettings { get; }
        public FileUploadSettings FileUploadSettings { get; }
        public SecuritySettings SecuritySettings { get; }
        public ApiSettings ApiSettings { get; }
        public DatabaseSettings DatabaseSettings { get; }
        public CacheSettings CacheSettings { get; }
        public EmailSettings EmailSettings { get; }
        public SmsSettings SmsSettings { get; }
        public ReportSettings ReportSettings { get; }
    }
}
