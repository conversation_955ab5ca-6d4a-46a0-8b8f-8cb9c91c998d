using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;
using System.Security.Claims;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SettingsController : ControllerBase
    {
        private readonly SchoolsDbContext _context;

        public SettingsController(SchoolsDbContext context)
        {
            _context = context;
        }

        #region General Settings

        [HttpGet("general")]
        public async Task<ActionResult<object>> GetGeneralSettings()
        {
            try
            {
                var settings = new
                {
                    InstitutionName = "مدرسة النموذجية",
                    DefaultCurrency = "SAR",
                    FinancialYear = DateTime.Now.Year,
                    EnableAutoBackup = true,
                    RequireApprovalForVouchers = true,
                    PaymentGracePeriod = 30,
                    LateFeePercentage = 5.0m,
                    AllowPartialPayments = true,
                    AutoSendReminders = true,
                    ReminderFrequency = 7,
                    DefaultCashAccountId = 1,
                    DefaultRevenueAccountId = 2,
                    AutoGenerateVoucherNumbers = true,
                    ReceiptVoucherPrefix = "RV",
                    PaymentVoucherPrefix = "PV",
                    EmailNotifications = true,
                    SmsNotifications = false,
                    FinanceNotificationEmail = "<EMAIL>",
                    NotifyOnOverduePayments = true,
                    NotifyOnLowBalance = true,
                    LowBalanceThreshold = 10000
                };

                return Ok(settings);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب الإعدادات العامة", error = ex.Message });
            }
        }

        [HttpPut("general")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UpdateGeneralSettings([FromBody] object settings)
        {
            try
            {
                // In a real implementation, you would save these settings to a database table
                // For now, we'll just return success
                return Ok(new { message = "تم تحديث الإعدادات العامة بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحديث الإعدادات العامة", error = ex.Message });
            }
        }

        #endregion

        #region Accounting Settings

        [HttpGet("accounting")]
        public async Task<ActionResult<object>> GetAccountingSettings()
        {
            try
            {
                var settings = new
                {
                    InstitutionName = "مدرسة النموذجية",
                    DefaultCurrency = "SAR",
                    FinancialYear = DateTime.Now.Year,
                    EnableAutoBackup = true,
                    RequireApprovalForVouchers = true,
                    PaymentGracePeriod = 30,
                    LateFeePercentage = 5.0m,
                    AllowPartialPayments = true,
                    AutoSendReminders = true,
                    ReminderFrequency = 7,
                    DefaultCashAccountId = 1,
                    DefaultRevenueAccountId = 2,
                    AutoGenerateVoucherNumbers = true,
                    ReceiptVoucherPrefix = "RV",
                    PaymentVoucherPrefix = "PV",
                    EmailNotifications = true,
                    SmsNotifications = false,
                    FinanceNotificationEmail = "<EMAIL>",
                    NotifyOnOverduePayments = true,
                    NotifyOnLowBalance = true,
                    LowBalanceThreshold = 10000
                };

                return Ok(settings);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب إعدادات المحاسبة", error = ex.Message });
            }
        }

        [HttpPut("accounting")]
        [Authorize(Roles = "Admin,Accountant")]
        public async Task<IActionResult> UpdateAccountingSettings([FromBody] object settings)
        {
            try
            {
                // In a real implementation, you would save these settings to a database table
                return Ok(new { message = "تم تحديث إعدادات المحاسبة بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحديث إعدادات المحاسبة", error = ex.Message });
            }
        }

        #endregion

        #region Academic Settings

        [HttpGet("academic")]
        public async Task<ActionResult<object>> GetAcademicSettings()
        {
            try
            {
                var currentYear = await _context.AcademicYears
                    .FirstOrDefaultAsync(ay => ay.IsCurrent && ay.IsActive);

                var settings = new
                {
                    CurrentAcademicYearId = currentYear?.Id ?? 0,
                    CurrentAcademicYearName = currentYear?.Name ?? "",
                    DefaultClassCapacity = 30,
                    DefaultSectionCapacity = 25,
                    AllowStudentTransfer = true,
                    RequireParentApproval = true,
                    AutoGenerateStudentNumbers = true,
                    StudentNumberPrefix = "STU",
                    AutoGenerateEmployeeNumbers = true,
                    EmployeeNumberPrefix = "EMP",
                    DefaultGradingScale = "Percentage",
                    PassingGrade = 50,
                    MaxAbsenceDays = 30,
                    EnableAttendanceTracking = true,
                    EnableGradeTracking = true,
                    EnableExamModule = true,
                    EnableLibraryModule = true,
                    EnableActivitiesModule = true
                };

                return Ok(settings);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب الإعدادات الأكاديمية", error = ex.Message });
            }
        }

        [HttpPut("academic")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UpdateAcademicSettings([FromBody] object settings)
        {
            try
            {
                return Ok(new { message = "تم تحديث الإعدادات الأكاديمية بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحديث الإعدادات الأكاديمية", error = ex.Message });
            }
        }

        #endregion

        #region System Information

        [HttpGet("system-info")]
        public async Task<ActionResult<object>> GetSystemInfo()
        {
            try
            {
                var systemInfo = new
                {
                    TotalStudents = await _context.StudentEnrollments.CountAsync(se => se.IsActive),
                    TotalVouchers = await _context.ReceiptVouchers.CountAsync(rv => !rv.IsDeleted) +
                                   await _context.PaymentVouchers.CountAsync(pv => !pv.IsDeleted),
                    TotalAccounts = await _context.Accounts.CountAsync(a => a.IsActive && !a.IsDeleted),
                    LastBackup = "لم يتم",
                    SystemVersion = "1.0.0",
                    DatabaseSize = "25 MB",
                    LastLogin = DateTime.Now.ToString("yyyy-MM-dd HH:mm"),
                    ActiveUsers = 1,
                    TotalTeachers = await _context.TeacherAssignments
                        .Select(ta => ta.TeacherId)
                        .Distinct()
                        .CountAsync(),
                    TotalClasses = await _context.Classes.CountAsync(c => c.IsActive),
                    TotalSubjects = await _context.Subjects.CountAsync(s => s.IsActive),
                    TotalExams = await _context.Exams.CountAsync(e => e.IsActive),
                    PendingApprovals = await _context.ReceiptVouchers
                        .CountAsync(rv => rv.Status == VoucherStatus.Draft && !rv.IsDeleted) +
                        await _context.PaymentVouchers
                        .CountAsync(pv => pv.Status == VoucherStatus.Draft && !pv.IsDeleted)
                };

                return Ok(systemInfo);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب معلومات النظام", error = ex.Message });
            }
        }

        #endregion

        #region User Preferences

        [HttpGet("user-preferences")]
        public async Task<ActionResult<object>> GetUserPreferences()
        {
            try
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                
                // In a real implementation, you would load user preferences from database
                var preferences = new
                {
                    Language = "ar",
                    Theme = "light",
                    DateFormat = "dd/MM/yyyy",
                    TimeFormat = "24h",
                    Currency = "SAR",
                    Timezone = "Asia/Riyadh",
                    NotificationsEnabled = true,
                    EmailNotifications = true,
                    SmsNotifications = false,
                    DashboardLayout = "default",
                    ItemsPerPage = 20,
                    AutoSave = true,
                    ShowTutorials = true
                };

                return Ok(preferences);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب تفضيلات المستخدم", error = ex.Message });
            }
        }

        [HttpPut("user-preferences")]
        public async Task<IActionResult> UpdateUserPreferences([FromBody] object preferences)
        {
            try
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                
                // In a real implementation, you would save user preferences to database
                return Ok(new { message = "تم تحديث تفضيلات المستخدم بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحديث تفضيلات المستخدم", error = ex.Message });
            }
        }

        #endregion

        #region Backup & Restore

        [HttpPost("backup")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> CreateBackup()
        {
            try
            {
                // In a real implementation, you would create a database backup
                await Task.Delay(1000); // Simulate backup process
                
                return Ok(new { 
                    message = "تم إنشاء النسخة الاحتياطية بنجاح",
                    backupFile = $"backup_{DateTime.Now:yyyyMMdd_HHmmss}.bak",
                    size = "25 MB",
                    createdAt = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في إنشاء النسخة الاحتياطية", error = ex.Message });
            }
        }

        [HttpGet("backups")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<IEnumerable<object>>> GetBackups()
        {
            try
            {
                // In a real implementation, you would list available backup files
                var backups = new[]
                {
                    new { 
                        fileName = "backup_20241201_120000.bak",
                        size = "25 MB",
                        createdAt = DateTime.Now.AddDays(-1),
                        type = "Full"
                    },
                    new { 
                        fileName = "backup_20241130_120000.bak",
                        size = "24 MB",
                        createdAt = DateTime.Now.AddDays(-2),
                        type = "Full"
                    }
                };

                return Ok(backups);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب قائمة النسخ الاحتياطية", error = ex.Message });
            }
        }

        [HttpPost("restore")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> RestoreBackup([FromBody] string backupFileName)
        {
            try
            {
                // In a real implementation, you would restore from the specified backup file
                await Task.Delay(2000); // Simulate restore process
                
                return Ok(new { 
                    message = "تم استعادة النسخة الاحتياطية بنجاح",
                    restoredFrom = backupFileName,
                    restoredAt = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في استعادة النسخة الاحتياطية", error = ex.Message });
            }
        }

        #endregion

        #region System Maintenance

        [HttpPost("maintenance/clear-cache")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> ClearCache()
        {
            try
            {
                // In a real implementation, you would clear application cache
                await Task.Delay(500);
                
                return Ok(new { message = "تم مسح ذاكرة التخزين المؤقت بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في مسح ذاكرة التخزين المؤقت", error = ex.Message });
            }
        }

        [HttpPost("maintenance/optimize-database")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> OptimizeDatabase()
        {
            try
            {
                // In a real implementation, you would optimize database
                await Task.Delay(3000);
                
                return Ok(new { 
                    message = "تم تحسين قاعدة البيانات بنجاح",
                    sizeBefore = "30 MB",
                    sizeAfter = "25 MB",
                    spaceSaved = "5 MB"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحسين قاعدة البيانات", error = ex.Message });
            }
        }

        #endregion
    }
}
