@echo off
chcp 65001 >nul
title إيقاف نظام إدارة المدارس

echo.
echo ================================================
echo 🛑 إيقاف نظام إدارة المدارس
echo ================================================
echo.

echo 🔍 البحث عن عمليات النظام...

echo 🛑 إيقاف عمليات .NET...
taskkill /f /im dotnet.exe >nul 2>&1
if errorlevel 1 (
    echo ℹ️ لا توجد عمليات .NET قيد التشغيل
) else (
    echo ✅ تم إيقاف عمليات .NET
)

echo 🛑 إيقاف نوافذ النظام...
taskkill /f /fi "WINDOWTITLE:Schools API*" >nul 2>&1
taskkill /f /fi "WINDOWTITLE:Schools Client*" >nul 2>&1

echo ⏳ انتظار...
timeout /t 2 /nobreak >nul

echo.
echo ================================================
echo ✅ تم إيقاف النظام بنجاح!
echo ================================================
echo.
echo 💡 لإعادة تشغيل النظام:
echo    • قم بتشغيل start.bat
echo    • أو run.bat
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
