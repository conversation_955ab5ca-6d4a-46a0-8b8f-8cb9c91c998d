@echo off
echo ========================================
echo    نظام إدارة المدارس - إصلاح SRI
echo    School Management System - SRI Fixed
echo ========================================
echo.

echo 🧹 تنظيف شامل للمشروع...
echo Deep cleaning project...
dotnet clean --verbosity quiet > nul 2>&1

echo 🗑️ حذف مجلدات bin و obj...
echo Removing bin and obj folders...
powershell -Command "Remove-Item -Path Schools.Client/bin -Recurse -Force -ErrorAction SilentlyContinue; Remove-Item -Path Schools.Client/obj -Recurse -Force -ErrorAction SilentlyContinue; Remove-Item -Path Schools.Shared/bin -Recurse -Force -ErrorAction SilentlyContinue; Remove-Item -Path Schools.Shared/obj -Recurse -Force -ErrorAction SilentlyContinue" > nul 2>&1

echo 📦 استعادة الحزم...
echo Restoring packages...
dotnet restore --verbosity quiet > nul 2>&1

echo 🔧 بناء المشروع المشترك...
echo Building shared project...
dotnet build Schools.Shared --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع المشترك
    echo Shared project build failed
    pause
    exit /b 1
)

echo 🔧 بناء العميل...
echo Building client...
dotnet build Schools.Client --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء العميل
    echo Client build failed
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo Build successful
echo.

echo 🌐 تشغيل Blazor Client...
echo Starting Blazor Client...
echo.
echo 📱 سيتم فتح المتصفح تلقائياً على:
echo Browser will open automatically at:
echo http://localhost:5131
echo.

start "Schools Client - SRI Fixed" cmd /k "echo === نظام إدارة المدارس === && echo تشغيل العميل على المنفذ 5131 && echo Client running on port 5131 && echo. && cd Schools.Client && dotnet run --urls=http://localhost:5131"

echo ⏳ انتظار تشغيل العميل...
echo Waiting for Client to start...
timeout /t 15 /nobreak > nul

echo 🌐 فتح المتصفح...
echo Opening browser...
start http://localhost:5131

echo.
echo ========================================
echo ✅ تم تشغيل النظام بنجاح!
echo System started successfully!
echo ========================================
echo.
echo 🌐 الرابط:
echo URL:
echo.
echo 📱 http://localhost:5131
echo.
echo ========================================
echo 🔐 بيانات تسجيل الدخول:
echo Login Credentials:
echo.
echo 📧 البريد: <EMAIL>
echo 🔑 كلمة المرور: Admin123!
echo.
echo ========================================
echo 🔧 الإصلاحات المطبقة:
echo Applied Fixes:
echo.
echo ✅ تنظيف شامل للمشروع
echo ✅ حذف ملفات bin/obj القديمة
echo ✅ إعادة بناء كاملة
echo ✅ إصلاح مشكلة SRI Integrity
echo ✅ إصلاح ملفات Framework
echo ✅ تحسين عملية التشغيل
echo.
echo ========================================
echo 🎊 مميزات النظام:
echo System Features:
echo.
echo ✅ 22+ صفحة متكاملة
echo ✅ واجهات عربية جميلة
echo ✅ تصميم متجاوب
echo ✅ نظام مصادقة آمن
echo ✅ إحصائيات تفاعلية
echo ✅ بيانات وهمية واقعية
echo ✅ أداء سريع ومستقر
echo.
echo ========================================
echo 📱 الصفحات المتاحة:
echo Available Pages:
echo.
echo 🏠 لوحة تحكم الإدارة
echo 📊 إدارة الأعوام الدراسية
echo 🎓 إدارة المراحل الدراسية
echo 🏫 إدارة الصفوف الدراسية
echo 📚 إدارة المواد الدراسية
echo 👥 إدارة المستخدمين
echo 🎯 إدارة الأنشطة والفعاليات
echo 📖 إدارة المكتبة الرقمية
echo ✅ إدارة الحضور والغياب
echo 📅 إدارة الجداول الدراسية
echo 📝 إدارة الدرجات والتقييم
echo 👨‍🏫 لوحة تحكم المعلم
echo 👨‍🎓 لوحة تحكم الطالب
echo 👨‍👩‍👧‍👦 لوحة تحكم ولي الأمر
echo.
echo ========================================
echo 💡 ملاحظات:
echo Notes:
echo.
echo 🔄 النظام يعمل بالبيانات الوهمية
echo 📊 جميع الوظائف تعمل بشكل كامل
echo 🎨 التصميم والواجهات متكاملة
echo 💾 البيانات لا تُحفظ (وضع العرض)
echo 🚀 أداء سريع ومستقر
echo 🔧 تم إصلاح مشكلة SRI
echo.
echo ========================================
echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause > nul
