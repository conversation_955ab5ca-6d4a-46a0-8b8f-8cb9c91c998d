using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;
using System.Security.Claims;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ExamsController : ControllerBase
    {
        private readonly SchoolsDbContext _context;

        public ExamsController(SchoolsDbContext context)
        {
            _context = context;
        }

        #region Exams Management

        [HttpGet]
        public async Task<ActionResult<IEnumerable<ExamDto>>> GetExams()
        {
            try
            {
                var exams = await _context.Exams
                    .Include(e => e.Subject)
                    .Include(e => e.Class)
                    .ThenInclude(c => c.AcademicGrade)
                    .Include(e => e.Questions)
                    .Where(e => e.IsActive)
                    .Select(e => new ExamDto
                    {
                        Id = e.Id,
                        Title = e.Title,
                        Description = e.Description,
                        SubjectId = e.SubjectId,
                        SubjectName = e.Subject.Name,
                        ClassId = e.ClassId,
                        ClassName = e.Class.Name,
                        GradeName = e.Class.AcademicGrade.Name,
                        StartDate = e.StartDate,
                        EndDate = e.EndDate,
                        DurationMinutes = e.DurationMinutes,
                        TotalMarks = e.TotalMarks,
                        PassingMarks = e.PassingMarks,
                        MaxAttempts = e.MaxAttempts,
                        ShowResults = e.ShowResults,
                        ShuffleQuestions = e.ShuffleQuestions,
                        IsActive = e.IsActive,
                        CreatedBy = e.CreatedBy,
                        CreatedAt = e.CreatedAt,
                        TotalQuestions = e.Questions.Count,
                        Status = GetExamStatus(e.StartDate, e.EndDate)
                    })
                    .OrderByDescending(e => e.CreatedAt)
                    .ToListAsync();

                return Ok(exams);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب الامتحانات", error = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ExamDto>> GetExam(int id)
        {
            try
            {
                var exam = await _context.Exams
                    .Include(e => e.Subject)
                    .Include(e => e.Class)
                    .ThenInclude(c => c.AcademicGrade)
                    .Include(e => e.Questions)
                    .ThenInclude(q => q.Options)
                    .FirstOrDefaultAsync(e => e.Id == id && e.IsActive);

                if (exam == null)
                {
                    return NotFound(new { message = "الامتحان غير موجود" });
                }

                var examDto = new ExamDto
                {
                    Id = exam.Id,
                    Title = exam.Title,
                    Description = exam.Description,
                    SubjectId = exam.SubjectId,
                    SubjectName = exam.Subject.Name,
                    ClassId = exam.ClassId,
                    ClassName = exam.Class.Name,
                    GradeName = exam.Class.AcademicGrade.Name,
                    StartDate = exam.StartDate,
                    EndDate = exam.EndDate,
                    DurationMinutes = exam.DurationMinutes,
                    TotalMarks = exam.TotalMarks,
                    PassingMarks = exam.PassingMarks,
                    MaxAttempts = exam.MaxAttempts,
                    ShowResults = exam.ShowResults,
                    ShuffleQuestions = exam.ShuffleQuestions,
                    IsActive = exam.IsActive,
                    CreatedBy = exam.CreatedBy,
                    CreatedAt = exam.CreatedAt,
                    TotalQuestions = exam.Questions.Count,
                    Status = GetExamStatus(exam.StartDate, exam.EndDate),
                    Questions = exam.Questions.Select(q => new ExamQuestionDto
                    {
                        Id = q.Id,
                        QuestionText = q.QuestionText,
                        QuestionType = q.QuestionType,
                        Points = q.Points,
                        OrderIndex = q.OrderIndex,
                        Options = q.Options.Select(o => new QuestionOptionDto
                        {
                            Id = o.Id,
                            OptionText = o.OptionText,
                            IsCorrect = o.IsCorrect,
                            OrderIndex = o.OrderIndex
                        }).OrderBy(o => o.OrderIndex).ToList()
                    }).OrderBy(q => q.OrderIndex).ToList()
                };

                return Ok(examDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب الامتحان", error = ex.Message });
            }
        }

        [HttpPost]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<ActionResult<ExamDto>> CreateExam(CreateExamDto model)
        {
            try
            {
                var exam = new Exam
                {
                    Title = model.Title,
                    Description = model.Description,
                    SubjectId = model.SubjectId,
                    ClassId = model.ClassId,
                    StartDate = model.StartDate,
                    EndDate = model.EndDate,
                    DurationMinutes = model.DurationMinutes,
                    TotalMarks = model.TotalMarks,
                    PassingMarks = model.PassingMarks,
                    MaxAttempts = model.MaxAttempts,
                    ShowResults = model.ShowResults,
                    ShuffleQuestions = model.ShuffleQuestions,
                    IsActive = true,
                    CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System",
                    CreatedAt = DateTime.UtcNow
                };

                _context.Exams.Add(exam);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetExam), new { id = exam.Id }, new { id = exam.Id });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في إنشاء الامتحان", error = ex.Message });
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<IActionResult> UpdateExam(int id, UpdateExamDto model)
        {
            try
            {
                var exam = await _context.Exams.FindAsync(id);
                if (exam == null || !exam.IsActive)
                {
                    return NotFound(new { message = "الامتحان غير موجود" });
                }

                // Check if exam has started
                if (exam.StartDate <= DateTime.UtcNow)
                {
                    return BadRequest(new { message = "لا يمكن تعديل امتحان بدأ بالفعل" });
                }

                if (!string.IsNullOrEmpty(model.Title))
                    exam.Title = model.Title;
                if (!string.IsNullOrEmpty(model.Description))
                    exam.Description = model.Description;
                if (model.StartDate.HasValue)
                    exam.StartDate = model.StartDate.Value;
                if (model.EndDate.HasValue)
                    exam.EndDate = model.EndDate.Value;
                if (model.DurationMinutes.HasValue)
                    exam.DurationMinutes = model.DurationMinutes.Value;
                if (model.TotalMarks.HasValue)
                    exam.TotalMarks = model.TotalMarks.Value;
                if (model.PassingMarks.HasValue)
                    exam.PassingMarks = model.PassingMarks.Value;
                if (model.MaxAttempts.HasValue)
                    exam.MaxAttempts = model.MaxAttempts.Value;
                if (model.ShowResults.HasValue)
                    exam.ShowResults = model.ShowResults.Value;
                if (model.ShuffleQuestions.HasValue)
                    exam.ShuffleQuestions = model.ShuffleQuestions.Value;

                exam.UpdatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System";
                exam.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تحديث الامتحان بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحديث الامتحان", error = ex.Message });
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<IActionResult> DeleteExam(int id)
        {
            try
            {
                var exam = await _context.Exams.FindAsync(id);
                if (exam == null || !exam.IsActive)
                {
                    return NotFound(new { message = "الامتحان غير موجود" });
                }

                // Check if exam has attempts
                var hasAttempts = await _context.ExamAttempts
                    .AnyAsync(ea => ea.ExamId == id);

                if (hasAttempts)
                {
                    return BadRequest(new { message = "لا يمكن حذف امتحان له محاولات" });
                }

                exam.IsActive = false;
                exam.DeletedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System";
                exam.DeletedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم حذف الامتحان بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في حذف الامتحان", error = ex.Message });
            }
        }

        #endregion

        #region Questions Management

        [HttpPost("{id}/questions")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<IActionResult> AddQuestion(int id, CreateExamQuestionDto model)
        {
            try
            {
                var exam = await _context.Exams.FindAsync(id);
                if (exam == null || !exam.IsActive)
                {
                    return NotFound(new { message = "الامتحان غير موجود" });
                }

                // Check if exam has started
                if (exam.StartDate <= DateTime.UtcNow)
                {
                    return BadRequest(new { message = "لا يمكن إضافة أسئلة لامتحان بدأ بالفعل" });
                }

                var question = new ExamQuestion
                {
                    ExamId = id,
                    QuestionText = model.QuestionText,
                    QuestionType = model.QuestionType,
                    Points = model.Points,
                    OrderIndex = model.OrderIndex,
                    CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System",
                    CreatedAt = DateTime.UtcNow
                };

                _context.ExamQuestions.Add(question);
                await _context.SaveChangesAsync();

                // Add options if provided
                if (model.Options != null && model.Options.Any())
                {
                    foreach (var optionDto in model.Options)
                    {
                        var option = new QuestionOption
                        {
                            QuestionId = question.Id,
                            OptionText = optionDto.OptionText,
                            IsCorrect = optionDto.IsCorrect,
                            OrderIndex = optionDto.OrderIndex
                        };
                        _context.QuestionOptions.Add(option);
                    }
                    await _context.SaveChangesAsync();
                }

                return Ok(new { message = "تم إضافة السؤال بنجاح", questionId = question.Id });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في إضافة السؤال", error = ex.Message });
            }
        }

        [HttpPut("{examId}/questions/{questionId}")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<IActionResult> UpdateQuestion(int examId, int questionId, UpdateExamQuestionDto model)
        {
            try
            {
                var exam = await _context.Exams.FindAsync(examId);
                if (exam == null || !exam.IsActive)
                {
                    return NotFound(new { message = "الامتحان غير موجود" });
                }

                // Check if exam has started
                if (exam.StartDate <= DateTime.UtcNow)
                {
                    return BadRequest(new { message = "لا يمكن تعديل أسئلة امتحان بدأ بالفعل" });
                }

                var question = await _context.ExamQuestions
                    .Include(q => q.Options)
                    .FirstOrDefaultAsync(q => q.Id == questionId && q.ExamId == examId);

                if (question == null)
                {
                    return NotFound(new { message = "السؤال غير موجود" });
                }

                if (!string.IsNullOrEmpty(model.QuestionText))
                    question.QuestionText = model.QuestionText;
                if (model.Points.HasValue)
                    question.Points = model.Points.Value;
                if (model.OrderIndex.HasValue)
                    question.OrderIndex = model.OrderIndex.Value;

                question.UpdatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier) ?? "System";
                question.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تحديث السؤال بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تحديث السؤال", error = ex.Message });
            }
        }

        [HttpDelete("{examId}/questions/{questionId}")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<IActionResult> DeleteQuestion(int examId, int questionId)
        {
            try
            {
                var exam = await _context.Exams.FindAsync(examId);
                if (exam == null || !exam.IsActive)
                {
                    return NotFound(new { message = "الامتحان غير موجود" });
                }

                // Check if exam has started
                if (exam.StartDate <= DateTime.UtcNow)
                {
                    return BadRequest(new { message = "لا يمكن حذف أسئلة امتحان بدأ بالفعل" });
                }

                var question = await _context.ExamQuestions
                    .FirstOrDefaultAsync(q => q.Id == questionId && q.ExamId == examId);

                if (question == null)
                {
                    return NotFound(new { message = "السؤال غير موجود" });
                }

                _context.ExamQuestions.Remove(question);
                await _context.SaveChangesAsync();

                return Ok(new { message = "تم حذف السؤال بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في حذف السؤال", error = ex.Message });
            }
        }

        #endregion

        #region Student Exam Taking

        [HttpGet("student/{studentId}/available")]
        public async Task<ActionResult<IEnumerable<AvailableExamDto>>> GetAvailableExams(string studentId)
        {
            try
            {
                // Get student's current enrollment
                var enrollment = await _context.StudentEnrollments
                    .FirstOrDefaultAsync(se => se.StudentId == studentId && se.IsActive);

                if (enrollment == null)
                {
                    return BadRequest(new { message = "الطالب غير مسجل في أي صف" });
                }

                var now = DateTime.UtcNow;
                var availableExams = await _context.Exams
                    .Include(e => e.Subject)
                    .Include(e => e.Class)
                    .Where(e => e.ClassId == enrollment.ClassId &&
                               e.StartDate <= now &&
                               e.EndDate >= now &&
                               e.IsActive)
                    .Select(e => new AvailableExamDto
                    {
                        Id = e.Id,
                        Title = e.Title,
                        Description = e.Description,
                        SubjectName = e.Subject.Name,
                        StartDate = e.StartDate,
                        EndDate = e.EndDate,
                        DurationMinutes = e.DurationMinutes,
                        TotalMarks = e.TotalMarks,
                        TotalQuestions = e.Questions.Count,
                        MaxAttempts = e.MaxAttempts,
                        AttemptsUsed = e.ExamAttempts.Count(ea => ea.StudentId == studentId),
                        IsAvailable = true,
                        CanTakeExam = e.ExamAttempts.Count(ea => ea.StudentId == studentId) < e.MaxAttempts,
                        LastAttemptDate = e.ExamAttempts
                            .Where(ea => ea.StudentId == studentId)
                            .OrderByDescending(ea => ea.StartTime)
                            .Select(ea => ea.StartTime)
                            .FirstOrDefault(),
                        BestScore = e.ExamAttempts
                            .Where(ea => ea.StudentId == studentId && ea.IsCompleted)
                            .Max(ea => (double?)ea.Score),
                        Status = "متاح"
                    })
                    .ToListAsync();

                return Ok(availableExams);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب الامتحانات المتاحة", error = ex.Message });
            }
        }

        [HttpPost("{id}/start")]
        public async Task<ActionResult<ExamAttemptDto>> StartExam(int id, [FromBody] string studentId)
        {
            try
            {
                var exam = await _context.Exams
                    .Include(e => e.Questions)
                    .ThenInclude(q => q.Options)
                    .FirstOrDefaultAsync(e => e.Id == id && e.IsActive);

                if (exam == null)
                {
                    return NotFound(new { message = "الامتحان غير موجود" });
                }

                var now = DateTime.UtcNow;
                if (now < exam.StartDate || now > exam.EndDate)
                {
                    return BadRequest(new { message = "الامتحان غير متاح حالياً" });
                }

                // Check attempts
                var attemptsCount = await _context.ExamAttempts
                    .CountAsync(ea => ea.ExamId == id && ea.StudentId == studentId);

                if (attemptsCount >= exam.MaxAttempts)
                {
                    return BadRequest(new { message = "تم استنفاد عدد المحاولات المسموحة" });
                }

                // Check if student has an active attempt
                var activeAttempt = await _context.ExamAttempts
                    .FirstOrDefaultAsync(ea => ea.ExamId == id &&
                                             ea.StudentId == studentId &&
                                             !ea.IsCompleted);

                if (activeAttempt != null)
                {
                    return BadRequest(new { message = "لديك محاولة نشطة بالفعل" });
                }

                // Create new attempt
                var attempt = new ExamAttempt
                {
                    ExamId = id,
                    StudentId = studentId,
                    StartTime = now,
                    EndTime = now.AddMinutes(exam.DurationMinutes),
                    IsCompleted = false,
                    Score = 0,
                    CreatedAt = now
                };

                _context.ExamAttempts.Add(attempt);
                await _context.SaveChangesAsync();

                // Prepare questions for student
                var questions = exam.Questions.AsQueryable();
                if (exam.ShuffleQuestions)
                {
                    questions = questions.OrderBy(q => Guid.NewGuid());
                }
                else
                {
                    questions = questions.OrderBy(q => q.OrderIndex);
                }

                var attemptDto = new ExamAttemptDto
                {
                    Id = attempt.Id,
                    ExamId = exam.Id,
                    ExamTitle = exam.Title,
                    StartTime = attempt.StartTime,
                    EndTime = attempt.EndTime,
                    DurationMinutes = exam.DurationMinutes,
                    TotalMarks = exam.TotalMarks,
                    Questions = questions.Select(q => new ExamQuestionDto
                    {
                        Id = q.Id,
                        QuestionText = q.QuestionText,
                        QuestionType = q.QuestionType,
                        Points = q.Points,
                        Options = q.Options.Select(o => new QuestionOptionDto
                        {
                            Id = o.Id,
                            OptionText = o.OptionText,
                            OrderIndex = o.OrderIndex
                            // Don't include IsCorrect for students
                        }).OrderBy(o => o.OrderIndex).ToList()
                    }).ToList()
                };

                return Ok(attemptDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في بدء الامتحان", error = ex.Message });
            }
        }

        [HttpPost("attempts/{attemptId}/submit")]
        public async Task<IActionResult> SubmitExam(int attemptId, SubmitExamDto model)
        {
            try
            {
                var attempt = await _context.ExamAttempts
                    .Include(ea => ea.Exam)
                    .ThenInclude(e => e.Questions)
                    .ThenInclude(q => q.Options)
                    .FirstOrDefaultAsync(ea => ea.Id == attemptId);

                if (attempt == null)
                {
                    return NotFound(new { message = "محاولة الامتحان غير موجودة" });
                }

                if (attempt.IsCompleted)
                {
                    return BadRequest(new { message = "تم تسليم الامتحان بالفعل" });
                }

                // Check if time is up
                if (DateTime.UtcNow > attempt.EndTime)
                {
                    return BadRequest(new { message = "انتهى وقت الامتحان" });
                }

                // Calculate score
                decimal totalScore = 0;
                foreach (var answer in model.Answers)
                {
                    var question = attempt.Exam.Questions.FirstOrDefault(q => q.Id == answer.QuestionId);
                    if (question != null)
                    {
                        // Save student answer
                        var studentAnswer = new StudentAnswer
                        {
                            AttemptId = attemptId,
                            QuestionId = answer.QuestionId,
                            SelectedOptionId = answer.SelectedOptionId,
                            TextAnswer = answer.TextAnswer,
                            IsCorrect = false
                        };

                        // Check if answer is correct
                        if (question.QuestionType == "MultipleChoice" && answer.SelectedOptionId.HasValue)
                        {
                            var selectedOption = question.Options.FirstOrDefault(o => o.Id == answer.SelectedOptionId.Value);
                            if (selectedOption != null && selectedOption.IsCorrect)
                            {
                                studentAnswer.IsCorrect = true;
                                totalScore += question.Points;
                            }
                        }

                        _context.StudentAnswers.Add(studentAnswer);
                    }
                }

                // Update attempt
                attempt.IsCompleted = true;
                attempt.CompletedAt = DateTime.UtcNow;
                attempt.Score = totalScore;

                await _context.SaveChangesAsync();

                return Ok(new {
                    message = "تم تسليم الامتحان بنجاح",
                    score = totalScore,
                    totalMarks = attempt.Exam.TotalMarks,
                    percentage = (totalScore / attempt.Exam.TotalMarks) * 100
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تسليم الامتحان", error = ex.Message });
            }
        }

        [HttpGet("attempts/{attemptId}/result")]
        public async Task<ActionResult<ExamResultDto>> GetExamResult(int attemptId)
        {
            try
            {
                var attempt = await _context.ExamAttempts
                    .Include(ea => ea.Exam)
                    .ThenInclude(e => e.Subject)
                    .Include(ea => ea.Student)
                    .Include(ea => ea.StudentAnswers)
                    .ThenInclude(sa => sa.Question)
                    .ThenInclude(q => q.Options)
                    .FirstOrDefaultAsync(ea => ea.Id == attemptId);

                if (attempt == null)
                {
                    return NotFound(new { message = "محاولة الامتحان غير موجودة" });
                }

                if (!attempt.IsCompleted)
                {
                    return BadRequest(new { message = "الامتحان لم يكتمل بعد" });
                }

                if (!attempt.Exam.ShowResults)
                {
                    return BadRequest(new { message = "نتائج هذا الامتحان غير متاحة للعرض" });
                }

                var result = new ExamResultDto
                {
                    AttemptId = attempt.Id,
                    ExamTitle = attempt.Exam.Title,
                    SubjectName = attempt.Exam.Subject.Name,
                    StudentName = $"{attempt.Student.FirstName} {attempt.Student.LastName}",
                    Score = attempt.Score,
                    TotalMarks = attempt.Exam.TotalMarks,
                    Percentage = (attempt.Score / attempt.Exam.TotalMarks) * 100,
                    IsPassed = attempt.Score >= attempt.Exam.PassingMarks,
                    StartTime = attempt.StartTime,
                    CompletedAt = attempt.CompletedAt,
                    TimeTaken = attempt.CompletedAt.HasValue ?
                        (attempt.CompletedAt.Value - attempt.StartTime).TotalMinutes : 0,
                    CorrectAnswers = attempt.StudentAnswers.Count(sa => sa.IsCorrect),
                    TotalQuestions = attempt.StudentAnswers.Count,
                    Grade = GetLetterGrade((attempt.Score / attempt.Exam.TotalMarks) * 100)
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في جلب نتيجة الامتحان", error = ex.Message });
            }
        }

        #endregion

        #region Helper Methods

        private string GetExamStatus(DateTime startDate, DateTime endDate)
        {
            var now = DateTime.UtcNow;
            if (now < startDate)
                return "قادم";
            else if (now >= startDate && now <= endDate)
                return "جاري";
            else
                return "منتهي";
        }

        private string GetLetterGrade(decimal percentage)
        {
            return percentage switch
            {
                >= 90 => "ممتاز",
                >= 80 => "جيد جداً",
                >= 70 => "جيد",
                >= 60 => "مقبول",
                >= 50 => "ضعيف",
                _ => "راسب"
            };
        }

        #endregion
    }
}
