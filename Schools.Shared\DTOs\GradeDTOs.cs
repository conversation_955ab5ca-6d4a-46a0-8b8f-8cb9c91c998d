using System.ComponentModel.DataAnnotations;

namespace Schools.Shared.DTOs
{
    // Main Grade DTO for display
    public class GradeDto
    {
        public int Id { get; set; }
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public string StudentEmail { get; set; } = string.Empty;
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public string SubjectCode { get; set; } = string.Empty;
        public int AcademicYearId { get; set; }
        public string AcademicYearName { get; set; } = string.Empty;
        public string ExamType { get; set; } = string.Empty;
        public decimal Score { get; set; }
        public decimal MaxScore { get; set; }
        public decimal Percentage { get; set; }
        public DateTime Date { get; set; }
        public string Semester { get; set; } = string.Empty;
        public string? Notes { get; set; }
        public string? RecordedBy { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    // Create Grade DTO
    public class CreateGradeDto
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int SubjectId { get; set; }

        [Required]
        public int AcademicYearId { get; set; }

        [Required]
        [StringLength(50)]
        public string ExamType { get; set; } = string.Empty;

        [Required]
        [Range(0, 1000)]
        public decimal Score { get; set; }

        [Required]
        [Range(0.1, 1000)]
        public decimal MaxScore { get; set; }

        [Required]
        public DateTime Date { get; set; }

        [Required]
        [StringLength(20)]
        public string Semester { get; set; } = string.Empty;

        public string? Notes { get; set; }
    }

    // Update Grade DTO
    public class UpdateGradeDto
    {
        [Range(0, 1000)]
        public decimal Score { get; set; }

        [Range(0.1, 1000)]
        public decimal MaxScore { get; set; }

        [StringLength(50)]
        public string ExamType { get; set; } = string.Empty;

        [StringLength(20)]
        public string Semester { get; set; } = string.Empty;

        public DateTime Date { get; set; }
        public string? Notes { get; set; }
    }

    public class BulkGradeDto
    {
        [Required]
        public int SubjectId { get; set; }

        [Required]
        public int AcademicYearId { get; set; }

        [Required]
        [StringLength(50)]
        public string ExamType { get; set; } = string.Empty;

        [Required]
        [Range(0.1, 1000)]
        public decimal MaxScore { get; set; }

        [Required]
        public DateTime Date { get; set; }

        [Required]
        [StringLength(20)]
        public string Semester { get; set; } = string.Empty;

        [Required]
        public List<StudentGradeInput> StudentGrades { get; set; } = new();
    }

    public class StudentGradeInput
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        [Range(0, 1000)]
        public decimal Score { get; set; }

        public string? Notes { get; set; }
    }

    // Grade Statistics DTO
    public class GradeStatisticsDto
    {
        public int TotalStudents { get; set; }
        public decimal AverageScore { get; set; }
        public decimal HighestScore { get; set; }
        public decimal LowestScore { get; set; }
        public decimal PassRate { get; set; }
        public List<ExamTypeStatDto> ExamTypeStats { get; set; } = new();
        public List<SubjectStatDto> SubjectStats { get; set; } = new();
    }

    public class ExamTypeStatDto
    {
        public string ExamType { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal AverageScore { get; set; }
    }

    public class SubjectStatDto
    {
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public int GradeCount { get; set; }
        public decimal AverageScore { get; set; }
    }



    // Student Grade Summary DTO
    public class StudentGradeSummaryDto
    {
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public int TotalGrades { get; set; }
        public double OverallAverage { get; set; }
        public string OverallGrade { get; set; } = string.Empty;
        public List<SubjectGradeDto> SubjectGrades { get; set; } = new();
    }

    public class SubjectGradeDto
    {
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public int GradeCount { get; set; }
        public double AverageScore { get; set; }
    }
}
