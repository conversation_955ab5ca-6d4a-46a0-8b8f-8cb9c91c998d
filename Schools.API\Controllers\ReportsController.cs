using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Schools.API.Data;
using Schools.API.Services;
using Schools.Shared.DTOs;
using System.Text;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReportsController : ControllerBase
    {
        private readonly ILogger<ReportsController> _logger;
        private readonly IReportService _reportService;

        public ReportsController(ILogger<ReportsController> logger, IReportService reportService)
        {
            _logger = logger;
            _reportService = reportService;
        }

        [HttpGet("student-performance")]
        public async Task<ActionResult<StudentPerformanceReportDto>> GetStudentPerformanceReport(
            [FromQuery] string? studentId = null,
            [FromQuery] int? classId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var report = await _reportService.GetStudentPerformanceReportAsync(studentId, classId, startDate, endDate);
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating student performance report");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("attendance")]
        public async Task<ActionResult<AttendanceReportDto>> GetAttendanceReport(
            [FromQuery] string? studentId = null,
            [FromQuery] int? classId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var report = await _reportService.GetAttendanceReportAsync(studentId, classId, startDate, endDate);
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating attendance report");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("financial")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<FinancialReportDto>> GetFinancialReport(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var report = await _reportService.GetFinancialReportAsync(startDate, endDate);
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating financial report");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("class-summary")]
        public async Task<ActionResult<ClassSummaryReportDto>> GetClassSummaryReport(
            [FromQuery] int? classId = null)
        {
            try
            {
                var report = await _reportService.GetClassSummaryReportAsync(classId);
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating class summary report");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("teacher-performance")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<TeacherPerformanceReportDto>> GetTeacherPerformanceReport(
            [FromQuery] int? teacherId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var report = await _reportService.GetTeacherPerformanceReportAsync(teacherId, startDate, endDate);
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating teacher performance report");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("export/{reportType}")]
        public async Task<IActionResult> ExportReport(string reportType, [FromQuery] string format = "csv")
        {
            try
            {
                var parameters = new Dictionary<string, object>();
                var content = await _reportService.ExportReportAsync(reportType, format, parameters);

                var contentType = format.ToLower() switch
                {
                    "csv" => "text/csv",
                    "json" => "application/json",
                    _ => "text/plain"
                };

                var fileName = $"{reportType}_report_{DateTime.UtcNow:yyyyMMdd}.{format}";
                return File(Encoding.UTF8.GetBytes(content), contentType, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report {ReportType}", reportType);
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
