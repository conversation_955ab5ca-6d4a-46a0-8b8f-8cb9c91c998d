using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Schools.Shared.DTOs;
using System.Text;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReportsController : ControllerBase
    {
        private readonly ILogger<ReportsController> _logger;

        public ReportsController(ILogger<ReportsController> logger)
        {
            _logger = logger;
        }

        [HttpGet("student-performance")]
        public async Task<ActionResult<StudentPerformanceReportDto>> GetStudentPerformanceReport(
            [FromQuery] int? studentId = null,
            [FromQuery] int? classId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var report = new StudentPerformanceReportDto
                {
                    TotalStudents = 450,
                    AverageGrade = 85.7,
                    HighestGrade = 98.5,
                    LowestGrade = 62.3,
                    GradeDistribution = new Dictionary<string, int>
                    {
                        { "A+", 45 }, { "A", 78 }, { "B+", 92 }, { "B", 67 },
                        { "C+", 34 }, { "C", 23 }, { "D", 12 }, { "F", 5 }
                    },
                    SubjectPerformance = new Dictionary<string, double>
                    {
                        { "الرياضيات", 85.2 }, { "العلوم", 78.8 }, { "اللغة العربية", 92.1 },
                        { "الإنجليزية", 88.5 }, { "التاريخ", 90.3 }, { "الجغرافيا", 86.7 }
                    }
                };

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating student performance report");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("attendance")]
        public async Task<ActionResult<AttendanceReportDto>> GetAttendanceReport(
            [FromQuery] int? studentId = null,
            [FromQuery] int? classId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var report = new AttendanceReportDto
                {
                    TotalDays = 30,
                    TotalStudents = 450,
                    OverallAttendanceRate = 92.8,
                    PresentDays = 12540,
                    AbsentDays = 960
                };

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating attendance report");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("financial")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<FinancialReportDto>> GetFinancialReport(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                startDate ??= DateTime.UtcNow.AddMonths(-1);
                endDate ??= DateTime.UtcNow;

                var report = new FinancialReportDto
                {
                    StartDate = startDate.Value,
                    EndDate = endDate.Value,
                    TotalRevenue = 450000,
                    TuitionFees = 400000,
                    OtherFees = 50000,
                    TotalExpenses = 270000,
                    NetIncome = 180000,
                    OutstandingFees = 22500,
                    PaymentRate = 95.5
                };

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating financial report");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("class-summary")]
        public async Task<ActionResult<ClassSummaryReportDto>> GetClassSummaryReport(
            [FromQuery] int? classId = null)
        {
            try
            {
                var classDetails = new List<ClassSummaryDetailDto>
                {
                    new() { ClassId = 1, ClassName = "الصف الأول أ", TotalStudents = 25, AverageGrade = 88.5, AttendanceRate = 94.2, SubjectCount = 8 },
                    new() { ClassId = 2, ClassName = "الصف الأول ب", TotalStudents = 24, AverageGrade = 86.7, AttendanceRate = 92.8, SubjectCount = 8 },
                    new() { ClassId = 3, ClassName = "الصف الثاني أ", TotalStudents = 26, AverageGrade = 85.3, AttendanceRate = 93.5, SubjectCount = 9 },
                    new() { ClassId = 4, ClassName = "الصف الثاني ب", TotalStudents = 25, AverageGrade = 87.1, AttendanceRate = 91.7, SubjectCount = 9 },
                    new() { ClassId = 5, ClassName = "الصف الثالث أ", TotalStudents = 23, AverageGrade = 84.9, AttendanceRate = 90.4, SubjectCount = 10 },
                    new() { ClassId = 6, ClassName = "الصف الثالث ب", TotalStudents = 24, AverageGrade = 86.2, AttendanceRate = 92.1, SubjectCount = 10 }
                };

                var summaryReport = new ClassSummaryReportDto
                {
                    TotalClasses = classDetails.Count,
                    TotalStudents = classDetails.Sum(c => c.TotalStudents),
                    OverallAverageGrade = classDetails.Average(c => c.AverageGrade),
                    OverallAttendanceRate = classDetails.Average(c => c.AttendanceRate),
                    ClassDetails = classId.HasValue ? classDetails.Where(c => c.ClassId == classId.Value).ToList() : classDetails
                };

                return Ok(summaryReport);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating class summary report");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("teacher-performance")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<TeacherPerformanceReportDto>> GetTeacherPerformanceReport(
            [FromQuery] int? teacherId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var teacherDetails = new List<TeacherPerformanceDetailDto>
                {
                    new() { TeacherId = 1, TeacherName = "أحمد محمد", SubjectsCount = 2, StudentsCount = 75, AverageGrade = 88.5, TotalGrades = 150 },
                    new() { TeacherId = 2, TeacherName = "فاطمة علي", SubjectsCount = 1, StudentsCount = 50, AverageGrade = 92.1, TotalGrades = 100 },
                    new() { TeacherId = 3, TeacherName = "محمد سالم", SubjectsCount = 2, StudentsCount = 80, AverageGrade = 85.7, TotalGrades = 160 },
                    new() { TeacherId = 4, TeacherName = "نورا أحمد", SubjectsCount = 1, StudentsCount = 45, AverageGrade = 90.3, TotalGrades = 90 },
                    new() { TeacherId = 5, TeacherName = "خالد يوسف", SubjectsCount = 2, StudentsCount = 70, AverageGrade = 86.9, TotalGrades = 140 }
                };

                var report = new TeacherPerformanceReportDto
                {
                    TotalTeachers = teacherDetails.Count,
                    OverallAverageGrade = teacherDetails.Average(t => t.AverageGrade),
                    TeacherDetails = teacherId.HasValue ? teacherDetails.Where(t => t.TeacherId == teacherId.Value).ToList() : teacherDetails
                };

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating teacher performance report");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("export/{reportType}")]
        public async Task<IActionResult> ExportReport(string reportType, [FromQuery] string format = "csv")
        {
            try
            {
                var content = reportType.ToLower() switch
                {
                    "students" => await ExportStudentsReport(format),
                    "attendance" => await ExportAttendanceReport(format),
                    "grades" => await ExportGradesReport(format),
                    "financial" => await ExportFinancialReport(format),
                    _ => throw new ArgumentException("Invalid report type")
                };

                var contentType = format.ToLower() switch
                {
                    "csv" => "text/csv",
                    "json" => "application/json",
                    _ => "text/plain"
                };

                var fileName = $"{reportType}_report_{DateTime.UtcNow:yyyyMMdd}.{format}";
                return File(Encoding.UTF8.GetBytes(content), contentType, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report {ReportType}", reportType);
                return StatusCode(500, "Internal server error");
            }
        }

        private async Task<string> ExportStudentsReport(string format)
        {
            if (format.ToLower() == "csv")
            {
                var csv = new StringBuilder();
                csv.AppendLine("ID,Name,Email,Class,CreatedDate");
                csv.AppendLine("1,أحمد محمد,<EMAIL>,الصف الأول أ,2024-01-15");
                csv.AppendLine("2,فاطمة علي,<EMAIL>,الصف الأول ب,2024-01-16");
                csv.AppendLine("3,محمد سالم,<EMAIL>,الصف الثاني أ,2024-01-17");
                return csv.ToString();
            }

            return System.Text.Json.JsonSerializer.Serialize(new[]
            {
                new { Id = 1, Name = "أحمد محمد", Email = "<EMAIL>", Class = "الصف الأول أ", CreatedDate = "2024-01-15" },
                new { Id = 2, Name = "فاطمة علي", Email = "<EMAIL>", Class = "الصف الأول ب", CreatedDate = "2024-01-16" },
                new { Id = 3, Name = "محمد سالم", Email = "<EMAIL>", Class = "الصف الثاني أ", CreatedDate = "2024-01-17" }
            });
        }

        private async Task<string> ExportAttendanceReport(string format)
        {
            if (format.ToLower() == "csv")
            {
                var csv = new StringBuilder();
                csv.AppendLine("Date,StudentName,IsPresent,Reason");
                csv.AppendLine("2024-01-15,أحمد محمد,True,");
                csv.AppendLine("2024-01-15,فاطمة علي,False,مرض");
                csv.AppendLine("2024-01-16,محمد سالم,True,");
                return csv.ToString();
            }

            return System.Text.Json.JsonSerializer.Serialize(new[]
            {
                new { Date = "2024-01-15", StudentName = "أحمد محمد", IsPresent = true, Reason = "" },
                new { Date = "2024-01-15", StudentName = "فاطمة علي", IsPresent = false, Reason = "مرض" },
                new { Date = "2024-01-16", StudentName = "محمد سالم", IsPresent = true, Reason = "" }
            });
        }

        private async Task<string> ExportGradesReport(string format)
        {
            if (format.ToLower() == "csv")
            {
                var csv = new StringBuilder();
                csv.AppendLine("Date,StudentName,Subject,Grade,Comments");
                csv.AppendLine("2024-01-15,أحمد محمد,الرياضيات,A+,ممتاز");
                csv.AppendLine("2024-01-15,فاطمة علي,العلوم,A,جيد جداً");
                csv.AppendLine("2024-01-16,محمد سالم,اللغة العربية,B+,جيد");
                return csv.ToString();
            }

            return System.Text.Json.JsonSerializer.Serialize(new[]
            {
                new { Date = "2024-01-15", StudentName = "أحمد محمد", Subject = "الرياضيات", Grade = "A+", Comments = "ممتاز" },
                new { Date = "2024-01-15", StudentName = "فاطمة علي", Subject = "العلوم", Grade = "A", Comments = "جيد جداً" },
                new { Date = "2024-01-16", StudentName = "محمد سالم", Subject = "اللغة العربية", Grade = "B+", Comments = "جيد" }
            });
        }

        private async Task<string> ExportFinancialReport(string format)
        {
            if (format.ToLower() == "csv")
            {
                var csv = new StringBuilder();
                csv.AppendLine("Month,Revenue,Expenses,NetIncome");
                csv.AppendLine("2024-01,450000,270000,180000");
                csv.AppendLine("2024-02,465000,275000,190000");
                csv.AppendLine("2024-03,440000,265000,175000");
                return csv.ToString();
            }

            return System.Text.Json.JsonSerializer.Serialize(new[]
            {
                new { Month = "2024-01", Revenue = 450000, Expenses = 270000, NetIncome = 180000 },
                new { Month = "2024-02", Revenue = 465000, Expenses = 275000, NetIncome = 190000 },
                new { Month = "2024-03", Revenue = 440000, Expenses = 265000, NetIncome = 175000 }
            });
        }
    }
}
