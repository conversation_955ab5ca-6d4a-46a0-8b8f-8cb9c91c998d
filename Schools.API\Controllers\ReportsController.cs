using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Schools.API.Data;
using Schools.Shared.DTOs;
using System.Text;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReportsController : ControllerBase
    {
        private readonly ILogger<ReportsController> _logger;
        private readonly ApplicationDbContext _context;

        public ReportsController(ILogger<ReportsController> logger, ApplicationDbContext context)
        {
            _logger = logger;
            _context = context;
        }

        [HttpGet("student-performance")]
        public async Task<ActionResult<StudentPerformanceReportDto>> GetStudentPerformanceReport(
            [FromQuery] string? studentId = null,
            [FromQuery] int? classId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                // Get students query
                var studentsQuery = _context.Users
                    .Where(u => u.StudentEnrollments.Any() && u.IsActive);

                if (!string.IsNullOrEmpty(studentId))
                    studentsQuery = studentsQuery.Where(s => s.Id == studentId);

                if (classId.HasValue)
                    studentsQuery = studentsQuery.Where(s => s.StudentEnrollments.Any(se => se.ClassId == classId.Value));

                // Get grades query
                var gradesQuery = _context.Grades.AsQueryable();

                if (startDate.HasValue)
                    gradesQuery = gradesQuery.Where(g => g.Date >= startDate.Value);

                if (endDate.HasValue)
                    gradesQuery = gradesQuery.Where(g => g.Date <= endDate.Value);

                // Calculate statistics
                var totalStudents = await studentsQuery.CountAsync();
                var grades = await gradesQuery.ToListAsync();

                var averageGrade = grades.Any() ? grades.Average(g => (double)g.Score) : 0;
                var highestGrade = grades.Any() ? grades.Max(g => (double)g.Score) : 0;
                var lowestGrade = grades.Any() ? grades.Min(g => (double)g.Score) : 0;

                var report = new StudentPerformanceReportDto
                {
                    TotalStudents = totalStudents,
                    AverageGrade = averageGrade,
                    HighestGrade = highestGrade,
                    LowestGrade = lowestGrade,
                    GradeDistribution = new Dictionary<string, int>(),
                    SubjectPerformance = new Dictionary<string, double>()
                };

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating student performance report");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("attendance")]
        public async Task<ActionResult<AttendanceReportDto>> GetAttendanceReport(
            [FromQuery] string? studentId = null,
            [FromQuery] int? classId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                startDate ??= DateTime.Now.AddDays(-30);
                endDate ??= DateTime.Now;

                // Get attendance records
                var attendanceQuery = _context.AttendanceRecords
                    .Where(a => a.Date >= startDate.Value && a.Date <= endDate.Value);

                if (!string.IsNullOrEmpty(studentId))
                    attendanceQuery = attendanceQuery.Where(a => a.StudentId == studentId);

                if (classId.HasValue)
                    attendanceQuery = attendanceQuery.Where(a => a.Student.StudentEnrollments.Any(se => se.ClassId == classId.Value));

                var attendanceRecords = await attendanceQuery
                    .Include(a => a.Student)
                    .ToListAsync();

                var totalStudents = await _context.Users
                    .Where(u => u.StudentEnrollments.Any() && u.IsActive)
                    .CountAsync();

                var totalSchoolDays = (endDate.Value - startDate.Value).Days + 1;
                var totalPossibleAttendance = totalStudents * totalSchoolDays;
                var totalPresent = attendanceRecords.Count(a => a.IsPresent);
                var overallAttendanceRate = totalPossibleAttendance > 0 ? (double)totalPresent / totalPossibleAttendance * 100 : 0;

                var report = new AttendanceReportDto
                {
                    ClassId = classId ?? 0,
                    ClassName = classId.HasValue ? await GetClassNameAsync(classId.Value) : "جميع الصفوف",
                    GradeName = "",
                    FromDate = startDate.Value,
                    ToDate = endDate.Value,
                    TotalStudents = totalStudents,
                    TotalSchoolDays = totalSchoolDays,
                    OverallAttendanceRate = overallAttendanceRate,
                    StudentAttendance = new List<StudentAttendanceReportDto>()
                };

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating attendance report");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("financial")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<FinancialReportDto>> GetFinancialReport(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                startDate ??= DateTime.UtcNow.AddMonths(-1);
                endDate ??= DateTime.UtcNow;

                // Get receipt vouchers (income)
                var receiptVouchers = await _context.ReceiptVouchers
                    .Where(rv => rv.Date >= startDate.Value && rv.Date <= endDate.Value)
                    .ToListAsync();

                // Get payment vouchers (expenses)
                var paymentVouchers = await _context.PaymentVouchers
                    .Where(pv => pv.Date >= startDate.Value && pv.Date <= endDate.Value)
                    .ToListAsync();

                var totalIncome = receiptVouchers.Sum(rv => rv.Amount);
                var totalExpenses = paymentVouchers.Sum(pv => pv.Amount);
                var netIncome = totalIncome - totalExpenses;

                var report = new FinancialReportDto
                {
                    FromDate = startDate.Value,
                    ToDate = endDate.Value,
                    TotalIncome = totalIncome,
                    TotalExpenses = totalExpenses,
                    NetIncome = netIncome,
                    StudentFeesCollected = receiptVouchers.Where(rv => rv.Category == "رسوم دراسية").Sum(rv => rv.Amount),
                    TotalReceiptVouchers = receiptVouchers.Count,
                    TotalPaymentVouchers = paymentVouchers.Count,
                    IncomeByCategory = new List<CategoryAmountDto>(),
                    ExpensesByCategory = new List<CategoryAmountDto>()
                };

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating financial report");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("class-summary")]
        public async Task<ActionResult<ClassSummaryReportDto>> GetClassSummaryReport(
            [FromQuery] int? classId = null)
        {
            try
            {
                var classDetails = new List<ClassSummaryDetailDto>
                {
                    new() { ClassId = 1, ClassName = "الصف الأول أ", TotalStudents = 25, AverageGrade = 88.5, AttendanceRate = 94.2, SubjectCount = 8 },
                    new() { ClassId = 2, ClassName = "الصف الأول ب", TotalStudents = 24, AverageGrade = 86.7, AttendanceRate = 92.8, SubjectCount = 8 },
                    new() { ClassId = 3, ClassName = "الصف الثاني أ", TotalStudents = 26, AverageGrade = 85.3, AttendanceRate = 93.5, SubjectCount = 9 },
                    new() { ClassId = 4, ClassName = "الصف الثاني ب", TotalStudents = 25, AverageGrade = 87.1, AttendanceRate = 91.7, SubjectCount = 9 },
                    new() { ClassId = 5, ClassName = "الصف الثالث أ", TotalStudents = 23, AverageGrade = 84.9, AttendanceRate = 90.4, SubjectCount = 10 },
                    new() { ClassId = 6, ClassName = "الصف الثالث ب", TotalStudents = 24, AverageGrade = 86.2, AttendanceRate = 92.1, SubjectCount = 10 }
                };

                var summaryReport = new ClassSummaryReportDto
                {
                    TotalClasses = classDetails.Count,
                    TotalStudents = classDetails.Sum(c => c.TotalStudents),
                    OverallAverageGrade = classDetails.Average(c => c.AverageGrade),
                    OverallAttendanceRate = classDetails.Average(c => c.AttendanceRate),
                    ClassDetails = classId.HasValue ? classDetails.Where(c => c.ClassId == classId.Value).ToList() : classDetails
                };

                return Ok(summaryReport);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating class summary report");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("teacher-performance")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<TeacherPerformanceReportDto>> GetTeacherPerformanceReport(
            [FromQuery] int? teacherId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var teacherDetails = new List<TeacherPerformanceDetailDto>
                {
                    new() { TeacherId = 1, TeacherName = "أحمد محمد", SubjectsCount = 2, StudentsCount = 75, AverageGrade = 88.5, TotalGrades = 150 },
                    new() { TeacherId = 2, TeacherName = "فاطمة علي", SubjectsCount = 1, StudentsCount = 50, AverageGrade = 92.1, TotalGrades = 100 },
                    new() { TeacherId = 3, TeacherName = "محمد سالم", SubjectsCount = 2, StudentsCount = 80, AverageGrade = 85.7, TotalGrades = 160 },
                    new() { TeacherId = 4, TeacherName = "نورا أحمد", SubjectsCount = 1, StudentsCount = 45, AverageGrade = 90.3, TotalGrades = 90 },
                    new() { TeacherId = 5, TeacherName = "خالد يوسف", SubjectsCount = 2, StudentsCount = 70, AverageGrade = 86.9, TotalGrades = 140 }
                };

                var report = new TeacherPerformanceReportDto
                {
                    TotalTeachers = teacherDetails.Count,
                    OverallAverageGrade = teacherDetails.Average(t => t.AverageGrade),
                    TeacherDetails = teacherId.HasValue ? teacherDetails.Where(t => t.TeacherId == teacherId.Value).ToList() : teacherDetails
                };

                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating teacher performance report");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("export/{reportType}")]
        public async Task<IActionResult> ExportReport(string reportType, [FromQuery] string format = "csv")
        {
            try
            {
                var content = reportType.ToLower() switch
                {
                    "students" => await ExportStudentsReport(format),
                    "attendance" => await ExportAttendanceReport(format),
                    "grades" => await ExportGradesReport(format),
                    "financial" => await ExportFinancialReport(format),
                    _ => throw new ArgumentException("Invalid report type")
                };

                var contentType = format.ToLower() switch
                {
                    "csv" => "text/csv",
                    "json" => "application/json",
                    _ => "text/plain"
                };

                var fileName = $"{reportType}_report_{DateTime.UtcNow:yyyyMMdd}.{format}";
                return File(Encoding.UTF8.GetBytes(content), contentType, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting report {ReportType}", reportType);
                return StatusCode(500, "Internal server error");
            }
        }

        private async Task<string> ExportStudentsReport(string format)
        {
            if (format.ToLower() == "csv")
            {
                var csv = new StringBuilder();
                csv.AppendLine("ID,Name,Email,Class,CreatedDate");
                csv.AppendLine("1,أحمد محمد,<EMAIL>,الصف الأول أ,2024-01-15");
                csv.AppendLine("2,فاطمة علي,<EMAIL>,الصف الأول ب,2024-01-16");
                csv.AppendLine("3,محمد سالم,<EMAIL>,الصف الثاني أ,2024-01-17");
                return csv.ToString();
            }

            return System.Text.Json.JsonSerializer.Serialize(new[]
            {
                new { Id = 1, Name = "أحمد محمد", Email = "<EMAIL>", Class = "الصف الأول أ", CreatedDate = "2024-01-15" },
                new { Id = 2, Name = "فاطمة علي", Email = "<EMAIL>", Class = "الصف الأول ب", CreatedDate = "2024-01-16" },
                new { Id = 3, Name = "محمد سالم", Email = "<EMAIL>", Class = "الصف الثاني أ", CreatedDate = "2024-01-17" }
            });
        }

        private async Task<string> ExportAttendanceReport(string format)
        {
            if (format.ToLower() == "csv")
            {
                var csv = new StringBuilder();
                csv.AppendLine("Date,StudentName,IsPresent,Reason");
                csv.AppendLine("2024-01-15,أحمد محمد,True,");
                csv.AppendLine("2024-01-15,فاطمة علي,False,مرض");
                csv.AppendLine("2024-01-16,محمد سالم,True,");
                return csv.ToString();
            }

            return System.Text.Json.JsonSerializer.Serialize(new[]
            {
                new { Date = "2024-01-15", StudentName = "أحمد محمد", IsPresent = true, Reason = "" },
                new { Date = "2024-01-15", StudentName = "فاطمة علي", IsPresent = false, Reason = "مرض" },
                new { Date = "2024-01-16", StudentName = "محمد سالم", IsPresent = true, Reason = "" }
            });
        }

        private async Task<string> ExportGradesReport(string format)
        {
            if (format.ToLower() == "csv")
            {
                var csv = new StringBuilder();
                csv.AppendLine("Date,StudentName,Subject,Grade,Comments");
                csv.AppendLine("2024-01-15,أحمد محمد,الرياضيات,A+,ممتاز");
                csv.AppendLine("2024-01-15,فاطمة علي,العلوم,A,جيد جداً");
                csv.AppendLine("2024-01-16,محمد سالم,اللغة العربية,B+,جيد");
                return csv.ToString();
            }

            return System.Text.Json.JsonSerializer.Serialize(new[]
            {
                new { Date = "2024-01-15", StudentName = "أحمد محمد", Subject = "الرياضيات", Grade = "A+", Comments = "ممتاز" },
                new { Date = "2024-01-15", StudentName = "فاطمة علي", Subject = "العلوم", Grade = "A", Comments = "جيد جداً" },
                new { Date = "2024-01-16", StudentName = "محمد سالم", Subject = "اللغة العربية", Grade = "B+", Comments = "جيد" }
            });
        }

        private async Task<string> ExportFinancialReport(string format)
        {
            if (format.ToLower() == "csv")
            {
                var csv = new StringBuilder();
                csv.AppendLine("Month,Revenue,Expenses,NetIncome");
                csv.AppendLine("2024-01,450000,270000,180000");
                csv.AppendLine("2024-02,465000,275000,190000");
                csv.AppendLine("2024-03,440000,265000,175000");
                return csv.ToString();
            }

            return System.Text.Json.JsonSerializer.Serialize(new[]
            {
                new { Month = "2024-01", Revenue = 450000, Expenses = 270000, NetIncome = 180000 },
                new { Month = "2024-02", Revenue = 465000, Expenses = 275000, NetIncome = 190000 },
                new { Month = "2024-03", Revenue = 440000, Expenses = 265000, NetIncome = 175000 }
            });
        }

        private async Task<string> GetClassNameAsync(int classId)
        {
            var className = await _context.Classes
                .Where(c => c.Id == classId)
                .Select(c => c.Name)
                .FirstOrDefaultAsync();

            return className ?? "غير محدد";
        }
    }
}
