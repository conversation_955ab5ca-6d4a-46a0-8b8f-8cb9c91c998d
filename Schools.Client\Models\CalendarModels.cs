namespace Schools.Client.Models
{
    public class CalendarEventModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public DateTime Start { get; set; }
        public DateTime End { get; set; }
        public bool AllDay { get; set; }
        public string Type { get; set; } = "";
        public string Category { get; set; } = "";
        public string Color { get; set; } = "";
        public string Location { get; set; } = "";
        public string CreatedBy { get; set; } = "";
        public DateTime CreatedAt { get; set; }
        public bool IsRecurring { get; set; }
        public RecurrenceModel? Recurrence { get; set; }
        public List<string> Attendees { get; set; } = new();
        public List<ReminderModel> Reminders { get; set; } = new();
        public string Status { get; set; } = "confirmed";
        public int Priority { get; set; } = 1;
        public Dictionary<string, object> CustomFields { get; set; } = new();
    }

    public class RecurrenceModel
    {
        public string Pattern { get; set; } = ""; // daily, weekly, monthly, yearly
        public int Interval { get; set; } = 1;
        public List<DayOfWeek> DaysOfWeek { get; set; } = new();
        public int? DayOfMonth { get; set; }
        public int? WeekOfMonth { get; set; }
        public int? MonthOfYear { get; set; }
        public DateTime? EndDate { get; set; }
        public int? OccurrenceCount { get; set; }
        public List<DateTime> Exceptions { get; set; } = new();
    }

    public class ReminderModel
    {
        public int Id { get; set; }
        public int EventId { get; set; }
        public string Type { get; set; } = ""; // email, popup, sms
        public int MinutesBefore { get; set; }
        public bool IsEnabled { get; set; } = true;
        public string Message { get; set; } = "";
        public DateTime? SentAt { get; set; }
    }

    public class CreateCalendarEventModel
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public DateTime Start { get; set; }
        public DateTime End { get; set; }
        public bool AllDay { get; set; }
        public string Type { get; set; } = "event";
        public string Category { get; set; } = "general";
        public string Color { get; set; } = "#007bff";
        public string Location { get; set; } = "";
        public bool IsRecurring { get; set; }
        public RecurrenceModel? Recurrence { get; set; }
        public List<string> Attendees { get; set; } = new();
        public List<CreateReminderModel> Reminders { get; set; } = new();
        public int Priority { get; set; } = 1;
    }

    public class CreateReminderModel
    {
        public string Type { get; set; } = "popup";
        public int MinutesBefore { get; set; } = 15;
        public string Message { get; set; } = "";
    }

    public class CalendarViewModel
    {
        public string View { get; set; } = "month"; // month, week, day, agenda
        public DateTime CurrentDate { get; set; } = DateTime.Today;
        public List<CalendarEventModel> Events { get; set; } = new();
        public CalendarFilterModel Filters { get; set; } = new();
        public CalendarSettingsModel Settings { get; set; } = new();
    }

    public class CalendarFilterModel
    {
        public List<string> EventTypes { get; set; } = new();
        public List<string> Categories { get; set; } = new();
        public List<string> Attendees { get; set; } = new();
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Search { get; set; }
        public bool ShowCompleted { get; set; } = true;
        public bool ShowCancelled { get; set; } = false;
    }

    public class CalendarSettingsModel
    {
        public string DefaultView { get; set; } = "month";
        public int WeekStartDay { get; set; } = 0; // 0 = Sunday, 1 = Monday
        public string TimeFormat { get; set; } = "HH:mm";
        public bool ShowWeekends { get; set; } = true;
        public bool ShowWeekNumbers { get; set; } = false;
        public int DefaultEventDuration { get; set; } = 60; // minutes
        public List<string> WorkingDays { get; set; } = new() { "Monday", "Tuesday", "Wednesday", "Thursday", "Friday" };
        public TimeSpan WorkingHoursStart { get; set; } = new(8, 0, 0);
        public TimeSpan WorkingHoursEnd { get; set; } = new(17, 0, 0);
        public Dictionary<string, string> CategoryColors { get; set; } = new();
    }

    public class HolidayModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public DateTime Date { get; set; }
        public bool IsRecurring { get; set; }
        public string Type { get; set; } = ""; // national, religious, school
        public string Description { get; set; } = "";
        public string Country { get; set; } = "SA";
        public bool IsActive { get; set; } = true;
    }

    public class AcademicCalendarModel
    {
        public int Id { get; set; }
        public string AcademicYear { get; set; } = "";
        public DateTime YearStart { get; set; }
        public DateTime YearEnd { get; set; }
        public List<SemesterPeriodModel> Semesters { get; set; } = new();
        public List<ExamPeriodModel> ExamPeriods { get; set; } = new();
        public List<VacationPeriodModel> Vacations { get; set; } = new();
        public List<HolidayModel> Holidays { get; set; } = new();
        public bool IsActive { get; set; } = true;
    }

    public class SemesterPeriodModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int WeekCount { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public class ExamPeriodModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Type { get; set; } = ""; // midterm, final, quiz
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string SemesterId { get; set; } = "";
        public List<string> Subjects { get; set; } = new();
    }

    public class VacationPeriodModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Type { get; set; } = ""; // summer, winter, spring, mid-term
        public string Description { get; set; } = "";
    }

    public static class CalendarEventTypes
    {
        public static readonly Dictionary<string, string> Types = new()
        {
            { "class", "حصة دراسية" },
            { "exam", "امتحان" },
            { "meeting", "اجتماع" },
            { "event", "فعالية" },
            { "holiday", "عطلة" },
            { "vacation", "إجازة" },
            { "assignment", "واجب" },
            { "deadline", "موعد نهائي" },
            { "conference", "مؤتمر" },
            { "workshop", "ورشة عمل" },
            { "sports", "نشاط رياضي" },
            { "cultural", "نشاط ثقافي" }
        };

        public static readonly Dictionary<string, string> Icons = new()
        {
            { "class", "fas fa-chalkboard" },
            { "exam", "fas fa-file-alt" },
            { "meeting", "fas fa-handshake" },
            { "event", "fas fa-calendar-star" },
            { "holiday", "fas fa-umbrella-beach" },
            { "vacation", "fas fa-plane" },
            { "assignment", "fas fa-tasks" },
            { "deadline", "fas fa-clock" },
            { "conference", "fas fa-microphone" },
            { "workshop", "fas fa-tools" },
            { "sports", "fas fa-futbol" },
            { "cultural", "fas fa-theater-masks" }
        };

        public static readonly Dictionary<string, string> Colors = new()
        {
            { "class", "#007bff" },
            { "exam", "#dc3545" },
            { "meeting", "#28a745" },
            { "event", "#ffc107" },
            { "holiday", "#17a2b8" },
            { "vacation", "#6f42c1" },
            { "assignment", "#fd7e14" },
            { "deadline", "#e83e8c" },
            { "conference", "#20c997" },
            { "workshop", "#6c757d" },
            { "sports", "#198754" },
            { "cultural", "#0d6efd" }
        };
    }

    public static class RecurrencePatterns
    {
        public static readonly Dictionary<string, string> Patterns = new()
        {
            { "daily", "يومي" },
            { "weekly", "أسبوعي" },
            { "monthly", "شهري" },
            { "yearly", "سنوي" },
            { "weekdays", "أيام العمل" },
            { "custom", "مخصص" }
        };
    }

    public static class ReminderTypes
    {
        public static readonly Dictionary<string, string> Types = new()
        {
            { "popup", "نافذة منبثقة" },
            { "email", "بريد إلكتروني" },
            { "sms", "رسالة نصية" },
            { "push", "إشعار فوري" }
        };
    }

    public class CalendarExportModel
    {
        public string Format { get; set; } = "ics"; // ics, csv, pdf
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<string> EventTypes { get; set; } = new();
        public List<string> Categories { get; set; } = new();
        public bool IncludeDetails { get; set; } = true;
        public bool IncludeAttendees { get; set; } = false;
        public string TimeZone { get; set; } = "Asia/Riyadh";
    }

    public class CalendarImportModel
    {
        public string Format { get; set; } = "ics";
        public byte[] FileContent { get; set; } = Array.Empty<byte>();
        public string FileName { get; set; } = "";
        public bool OverwriteExisting { get; set; } = false;
        public string DefaultCategory { get; set; } = "imported";
        public string DefaultColor { get; set; } = "#6c757d";
    }
}
