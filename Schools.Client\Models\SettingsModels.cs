namespace Schools.Client.Models
{
    public class SystemSettingsModel
    {
        public string SchoolName { get; set; } = "";
        public string SchoolLogo { get; set; } = "";
        public string SchoolAddress { get; set; } = "";
        public string SchoolPhone { get; set; } = "";
        public string SchoolEmail { get; set; } = "";
        public string SchoolWebsite { get; set; } = "";
        public string TimeZone { get; set; } = "Asia/Riyadh";
        public string DefaultLanguage { get; set; } = "ar";
        public string Currency { get; set; } = "SAR";
        public string DateFormat { get; set; } = "yyyy-MM-dd";
        public string TimeFormat { get; set; } = "HH:mm";
        public bool MaintenanceMode { get; set; } = false;
        public string MaintenanceMessage { get; set; } = "";
    }

    public class AcademicSettingsModel
    {
        public DateTime AcademicYearStart { get; set; }
        public DateTime AcademicYearEnd { get; set; }
        public int SemesterCount { get; set; } = 2;
        public List<SemesterModel> Semesters { get; set; } = new();
        public string GradingSystem { get; set; } = "letter"; // letter, percentage, points
        public double PassingGrade { get; set; } = 60;
        public int MaxAbsenceDays { get; set; } = 15;
        public bool AllowMakeupExams { get; set; } = true;
        public int MakeupExamDays { get; set; } = 7;
        public List<GradeScaleModel> GradeScale { get; set; } = new();
    }

    public class SemesterModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsActive { get; set; }
    }

    public class GradeScaleModel
    {
        public string Grade { get; set; } = "";
        public double MinPercentage { get; set; }
        public double MaxPercentage { get; set; }
        public double GradePoints { get; set; }
        public string Description { get; set; } = "";
    }

    public class SecuritySettingsModel
    {
        public bool RequireStrongPasswords { get; set; } = true;
        public int MinPasswordLength { get; set; } = 8;
        public int PasswordExpiryDays { get; set; } = 90;
        public bool EnableTwoFactorAuth { get; set; } = false;
        public int SessionTimeoutMinutes { get; set; } = 30;
        public int MaxLoginAttempts { get; set; } = 5;
        public int LockoutDurationMinutes { get; set; } = 15;
        public bool LogSecurityEvents { get; set; } = true;
        public List<string> AllowedIpRanges { get; set; } = new();
        public bool RequireEmailVerification { get; set; } = true;
    }

    public class SystemNotificationSettingsModel
    {
        public bool EnableEmailNotifications { get; set; } = true;
        public bool EnableSmsNotifications { get; set; } = false;
        public bool EnablePushNotifications { get; set; } = true;
        public string SmtpServer { get; set; } = "";
        public int SmtpPort { get; set; } = 587;
        public string SmtpUsername { get; set; } = "";
        public string SmtpPassword { get; set; } = "";
        public bool SmtpUseSsl { get; set; } = true;
        public string SmsProvider { get; set; } = "";
        public string SmsApiKey { get; set; } = "";
        public string DefaultSender { get; set; } = "";
    }

    public class BackupSettingsModel
    {
        public bool AutoBackupEnabled { get; set; } = true;
        public string BackupFrequency { get; set; } = "daily"; // daily, weekly, monthly
        public TimeSpan BackupTime { get; set; } = new(2, 0, 0);
        public int RetentionDays { get; set; } = 30;
        public string BackupLocation { get; set; } = "";
        public bool BackupToCloud { get; set; } = false;
        public string CloudProvider { get; set; } = "";
        public string CloudCredentials { get; set; } = "";
        public bool CompressBackups { get; set; } = true;
        public bool EncryptBackups { get; set; } = true;
    }

    public class IntegrationSettingsModel
    {
        public bool EnableLdapAuth { get; set; } = false;
        public string LdapServer { get; set; } = "";
        public string LdapDomain { get; set; } = "";
        public bool EnableSsoAuth { get; set; } = false;
        public string SsoProvider { get; set; } = "";
        public string SsoClientId { get; set; } = "";
        public string SsoClientSecret { get; set; } = "";
        public bool EnableApiAccess { get; set; } = true;
        public List<ApiKeyModel> ApiKeys { get; set; } = new();
        public Dictionary<string, object> ThirdPartyIntegrations { get; set; } = new();
    }

    public class ApiKeyModel
    {
        public string Id { get; set; } = "";
        public string Name { get; set; } = "";
        public string Key { get; set; } = "";
        public List<string> Permissions { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public bool IsActive { get; set; } = true;
        public string LastUsedIp { get; set; } = "";
        public DateTime? LastUsedAt { get; set; }
    }

    public class UserPreferencesModel
    {
        public string UserId { get; set; } = "";
        public string Theme { get; set; } = "light"; // light, dark, auto
        public string Language { get; set; } = "ar";
        public string TimeZone { get; set; } = "Asia/Riyadh";
        public string DateFormat { get; set; } = "yyyy-MM-dd";
        public string TimeFormat { get; set; } = "HH:mm";
        public int ItemsPerPage { get; set; } = 20;
        public bool ShowNotifications { get; set; } = true;
        public bool PlaySounds { get; set; } = true;
        public Dictionary<string, object> DashboardLayout { get; set; } = new();
        public Dictionary<string, bool> FeatureFlags { get; set; } = new();
    }

    public class FeatureToggleModel
    {
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public bool IsEnabled { get; set; }
        public List<string> EnabledForRoles { get; set; } = new();
        public List<string> EnabledForUsers { get; set; } = new();
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public double RolloutPercentage { get; set; } = 100;
    }

    public class MaintenanceWindowModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public bool IsActive { get; set; }
        public string NotificationMessage { get; set; } = "";
        public List<string> AffectedServices { get; set; } = new();
    }

    public static class SettingsCategories
    {
        public static readonly Dictionary<string, string> Categories = new()
        {
            { "system", "إعدادات النظام" },
            { "academic", "الإعدادات الأكاديمية" },
            { "security", "إعدادات الأمان" },
            { "notifications", "إعدادات الإشعارات" },
            { "backup", "إعدادات النسخ الاحتياطي" },
            { "integrations", "إعدادات التكامل" },
            { "appearance", "إعدادات المظهر" },
            { "performance", "إعدادات الأداء" }
        };

        public static readonly Dictionary<string, string> Icons = new()
        {
            { "system", "fas fa-cog" },
            { "academic", "fas fa-graduation-cap" },
            { "security", "fas fa-shield-alt" },
            { "notifications", "fas fa-bell" },
            { "backup", "fas fa-database" },
            { "integrations", "fas fa-plug" },
            { "appearance", "fas fa-palette" },
            { "performance", "fas fa-tachometer-alt" }
        };
    }

    public static class Themes
    {
        public static readonly Dictionary<string, string> AvailableThemes = new()
        {
            { "light", "فاتح" },
            { "dark", "داكن" },
            { "auto", "تلقائي" },
            { "blue", "أزرق" },
            { "green", "أخضر" },
            { "purple", "بنفسجي" }
        };
    }

    public static class Languages
    {
        public static readonly Dictionary<string, string> SupportedLanguages = new()
        {
            { "ar", "العربية" },
            { "en", "English" },
            { "fr", "Français" },
            { "es", "Español" }
        };
    }

    public static class TimeZones
    {
        public static readonly Dictionary<string, string> CommonTimeZones = new()
        {
            { "Asia/Riyadh", "الرياض (GMT+3)" },
            { "Asia/Dubai", "دبي (GMT+4)" },
            { "Asia/Kuwait", "الكويت (GMT+3)" },
            { "Africa/Cairo", "القاهرة (GMT+2)" },
            { "UTC", "التوقيت العالمي (UTC)" }
        };
    }

    public class AuditLogModel
    {
        public int Id { get; set; }
        public string UserId { get; set; } = "";
        public string UserName { get; set; } = "";
        public string Action { get; set; } = "";
        public string Entity { get; set; } = "";
        public string EntityId { get; set; } = "";
        public string OldValues { get; set; } = "";
        public string NewValues { get; set; } = "";
        public DateTime Timestamp { get; set; }
        public string IpAddress { get; set; } = "";
        public string UserAgent { get; set; } = "";
    }
}
