namespace Schools.Shared.DTOs;

public class StudentPerformanceDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public int ClassId { get; set; }
    public string ClassName { get; set; } = string.Empty;
    public List<int> SubjectIds { get; set; } = new();
    public int ExamCount { get; set; }
    public double AverageScore { get; set; }
    public double HighestScore { get; set; }
    public double LowestScore { get; set; }
    public double ImprovementRate { get; set; }
    public DateTime LastExamDate { get; set; }
    public List<ExamPerformanceDto> RecentExams { get; set; } = new();
}

public class ExamPerformanceDto
{
    public int ExamId { get; set; }
    public string ExamTitle { get; set; } = string.Empty;
    public string SubjectName { get; set; } = string.Empty;
    public double Score { get; set; }
    public double MaxScore { get; set; }
    public double Percentage { get; set; }
    public DateTime ExamDate { get; set; }
    public string Grade { get; set; } = string.Empty;
    public bool IsPassed { get; set; }
}

public class PerformanceTrendDto
{
    public string Period { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int ExamCount { get; set; }
    public double AverageScore { get; set; }
    public int StudentCount { get; set; }
    public double PassRate { get; set; }
    public Dictionary<string, int> GradeDistribution { get; set; } = new();
}

public class ExamSettingsDto
{
    // General Settings
    public int DefaultDurationMinutes { get; set; } = 60;
    public int DefaultMaxAttempts { get; set; } = 1;
    public int DefaultPassingPercentage { get; set; } = 50;
    public int AutoSaveIntervalSeconds { get; set; } = 60;
    public bool ShowResultsImmediately { get; set; } = true;
    public bool AllowRetakeByDefault { get; set; } = false;
    public bool RandomizeQuestions { get; set; } = false;
    public bool RandomizeOptions { get; set; } = false;

    // Security Settings
    public int SessionTimeoutMinutes { get; set; } = 30;
    public int MaxLoginAttempts { get; set; } = 5;
    public int LockoutDurationMinutes { get; set; } = 15;
    public bool PreventTabSwitching { get; set; } = false;
    public bool PreventCopyPaste { get; set; } = false;
    public bool PreventRightClick { get; set; } = false;
    public bool EnableFullScreen { get; set; } = false;
    public bool LogUserActivity { get; set; } = true;

    // Notification Settings
    public int TimeWarningMinutes { get; set; } = 5;
    public bool EmailNotifications { get; set; } = true;
    public bool SmsNotifications { get; set; } = false;
    public bool NotifyOnExamStart { get; set; } = true;
    public bool NotifyOnExamEnd { get; set; } = true;
    public bool NotifyOnResultsAvailable { get; set; } = true;

    // Grading Settings
    public string GradingSystem { get; set; } = "Arabic"; // Percentage, Letter, Arabic, Points
    public int ExcellentThreshold { get; set; } = 90;
    public int VeryGoodThreshold { get; set; } = 80;
    public int GoodThreshold { get; set; } = 70;
    public int AcceptableThreshold { get; set; } = 60;
    public bool ShowDetailedGrades { get; set; } = true;
    public bool AllowPartialCredit { get; set; } = false;
}

public class QuestionBankDto
{
    public int Id { get; set; }
    public string QuestionText { get; set; } = string.Empty;
    public QuestionType Type { get; set; }
    public int SubjectId { get; set; }
    public string SubjectName { get; set; } = string.Empty;
    public int ClassId { get; set; }
    public string ClassName { get; set; } = string.Empty;
    public string Difficulty { get; set; } = string.Empty; // Easy, Medium, Hard
    public int Marks { get; set; }
    public string? ImageUrl { get; set; }
    public string? CorrectAnswer { get; set; }
    public string? Explanation { get; set; }
    public List<string> Keywords { get; set; } = new();
    public List<QuestionOptionDto> Options { get; set; } = new();
    public int UsageCount { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime? LastUsedDate { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class ExamReportSummaryDto
{
    public int TotalExams { get; set; }
    public int TotalStudents { get; set; }
    public double AverageScore { get; set; }
    public double PassRate { get; set; }
    public Dictionary<string, int> GradeDistribution { get; set; } = new();
    public int TotalAttempts { get; set; }
    public double CompletionRate { get; set; }
}

public class DetailedExamReportDto
{
    public int ExamId { get; set; }
    public string ExamTitle { get; set; } = string.Empty;
    public string ExamDescription { get; set; } = string.Empty;
    public string SubjectName { get; set; } = string.Empty;
    public string ClassName { get; set; } = string.Empty;
    public DateTime ExamDate { get; set; }
    public int TotalStudents { get; set; }
    public double AverageScore { get; set; }
    public double PassRate { get; set; }
    public double HighestScore { get; set; }
    public double LowestScore { get; set; }
    public int TotalQuestions { get; set; }
    public int TotalMarks { get; set; }
    public double AverageTimeSpent { get; set; }
}

public class ComparativeDataDto
{
    public string Title { get; set; } = string.Empty;
    public Dictionary<string, string> Metrics { get; set; } = new();
}

public class AdvancedStatisticsDto
{
    public double HighestScore { get; set; }
    public double LowestScore { get; set; }
    public double MedianScore { get; set; }
    public double StandardDeviation { get; set; }
    public double HardestQuestionAccuracy { get; set; }
    public double EasiestQuestionAccuracy { get; set; }
    public double AverageTimePerQuestion { get; set; }
    public double CompletionRate { get; set; }
    public Dictionary<string, double> QuestionTypeAccuracy { get; set; } = new();
    public Dictionary<string, double> DifficultyLevelAccuracy { get; set; } = new();
}
