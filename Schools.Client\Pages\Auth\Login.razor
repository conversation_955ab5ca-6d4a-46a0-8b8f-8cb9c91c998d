@page "/login"
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@using System.ComponentModel.DataAnnotations
@using Schools.Client.Services
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>تسجيل الدخول - نظام إدارة المدارس</PageTitle>

<div class="container-fluid vh-100 d-flex align-items-center justify-content-center bg-light">
    <div class="row w-100">
        <div class="col-md-6 col-lg-4 mx-auto">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h3 class="mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>
                        نظام إدارة المدارس
                    </h3>
                    <p class="mb-0 mt-2">تسجيل الدخول</p>
                </div>
                <div class="card-body p-4">
                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            @errorMessage
                        </div>
                    }

                    <EditForm Model="loginModel" OnValidSubmit="HandleLogin">
                        <DataAnnotationsValidator />

                        <div class="mb-3">
                            <label class="form-label">اختر الدور:</label>
                            <select @bind="loginModel.Role" class="form-select">
                                <option value="">-- اختر الدور --</option>
                                <option value="Admin">مدير النظام</option>
                                <option value="Teacher">معلم</option>
                                <option value="Student">طالب</option>
                                <option value="Parent">ولي أمر</option>
                                <option value="Employee">موظف</option>
                                <option value="Accountant">محاسب</option>
                            </select>
                            <ValidationMessage For="() => loginModel.Role" class="text-danger" />
                        </div>

                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني:</label>
                            <input type="email" @bind="loginModel.Email" class="form-control" placeholder="أدخل البريد الإلكتروني" />
                            <ValidationMessage For="() => loginModel.Email" class="text-danger" />
                        </div>

                        <div class="mb-4">
                            <label class="form-label">كلمة المرور:</label>
                            <input type="password" @bind="loginModel.Password" class="form-control" placeholder="أدخل كلمة المرور" />
                            <ValidationMessage For="() => loginModel.Password" class="text-danger" />
                        </div>

                        <button type="submit" class="btn btn-primary w-100 py-2" disabled="@isLoading">
                            @if (isLoading)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                <span>جاري تسجيل الدخول...</span>
                            }
                            else
                            {
                                <i class="fas fa-sign-in-alt me-2"></i>
                                <span>تسجيل الدخول</span>
                            }
                        </button>
                    </EditForm>

                    <hr class="my-4">

                    <div class="text-center">
                        <h6 class="text-muted mb-3">تسجيل دخول سريع:</h6>
                        <div class="row g-2">
                            <div class="col-6">
                                <button class="btn btn-outline-primary btn-sm w-100" @onclick="@(() => QuickLogin("Admin"))">
                                    <i class="fas fa-user-shield"></i> مدير
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-success btn-sm w-100" @onclick="@(() => QuickLogin("Teacher"))">
                                    <i class="fas fa-chalkboard-teacher"></i> معلم
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-info btn-sm w-100" @onclick="@(() => QuickLogin("Student"))">
                                    <i class="fas fa-user-graduate"></i> طالب
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-outline-warning btn-sm w-100" @onclick="@(() => QuickLogin("Parent"))">
                                    <i class="fas fa-users"></i> ولي أمر
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer text-center text-muted py-3">
                    <small>© 2024 نظام إدارة المدارس - جميع الحقوق محفوظة</small>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private LoginModel loginModel = new();
    private string errorMessage = "";
    private bool isLoading = false;

    public class LoginModel
    {
        [Required(ErrorMessage = "الدور مطلوب")]
        public string Role { get; set; } = "";

        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        public string Email { get; set; } = "";

        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        public string Password { get; set; } = "";
    }

    private async Task HandleLogin()
    {
        isLoading = true;
        errorMessage = "";

        try
        {
            // Simulate login process
            await Task.Delay(1000);

            // Create mock user claims
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.Name, loginModel.Email),
                new Claim(ClaimTypes.Email, loginModel.Email),
                new Claim(ClaimTypes.Role, loginModel.Role),
                new Claim(ClaimTypes.NameIdentifier, Guid.NewGuid().ToString()),
                new Claim("FullName", GetFullNameByRole(loginModel.Role))
            };

            // Store in session storage for demo purposes
            await JSRuntime.InvokeVoidAsync("sessionStorage.setItem", "userClaims", System.Text.Json.JsonSerializer.Serialize(claims.Select(c => new { c.Type, c.Value })));

            // Notify authentication state changed
            if (AuthenticationStateProvider is CustomAuthenticationStateProvider customProvider)
            {
                customProvider.NotifyUserAuthentication(claims);
            }

            // Navigate to appropriate dashboard
            var dashboardUrl = GetDashboardUrl(loginModel.Role);
            Navigation.NavigateTo(dashboardUrl);
        }
        catch (Exception ex)
        {
            errorMessage = "حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.";
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task QuickLogin(string role)
    {
        loginModel.Role = role;
        loginModel.Email = GetDefaultEmail(role);
        loginModel.Password = "123456";

        await HandleLogin();
    }

    private string GetDefaultEmail(string role) => role.ToLower() switch
    {
        "admin" => "<EMAIL>",
        "teacher" => "<EMAIL>",
        "student" => "<EMAIL>",
        "parent" => "<EMAIL>",
        "employee" => "<EMAIL>",
        "accountant" => "<EMAIL>",
        _ => "<EMAIL>"
    };

    private string GetFullNameByRole(string role) => role switch
    {
        "Admin" => "أحمد المدير",
        "Teacher" => "فاطمة المعلمة",
        "Student" => "محمد الطالب",
        "Parent" => "خالد ولي الأمر",
        "Employee" => "نورا الموظفة",
        "Accountant" => "سالم المحاسب",
        _ => "مستخدم النظام"
    };

    private string GetDashboardUrl(string role) => role.ToLower() switch
    {
        "admin" => "/admin",
        "teacher" => "/teacher",
        "student" => "/student",
        "parent" => "/parent",
        "employee" => "/employee",
        "accountant" => "/accountant",
        _ => "/"
    };
}
