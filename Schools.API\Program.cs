using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Schools.API;
using Schools.API.Middleware;
using Schools.API.Services;
using Schools.Data;
using Schools.Shared.Models;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// Configure Settings
builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection("JwtSettings"));
builder.Services.Configure<SchoolSettings>(builder.Configuration.GetSection("SchoolSettings"));
builder.Services.Configure<LibrarySettings>(builder.Configuration.GetSection("LibrarySettings"));
builder.Services.Configure<AttendanceSettings>(builder.Configuration.GetSection("AttendanceSettings"));
builder.Services.Configure<GradingSettings>(builder.Configuration.GetSection("GradingSettings"));
builder.Services.Configure<ActivitySettings>(builder.Configuration.GetSection("ActivitySettings"));
builder.Services.Configure<NotificationSettings>(builder.Configuration.GetSection("NotificationSettings"));
builder.Services.Configure<FileUploadSettings>(builder.Configuration.GetSection("FileUploadSettings"));
builder.Services.Configure<SecuritySettings>(builder.Configuration.GetSection("SecuritySettings"));
builder.Services.Configure<ApiSettings>(builder.Configuration.GetSection("ApiSettings"));
builder.Services.Configure<DatabaseSettings>(builder.Configuration.GetSection("DatabaseSettings"));
builder.Services.Configure<CacheSettings>(builder.Configuration.GetSection("CacheSettings"));
builder.Services.Configure<EmailSettings>(builder.Configuration.GetSection("EmailSettings"));
builder.Services.Configure<SmsSettings>(builder.Configuration.GetSection("SmsSettings"));
builder.Services.Configure<ReportSettings>(builder.Configuration.GetSection("ReportSettings"));

// Register Settings Service
builder.Services.AddScoped<Schools.API.Services.ISettingsService, Schools.API.Services.SettingsService>();

// Add CORS services
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowBlazorClient", policy =>
    {
        policy.WithOrigins("http://localhost:5131", "https://localhost:5131", "http://localhost:5000", "https://localhost:5001")
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add custom services
builder.Services.AddScoped<IReportService, ReportServiceImpl>();
builder.Services.AddScoped<INotificationService, NotificationService>();
builder.Services.AddScoped<IStudentService, StudentService>();
builder.Services.AddScoped<ITeacherService, TeacherService>();
builder.Services.AddScoped<IExamService, ExamService>();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Database Configuration
var databaseSettings = builder.Configuration.GetSection("DatabaseSettings").Get<DatabaseSettings>() ?? new DatabaseSettings();
builder.Services.AddDbContext<SchoolsDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"), sqlOptions =>
    {
        sqlOptions.CommandTimeout(databaseSettings.CommandTimeout);
        sqlOptions.EnableRetryOnFailure(
            maxRetryCount: databaseSettings.MaxRetryCount,
            maxRetryDelay: TimeSpan.Parse(databaseSettings.MaxRetryDelay),
            errorNumbersToAdd: null);
    });

    if (databaseSettings.EnableSensitiveDataLogging)
        options.EnableSensitiveDataLogging();

    if (databaseSettings.EnableDetailedErrors)
        options.EnableDetailedErrors();

    if (databaseSettings.EnableServiceProviderCaching)
        options.EnableServiceProviderCaching();
});

// Add ApplicationDbContext alias for compatibility
builder.Services.AddScoped<Schools.API.Data.ApplicationDbContext>(provider =>
    provider.GetRequiredService<SchoolsDbContext>());

// Identity Configuration
var securitySettings = builder.Configuration.GetSection("SecuritySettings").Get<SecuritySettings>() ?? new SecuritySettings();
builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options =>
{
    // Password settings from configuration
    options.Password.RequireDigit = securitySettings.RequireDigit;
    options.Password.RequireLowercase = securitySettings.RequireLowercase;
    options.Password.RequireNonAlphanumeric = securitySettings.RequireNonAlphanumeric;
    options.Password.RequireUppercase = securitySettings.RequireUppercase;
    options.Password.RequiredLength = securitySettings.PasswordMinLength;
    options.Password.RequiredUniqueChars = 1;

    // Lockout settings from configuration
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(securitySettings.LockoutTimeSpanMinutes);
    options.Lockout.MaxFailedAccessAttempts = securitySettings.MaxFailedAccessAttempts;
    options.Lockout.AllowedForNewUsers = securitySettings.AllowedForNewUsers;

    // User settings from configuration
    options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
    options.User.RequireUniqueEmail = true;

    // Email and phone confirmation
    options.SignIn.RequireConfirmedEmail = securitySettings.RequireConfirmedEmail;
    options.SignIn.RequireConfirmedPhoneNumber = securitySettings.RequireConfirmedPhoneNumber;
})
.AddEntityFrameworkStores<SchoolsDbContext>()
.AddDefaultTokenProviders();

// JWT Configuration
var jwtSettings = builder.Configuration.GetSection("JwtSettings").Get<JwtSettings>() ?? new JwtSettings();
var key = Encoding.ASCII.GetBytes(jwtSettings.Secret);

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = jwtSettings.ValidateIssuerSigningKey,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = jwtSettings.ValidateIssuer,
        ValidIssuer = jwtSettings.Issuer,
        ValidateAudience = jwtSettings.ValidateAudience,
        ValidAudience = jwtSettings.Audience,
        ValidateLifetime = jwtSettings.ValidateLifetime,
        RequireExpirationTime = jwtSettings.RequireExpirationTime,
        RequireSignedTokens = jwtSettings.RequireSignedTokens,
        ClockSkew = TimeSpan.FromMinutes(jwtSettings.ClockSkewInMinutes)
    };
});

// Authorization
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("AdminOnly", policy => policy.RequireRole("Admin"));
    options.AddPolicy("TeacherOnly", policy => policy.RequireRole("Teacher"));
    options.AddPolicy("StudentOnly", policy => policy.RequireRole("Student"));
    options.AddPolicy("ParentOnly", policy => policy.RequireRole("Parent"));
    options.AddPolicy("EmployeeOnly", policy => policy.RequireRole("Employee"));
    options.AddPolicy("AccountantOnly", policy => policy.RequireRole("Accountant"));
    options.AddPolicy("TeacherOrAdmin", policy => policy.RequireRole("Teacher", "Admin"));
    options.AddPolicy("StudentOrParent", policy => policy.RequireRole("Student", "Parent"));
});

// CORS Configuration already added above

// SignalR
builder.Services.AddSignalR();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Add custom middleware
app.UseMiddleware<ExceptionHandlingMiddleware>();
app.UseMiddleware<RequestLoggingMiddleware>();
app.UseMiddleware<RateLimitingMiddleware>();

// Use CORS
app.UseCors("AllowBlazorClient");

// Only use HTTPS redirection in production
if (!app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
}

app.UseAuthentication();

// Add development auth middleware for demo purposes
if (app.Environment.IsDevelopment())
{
    app.UseDevelopmentAuth();
}

app.UseAuthorization();

app.MapControllers();

// SignalR Hubs
app.MapHub<NotificationHub>("/notificationHub");

// Initialize seed data
await SeedData.Initialize(app.Services);

app.Run();
