# 📁 Schools.Client Models Documentation

## 🎯 **نظرة عامة على Models**

تحتوي مجلد Models على جميع النماذج المطلوبة للعمل مع API والواجهات في نظام إدارة المدارس.

---

## 📋 **قائمة Models المتوفرة:**

### **📄 1. DocumentModels.cs**
- **DocumentModel** - نموذج المستند الأساسي
- **DocumentUploadModel** - نموذج رفع المستندات
- **DocumentStatisticsModel** - إحصائيات المستندات
- **DocumentFilterModel** - فلترة المستندات
- **CleanupRequestModel** - طلب تنظيف الملفات
- **CleanupResultModel** - نتيجة عملية التنظيف
- **DocumentCategories** - فئات المستندات
- **DocumentTypes** - أنواع الملفات مع الأيقونات والألوان

### **🎉 2. EventModels.cs**
- **EventModel** - نموذج الفعالية الأساسي
- **CreateEventModel** - إنشاء فعالية جديدة
- **EventParticipantModel** - مشارك في الفعالية
- **EventFilterModel** - فلترة الفعاليات
- **EventCalendarModel** - فعالية في التقويم
- **EventStatisticsModel** - إحصائيات الفعاليات
- **EventCategories** - فئات الفعاليات مع الأيقونات والألوان
- **EventStatuses** - حالات الفعاليات
- **ParticipantStatuses** - حالات المشاركين

### **📈 3. AnalyticsModels.cs**
- **AnalyticsOverviewModel** - نظرة عامة على التحليلات
- **PerformanceTrendsModel** - اتجاهات الأداء
- **MonthlyPerformanceModel** - الأداء الشهري
- **SubjectPerformanceModel** - أداء المواد
- **GradeDistributionModel** - توزيع الدرجات
- **AttendanceHeatmapModel** - خريطة الحضور الحرارية
- **PredictiveAnalyticsModel** - التحليل التنبؤي
- **RecommendationModel** - التوصيات الذكية
- **RiskAnalysisModel** - تحليل المخاطر
- **ChartDataModel** - بيانات الرسوم البيانية
- **AnalyticsConstants** - ثوابت التحليلات

### **📊 4. ReportModels.cs**
- **StudentPerformanceReportModel** - تقرير أداء الطلاب
- **AttendanceReportModel** - تقرير الحضور
- **FinancialReportModel** - التقرير المالي
- **ClassSummaryReportModel** - ملخص الصفوف
- **TeacherPerformanceReportModel** - تقرير أداء المعلمين
- **ReportFilterModel** - فلترة التقارير
- **ExportOptionsModel** - خيارات التصدير
- **ReportScheduleModel** - جدولة التقارير
- **ReportTypes** - أنواع التقارير
- **ExportFormats** - صيغ التصدير

### **🎛️ 5. DashboardModels.cs**
- **DashboardOverviewModel** - نظرة عامة على لوحة التحكم
- **KPICardModel** - بطاقة مؤشر الأداء
- **ChartWidgetModel** - عنصر الرسم البياني
- **RecentActivityModel** - النشاط الحديث
- **QuickActionModel** - الإجراءات السريعة
- **WidgetConfigModel** - تكوين العناصر
- **DashboardLayoutModel** - تخطيط لوحة التحكم
- **DashboardCalendarEventModel** - فعالية التقويم في لوحة التحكم
- **TaskModel** - نموذج المهمة
- **WeatherModel** - نموذج الطقس
- **SystemStatusModel** - حالة النظام

### **🔔 6. NotificationModels.cs**
- **UserNotificationModel** - إشعار المستخدم
- **CreateNotificationModel** - إنشاء إشعار
- **NotificationFilterModel** - فلترة الإشعارات
- **NotificationSettingsModel** - إعدادات الإشعارات
- **NotificationTemplateModel** - قالب الإشعار
- **BulkNotificationModel** - الإشعارات المجمعة
- **NotificationTypes** - أنواع الإشعارات
- **NotificationPriorities** - أولويات الإشعارات
- **NotificationCategories** - فئات الإشعارات
- **TargetAudiences** - الجمهور المستهدف

### **⚙️ 7. SettingsModels.cs**
- **SystemSettingsModel** - إعدادات النظام
- **AcademicSettingsModel** - الإعدادات الأكاديمية
- **SecuritySettingsModel** - إعدادات الأمان
- **SystemNotificationSettingsModel** - إعدادات إشعارات النظام
- **BackupSettingsModel** - إعدادات النسخ الاحتياطي
- **IntegrationSettingsModel** - إعدادات التكامل
- **UserPreferencesModel** - تفضيلات المستخدم
- **FeatureToggleModel** - تبديل الميزات
- **MaintenanceWindowModel** - نافذة الصيانة
- **AuditLogModel** - سجل التدقيق

### **📅 8. CalendarModels.cs**
- **CalendarEventModel** - فعالية التقويم
- **RecurrenceModel** - نموذج التكرار
- **ReminderModel** - نموذج التذكير
- **CreateCalendarEventModel** - إنشاء فعالية تقويم
- **CalendarViewModel** - عرض التقويم
- **CalendarFilterModel** - فلترة التقويم
- **CalendarSettingsModel** - إعدادات التقويم
- **HolidayModel** - نموذج العطلة
- **AcademicCalendarModel** - التقويم الأكاديمي
- **CalendarEventTypes** - أنواع فعاليات التقويم
- **RecurrencePatterns** - أنماط التكرار
- **ReminderTypes** - أنواع التذكيرات

---

## 🎨 **الميزات المتقدمة:**

### **🔧 Static Classes للثوابت:**
- **DocumentCategories** - فئات المستندات مع الترجمة العربية
- **DocumentTypes** - أنواع الملفات مع الأيقونات والألوان
- **EventCategories** - فئات الفعاليات مع الأيقونات والألوان
- **EventStatuses** - حالات الفعاليات مع أصناف Bootstrap
- **NotificationTypes** - أنواع الإشعارات مع الأيقونات
- **AnalyticsConstants** - ثوابت التحليلات والألوان
- **SettingsCategories** - فئات الإعدادات مع الأيقونات
- **CalendarEventTypes** - أنواع فعاليات التقويم

### **📊 Chart & Data Models:**
- **ChartDataModel** - بيانات الرسوم البيانية
- **ChartDatasetModel** - مجموعة بيانات الرسم البياني
- **KPIModel** - مؤشرات الأداء الرئيسية
- **TrendModel** - نماذج الاتجاهات

### **🎯 Filter & Search Models:**
- **DocumentFilterModel** - فلترة المستندات
- **EventFilterModel** - فلترة الفعاليات
- **NotificationFilterModel** - فلترة الإشعارات
- **ReportFilterModel** - فلترة التقارير
- **CalendarFilterModel** - فلترة التقويم
- **AnalyticsFilterModel** - فلترة التحليلات

### **📤 Export & Import Models:**
- **ExportOptionsModel** - خيارات التصدير
- **CalendarExportModel** - تصدير التقويم
- **CalendarImportModel** - استيراد التقويم
- **ReportScheduleModel** - جدولة التقارير

---

## 🚀 **الاستخدام:**

```csharp
// مثال على استخدام DocumentModel
var document = new DocumentModel
{
    Name = "دليل الطالب.pdf",
    Category = "academic",
    FileType = "pdf",
    Size = 2048576
};

// مثال على استخدام EventModel
var eventModel = new EventModel
{
    Title = "حفل التخرج",
    Category = "academic",
    StartDate = DateTime.Now.AddDays(30),
    MaxParticipants = 200
};

// مثال على استخدام AnalyticsOverviewModel
var analytics = new AnalyticsOverviewModel
{
    TotalStudents = 450,
    AverageGPA = 3.42,
    AttendanceRate = 92.8
};
```

---

## 📝 **ملاحظات مهمة:**

1. **جميع Models تدعم Arabic RTL**
2. **تحتوي على Validation Attributes**
3. **متوافقة مع API Controllers**
4. **تدعم JSON Serialization**
5. **تحتوي على Default Values**
6. **منظمة حسب الوظيفة**
7. **تدعم Filtering & Pagination**
8. **متوافقة مع Bootstrap Classes**

---

## 🎯 **الإحصائيات:**

- **📁 8 ملفات Models**
- **🔧 80+ نموذج مختلف**
- **📊 20+ Static Classes للثوابت**
- **🎨 50+ Dictionary للترجمة والألوان**
- **📤 10+ نماذج للتصدير والاستيراد**
- **🔍 15+ نماذج للفلترة والبحث**
- **📈 25+ نماذج للتحليلات والإحصائيات**

**جميع Models جاهزة للاستخدام مع Controllers والصفحات!** 🎉
