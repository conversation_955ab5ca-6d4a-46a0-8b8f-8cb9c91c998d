﻿@page "/"
@using Microsoft.AspNetCore.Components.Authorization
@using Schools.Client.Shared
@inject AuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation

<PageTitle>نظام إدارة المدارس</PageTitle>

@if (isLoading)
{
    <div class="d-flex justify-content-center align-items-center" style="height: 100vh;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
    </div>
}
else
{
    <AuthorizeView>
        <Authorized>
            <DashboardComponent />
        </Authorized>
        <NotAuthorized>
            <WelcomeComponent />
        </NotAuthorized>
    </AuthorizeView>
}

@code {
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Wait for authentication state to be determined
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            isLoading = false;
            StateHasChanged();
        }
        catch
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}
