using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Schools.Shared.DTOs;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class EventsController : ControllerBase
    {
        private readonly ILogger<EventsController> _logger;

        public EventsController(ILogger<EventsController> logger)
        {
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<EventDto>>> GetEvents()
        {
            try
            {
                var events = new List<EventDto>
                {
                    new() { Id = 1, Title = "حفل التخرج", Description = "حفل تخرج الطلاب للعام الدراسي", Category = "academic", StartDate = DateTime.Now.AddDays(15), EndDate = DateTime.Now.AddDays(15).AddHours(3), Location = "القاعة الكبرى", MaxParticipants = 200, CurrentParticipants = 150, Status = "scheduled", CreatedBy = "Admin", CreatedDate = DateTime.Now.AddDays(-10), IsPublic = true, RequiresApproval = false },
                    new() { Id = 2, Title = "مباراة كرة القدم", Description = "مباراة ودية بين المدارس", Category = "sports", StartDate = DateTime.Now.AddDays(7), EndDate = DateTime.Now.AddDays(7).AddHours(2), Location = "الملعب الرياضي", MaxParticipants = 50, CurrentParticipants = 35, Status = "active", CreatedBy = "Admin", CreatedDate = DateTime.Now.AddDays(-5), IsPublic = true, RequiresApproval = false },
                    new() { Id = 3, Title = "معرض العلوم", Description = "معرض مشاريع الطلاب العلمية", Category = "academic", StartDate = DateTime.Now.AddDays(20), EndDate = DateTime.Now.AddDays(22), Location = "مختبر العلوم", MaxParticipants = 80, CurrentParticipants = 60, Status = "scheduled", CreatedBy = "Admin", CreatedDate = DateTime.Now.AddDays(-8), IsPublic = true, RequiresApproval = true },
                    new() { Id = 4, Title = "أمسية شعرية", Description = "أمسية شعرية للطلاب الموهوبين", Category = "cultural", StartDate = DateTime.Now.AddDays(10), EndDate = DateTime.Now.AddDays(10).AddHours(2), Location = "المسرح", MaxParticipants = 120, CurrentParticipants = 90, Status = "scheduled", CreatedBy = "Admin", CreatedDate = DateTime.Now.AddDays(-6), IsPublic = true, RequiresApproval = false },
                    new() { Id = 5, Title = "اجتماع أولياء الأمور", Description = "اجتماع دوري مع أولياء الأمور", Category = "administrative", StartDate = DateTime.Now.AddDays(5), EndDate = DateTime.Now.AddDays(5).AddHours(2), Location = "القاعة الرئيسية", MaxParticipants = 150, CurrentParticipants = 120, Status = "active", CreatedBy = "Admin", CreatedDate = DateTime.Now.AddDays(-3), IsPublic = true, RequiresApproval = false },
                    new() { Id = 6, Title = "رحلة ترفيهية", Description = "رحلة ترفيهية للطلاب المتفوقين", Category = "social", StartDate = DateTime.Now.AddDays(25), EndDate = DateTime.Now.AddDays(25).AddHours(8), Location = "الحديقة الوطنية", MaxParticipants = 60, CurrentParticipants = 45, Status = "scheduled", CreatedBy = "Admin", CreatedDate = DateTime.Now.AddDays(-12), IsPublic = true, RequiresApproval = true }
                };

                return Ok(events);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving events");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<EventDto>> GetEvent(int id)
        {
            try
            {
                var events = await GetEvents();
                var eventsList = ((OkObjectResult)events.Result!)?.Value as List<EventDto>;
                var found = eventsList?.FirstOrDefault(e => e.Id == id);
                
                if (found == null)
                    return NotFound();

                // Add mock participants
                found.Participants = new List<EventParticipantDto>
                {
                    new() { Id = 1, EventId = id, UserId = 1, UserName = "أحمد محمد", RegistrationDate = DateTime.Now.AddDays(-2), Status = "confirmed" },
                    new() { Id = 2, EventId = id, UserId = 2, UserName = "فاطمة علي", RegistrationDate = DateTime.Now.AddDays(-1), Status = "confirmed" },
                    new() { Id = 3, EventId = id, UserId = 3, UserName = "محمد سالم", RegistrationDate = DateTime.Now.AddHours(-5), Status = "pending" }
                };

                return Ok(found);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving event {EventId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<ActionResult<EventDto>> CreateEvent(CreateEventDto createDto)
        {
            try
            {
                if (createDto.StartDate >= createDto.EndDate)
                    return BadRequest("End date must be after start date");

                var newEvent = new EventDto
                {
                    Id = new Random().Next(100, 999),
                    Title = createDto.Title,
                    Description = createDto.Description,
                    Category = createDto.Category,
                    StartDate = createDto.StartDate,
                    EndDate = createDto.EndDate,
                    Location = createDto.Location,
                    MaxParticipants = createDto.MaxParticipants,
                    CurrentParticipants = 0,
                    Status = "scheduled",
                    CreatedBy = User.Identity?.Name ?? "System",
                    CreatedDate = DateTime.UtcNow,
                    IsPublic = createDto.IsPublic,
                    RequiresApproval = createDto.RequiresApproval,
                    Participants = new List<EventParticipantDto>()
                };

                return CreatedAtAction(nameof(GetEvent), new { id = newEvent.Id }, newEvent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating event");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<IActionResult> UpdateEvent(int id, UpdateEventDto updateDto)
        {
            try
            {
                if (updateDto.StartDate >= updateDto.EndDate)
                    return BadRequest("End date must be after start date");

                // Mock update operation
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating event {EventId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteEvent(int id)
        {
            try
            {
                // Mock delete operation
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting event {EventId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("{id}/register")]
        public async Task<ActionResult<EventParticipantDto>> RegisterForEvent(int id, RegisterEventDto registerDto)
        {
            try
            {
                var participant = new EventParticipantDto
                {
                    Id = new Random().Next(100, 999),
                    EventId = id,
                    UserId = registerDto.UserId,
                    UserName = registerDto.UserName,
                    RegistrationDate = DateTime.UtcNow,
                    Status = "confirmed"
                };

                return Ok(participant);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering for event {EventId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("{id}/unregister/{userId}")]
        public async Task<IActionResult> UnregisterFromEvent(int id, int userId)
        {
            try
            {
                // Mock unregister operation
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unregistering from event {EventId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("calendar")]
        public async Task<ActionResult<IEnumerable<CalendarEventDto>>> GetCalendarEvents(
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            try
            {
                var events = await GetEvents();
                var eventsList = ((OkObjectResult)events.Result!)?.Value as List<EventDto>;
                
                var calendarEvents = eventsList?
                    .Where(e => e.StartDate >= startDate && e.EndDate <= endDate)
                    .Select(e => new CalendarEventDto
                    {
                        Id = e.Id,
                        Title = e.Title,
                        Start = e.StartDate,
                        End = e.EndDate,
                        Category = e.Category,
                        Status = e.Status,
                        Location = e.Location
                    }).ToList() ?? new List<CalendarEventDto>();

                return Ok(calendarEvents);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving calendar events");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("statistics")]
        public async Task<ActionResult<EventStatisticsDto>> GetEventStatistics()
        {
            try
            {
                var statistics = new EventStatisticsDto
                {
                    TotalEvents = 25,
                    ActiveEvents = 3,
                    ScheduledEvents = 18,
                    CompletedEvents = 4,
                    TotalParticipants = 450,
                    CategoryDistribution = new Dictionary<string, int>
                    {
                        { "academic", 10 },
                        { "sports", 6 },
                        { "cultural", 4 },
                        { "social", 3 },
                        { "administrative", 2 }
                    },
                    MonthlyEventCounts = new Dictionary<string, int>
                    {
                        { "2024-01", 3 },
                        { "2024-02", 4 },
                        { "2024-03", 5 },
                        { "2024-04", 6 },
                        { "2024-05", 4 },
                        { "2024-06", 3 }
                    }
                };

                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving event statistics");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
