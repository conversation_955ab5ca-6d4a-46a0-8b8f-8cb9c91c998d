using System.Security.Claims;
using System.Text.Json;

namespace Schools.API.Middleware
{
    public class DevelopmentAuthMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<DevelopmentAuthMiddleware> _logger;

        public DevelopmentAuthMiddleware(RequestDelegate next, ILogger<DevelopmentAuthMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Only apply in development environment
            if (!context.RequestServices.GetRequiredService<IWebHostEnvironment>().IsDevelopment())
            {
                await _next(context);
                return;
            }

            // Check if Authorization header exists
            var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();
            if (authHeader != null && authHeader.StartsWith("Bearer "))
            {
                var token = authHeader.Substring("Bearer ".Length).Trim();
                
                try
                {
                    // Parse the simple JWT token created by the client
                    var parts = token.Split('.');
                    if (parts.Length == 3)
                    {
                        var payload = parts[1];
                        // Add padding if needed
                        switch (payload.Length % 4)
                        {
                            case 2: payload += "=="; break;
                            case 3: payload += "="; break;
                        }
                        
                        // Convert from URL-safe base64
                        payload = payload.Replace('-', '+').Replace('_', '/');
                        
                        var payloadBytes = Convert.FromBase64String(payload);
                        var payloadJson = System.Text.Encoding.UTF8.GetString(payloadBytes);
                        var payloadData = JsonSerializer.Deserialize<JsonElement>(payloadJson);

                        // Extract claims from payload
                        var claims = new List<Claim>();
                        
                        if (payloadData.TryGetProperty("sub", out var sub))
                            claims.Add(new Claim(ClaimTypes.NameIdentifier, sub.GetString() ?? ""));
                        
                        if (payloadData.TryGetProperty("name", out var name))
                            claims.Add(new Claim(ClaimTypes.Name, name.GetString() ?? ""));
                        
                        if (payloadData.TryGetProperty("email", out var email))
                            claims.Add(new Claim(ClaimTypes.Email, email.GetString() ?? ""));
                        
                        if (payloadData.TryGetProperty("role", out var role))
                            claims.Add(new Claim(ClaimTypes.Role, role.GetString() ?? ""));

                        // Create identity and principal
                        var identity = new ClaimsIdentity(claims, "DevelopmentAuth");
                        var principal = new ClaimsPrincipal(identity);
                        
                        // Set the user for this request
                        context.User = principal;
                        
                        _logger.LogInformation($"Development auth: User {name.GetString()} with role {role.GetString()} authenticated");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"Failed to parse development token: {ex.Message}");
                }
            }

            await _next(context);
        }
    }

    public static class DevelopmentAuthMiddlewareExtensions
    {
        public static IApplicationBuilder UseDevelopmentAuth(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<DevelopmentAuthMiddleware>();
        }
    }
}
