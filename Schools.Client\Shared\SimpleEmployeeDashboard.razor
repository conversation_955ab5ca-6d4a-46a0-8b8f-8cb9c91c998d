@inject IJSRuntime JSRuntime

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h4><i class="fas fa-user-tie me-2"></i>Employee Dashboard</h4>
                </div>
                <div class="card-body">
                    <p>Welcome to Employee Dashboard</p>
                    <div class="row g-3">
                        <div class="col-md-3">
                            <button class="btn btn-primary w-100" @onclick='() => ShowAlert("Attendance")'>
                                <i class="fas fa-clock"></i><br>Attendance
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-success w-100" @onclick='() => ShowAlert("Requests")'>
                                <i class="fas fa-paper-plane"></i><br>Requests
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-info w-100" @onclick='() => ShowAlert("Salary")'>
                                <i class="fas fa-money-bill-wave"></i><br>Salary
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-warning w-100" @onclick='() => ShowAlert("Leave")'>
                                <i class="fas fa-calendar-times"></i><br>Leave
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private void ShowAlert(string feature)
    {
        JSRuntime.InvokeVoidAsync("alert", $"Feature: {feature}");
    }
}
