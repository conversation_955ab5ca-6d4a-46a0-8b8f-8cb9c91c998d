namespace Schools.Shared.DTOs
{
    // Document DTOs
    public class DocumentDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string FileType { get; set; } = "";
        public string Category { get; set; } = "";
        public long Size { get; set; }
        public string FilePath { get; set; } = "";
        public DateTime UploadDate { get; set; }
        public string UploadedBy { get; set; } = "";
        public int ViewCount { get; set; }
        public int DownloadCount { get; set; }
        public bool IsPublic { get; set; }
    }

    public class DocumentUploadDto
    {
        public object File { get; set; } = null!; // Will be IFormFile in API
        public string? Name { get; set; }
        public string? Description { get; set; }
        public string? Category { get; set; }
        public bool IsPublic { get; set; } = true;
    }

    public class DocumentUpdateDto
    {
        public string? Name { get; set; }
        public string? Description { get; set; }
        public string? Category { get; set; }
        public bool IsPublic { get; set; }
    }

    public class DocumentStatisticsDto
    {
        public int TotalDocuments { get; set; }
        public long TotalSize { get; set; }
        public int TotalDownloads { get; set; }
        public int TotalViews { get; set; }
        public Dictionary<string, int> CategoryDistribution { get; set; } = new();
        public Dictionary<string, int> TypeDistribution { get; set; } = new();
        public Dictionary<string, long> TypeSizeDistribution { get; set; } = new();
    }

    public class CleanupRequestDto
    {
        public int MonthsOld { get; set; } = 6;
    }

    public class CleanupResultDto
    {
        public int DeletedCount { get; set; }
        public long FreedSpace { get; set; }
        public string Message { get; set; } = "";
    }

    // Event DTOs
    public class EventDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Category { get; set; } = "";
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Location { get; set; } = "";
        public int MaxParticipants { get; set; }
        public int CurrentParticipants { get; set; }
        public string Status { get; set; } = "";
        public string CreatedBy { get; set; } = "";
        public DateTime CreatedDate { get; set; }
        public bool IsPublic { get; set; }
        public bool RequiresApproval { get; set; }
        public List<EventParticipantDto> Participants { get; set; } = new();
    }

    public class CreateEventDto
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Category { get; set; } = "";
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Location { get; set; } = "";
        public int MaxParticipants { get; set; }
        public bool IsPublic { get; set; } = true;
        public bool RequiresApproval { get; set; } = false;
    }

    public class UpdateEventDto
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Category { get; set; } = "";
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Location { get; set; } = "";
        public int MaxParticipants { get; set; }
        public bool IsPublic { get; set; }
        public bool RequiresApproval { get; set; }
    }

    public class EventParticipantDto
    {
        public int Id { get; set; }
        public int EventId { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; } = "";
        public DateTime RegistrationDate { get; set; }
        public string Status { get; set; } = "";
    }

    public class RegisterEventDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = "";
    }

    public class UpdateEventStatusDto
    {
        public string Status { get; set; } = "";
    }

    public class CalendarEventDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public DateTime Start { get; set; }
        public DateTime End { get; set; }
        public string Category { get; set; } = "";
        public string Status { get; set; } = "";
        public string Location { get; set; } = "";
    }

    public class EventStatisticsDto
    {
        public int TotalEvents { get; set; }
        public int ActiveEvents { get; set; }
        public int ScheduledEvents { get; set; }
        public int CompletedEvents { get; set; }
        public int TotalParticipants { get; set; }
        public Dictionary<string, int> CategoryDistribution { get; set; } = new();
        public Dictionary<string, int> MonthlyEventCounts { get; set; } = new();
    }

    // Analytics DTOs
    public class AnalyticsOverviewDto
    {
        public int TotalStudents { get; set; }
        public int TotalTeachers { get; set; }
        public int TotalClasses { get; set; }
        public int TotalSubjects { get; set; }
        public double AverageGPA { get; set; }
        public double AttendanceRate { get; set; }
        public double StudentGrowthRate { get; set; }
        public double SatisfactionScore { get; set; }
        public double GPAImprovement { get; set; }
    }

    public class PerformanceTrendsDto
    {
        public string TimeRange { get; set; } = "";
        public List<MonthlyPerformanceDto> MonthlyData { get; set; } = new();
    }

    public class MonthlyPerformanceDto
    {
        public string Month { get; set; } = "";
        public string MonthName { get; set; } = "";
        public double AverageGPA { get; set; }
        public double AttendanceRate { get; set; }
    }

    public class SubjectPerformanceDto
    {
        public List<SubjectPerformanceItemDto> Subjects { get; set; } = new();
    }

    public class SubjectPerformanceItemDto
    {
        public string SubjectName { get; set; } = "";
        public double AverageScore { get; set; }
        public int TotalStudents { get; set; }
        public double PassingRate { get; set; }
    }

    public class GradeDistributionDto
    {
        public Dictionary<string, int> Distribution { get; set; } = new();
        public int TotalGrades { get; set; }
    }

    public class AttendanceHeatmapDto
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<DailyAttendanceDto> DailyData { get; set; } = new();
    }

    public class DailyAttendanceDto
    {
        public DateTime Date { get; set; }
        public double AttendanceRate { get; set; }
        public int TotalStudents { get; set; }
        public int PresentStudents { get; set; }
    }

    public class PredictiveAnalyticsDto
    {
        public double PredictedStudentGrowth { get; set; }
        public double PredictedPerformanceImprovement { get; set; }
        public double PredictedAttendanceRate { get; set; }
        public double PredictedGPA { get; set; }
        public double ConfidenceLevel { get; set; }
        public DateTime PredictionDate { get; set; }
    }

    public class RecommendationDto
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Priority { get; set; } = "";
        public string Category { get; set; } = "";
        public string EstimatedImpact { get; set; } = "";
    }

    public class RiskAnalysisDto
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Severity { get; set; } = "";
        public int RiskLevel { get; set; }
        public string Category { get; set; } = "";
        public string[] RecommendedActions { get; set; } = Array.Empty<string>();
    }

    // Report DTOs
    public class StudentPerformanceReportDto
    {
        public int TotalStudents { get; set; }
        public double AverageGrade { get; set; }
        public double HighestGrade { get; set; }
        public double LowestGrade { get; set; }
        public Dictionary<string, int> GradeDistribution { get; set; } = new();
        public Dictionary<string, double> SubjectPerformance { get; set; } = new();
    }



    public class FinancialReportDto
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TuitionFees { get; set; }
        public decimal OtherFees { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetIncome { get; set; }
        public decimal OutstandingFees { get; set; }
        public double PaymentRate { get; set; }
    }

    public class ClassSummaryReportDto
    {
        public int TotalClasses { get; set; }
        public int TotalStudents { get; set; }
        public double OverallAverageGrade { get; set; }
        public double OverallAttendanceRate { get; set; }
        public List<ClassSummaryDetailDto> ClassDetails { get; set; } = new();
    }

    public class ClassSummaryDetailDto
    {
        public int ClassId { get; set; }
        public string ClassName { get; set; } = "";
        public int TotalStudents { get; set; }
        public double AverageGrade { get; set; }
        public double AttendanceRate { get; set; }
        public int SubjectCount { get; set; }
    }

    public class TeacherPerformanceReportDto
    {
        public int TotalTeachers { get; set; }
        public double OverallAverageGrade { get; set; }
        public List<TeacherPerformanceDetailDto> TeacherDetails { get; set; } = new();
    }

    public class TeacherPerformanceDetailDto
    {
        public int TeacherId { get; set; }
        public string TeacherName { get; set; } = "";
        public int SubjectsCount { get; set; }
        public int StudentsCount { get; set; }
        public double AverageGrade { get; set; }
        public int TotalGrades { get; set; }
    }

    public class CategoryAmountDto
    {
        public string Category { get; set; } = string.Empty;
        public decimal Amount { get; set; }
    }
}
