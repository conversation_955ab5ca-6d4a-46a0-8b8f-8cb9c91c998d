@page "/accounting/students/{StudentId:int}/payment"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Accountant,Admin")]

<PageTitle>تسجيل دفعة طالب - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-money-bill text-success me-2"></i>
                        تسجيل دفعة طالب
                    </h2>
                    <p class="text-muted mb-0">تسجيل دفعة جديدة للطالب @studentFee?.StudentName</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-outline-secondary" @onclick="GoBack">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل بيانات الطالب...</p>
        </div>
    }
    else if (studentFee == null)
    {
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            لم يتم العثور على بيانات الطالب
        </div>
    }
    else
    {
        <div class="row">
            <!-- Student Info -->
            <div class="col-md-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            معلومات الطالب
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <div class="avatar-lg bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center">
                                <i class="fas fa-user fa-2x"></i>
                            </div>
                        </div>
                        <h5 class="text-center mb-3">@studentFee.StudentName</h5>

                        <div class="info-item mb-2">
                            <strong>رقم الطالب:</strong> @studentFee.StudentNumber
                        </div>
                        <div class="info-item mb-2">
                            <strong>الصف:</strong> @studentFee.GradeName
                        </div>
                        <div class="info-item mb-2">
                            <strong>الفصل:</strong> @studentFee.ClassName
                        </div>

                        <hr>

                        <div class="financial-summary">
                            <div class="d-flex justify-content-between mb-2">
                                <span>إجمالي الرسوم:</span>
                                <strong class="text-primary">@studentFee.TotalFees.ToString("C")</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>المدفوع:</span>
                                <strong class="text-success">@studentFee.PaidAmount.ToString("C")</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>المتبقي:</span>
                                <strong class="text-danger">@studentFee.OutstandingAmount.ToString("C")</strong>
                            </div>
                        </div>

                        <div class="mt-3">
                            <span class="badge @GetPaymentStatusBadge(studentFee.PaymentStatus) fs-6">
                                @GetPaymentStatusText(studentFee.PaymentStatus)
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="col-md-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-credit-card me-2"></i>
                            تسجيل الدفعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <EditForm Model="payment" OnValidSubmit="ProcessPayment">
                            <DataAnnotationsValidator />

                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">مبلغ الدفعة <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">ر.س</span>
                                        <InputNumber @bind-Value="payment.Amount" class="form-control" max="@studentFee.OutstandingAmount" />
                                    </div>
                                    <ValidationMessage For="() => payment.Amount" />
                                    <small class="text-muted">الحد الأقصى: @studentFee.OutstandingAmount.ToString("C")</small>
                                </div>

                                <div class="col-md-6">
                                    <label class="form-label">تاريخ الدفع <span class="text-danger">*</span></label>
                                    <InputDate @bind-Value="payment.PaymentDate" class="form-control" />
                                    <ValidationMessage For="() => payment.PaymentDate" />
                                </div>

                                <div class="col-md-6">
                                    <label class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                                    <InputSelect @bind-Value="payment.PaymentMethod" class="form-select">
                                        <option value="">اختر طريقة الدفع</option>
                                        <option value="Cash">نقدي</option>
                                        <option value="Bank">تحويل بنكي</option>
                                        <option value="Check">شيك</option>
                                        <option value="Card">بطاقة ائتمان</option>
                                    </InputSelect>
                                    <ValidationMessage For="() => payment.PaymentMethod" />
                                </div>

                                <div class="col-md-6">
                                    <label class="form-label">رقم المرجع</label>
                                    <InputText @bind-Value="payment.Reference" class="form-control" placeholder="رقم الإيصال أو المرجع" />
                                </div>

                                <div class="col-12">
                                    <label class="form-label">ملاحظات</label>
                                    <InputTextArea @bind-Value="payment.Notes" class="form-control" rows="3" placeholder="أي ملاحظات إضافية..." />
                                </div>
                            </div>

                            <!-- Fee Items Selection -->
                            @if (studentFee.FeeItems?.Any() == true)
                            {
                                <div class="mt-4">
                                    <h6 class="mb-3">
                                        <i class="fas fa-list me-2"></i>
                                        بنود الرسوم المستحقة
                                    </h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>
                                                        <input type="checkbox" @onchange="SelectAllFeeItems" />
                                                    </th>
                                                    <th>البند</th>
                                                    <th>المبلغ</th>
                                                    <th>تاريخ الاستحقاق</th>
                                                    <th>الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var item in studentFee.FeeItems.Where(f => !f.IsPaid))
                                                {
                                                    <tr>
                                                        <td>
                                                            <input type="checkbox" @onchange="(e) => SelectFeeItem(item.Id, e)"
                                                                   checked="@payment.FeeItemIds.Contains(item.Id)" />
                                                        </td>
                                                        <td>
                                                            <strong>@item.FeeType</strong>
                                                            @if (!string.IsNullOrEmpty(item.Description))
                                                            {
                                                                <br>
                                                                <small class="text-muted">@item.Description</small>
                                                            }
                                                        </td>
                                                        <td>
                                                            <strong class="text-primary">@item.Amount.ToString("C")</strong>
                                                        </td>
                                                        <td>
                                                            @item.DueDate.ToString("dd/MM/yyyy")
                                                            @if (item.DueDate < DateTime.Now)
                                                            {
                                                                <br>
                                                                <small class="text-danger">متأخر</small>
                                                            }
                                                        </td>
                                                        <td>
                                                            <span class="badge @(item.IsPaid ? "bg-success" : "bg-warning")">
                                                                @(item.IsPaid ? "مدفوع" : "مستحق")
                                                            </span>
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            }

                            <!-- Action Buttons -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="d-flex justify-content-end gap-2">
                                        <button type="button" class="btn btn-outline-secondary" @onclick="GoBack">
                                            <i class="fas fa-times me-2"></i>
                                            إلغاء
                                        </button>
                                        <button type="submit" class="btn btn-success" disabled="@isSaving">
                                            @if (isSaving)
                                            {
                                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                            }
                                            else
                                            {
                                                <i class="fas fa-save me-2"></i>
                                            }
                                            تسجيل الدفعة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </EditForm>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public int StudentId { get; set; }

    private StudentFeeDto? studentFee;
    private StudentPaymentDto payment = new();

    private bool isLoading = true;
    private bool isSaving = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadStudentData();
        InitializePayment();
    }

    private async Task LoadStudentData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            studentFee = await ApiService.GetStudentFeeAsync(StudentId);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل بيانات الطالب: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void InitializePayment()
    {
        payment = new StudentPaymentDto
        {
            StudentId = StudentId,
            PaymentDate = DateTime.Now,
            Amount = studentFee?.OutstandingAmount ?? 0,
            FeeItemIds = new List<int>()
        };
    }

    private async Task ProcessPayment()
    {
        if (!ValidatePayment())
            return;

        try
        {
            isSaving = true;
            StateHasChanged();

            var success = await ApiService.RecordStudentPaymentAsync(payment);
            if (success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "تم تسجيل الدفعة بنجاح");
                Navigation.NavigateTo("/accounting/fees");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ أثناء تسجيل الدفعة");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تسجيل الدفعة: {ex.Message}");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private bool ValidatePayment()
    {
        if (payment.Amount <= 0)
        {
            JSRuntime.InvokeVoidAsync("alert", "يرجى إدخال مبلغ صحيح");
            return false;
        }

        if (payment.Amount > studentFee?.OutstandingAmount)
        {
            JSRuntime.InvokeVoidAsync("alert", "المبلغ المدخل أكبر من المبلغ المستحق");
            return false;
        }

        if (string.IsNullOrWhiteSpace(payment.PaymentMethod))
        {
            JSRuntime.InvokeVoidAsync("alert", "يرجى اختيار طريقة الدفع");
            return false;
        }

        return true;
    }

    private void SelectFeeItem(int feeItemId, ChangeEventArgs e)
    {
        if ((bool)e.Value!)
        {
            if (!payment.FeeItemIds.Contains(feeItemId))
            {
                payment.FeeItemIds.Add(feeItemId);
            }
        }
        else
        {
            payment.FeeItemIds.Remove(feeItemId);
        }

        // Update payment amount based on selected items
        UpdatePaymentAmount();
    }

    private void SelectAllFeeItems(ChangeEventArgs e)
    {
        if ((bool)e.Value!)
        {
            payment.FeeItemIds = studentFee?.FeeItems?.Where(f => !f.IsPaid).Select(f => f.Id).ToList() ?? new List<int>();
        }
        else
        {
            payment.FeeItemIds.Clear();
        }

        UpdatePaymentAmount();
        StateHasChanged();
    }

    private void UpdatePaymentAmount()
    {
        if (studentFee?.FeeItems != null && payment.FeeItemIds.Any())
        {
            payment.Amount = studentFee.FeeItems
                .Where(f => payment.FeeItemIds.Contains(f.Id))
                .Sum(f => f.Amount);
        }
        else
        {
            payment.Amount = studentFee?.OutstandingAmount ?? 0;
        }

        StateHasChanged();
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/accounting/fees");
    }

    private string GetPaymentStatusBadge(string status)
    {
        return status switch
        {
            "paid" => "bg-success",
            "partial" => "bg-warning",
            "unpaid" => "bg-secondary",
            "overdue" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetPaymentStatusText(string status)
    {
        return status switch
        {
            "paid" => "مدفوع بالكامل",
            "partial" => "مدفوع جزئياً",
            "unpaid" => "غير مدفوع",
            "overdue" => "متأخر",
            _ => "غير محدد"
        };
    }
}
