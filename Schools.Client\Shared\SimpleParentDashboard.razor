@inject IJSRuntime JSRuntime

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h4><i class="fas fa-users me-2"></i>Parent Dashboard</h4>
                </div>
                <div class="card-body">
                    <p>Welcome to Parent Dashboard</p>
                    <div class="row g-3">
                        <div class="col-md-3">
                            <button class="btn btn-primary w-100" @onclick='() => ShowAlert("Children")'>
                                <i class="fas fa-child"></i><br>Children
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-success w-100" @onclick='() => ShowAlert("Grades")'>
                                <i class="fas fa-star"></i><br>Grades
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-info w-100" @onclick='() => ShowAlert("Fees")'>
                                <i class="fas fa-dollar-sign"></i><br>Fees
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-warning w-100" @onclick='() => ShowAlert("Communication")'>
                                <i class="fas fa-comments"></i><br>Communication
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private void ShowAlert(string feature)
    {
        JSRuntime.InvokeVoidAsync("alert", $"Feature: {feature}");
    }
}
