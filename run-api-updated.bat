@echo off
echo ========================================
echo    تشغيل نظام إدارة المدارس - النسخة المحدثة
echo    School Management System - API Updated
echo ========================================
echo.

echo 🔧 بناء المشروع...
echo Building project...
dotnet build --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    echo Build failed
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo Build successful
echo.

echo 🚀 بدء تشغيل الخدمات...
echo Starting services...
echo.

echo 📡 تشغيل API Server...
echo Starting API Server...
start "Schools API" cmd /k "cd Schools.API && dotnet run --urls=http://localhost:5261"

echo ⏳ انتظار تشغيل API...
echo Waiting for API to start...
timeout /t 5 /nobreak > nul

echo 🌐 تشغيل Blazor Client...
echo Starting Blazor Client...
start "Schools Client" cmd /k "cd Schools.Client && dotnet run --urls=http://localhost:5131"

echo ⏳ انتظار تشغيل العميل...
echo Waiting for Client to start...
timeout /t 8 /nobreak > nul

echo.
echo ========================================
echo ✅ تم تشغيل النظام بنجاح!
echo System started successfully!
echo ========================================
echo.
echo 🌐 الروابط المتاحة:
echo Available URLs:
echo.
echo 📱 العميل (Client): http://localhost:5131
echo 🔧 API: http://localhost:5261
echo 📚 Swagger: http://localhost:5261/swagger
echo.
echo ========================================
echo 🔐 بيانات تسجيل الدخول:
echo Login Credentials:
echo.
echo 📧 البريد: <EMAIL>
echo 🔑 كلمة المرور: Admin123!
echo.
echo ========================================
echo 🆕 الميزات الجديدة المضافة:
echo New Features Added:
echo.
echo ✅ إدارة الأنشطة والفعاليات مع API
echo ✅ إدارة المكتبة الرقمية مع API  
echo ✅ إدارة الحضور والغياب المحسن
echo ✅ إدارة الدرجات والتقييم المتقدم
echo ✅ إدارة الجداول الدراسية التفاعلية
echo ✅ خدمات API شاملة ومتقدمة
echo ✅ واجهات محدثة تعمل مع قاعدة البيانات
echo.
echo ========================================
echo 📋 ملاحظات مهمة:
echo Important Notes:
echo.
echo 🔄 جميع الصفحات محدثة لتعمل مع API
echo 🛡️ نظام مصادقة وتفويض محسن
echo 📊 إحصائيات وتقارير تفاعلية
echo 🔍 بحث وفلترة متقدمة
echo 📱 تصميم متجاوب ومحسن
echo.
echo ========================================
echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause > nul
