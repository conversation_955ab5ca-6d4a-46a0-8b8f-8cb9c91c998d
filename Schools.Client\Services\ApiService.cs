using Schools.Shared.DTOs;
using System.Net.Http.Json;
using System.Text.Json;

namespace Schools.Client.Services
{
    public class ApiService : IApiService
    {
        private readonly HttpClient _httpClient;
        private readonly IAuthService _authService;

        public ApiService(HttpClient httpClient, IAuthService authService)
        {
            _httpClient = httpClient;
            _authService = authService;
        }

        private async Task SetAuthorizationHeaderAsync()
        {
            var token = await _authService.GetTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }
        }

        #region Academic Years

        public async Task<IEnumerable<AcademicYearDto>> GetAcademicYearsAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/admin/academic-years");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<AcademicYearDto>>() ?? new List<AcademicYearDto>();
                }
            }
            catch { }
            return new List<AcademicYearDto>();
        }

        public async Task<AcademicYearDto?> CreateAcademicYearAsync(CreateAcademicYearDto academicYear)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/admin/academic-years", academicYear);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<AcademicYearDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> UpdateAcademicYearAsync(int id, CreateAcademicYearDto academicYear)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PutAsJsonAsync($"api/admin/academic-years/{id}", academicYear);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteAcademicYearAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/admin/academic-years/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ActivateAcademicYearAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsync($"api/admin/academic-years/{id}/activate", null);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Grades

        public async Task<IEnumerable<AcademicGradeDto>> GetGradesAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/admin/grades");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<AcademicGradeDto>>() ?? new List<AcademicGradeDto>();
                }
            }
            catch { }
            return new List<AcademicGradeDto>();
        }

        public async Task<AcademicGradeDto?> CreateGradeAsync(CreateAcademicGradeDto grade)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/admin/grades", grade);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<AcademicGradeDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> UpdateGradeAsync(int id, CreateAcademicGradeDto grade)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PutAsJsonAsync($"api/admin/grades/{id}", grade);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteGradeAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/admin/grades/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Classes

        public async Task<IEnumerable<ClassDto>> GetClassesAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/classes");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<ClassDto>>() ?? new List<ClassDto>();
                }
            }
            catch { }
            return new List<ClassDto>();
        }

        public async Task<ClassDto?> GetClassAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/classes/{id}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ClassDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<ClassDto?> CreateClassAsync(CreateClassDto classDto)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/classes", classDto);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ClassDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> UpdateClassAsync(int id, CreateClassDto classDto)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PutAsJsonAsync($"api/classes/{id}", classDto);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteClassAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/classes/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<IEnumerable<UserDto>> GetClassStudentsAsync(int classId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/classes/{classId}/students");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<UserDto>>() ?? new List<UserDto>();
                }
            }
            catch { }
            return new List<UserDto>();
        }

        #endregion

        #region Sections

        public async Task<IEnumerable<SectionDto>> GetSectionsAsync(int classId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/classes/{classId}/sections");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<SectionDto>>() ?? new List<SectionDto>();
                }
            }
            catch { }
            return new List<SectionDto>();
        }

        public async Task<SectionDto?> CreateSectionAsync(int classId, CreateSectionDto section)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync($"api/classes/{classId}/sections", section);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<SectionDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> UpdateSectionAsync(int id, CreateSectionDto section)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PutAsJsonAsync($"api/classes/sections/{id}", section);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteSectionAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/classes/sections/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Subjects

        public async Task<IEnumerable<SubjectDto>> GetSubjectsAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/subjects");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<SubjectDto>>() ?? new List<SubjectDto>();
                }
            }
            catch { }
            return new List<SubjectDto>();
        }

        public async Task<SubjectDto?> GetSubjectAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/subjects/{id}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<SubjectDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<SubjectDto?> CreateSubjectAsync(CreateSubjectDto subject)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/subjects", subject);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<SubjectDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> UpdateSubjectAsync(int id, CreateSubjectDto subject)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PutAsJsonAsync($"api/subjects/{id}", subject);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteSubjectAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/subjects/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region User Management

        public async Task<IEnumerable<UserDto>> GetUsersAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/admin/users");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<UserDto>>() ?? new List<UserDto>();
                }
            }
            catch { }
            return new List<UserDto>();
        }

        public async Task<IEnumerable<UserDto>> GetPendingUsersAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/admin/pending-users");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<UserDto>>() ?? new List<UserDto>();
                }
            }
            catch { }
            return new List<UserDto>();
        }

        public async Task<UserDto?> GetUserAsync(string userId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/admin/users/{userId}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<UserDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> ApproveUserAsync(string userId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsync($"api/admin/approve-user/{userId}", null);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> RejectUserAsync(string userId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsync($"api/admin/reject-user/{userId}", null);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ActivateUserAsync(string userId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsync($"api/admin/activate-user/{userId}", null);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeactivateUserAsync(string userId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsync($"api/admin/deactivate-user/{userId}", null);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteUserAsync(string userId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/admin/users/{userId}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<IEnumerable<UserDto>> GetTeachersAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/admin/users?role=Teacher");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<UserDto>>() ?? new List<UserDto>();
                }
            }
            catch { }
            return new List<UserDto>();
        }

        #endregion

        #region Activities

        public async Task<IEnumerable<ActivityDto>> GetActivitiesAsync(string? type = null, string? status = null, DateTime? date = null, string? search = null, int page = 1, int pageSize = 20)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var queryParams = new List<string>();
                if (!string.IsNullOrEmpty(type)) queryParams.Add($"type={type}");
                if (!string.IsNullOrEmpty(status)) queryParams.Add($"status={status}");
                if (date.HasValue) queryParams.Add($"date={date.Value:yyyy-MM-dd}");
                if (!string.IsNullOrEmpty(search)) queryParams.Add($"search={search}");
                queryParams.Add($"page={page}");
                queryParams.Add($"pageSize={pageSize}");

                var queryString = string.Join("&", queryParams);
                var response = await _httpClient.GetAsync($"api/activities?{queryString}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<ActivityDto>>() ?? new List<ActivityDto>();
                }
            }
            catch { }
            return new List<ActivityDto>();
        }

        public async Task<ActivityDto?> GetActivityAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/activities/{id}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ActivityDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<ActivityDto?> CreateActivityAsync(CreateActivityDto activity)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/activities", activity);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ActivityDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> UpdateActivityAsync(int id, UpdateActivityDto activity)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PutAsJsonAsync($"api/activities/{id}", activity);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteActivityAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/activities/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> RegisterForActivityAsync(int activityId, string studentId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync($"api/activities/{activityId}/register", new { StudentId = studentId });
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<ActivityStatisticsDto?> GetActivityStatisticsAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/activities/statistics");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ActivityStatisticsDto>();
                }
            }
            catch { }
            return null;
        }

        #endregion

        #region Library

        public async Task<IEnumerable<BookDto>> GetBooksAsync(string? category = null, string? status = null, string? grade = null, string? search = null, int page = 1, int pageSize = 20)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var queryParams = new List<string>();
                if (!string.IsNullOrEmpty(category)) queryParams.Add($"category={category}");
                if (!string.IsNullOrEmpty(status)) queryParams.Add($"status={status}");
                if (!string.IsNullOrEmpty(grade)) queryParams.Add($"grade={grade}");
                if (!string.IsNullOrEmpty(search)) queryParams.Add($"search={search}");
                queryParams.Add($"page={page}");
                queryParams.Add($"pageSize={pageSize}");

                var queryString = string.Join("&", queryParams);
                var response = await _httpClient.GetAsync($"api/library/books?{queryString}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<BookDto>>() ?? new List<BookDto>();
                }
            }
            catch { }
            return new List<BookDto>();
        }

        public async Task<BookDto?> GetBookAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/library/books/{id}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<BookDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<BookDto?> CreateBookAsync(CreateBookDto book)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/library/books", book);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<BookDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> UpdateBookAsync(int id, UpdateBookDto book)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PutAsJsonAsync($"api/library/books/{id}", book);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteBookAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/library/books/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> BorrowBookAsync(int bookId, string studentId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync($"api/library/books/{bookId}/borrow", new { StudentId = studentId });
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ReturnBookAsync(int bookId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsync($"api/library/books/{bookId}/return", null);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<LibraryStatisticsDto?> GetLibraryStatisticsAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/library/statistics");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<LibraryStatisticsDto>();
                }
            }
            catch { }
            return null;
        }

        #endregion

        #region Attendance

        public async Task<IEnumerable<AttendanceDto>> GetAttendanceAsync(int? classId = null, DateTime? date = null, string? studentId = null, int page = 1, int pageSize = 20)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var queryParams = new List<string>();
                if (classId.HasValue) queryParams.Add($"classId={classId}");
                if (date.HasValue) queryParams.Add($"date={date.Value:yyyy-MM-dd}");
                if (!string.IsNullOrEmpty(studentId)) queryParams.Add($"studentId={studentId}");
                queryParams.Add($"page={page}");
                queryParams.Add($"pageSize={pageSize}");

                var queryString = string.Join("&", queryParams);
                var response = await _httpClient.GetAsync($"api/attendance?{queryString}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<AttendanceDto>>() ?? new List<AttendanceDto>();
                }
            }
            catch { }
            return new List<AttendanceDto>();
        }

        public async Task<AttendanceDto?> CreateAttendanceAsync(CreateAttendanceDto attendance)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/attendance", attendance);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<AttendanceDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> UpdateAttendanceAsync(int id, UpdateAttendanceDto attendance)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PutAsJsonAsync($"api/attendance/{id}", attendance);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> CreateBulkAttendanceAsync(BulkAttendanceDto bulkAttendance)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/attendance/bulk", bulkAttendance);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<AttendanceStatisticsDto?> GetAttendanceStatisticsAsync(int? classId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var queryParams = new List<string>();
                if (classId.HasValue) queryParams.Add($"classId={classId}");
                if (startDate.HasValue) queryParams.Add($"startDate={startDate.Value:yyyy-MM-dd}");
                if (endDate.HasValue) queryParams.Add($"endDate={endDate.Value:yyyy-MM-dd}");

                var queryString = string.Join("&", queryParams);
                var response = await _httpClient.GetAsync($"api/attendance/statistics?{queryString}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<AttendanceStatisticsDto>();
                }
            }
            catch { }
            return null;
        }

        #endregion

        #region Schedules

        public async Task<IEnumerable<ScheduleDto>> GetSchedulesAsync(int? classId = null, DateTime? date = null, string? teacherId = null)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var queryParams = new List<string>();
                if (classId.HasValue) queryParams.Add($"classId={classId}");
                if (date.HasValue) queryParams.Add($"date={date.Value:yyyy-MM-dd}");
                if (!string.IsNullOrEmpty(teacherId)) queryParams.Add($"teacherId={teacherId}");

                var queryString = string.Join("&", queryParams);
                var response = await _httpClient.GetAsync($"api/schedule?{queryString}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<ScheduleDto>>() ?? new List<ScheduleDto>();
                }
            }
            catch { }
            return new List<ScheduleDto>();
        }

        public async Task<ScheduleDto?> CreateScheduleAsync(CreateScheduleDto schedule)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/schedule", schedule);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ScheduleDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> UpdateScheduleAsync(int id, UpdateScheduleDto schedule)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PutAsJsonAsync($"api/schedule/{id}", schedule);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteScheduleAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/schedule/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<WeeklyScheduleDto?> GetWeeklyScheduleAsync(int classId, DateTime startDate)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/schedule/weekly?classId={classId}&startDate={startDate:yyyy-MM-dd}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<WeeklyScheduleDto>();
                }
            }
            catch { }
            return null;
        }

        #endregion

        #region Grades Management

        public async Task<IEnumerable<GradeDto>> GetStudentGradesAsync(string? studentId = null, int? subjectId = null, string? examType = null, string? semester = null, int? academicYearId = null, int page = 1, int pageSize = 50)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var queryParams = new List<string>();
                if (!string.IsNullOrEmpty(studentId)) queryParams.Add($"studentId={studentId}");
                if (subjectId.HasValue) queryParams.Add($"subjectId={subjectId}");
                if (!string.IsNullOrEmpty(examType)) queryParams.Add($"examType={examType}");
                if (!string.IsNullOrEmpty(semester)) queryParams.Add($"semester={semester}");
                if (academicYearId.HasValue) queryParams.Add($"academicYearId={academicYearId}");
                queryParams.Add($"page={page}");
                queryParams.Add($"pageSize={pageSize}");

                var queryString = string.Join("&", queryParams);
                var response = await _httpClient.GetAsync($"api/grades?{queryString}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<GradeDto>>() ?? new List<GradeDto>();
                }
            }
            catch { }
            return new List<GradeDto>();
        }

        public async Task<GradeDto?> CreateStudentGradeAsync(CreateGradeDto grade)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/grades", grade);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<GradeDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> UpdateStudentGradeAsync(int id, UpdateGradeDto grade)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PutAsJsonAsync($"api/grades/{id}", grade);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteStudentGradeAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/grades/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> CreateBulkGradesAsync(BulkGradeDto bulkGrades)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/grades/bulk", bulkGrades);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<GradeStatisticsDto?> GetGradeStatisticsAsync(int? subjectId = null, string? examType = null, string? semester = null, int? academicYearId = null)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var queryParams = new List<string>();
                if (subjectId.HasValue) queryParams.Add($"subjectId={subjectId}");
                if (!string.IsNullOrEmpty(examType)) queryParams.Add($"examType={examType}");
                if (!string.IsNullOrEmpty(semester)) queryParams.Add($"semester={semester}");
                if (academicYearId.HasValue) queryParams.Add($"academicYearId={academicYearId}");

                var queryString = string.Join("&", queryParams);
                var response = await _httpClient.GetAsync($"api/grades/statistics?{queryString}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<GradeStatisticsDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<StudentGradeSummaryDto?> GetStudentGradeSummaryAsync(string studentId, string? semester = null, int? academicYearId = null)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var queryParams = new List<string>();
                if (!string.IsNullOrEmpty(semester)) queryParams.Add($"semester={semester}");
                if (academicYearId.HasValue) queryParams.Add($"academicYearId={academicYearId}");

                var queryString = string.Join("&", queryParams);
                var response = await _httpClient.GetAsync($"api/grades/student/{studentId}/summary?{queryString}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<StudentGradeSummaryDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<IEnumerable<AttendanceDto>> GetDailyAttendanceAsync(DateTime date)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/attendance/daily/{date:yyyy-MM-dd}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<AttendanceDto>>() ?? new List<AttendanceDto>();
                }
            }
            catch { }
            return new List<AttendanceDto>();
        }

        #endregion

        #region Parents Management

        public async Task<List<ParentDto>> GetParentsAsync(Dictionary<string, string?>? queryParams = null)
        {
            try
            {
                await SetAuthorizationHeaderAsync();

                var queryString = "";
                if (queryParams != null && queryParams.Any())
                {
                    var parameters = queryParams
                        .Where(kvp => !string.IsNullOrEmpty(kvp.Value))
                        .Select(kvp => $"{kvp.Key}={Uri.EscapeDataString(kvp.Value!)}");
                    queryString = "?" + string.Join("&", parameters);
                }

                var response = await _httpClient.GetAsync($"api/parents{queryString}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<ParentDto>>() ?? new List<ParentDto>();
                }
            }
            catch { }
            return new List<ParentDto>();
        }

        public async Task<ParentDto?> GetParentAsync(string parentId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/parents/{parentId}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ParentDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<ParentDto?> CreateParentAsync(CreateParentDto parent)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/parents", parent);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ParentDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> UpdateParentAsync(string parentId, UpdateParentDto parent)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PutAsJsonAsync($"api/parents/{parentId}", parent);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteParentAsync(string parentId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/parents/{parentId}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<ParentChildDto>> GetParentChildrenAsync(string parentId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/parents/{parentId}/children");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<ParentChildDto>>() ?? new List<ParentChildDto>();
                }
            }
            catch { }
            return new List<ParentChildDto>();
        }

        public async Task<ParentDashboardDto?> GetParentDashboardAsync(string parentId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/parents/{parentId}/dashboard");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ParentDashboardDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> LinkParentStudentAsync(LinkParentStudentDto linkDto)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/parents/link-student", linkDto);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UnlinkParentStudentAsync(string parentId, string studentId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/parents/{parentId}/unlink-student/{studentId}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<StudentSummaryDto>> GetAvailableStudentsForParentLinkAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/parents/available-students");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<StudentSummaryDto>>() ?? new List<StudentSummaryDto>();
                }
            }
            catch { }
            return new List<StudentSummaryDto>();
        }

        public async Task<dynamic> GetParentsStatisticsAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/parents/statistics");
                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<dynamic>(jsonString) ?? new { TotalParents = 0, ActiveParents = 0, AverageChildrenPerParent = 0.0 };
                }
            }
            catch { }
            return new { TotalParents = 0, ActiveParents = 0, AverageChildrenPerParent = 0.0 };
        }

        public async Task<string?> GetCurrentUserIdAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/auth/current-user");
                if (response.IsSuccessStatusCode)
                {
                    var userInfo = await response.Content.ReadFromJsonAsync<dynamic>();
                    return userInfo?.GetProperty("id").GetString();
                }
            }
            catch { }
            return null;
        }

        #endregion

        #region Electronic Exams

        public async Task<List<ExamDto>> GetExamsAsync(Dictionary<string, string?>? queryParams = null)
        {
            try
            {
                await SetAuthorizationHeaderAsync();

                var queryString = "";
                if (queryParams != null && queryParams.Any())
                {
                    var parameters = queryParams
                        .Where(kvp => !string.IsNullOrEmpty(kvp.Value))
                        .Select(kvp => $"{kvp.Key}={Uri.EscapeDataString(kvp.Value!)}");
                    queryString = "?" + string.Join("&", parameters);
                }

                var response = await _httpClient.GetAsync($"api/exams{queryString}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<ExamDto>>() ?? new List<ExamDto>();
                }
            }
            catch { }
            return new List<ExamDto>();
        }

        public async Task<ExamDto?> GetExamAsync(int examId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/exams/{examId}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ExamDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<ExamDto?> CreateExamAsync(CreateExamDto exam)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/exams", exam);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ExamDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> UpdateExamAsync(int examId, UpdateExamDto exam)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PutAsJsonAsync($"api/exams/{examId}", exam);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteExamAsync(int examId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/exams/{examId}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<dynamic> GetExamStatisticsAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/exams/statistics");
                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<dynamic>(jsonString) ?? new { TotalExams = 0, ActiveExams = 0, TotalAttempts = 0, AverageScore = 0.0 };
                }
            }
            catch { }
            return new { TotalExams = 0, ActiveExams = 0, TotalAttempts = 0, AverageScore = 0.0 };
        }

        // Student Exam Methods
        public async Task<StudentExamDashboardDto?> GetStudentExamDashboardAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/exams/student/dashboard");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<StudentExamDashboardDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<dynamic> GetStudentExamStatusAsync(int examId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/exams/{examId}/student-status");
                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<dynamic>(jsonString) ?? new { CanTakeExam = false, AttemptsUsed = 0, UnavailableReason = "" };
                }
            }
            catch { }
            return new { CanTakeExam = false, AttemptsUsed = 0, UnavailableReason = "خطأ في تحميل حالة الامتحان" };
        }

        public async Task<ExamAttemptDto?> StartExamAsync(StartExamDto startDto)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/exams/start", startDto);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ExamAttemptDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<ExamAttemptDto?> GetCurrentExamAttemptAsync(int examId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/exams/{examId}/current-attempt");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ExamAttemptDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> SaveExamAnswerAsync(SubmitAnswerDto answer)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/exams/save-answer", answer);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<ExamResultDto?> SubmitExamAsync(SubmitExamDto submitDto)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/exams/submit", submitDto);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ExamResultDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<ExamResultDto?> GetExamResultAsync(int attemptId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/exams/result/{attemptId}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ExamResultDto>();
                }
            }
            catch { }
            return null;
        }

        // Exam Questions Management
        public async Task<ExamQuestionDto?> CreateExamQuestionAsync(CreateExamQuestionDto question)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/exams/questions", question);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ExamQuestionDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> UpdateExamQuestionAsync(int questionId, CreateExamQuestionDto question)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PutAsJsonAsync($"api/exams/questions/{questionId}", question);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteExamQuestionAsync(int questionId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/exams/questions/{questionId}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        // Exam Results Management
        public async Task<List<ExamResultDto>?> GetExamResultsAsync(int examId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/exams/{examId}/results");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<ExamResultDto>>();
                }
            }
            catch { }
            return null;
        }

        public async Task<ExamStatisticsDto?> GetExamStatisticsDetailedAsync(int examId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/exams/{examId}/statistics");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ExamStatisticsDto>();
                }
            }
            catch { }
            return null;
        }

        // Exam Publishing
        public async Task<bool> PublishExamAsync(int examId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsync($"api/exams/{examId}/publish", null);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        // Student Performance Analysis
        public async Task<List<StudentPerformanceDto>?> GetStudentPerformanceAsync(Dictionary<string, string?> queryParams)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var queryString = string.Join("&", queryParams.Where(p => !string.IsNullOrEmpty(p.Value)).Select(p => $"{p.Key}={p.Value}"));
                var response = await _httpClient.GetAsync($"api/analytics/student-performance?{queryString}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<StudentPerformanceDto>>();
                }
            }
            catch { }
            return null;
        }

        public async Task<List<PerformanceTrendDto>?> GetPerformanceTrendAsync(string period)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/analytics/performance-trend?period={period}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<PerformanceTrendDto>>();
                }
            }
            catch { }
            return null;
        }

        // Exam Settings
        public async Task<ExamSettingsDto?> GetExamSettingsAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/exams/settings");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ExamSettingsDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> UpdateExamSettingsAsync(ExamSettingsDto settings)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PutAsJsonAsync("api/exams/settings", settings);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        // Question Bank Management
        public async Task<List<QuestionBankDto>?> GetQuestionBankAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/question-bank");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<QuestionBankDto>>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> DuplicateQuestionAsync(int questionId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsync($"api/question-bank/{questionId}/duplicate", null);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteQuestionFromBankAsync(int questionId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/question-bank/{questionId}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        // Exam Reports
        public async Task<ExamReportSummaryDto?> GetExamReportSummaryAsync(Dictionary<string, string?> filters)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var queryString = string.Join("&", filters.Where(p => !string.IsNullOrEmpty(p.Value)).Select(p => $"{p.Key}={p.Value}"));
                var response = await _httpClient.GetAsync($"api/reports/exam-summary?{queryString}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<ExamReportSummaryDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<List<DetailedExamReportDto>?> GetDetailedExamReportsAsync(Dictionary<string, string?> filters)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var queryString = string.Join("&", filters.Where(p => !string.IsNullOrEmpty(p.Value)).Select(p => $"{p.Key}={p.Value}"));
                var response = await _httpClient.GetAsync($"api/reports/detailed-exams?{queryString}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<DetailedExamReportDto>>();
                }
            }
            catch { }
            return null;
        }

        public async Task<List<ComparativeDataDto>?> GetComparativeDataAsync(Dictionary<string, string?> filters)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var queryString = string.Join("&", filters.Where(p => !string.IsNullOrEmpty(p.Value)).Select(p => $"{p.Key}={p.Value}"));
                var response = await _httpClient.GetAsync($"api/reports/comparative?{queryString}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<ComparativeDataDto>>();
                }
            }
            catch { }
            return null;
        }

        public async Task<AdvancedStatisticsDto?> GetAdvancedStatisticsAsync(Dictionary<string, string?> filters)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var queryString = string.Join("&", filters.Where(p => !string.IsNullOrEmpty(p.Value)).Select(p => $"{p.Key}={p.Value}"));
                var response = await _httpClient.GetAsync($"api/reports/advanced-statistics?{queryString}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<AdvancedStatisticsDto>();
                }
            }
            catch { }
            return null;
        }

        // Accounting System
        public async Task<AccountingDashboardDto?> GetAccountingDashboardAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/accounting/dashboard");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<AccountingDashboardDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<List<ReceiptVoucherDto>?> GetReceiptVouchersAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/accounting/receipt-vouchers");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<ReceiptVoucherDto>>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> ApproveReceiptVoucherAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsync($"api/accounting/receipt-vouchers/{id}/approve", null);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> PostReceiptVoucherAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsync($"api/accounting/receipt-vouchers/{id}/post", null);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteReceiptVoucherAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/accounting/receipt-vouchers/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<PaymentVoucherDto>?> GetPaymentVouchersAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/accounting/payment-vouchers");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<PaymentVoucherDto>>();
                }
            }
            catch { }
            return null;
        }

        public async Task<List<JournalEntryDto>?> GetJournalEntriesAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/accounting/journal-entries");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<JournalEntryDto>>();
                }
            }
            catch { }
            return null;
        }

        public async Task<List<AccountDto>?> GetAccountsAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/accounting/accounts");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<AccountDto>>();
                }
            }
            catch { }
            return null;
        }

        public async Task<AccountStatementDto?> GetAccountStatementAsync(int accountId, DateTime fromDate, DateTime toDate)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/accounting/accounts/{accountId}/statement?fromDate={fromDate:yyyy-MM-dd}&toDate={toDate:yyyy-MM-dd}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<AccountStatementDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<TrialBalanceDto?> GetTrialBalanceAsync(DateTime asOfDate)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/accounting/reports/trial-balance?asOfDate={asOfDate:yyyy-MM-dd}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<TrialBalanceDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<IncomeStatementDto?> GetIncomeStatementAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/accounting/reports/income-statement?fromDate={fromDate:yyyy-MM-dd}&toDate={toDate:yyyy-MM-dd}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IncomeStatementDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<BalanceSheetDto?> GetBalanceSheetAsync(DateTime asOfDate)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/accounting/reports/balance-sheet?asOfDate={asOfDate:yyyy-MM-dd}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<BalanceSheetDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> ApprovePaymentVoucherAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsync($"api/accounting/payment-vouchers/{id}/approve", null);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> PostPaymentVoucherAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsync($"api/accounting/payment-vouchers/{id}/post", null);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeletePaymentVoucherAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/accounting/payment-vouchers/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> PostJournalEntryAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsync($"api/accounting/journal-entries/{id}/post", null);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteJournalEntryAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/accounting/journal-entries/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteAccountAsync(int id)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.DeleteAsync($"api/accounting/accounts/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> CreateReceiptVoucherAsync(ReceiptVoucherDto voucher)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/accounting/receipt-vouchers", voucher);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        // Student Fees Methods
        public async Task<List<StudentFeeDto>?> GetStudentFeesAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/accounting/student-fees");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<StudentFeeDto>>();
                }
            }
            catch { }
            return null;
        }

        public async Task<StudentFeeDto?> GetStudentFeeAsync(int studentId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync($"api/accounting/student-fees/{studentId}");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<StudentFeeDto>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> RecordStudentPaymentAsync(StudentPaymentDto payment)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/accounting/student-payments", payment);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> SendPaymentReminderAsync(int studentId)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsync($"api/accounting/student-fees/{studentId}/reminder", null);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> SendBulkPaymentRemindersAsync(List<int> studentIds)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/accounting/student-fees/bulk-reminders", studentIds);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<FeeStructureDto>?> GetFeeStructuresAsync()
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.GetAsync("api/accounting/fee-structures");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<FeeStructureDto>>();
                }
            }
            catch { }
            return null;
        }

        public async Task<bool> CreateFeeStructureAsync(FeeStructureDto feeStructure)
        {
            try
            {
                await SetAuthorizationHeaderAsync();
                var response = await _httpClient.PostAsJsonAsync("api/accounting/fee-structures", feeStructure);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}
