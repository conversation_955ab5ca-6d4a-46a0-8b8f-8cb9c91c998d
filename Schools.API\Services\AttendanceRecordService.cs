using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;

namespace Schools.API.Services
{
    public interface IAttendanceRecordService
    {
        Task<AttendanceRecordDto> CreateAttendanceRecordAsync(CreateAttendanceRecordDto createDto, string recordedBy);
        Task<AttendanceRecordDto?> UpdateAttendanceRecordAsync(int id, UpdateAttendanceRecordDto updateDto, string modifiedBy);
        Task<bool> DeleteAttendanceRecordAsync(int id);
        Task<AttendanceRecordDto?> GetAttendanceRecordByIdAsync(int id);
        Task<List<AttendanceRecordDto>> GetAttendanceRecordsAsync(AttendanceSearchDto searchDto);
        Task<List<AttendanceRecordDto>> GetStudentAttendanceAsync(string studentId, DateTime? startDate = null, DateTime? endDate = null);
        Task<List<AttendanceRecordDto>> GetClassAttendanceAsync(int classId, DateTime date);
        Task<List<AttendanceRecordDto>> GetDailyAttendanceAsync(DateTime date);
        Task<bool> BulkCreateAttendanceAsync(BulkAttendanceDto bulkDto, string recordedBy);
        Task<bool> VerifyAttendanceRecordAsync(int id, string verifiedBy);
        Task<EnhancedAttendanceSummaryDto?> GetStudentAttendanceSummaryAsync(string studentId, int academicYearId, int? semesterId = null);
        Task<EnhancedAttendanceStatisticsDto> GetAttendanceStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null, int? classId = null);
        Task<List<AttendanceAlertDto>> GetAttendanceAlertsAsync(bool includeResolved = false);
        Task<bool> ResolveAttendanceAlertAsync(int alertId, string resolvedBy, string? resolutionNotes = null);
        Task<bool> SendParentNotificationAsync(int attendanceRecordId);
        Task<bool> UpdateAttendanceSummariesAsync(string? studentId = null);
        Task<List<AttendanceRecordDto>> GetPendingFollowUpsAsync();
        Task<bool> MarkFollowUpCompleteAsync(int attendanceRecordId, string completedBy, string? notes = null);
    }

    public class AttendanceRecordService : IAttendanceRecordService
    {
        private readonly SchoolsDbContext _context;
        private readonly ILogger<AttendanceRecordService> _logger;

        public AttendanceRecordService(SchoolsDbContext context, ILogger<AttendanceRecordService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<AttendanceRecordDto> CreateAttendanceRecordAsync(CreateAttendanceRecordDto createDto, string recordedBy)
        {
            try
            {
                // Check if student exists
                var student = await _context.Users.FirstOrDefaultAsync(u => u.Id == createDto.StudentId);
                if (student == null)
                    throw new InvalidOperationException("Student not found");

                // Check for duplicate record
                var existingRecord = await _context.AttendanceRecords
                    .FirstOrDefaultAsync(ar => ar.StudentId == createDto.StudentId &&
                                              ar.Date.Date == createDto.Date.Date &&
                                              ar.ScheduleId == createDto.ScheduleId);

                if (existingRecord != null)
                    throw new InvalidOperationException("Attendance record already exists for this student on this date");

                var attendanceRecord = new AttendanceRecord
                {
                    StudentId = createDto.StudentId,
                    Date = createDto.Date,
                    ClassId = createDto.ClassId,
                    SubjectId = createDto.SubjectId,
                    ScheduleId = createDto.ScheduleId,
                    Status = createDto.Status,
                    IsPresent = createDto.IsPresent,
                    IsLate = createDto.IsLate,
                    IsExcused = createDto.IsExcused,
                    CheckInTime = createDto.CheckInTime,
                    CheckOutTime = createDto.CheckOutTime,
                    ArrivalTime = createDto.ArrivalTime,
                    DepartureTime = createDto.DepartureTime,
                    Reason = createDto.Reason,
                    Notes = createDto.Notes,
                    RecordedBy = recordedBy,
                    RecordedAt = DateTime.UtcNow,
                    Temperature = createDto.Temperature,
                    HealthCheckPassed = createDto.HealthCheckPassed,
                    HealthNotes = createDto.HealthNotes,
                    CheckInLocation = createDto.CheckInLocation,
                    CheckOutLocation = createDto.CheckOutLocation,
                    Method = createDto.Method,
                    Latitude = createDto.Latitude,
                    Longitude = createDto.Longitude,
                    AcademicYearId = createDto.AcademicYearId,
                    SemesterId = createDto.SemesterId,
                    RequiresFollowUp = createDto.RequiresFollowUp,
                    FollowUpDate = createDto.FollowUpDate,
                    FollowUpNotes = createDto.FollowUpNotes,
                    CreatedAt = DateTime.UtcNow
                };

                _context.AttendanceRecords.Add(attendanceRecord);
                await _context.SaveChangesAsync();

                // Check for alerts
                await CheckAndCreateAlertsAsync(createDto.StudentId);

                // Update attendance summary
                await UpdateStudentAttendanceSummaryAsync(createDto.StudentId, createDto.Date);

                return await MapToAttendanceRecordDtoAsync(attendanceRecord);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating attendance record for student {StudentId}", createDto.StudentId);
                throw;
            }
        }

        public async Task<AttendanceRecordDto?> UpdateAttendanceRecordAsync(int id, UpdateAttendanceRecordDto updateDto, string modifiedBy)
        {
            try
            {
                var record = await _context.AttendanceRecords.FirstOrDefaultAsync(ar => ar.Id == id);
                if (record == null)
                    return null;

                // Update fields if provided
                if (updateDto.Status.HasValue)
                    record.Status = updateDto.Status.Value;

                if (updateDto.IsPresent.HasValue)
                    record.IsPresent = updateDto.IsPresent.Value;

                if (updateDto.IsLate.HasValue)
                    record.IsLate = updateDto.IsLate.Value;

                if (updateDto.IsExcused.HasValue)
                    record.IsExcused = updateDto.IsExcused.Value;

                if (updateDto.CheckInTime.HasValue)
                    record.CheckInTime = updateDto.CheckInTime;

                if (updateDto.CheckOutTime.HasValue)
                    record.CheckOutTime = updateDto.CheckOutTime;

                if (updateDto.ArrivalTime.HasValue)
                    record.ArrivalTime = updateDto.ArrivalTime;

                if (updateDto.DepartureTime.HasValue)
                    record.DepartureTime = updateDto.DepartureTime;

                if (!string.IsNullOrEmpty(updateDto.Reason))
                    record.Reason = updateDto.Reason;

                if (!string.IsNullOrEmpty(updateDto.Notes))
                    record.Notes = updateDto.Notes;

                if (updateDto.Temperature.HasValue)
                    record.Temperature = updateDto.Temperature;

                if (updateDto.HealthCheckPassed.HasValue)
                    record.HealthCheckPassed = updateDto.HealthCheckPassed.Value;

                if (!string.IsNullOrEmpty(updateDto.HealthNotes))
                    record.HealthNotes = updateDto.HealthNotes;

                if (updateDto.RequiresFollowUp.HasValue)
                    record.RequiresFollowUp = updateDto.RequiresFollowUp.Value;

                if (updateDto.FollowUpDate.HasValue)
                    record.FollowUpDate = updateDto.FollowUpDate;

                if (!string.IsNullOrEmpty(updateDto.FollowUpNotes))
                    record.FollowUpNotes = updateDto.FollowUpNotes;

                if (updateDto.IsVerified.HasValue && updateDto.IsVerified.Value)
                {
                    record.IsVerified = true;
                    record.VerifiedBy = modifiedBy;
                    record.VerifiedAt = DateTime.UtcNow;
                }

                record.ModifiedBy = modifiedBy;
                record.ModifiedAt = DateTime.UtcNow;
                record.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                // Update attendance summary
                await UpdateStudentAttendanceSummaryAsync(record.StudentId, record.Date);

                return await MapToAttendanceRecordDtoAsync(record);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating attendance record {Id}", id);
                throw;
            }
        }

        public async Task<bool> DeleteAttendanceRecordAsync(int id)
        {
            try
            {
                var record = await _context.AttendanceRecords.FirstOrDefaultAsync(ar => ar.Id == id);
                if (record == null)
                    return false;

                _context.AttendanceRecords.Remove(record);
                await _context.SaveChangesAsync();

                // Update attendance summary
                await UpdateStudentAttendanceSummaryAsync(record.StudentId, record.Date);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting attendance record {Id}", id);
                throw;
            }
        }

        public async Task<AttendanceRecordDto?> GetAttendanceRecordByIdAsync(int id)
        {
            try
            {
                var record = await _context.AttendanceRecords
                    .Include(ar => ar.Student)
                    .Include(ar => ar.Class)
                    .Include(ar => ar.Subject)
                    .Include(ar => ar.RecordedByUser)
                    .Include(ar => ar.VerifiedByUser)
                    .FirstOrDefaultAsync(ar => ar.Id == id);

                return record != null ? await MapToAttendanceRecordDtoAsync(record) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting attendance record {Id}", id);
                throw;
            }
        }

        private async Task<AttendanceRecordDto> MapToAttendanceRecordDtoAsync(AttendanceRecord record)
        {
            // Load related entities if not already loaded
            if (record.Student == null)
            {
                await _context.Entry(record)
                    .Reference(ar => ar.Student)
                    .LoadAsync();
            }

            if (record.Class == null && record.ClassId.HasValue)
            {
                await _context.Entry(record)
                    .Reference(ar => ar.Class)
                    .LoadAsync();
            }

            if (record.Subject == null && record.SubjectId.HasValue)
            {
                await _context.Entry(record)
                    .Reference(ar => ar.Subject)
                    .LoadAsync();
            }

            if (record.RecordedByUser == null && !string.IsNullOrEmpty(record.RecordedBy))
            {
                await _context.Entry(record)
                    .Reference(ar => ar.RecordedByUser)
                    .LoadAsync();
            }

            if (record.VerifiedByUser == null && !string.IsNullOrEmpty(record.VerifiedBy))
            {
                await _context.Entry(record)
                    .Reference(ar => ar.VerifiedByUser)
                    .LoadAsync();
            }

            return new AttendanceRecordDto
            {
                Id = record.Id,
                StudentId = record.StudentId,
                StudentName = $"{record.Student.FirstName} {record.Student.LastName}",
                StudentNumber = record.Student.UserName ?? "",
                StudentEmail = record.Student.Email ?? "",
                Date = record.Date,
                ClassId = record.ClassId,
                ClassName = record.Class?.Name,
                SubjectId = record.SubjectId,
                SubjectName = record.Subject?.Name,
                Status = record.Status,
                IsPresent = record.IsPresent,
                IsLate = record.IsLate,
                IsExcused = record.IsExcused,
                CheckInTime = record.CheckInTime,
                CheckOutTime = record.CheckOutTime,
                ArrivalTime = record.ArrivalTime,
                DepartureTime = record.DepartureTime,
                Reason = record.Reason,
                Notes = record.Notes,
                RecordedBy = record.RecordedBy,
                RecordedByName = record.RecordedByUser != null ? $"{record.RecordedByUser.FirstName} {record.RecordedByUser.LastName}" : null,
                RecordedAt = record.RecordedAt,
                Temperature = record.Temperature,
                HealthCheckPassed = record.HealthCheckPassed,
                HealthNotes = record.HealthNotes,
                Method = record.Method,
                IsVerified = record.IsVerified,
                VerifiedBy = record.VerifiedBy,
                VerifiedByName = record.VerifiedByUser != null ? $"{record.VerifiedByUser.FirstName} {record.VerifiedByUser.LastName}" : null,
                VerifiedAt = record.VerifiedAt,
                ParentNotified = record.ParentNotified,
                ParentNotificationSent = record.ParentNotificationSent,
                RequiresFollowUp = record.RequiresFollowUp,
                FollowUpDate = record.FollowUpDate,
                FollowUpNotes = record.FollowUpNotes
            };
        }

        public async Task<List<AttendanceRecordDto>> GetAttendanceRecordsAsync(AttendanceSearchDto searchDto)
        {
            try
            {
                var query = _context.AttendanceRecords
                    .Include(ar => ar.Student)
                    .Include(ar => ar.Class)
                    .Include(ar => ar.Subject)
                    .Include(ar => ar.RecordedByUser)
                    .Include(ar => ar.VerifiedByUser)
                    .AsQueryable();

                // Apply filters
                if (searchDto.StartDate.HasValue)
                    query = query.Where(ar => ar.Date >= searchDto.StartDate.Value);

                if (searchDto.EndDate.HasValue)
                    query = query.Where(ar => ar.Date <= searchDto.EndDate.Value);

                if (!string.IsNullOrEmpty(searchDto.StudentId))
                    query = query.Where(ar => ar.StudentId == searchDto.StudentId);

                if (searchDto.ClassId.HasValue)
                    query = query.Where(ar => ar.ClassId == searchDto.ClassId.Value);

                if (searchDto.SubjectId.HasValue)
                    query = query.Where(ar => ar.SubjectId == searchDto.SubjectId.Value);

                if (searchDto.Status.HasValue)
                    query = query.Where(ar => ar.Status == searchDto.Status.Value);

                if (searchDto.IsLate.HasValue)
                    query = query.Where(ar => ar.IsLate == searchDto.IsLate.Value);

                if (searchDto.IsExcused.HasValue)
                    query = query.Where(ar => ar.IsExcused == searchDto.IsExcused.Value);

                if (searchDto.RequiresFollowUp.HasValue)
                    query = query.Where(ar => ar.RequiresFollowUp == searchDto.RequiresFollowUp.Value);

                if (searchDto.IsVerified.HasValue)
                    query = query.Where(ar => ar.IsVerified == searchDto.IsVerified.Value);

                if (searchDto.Method.HasValue)
                    query = query.Where(ar => ar.Method == searchDto.Method.Value);

                // Apply sorting
                if (!string.IsNullOrEmpty(searchDto.SortBy))
                {
                    switch (searchDto.SortBy.ToLower())
                    {
                        case "date":
                            query = searchDto.SortDescending ? query.OrderByDescending(ar => ar.Date) : query.OrderBy(ar => ar.Date);
                            break;
                        case "student":
                            query = searchDto.SortDescending ? query.OrderByDescending(ar => ar.Student.FirstName) : query.OrderBy(ar => ar.Student.FirstName);
                            break;
                        case "status":
                            query = searchDto.SortDescending ? query.OrderByDescending(ar => ar.Status) : query.OrderBy(ar => ar.Status);
                            break;
                        default:
                            query = query.OrderByDescending(ar => ar.Date);
                            break;
                    }
                }
                else
                {
                    query = query.OrderByDescending(ar => ar.Date);
                }

                // Apply pagination
                var skip = (searchDto.Page - 1) * searchDto.PageSize;
                var records = await query.Skip(skip).Take(searchDto.PageSize).ToListAsync();

                var result = new List<AttendanceRecordDto>();
                foreach (var record in records)
                {
                    result.Add(await MapToAttendanceRecordDtoAsync(record));
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting attendance records");
                throw;
            }
        }

        public async Task<List<AttendanceRecordDto>> GetStudentAttendanceAsync(string studentId, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                var query = _context.AttendanceRecords
                    .Where(ar => ar.StudentId == studentId)
                    .Include(ar => ar.Student)
                    .Include(ar => ar.Class)
                    .Include(ar => ar.Subject)
                    .Include(ar => ar.RecordedByUser)
                    .Include(ar => ar.VerifiedByUser)
                    .AsQueryable();

                if (startDate.HasValue)
                    query = query.Where(ar => ar.Date >= startDate.Value);

                if (endDate.HasValue)
                    query = query.Where(ar => ar.Date <= endDate.Value);

                var records = await query.OrderByDescending(ar => ar.Date).ToListAsync();

                var result = new List<AttendanceRecordDto>();
                foreach (var record in records)
                {
                    result.Add(await MapToAttendanceRecordDtoAsync(record));
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting student attendance for {StudentId}", studentId);
                throw;
            }
        }

        public async Task<List<AttendanceRecordDto>> GetClassAttendanceAsync(int classId, DateTime date)
        {
            try
            {
                var records = await _context.AttendanceRecords
                    .Where(ar => ar.ClassId == classId && ar.Date.Date == date.Date)
                    .Include(ar => ar.Student)
                    .Include(ar => ar.Class)
                    .Include(ar => ar.Subject)
                    .Include(ar => ar.RecordedByUser)
                    .Include(ar => ar.VerifiedByUser)
                    .OrderBy(ar => ar.Student.FirstName)
                    .ThenBy(ar => ar.Student.LastName)
                    .ToListAsync();

                var result = new List<AttendanceRecordDto>();
                foreach (var record in records)
                {
                    result.Add(await MapToAttendanceRecordDtoAsync(record));
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting class attendance for class {ClassId} on {Date}", classId, date);
                throw;
            }
        }

        public async Task<List<AttendanceRecordDto>> GetDailyAttendanceAsync(DateTime date)
        {
            try
            {
                var records = await _context.AttendanceRecords
                    .Where(ar => ar.Date.Date == date.Date)
                    .Include(ar => ar.Student)
                    .Include(ar => ar.Class)
                    .Include(ar => ar.Subject)
                    .Include(ar => ar.RecordedByUser)
                    .Include(ar => ar.VerifiedByUser)
                    .OrderBy(ar => ar.Class.Name)
                    .ThenBy(ar => ar.Student.FirstName)
                    .ThenBy(ar => ar.Student.LastName)
                    .ToListAsync();

                var result = new List<AttendanceRecordDto>();
                foreach (var record in records)
                {
                    result.Add(await MapToAttendanceRecordDtoAsync(record));
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting daily attendance for {Date}", date);
                throw;
            }
        }

        public async Task<bool> BulkCreateAttendanceAsync(BulkAttendanceDto bulkDto, string recordedBy)
        {
            try
            {
                var attendanceRecords = new List<AttendanceRecord>();

                foreach (var studentAttendance in bulkDto.StudentAttendances)
                {
                    // Check if student exists
                    var student = await _context.Users.FirstOrDefaultAsync(u => u.Id == studentAttendance.StudentId);
                    if (student == null)
                        continue; // Skip invalid students

                    // Check for duplicate record
                    var existingRecord = await _context.AttendanceRecords
                        .FirstOrDefaultAsync(ar => ar.StudentId == studentAttendance.StudentId &&
                                                  ar.Date.Date == bulkDto.Date.Date &&
                                                  ar.ClassId == bulkDto.ClassId);

                    if (existingRecord != null)
                        continue; // Skip duplicates

                    // Parse status from string to enum
                    if (!Enum.TryParse<AttendanceStatus>(studentAttendance.Status, out var status))
                        status = AttendanceStatus.Absent;

                    var attendanceRecord = new AttendanceRecord
                    {
                        StudentId = studentAttendance.StudentId,
                        Date = bulkDto.Date,
                        ClassId = bulkDto.ClassId,
                        Status = status,
                        IsPresent = status == AttendanceStatus.Present,
                        IsLate = status == AttendanceStatus.Late,
                        IsExcused = status == AttendanceStatus.Excused,
                        ArrivalTime = studentAttendance.ArrivalTime,
                        Notes = studentAttendance.Notes,
                        RecordedBy = recordedBy,
                        RecordedAt = DateTime.UtcNow,
                        Method = AttendanceMethod.Manual,
                        CreatedAt = DateTime.UtcNow
                    };

                    attendanceRecords.Add(attendanceRecord);
                }

                if (attendanceRecords.Any())
                {
                    _context.AttendanceRecords.AddRange(attendanceRecords);
                    await _context.SaveChangesAsync();

                    // Update attendance summaries for all students
                    foreach (var record in attendanceRecords)
                    {
                        await UpdateStudentAttendanceSummaryAsync(record.StudentId, record.Date);
                        await CheckAndCreateAlertsAsync(record.StudentId);
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating bulk attendance for class {ClassId} on {Date}", bulkDto.ClassId, bulkDto.Date);
                throw;
            }
        }

        public async Task<bool> VerifyAttendanceRecordAsync(int id, string verifiedBy)
        {
            try
            {
                var record = await _context.AttendanceRecords.FirstOrDefaultAsync(ar => ar.Id == id);
                if (record == null)
                    return false;

                record.IsVerified = true;
                record.VerifiedBy = verifiedBy;
                record.VerifiedAt = DateTime.UtcNow;
                record.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying attendance record {Id}", id);
                throw;
            }
        }

        public async Task<EnhancedAttendanceSummaryDto?> GetStudentAttendanceSummaryAsync(string studentId, int academicYearId, int? semesterId = null)
        {
            try
            {
                var summary = await _context.AttendanceSummaries
                    .Include(s => s.Student)
                    .Include(s => s.AcademicYear)
                    .Include(s => s.Class)
                    .FirstOrDefaultAsync(s => s.StudentId == studentId &&
                                            s.AcademicYearId == academicYearId &&
                                            (semesterId == null || s.SemesterId == semesterId));

                if (summary == null)
                {
                    // Calculate summary from attendance records
                    var records = await _context.AttendanceRecords
                        .Where(ar => ar.StudentId == studentId &&
                                   ar.AcademicYearId == academicYearId &&
                                   (semesterId == null || ar.SemesterId == semesterId))
                        .ToListAsync();

                    if (!records.Any())
                        return null;

                    var totalDays = records.Count;
                    var presentDays = records.Count(r => r.IsPresent);
                    var absentDays = records.Count(r => !r.IsPresent);
                    var lateDays = records.Count(r => r.IsLate);
                    var excusedDays = records.Count(r => r.IsExcused);

                    var student = await _context.Users.FirstOrDefaultAsync(u => u.Id == studentId);
                    var academicYear = await _context.AcademicYears.FirstOrDefaultAsync(ay => ay.Id == academicYearId);

                    return new EnhancedAttendanceSummaryDto
                    {
                        StudentId = studentId,
                        StudentName = student != null ? $"{student.FirstName} {student.LastName}" : "",
                        StudentNumber = student?.UserName ?? "",
                        AcademicYearId = academicYearId,
                        AcademicYearName = academicYear?.Name ?? "",
                        SemesterId = semesterId,
                        TotalDays = totalDays,
                        PresentDays = presentDays,
                        AbsentDays = absentDays,
                        LateDays = lateDays,
                        ExcusedDays = excusedDays,
                        AttendanceRate = totalDays > 0 ? (decimal)presentDays / totalDays * 100 : 0,
                        LateRate = totalDays > 0 ? (decimal)lateDays / totalDays * 100 : 0,
                        LastUpdated = DateTime.UtcNow
                    };
                }

                return new EnhancedAttendanceSummaryDto
                {
                    Id = summary.Id,
                    StudentId = summary.StudentId,
                    StudentName = $"{summary.Student.FirstName} {summary.Student.LastName}",
                    StudentNumber = summary.Student.UserName ?? "",
                    AcademicYearId = summary.AcademicYearId,
                    AcademicYearName = summary.AcademicYear.Name,
                    SemesterId = summary.SemesterId,
                    ClassId = summary.ClassId,
                    ClassName = summary.Class?.Name,
                    TotalDays = summary.TotalDays,
                    PresentDays = summary.PresentDays,
                    AbsentDays = summary.AbsentDays,
                    LateDays = summary.LateDays,
                    ExcusedDays = summary.ExcusedDays,
                    AttendanceRate = summary.AttendanceRate,
                    LateRate = summary.LateRate,
                    LastUpdated = summary.LastUpdated
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting attendance summary for student {StudentId}", studentId);
                throw;
            }
        }

        public async Task<EnhancedAttendanceStatisticsDto> GetAttendanceStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null, int? classId = null)
        {
            try
            {
                var query = _context.AttendanceRecords.AsQueryable();

                if (startDate.HasValue)
                    query = query.Where(ar => ar.Date >= startDate.Value);

                if (endDate.HasValue)
                    query = query.Where(ar => ar.Date <= endDate.Value);

                if (classId.HasValue)
                    query = query.Where(ar => ar.ClassId == classId.Value);

                var records = await query.ToListAsync();
                var today = DateTime.Today;
                var todayRecords = records.Where(r => r.Date.Date == today).ToList();

                var totalStudents = records.Select(r => r.StudentId).Distinct().Count();
                var totalRecords = records.Count;
                var presentToday = todayRecords.Count(r => r.IsPresent);
                var absentToday = todayRecords.Count(r => !r.IsPresent);
                var lateToday = todayRecords.Count(r => r.IsLate);

                var overallAttendanceRate = totalRecords > 0 ? (decimal)records.Count(r => r.IsPresent) / totalRecords * 100 : 0;
                var todayAttendanceRate = todayRecords.Count > 0 ? (decimal)presentToday / todayRecords.Count * 100 : 0;

                var activeAlerts = await _context.AttendanceAlerts.CountAsync(aa => !aa.IsResolved);
                var pendingFollowUps = await _context.AttendanceRecords.CountAsync(ar => ar.RequiresFollowUp && ar.FollowUpDate <= DateTime.UtcNow);

                var statusBreakdown = records.GroupBy(r => r.Status.ToString())
                    .ToDictionary(g => g.Key, g => g.Count());

                return new EnhancedAttendanceStatisticsDto
                {
                    TotalStudents = totalStudents,
                    TotalRecords = totalRecords,
                    PresentToday = presentToday,
                    AbsentToday = absentToday,
                    LateToday = lateToday,
                    OverallAttendanceRate = overallAttendanceRate,
                    TodayAttendanceRate = todayAttendanceRate,
                    ActiveAlerts = activeAlerts,
                    PendingFollowUps = pendingFollowUps,
                    StatusBreakdown = statusBreakdown
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting attendance statistics");
                throw;
            }
        }

        public async Task<List<AttendanceAlertDto>> GetAttendanceAlertsAsync(bool includeResolved = false)
        {
            // Implementation will be added
            return new List<AttendanceAlertDto>();
        }

        public async Task<bool> ResolveAttendanceAlertAsync(int alertId, string resolvedBy, string? resolutionNotes = null)
        {
            // Implementation will be added
            return true;
        }

        public async Task<bool> SendParentNotificationAsync(int attendanceRecordId)
        {
            // Implementation will be added
            return true;
        }

        public async Task<bool> UpdateAttendanceSummariesAsync(string? studentId = null)
        {
            // Implementation will be added
            return true;
        }

        public async Task<List<AttendanceRecordDto>> GetPendingFollowUpsAsync()
        {
            // Implementation will be added
            return new List<AttendanceRecordDto>();
        }

        public async Task<bool> MarkFollowUpCompleteAsync(int attendanceRecordId, string completedBy, string? notes = null)
        {
            // Implementation will be added
            return true;
        }

        // Helper methods
        private async Task CheckAndCreateAlertsAsync(string studentId)
        {
            // Implementation will be added
        }

        private async Task UpdateStudentAttendanceSummaryAsync(string studentId, DateTime date)
        {
            // Implementation will be added
        }
    }
}
