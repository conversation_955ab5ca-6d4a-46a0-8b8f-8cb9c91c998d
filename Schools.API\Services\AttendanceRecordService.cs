using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;

namespace Schools.API.Services
{
    public interface IAttendanceRecordService
    {
        Task<AttendanceRecordDto> CreateAttendanceRecordAsync(CreateAttendanceRecordDto createDto, string recordedBy);
        Task<AttendanceRecordDto?> UpdateAttendanceRecordAsync(int id, UpdateAttendanceRecordDto updateDto, string modifiedBy);
        Task<bool> DeleteAttendanceRecordAsync(int id);
        Task<AttendanceRecordDto?> GetAttendanceRecordByIdAsync(int id);
        Task<List<AttendanceRecordDto>> GetAttendanceRecordsAsync(AttendanceSearchDto searchDto);
        Task<List<AttendanceRecordDto>> GetStudentAttendanceAsync(string studentId, DateTime? startDate = null, DateTime? endDate = null);
        Task<List<AttendanceRecordDto>> GetClassAttendanceAsync(int classId, DateTime date);
        Task<List<AttendanceRecordDto>> GetDailyAttendanceAsync(DateTime date);
        Task<bool> BulkCreateAttendanceAsync(BulkAttendanceDto bulkDto, string recordedBy);
        Task<bool> VerifyAttendanceRecordAsync(int id, string verifiedBy);
        Task<EnhancedAttendanceSummaryDto?> GetStudentAttendanceSummaryAsync(string studentId, int academicYearId, int? semesterId = null);
        Task<EnhancedAttendanceStatisticsDto> GetAttendanceStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null, int? classId = null);
        Task<List<AttendanceAlertDto>> GetAttendanceAlertsAsync(bool includeResolved = false);
        Task<bool> ResolveAttendanceAlertAsync(int alertId, string resolvedBy, string? resolutionNotes = null);
        Task<bool> SendParentNotificationAsync(int attendanceRecordId);
        Task<bool> UpdateAttendanceSummariesAsync(string? studentId = null);
        Task<List<AttendanceRecordDto>> GetPendingFollowUpsAsync();
        Task<bool> MarkFollowUpCompleteAsync(int attendanceRecordId, string completedBy, string? notes = null);
    }

    public class AttendanceRecordService : IAttendanceRecordService
    {
        private readonly SchoolsDbContext _context;
        private readonly ILogger<AttendanceRecordService> _logger;

        public AttendanceRecordService(SchoolsDbContext context, ILogger<AttendanceRecordService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<AttendanceRecordDto> CreateAttendanceRecordAsync(CreateAttendanceRecordDto createDto, string recordedBy)
        {
            try
            {
                // Check if student exists
                var student = await _context.Users.FirstOrDefaultAsync(u => u.Id == createDto.StudentId);
                if (student == null)
                    throw new InvalidOperationException("Student not found");

                // Check for duplicate record
                var existingRecord = await _context.AttendanceRecords
                    .FirstOrDefaultAsync(ar => ar.StudentId == createDto.StudentId && 
                                              ar.Date.Date == createDto.Date.Date &&
                                              ar.ScheduleId == createDto.ScheduleId);

                if (existingRecord != null)
                    throw new InvalidOperationException("Attendance record already exists for this student on this date");

                var attendanceRecord = new AttendanceRecord
                {
                    StudentId = createDto.StudentId,
                    Date = createDto.Date,
                    ClassId = createDto.ClassId,
                    SubjectId = createDto.SubjectId,
                    ScheduleId = createDto.ScheduleId,
                    Status = createDto.Status,
                    IsPresent = createDto.IsPresent,
                    IsLate = createDto.IsLate,
                    IsExcused = createDto.IsExcused,
                    CheckInTime = createDto.CheckInTime,
                    CheckOutTime = createDto.CheckOutTime,
                    ArrivalTime = createDto.ArrivalTime,
                    DepartureTime = createDto.DepartureTime,
                    Reason = createDto.Reason,
                    Notes = createDto.Notes,
                    RecordedBy = recordedBy,
                    RecordedAt = DateTime.UtcNow,
                    Temperature = createDto.Temperature,
                    HealthCheckPassed = createDto.HealthCheckPassed,
                    HealthNotes = createDto.HealthNotes,
                    CheckInLocation = createDto.CheckInLocation,
                    CheckOutLocation = createDto.CheckOutLocation,
                    Method = createDto.Method,
                    Latitude = createDto.Latitude,
                    Longitude = createDto.Longitude,
                    AcademicYearId = createDto.AcademicYearId,
                    SemesterId = createDto.SemesterId,
                    RequiresFollowUp = createDto.RequiresFollowUp,
                    FollowUpDate = createDto.FollowUpDate,
                    FollowUpNotes = createDto.FollowUpNotes,
                    CreatedAt = DateTime.UtcNow
                };

                _context.AttendanceRecords.Add(attendanceRecord);
                await _context.SaveChangesAsync();

                // Check for alerts
                await CheckAndCreateAlertsAsync(createDto.StudentId);

                // Update attendance summary
                await UpdateStudentAttendanceSummaryAsync(createDto.StudentId, createDto.Date);

                return await MapToAttendanceRecordDtoAsync(attendanceRecord);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating attendance record for student {StudentId}", createDto.StudentId);
                throw;
            }
        }

        public async Task<AttendanceRecordDto?> UpdateAttendanceRecordAsync(int id, UpdateAttendanceRecordDto updateDto, string modifiedBy)
        {
            try
            {
                var record = await _context.AttendanceRecords.FirstOrDefaultAsync(ar => ar.Id == id);
                if (record == null)
                    return null;

                // Update fields if provided
                if (updateDto.Status.HasValue)
                    record.Status = updateDto.Status.Value;

                if (updateDto.IsPresent.HasValue)
                    record.IsPresent = updateDto.IsPresent.Value;

                if (updateDto.IsLate.HasValue)
                    record.IsLate = updateDto.IsLate.Value;

                if (updateDto.IsExcused.HasValue)
                    record.IsExcused = updateDto.IsExcused.Value;

                if (updateDto.CheckInTime.HasValue)
                    record.CheckInTime = updateDto.CheckInTime;

                if (updateDto.CheckOutTime.HasValue)
                    record.CheckOutTime = updateDto.CheckOutTime;

                if (updateDto.ArrivalTime.HasValue)
                    record.ArrivalTime = updateDto.ArrivalTime;

                if (updateDto.DepartureTime.HasValue)
                    record.DepartureTime = updateDto.DepartureTime;

                if (!string.IsNullOrEmpty(updateDto.Reason))
                    record.Reason = updateDto.Reason;

                if (!string.IsNullOrEmpty(updateDto.Notes))
                    record.Notes = updateDto.Notes;

                if (updateDto.Temperature.HasValue)
                    record.Temperature = updateDto.Temperature;

                if (updateDto.HealthCheckPassed.HasValue)
                    record.HealthCheckPassed = updateDto.HealthCheckPassed.Value;

                if (!string.IsNullOrEmpty(updateDto.HealthNotes))
                    record.HealthNotes = updateDto.HealthNotes;

                if (updateDto.RequiresFollowUp.HasValue)
                    record.RequiresFollowUp = updateDto.RequiresFollowUp.Value;

                if (updateDto.FollowUpDate.HasValue)
                    record.FollowUpDate = updateDto.FollowUpDate;

                if (!string.IsNullOrEmpty(updateDto.FollowUpNotes))
                    record.FollowUpNotes = updateDto.FollowUpNotes;

                if (updateDto.IsVerified.HasValue && updateDto.IsVerified.Value)
                {
                    record.IsVerified = true;
                    record.VerifiedBy = modifiedBy;
                    record.VerifiedAt = DateTime.UtcNow;
                }

                record.ModifiedBy = modifiedBy;
                record.ModifiedAt = DateTime.UtcNow;
                record.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                // Update attendance summary
                await UpdateStudentAttendanceSummaryAsync(record.StudentId, record.Date);

                return await MapToAttendanceRecordDtoAsync(record);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating attendance record {Id}", id);
                throw;
            }
        }

        public async Task<bool> DeleteAttendanceRecordAsync(int id)
        {
            try
            {
                var record = await _context.AttendanceRecords.FirstOrDefaultAsync(ar => ar.Id == id);
                if (record == null)
                    return false;

                _context.AttendanceRecords.Remove(record);
                await _context.SaveChangesAsync();

                // Update attendance summary
                await UpdateStudentAttendanceSummaryAsync(record.StudentId, record.Date);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting attendance record {Id}", id);
                throw;
            }
        }

        public async Task<AttendanceRecordDto?> GetAttendanceRecordByIdAsync(int id)
        {
            try
            {
                var record = await _context.AttendanceRecords
                    .Include(ar => ar.Student)
                    .Include(ar => ar.Class)
                    .Include(ar => ar.Subject)
                    .Include(ar => ar.RecordedByUser)
                    .Include(ar => ar.VerifiedByUser)
                    .FirstOrDefaultAsync(ar => ar.Id == id);

                return record != null ? await MapToAttendanceRecordDtoAsync(record) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting attendance record {Id}", id);
                throw;
            }
        }

        private async Task<AttendanceRecordDto> MapToAttendanceRecordDtoAsync(AttendanceRecord record)
        {
            // Load related entities if not already loaded
            if (record.Student == null)
            {
                await _context.Entry(record)
                    .Reference(ar => ar.Student)
                    .LoadAsync();
            }

            if (record.Class == null && record.ClassId.HasValue)
            {
                await _context.Entry(record)
                    .Reference(ar => ar.Class)
                    .LoadAsync();
            }

            if (record.Subject == null && record.SubjectId.HasValue)
            {
                await _context.Entry(record)
                    .Reference(ar => ar.Subject)
                    .LoadAsync();
            }

            if (record.RecordedByUser == null && !string.IsNullOrEmpty(record.RecordedBy))
            {
                await _context.Entry(record)
                    .Reference(ar => ar.RecordedByUser)
                    .LoadAsync();
            }

            if (record.VerifiedByUser == null && !string.IsNullOrEmpty(record.VerifiedBy))
            {
                await _context.Entry(record)
                    .Reference(ar => ar.VerifiedByUser)
                    .LoadAsync();
            }

            return new AttendanceRecordDto
            {
                Id = record.Id,
                StudentId = record.StudentId,
                StudentName = $"{record.Student.FirstName} {record.Student.LastName}",
                StudentNumber = record.Student.UserName ?? "",
                StudentEmail = record.Student.Email ?? "",
                Date = record.Date,
                ClassId = record.ClassId,
                ClassName = record.Class?.Name,
                SubjectId = record.SubjectId,
                SubjectName = record.Subject?.Name,
                Status = record.Status,
                IsPresent = record.IsPresent,
                IsLate = record.IsLate,
                IsExcused = record.IsExcused,
                CheckInTime = record.CheckInTime,
                CheckOutTime = record.CheckOutTime,
                ArrivalTime = record.ArrivalTime,
                DepartureTime = record.DepartureTime,
                Reason = record.Reason,
                Notes = record.Notes,
                RecordedBy = record.RecordedBy,
                RecordedByName = record.RecordedByUser != null ? $"{record.RecordedByUser.FirstName} {record.RecordedByUser.LastName}" : null,
                RecordedAt = record.RecordedAt,
                Temperature = record.Temperature,
                HealthCheckPassed = record.HealthCheckPassed,
                HealthNotes = record.HealthNotes,
                Method = record.Method,
                IsVerified = record.IsVerified,
                VerifiedBy = record.VerifiedBy,
                VerifiedByName = record.VerifiedByUser != null ? $"{record.VerifiedByUser.FirstName} {record.VerifiedByUser.LastName}" : null,
                VerifiedAt = record.VerifiedAt,
                ParentNotified = record.ParentNotified,
                ParentNotificationSent = record.ParentNotificationSent,
                RequiresFollowUp = record.RequiresFollowUp,
                FollowUpDate = record.FollowUpDate,
                FollowUpNotes = record.FollowUpNotes
            };
        }

        // Placeholder methods - will be implemented in the next part
        public async Task<List<AttendanceRecordDto>> GetAttendanceRecordsAsync(AttendanceSearchDto searchDto)
        {
            // Implementation will be added
            return new List<AttendanceRecordDto>();
        }

        public async Task<List<AttendanceRecordDto>> GetStudentAttendanceAsync(string studentId, DateTime? startDate = null, DateTime? endDate = null)
        {
            // Implementation will be added
            return new List<AttendanceRecordDto>();
        }

        public async Task<List<AttendanceRecordDto>> GetClassAttendanceAsync(int classId, DateTime date)
        {
            // Implementation will be added
            return new List<AttendanceRecordDto>();
        }

        public async Task<List<AttendanceRecordDto>> GetDailyAttendanceAsync(DateTime date)
        {
            // Implementation will be added
            return new List<AttendanceRecordDto>();
        }

        public async Task<bool> BulkCreateAttendanceAsync(BulkAttendanceDto bulkDto, string recordedBy)
        {
            // Implementation will be added
            return true;
        }

        public async Task<bool> VerifyAttendanceRecordAsync(int id, string verifiedBy)
        {
            // Implementation will be added
            return true;
        }

        public async Task<EnhancedAttendanceSummaryDto?> GetStudentAttendanceSummaryAsync(string studentId, int academicYearId, int? semesterId = null)
        {
            // Implementation will be added
            return null;
        }

        public async Task<EnhancedAttendanceStatisticsDto> GetAttendanceStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null, int? classId = null)
        {
            // Implementation will be added
            return new EnhancedAttendanceStatisticsDto();
        }

        public async Task<List<AttendanceAlertDto>> GetAttendanceAlertsAsync(bool includeResolved = false)
        {
            // Implementation will be added
            return new List<AttendanceAlertDto>();
        }

        public async Task<bool> ResolveAttendanceAlertAsync(int alertId, string resolvedBy, string? resolutionNotes = null)
        {
            // Implementation will be added
            return true;
        }

        public async Task<bool> SendParentNotificationAsync(int attendanceRecordId)
        {
            // Implementation will be added
            return true;
        }

        public async Task<bool> UpdateAttendanceSummariesAsync(string? studentId = null)
        {
            // Implementation will be added
            return true;
        }

        public async Task<List<AttendanceRecordDto>> GetPendingFollowUpsAsync()
        {
            // Implementation will be added
            return new List<AttendanceRecordDto>();
        }

        public async Task<bool> MarkFollowUpCompleteAsync(int attendanceRecordId, string completedBy, string? notes = null)
        {
            // Implementation will be added
            return true;
        }

        // Helper methods
        private async Task CheckAndCreateAlertsAsync(string studentId)
        {
            // Implementation will be added
        }

        private async Task UpdateStudentAttendanceSummaryAsync(string studentId, DateTime date)
        {
            // Implementation will be added
        }
    }
}
