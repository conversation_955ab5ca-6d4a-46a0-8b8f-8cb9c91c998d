using Schools.Shared.DTOs;

namespace Schools.API.Services;

public interface IReportService
{
    Task<StudentPerformanceReportDto> GetStudentPerformanceReportAsync(
        string? studentId = null, 
        int? classId = null, 
        DateTime? startDate = null, 
        DateTime? endDate = null);

    Task<AttendanceReportDto> GetAttendanceReportAsync(
        string? studentId = null, 
        int? classId = null, 
        DateTime? startDate = null, 
        DateTime? endDate = null);

    Task<FinancialReportDto> GetFinancialReportAsync(
        DateTime? startDate = null, 
        DateTime? endDate = null);

    Task<ClassSummaryReportDto> GetClassSummaryReportAsync(int? classId = null);

    Task<TeacherPerformanceReportDto> GetTeacherPerformanceReportAsync(
        int? teacherId = null, 
        DateTime? startDate = null, 
        DateTime? endDate = null);

    Task<string> ExportReportAsync(string reportType, string format, Dictionary<string, object>? parameters = null);
}
