@page "/admin/activities"
@using Schools.Shared.Models
@using Schools.Shared.DTOs
@using Schools.Client.Services
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Admin")]

<PageTitle>إدارة الأنشطة والفعاليات</PageTitle>

<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-warning text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="text-white mb-1">
                                <i class="fas fa-calendar-check me-2"></i>
                                إدارة الأنشطة والفعاليات
                            </h2>
                            <p class="text-white-75 mb-0">تنظيم وإدارة الأنشطة المدرسية والفعاليات التعليمية</p>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-trophy fa-3x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@totalActivities</h4>
                            <p class="mb-0">إجمالي الأنشطة</p>
                        </div>
                        <i class="fas fa-calendar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@upcomingActivities</h4>
                            <p class="mb-0">أنشطة قادمة</p>
                        </div>
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@activeActivities</h4>
                            <p class="mb-0">أنشطة جارية</p>
                        </div>
                        <i class="fas fa-play fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@totalParticipants</h4>
                            <p class="mb-0">إجمالي المشاركين</p>
                        </div>
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Controls -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        البحث والفلترة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" placeholder="البحث عن نشاط..." @bind="searchTerm" @oninput="OnSearchChanged">
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <select class="form-select" @bind="selectedType" @bind:after="FilterActivities">
                                <option value="">جميع الأنواع</option>
                                <option value="Sports">رياضي</option>
                                <option value="Cultural">ثقافي</option>
                                <option value="Scientific">علمي</option>
                                <option value="Social">اجتماعي</option>
                                <option value="Religious">ديني</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <select class="form-select" @bind="selectedStatus" @bind:after="FilterActivities">
                                <option value="">جميع الحالات</option>
                                <option value="Planned">مخطط</option>
                                <option value="Active">جاري</option>
                                <option value="Completed">مكتمل</option>
                                <option value="Cancelled">ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <input type="date" class="form-control" @bind="selectedDate" @bind:after="FilterActivities">
                        </div>
                        <div class="col-md-3 mb-3">
                            <button class="btn btn-primary me-2" @onclick="ShowAddActivityModal">
                                <i class="fas fa-plus me-2"></i>
                                إضافة نشاط
                            </button>
                            <button class="btn btn-success" @onclick="ShowCalendarView">
                                <i class="fas fa-calendar-alt me-2"></i>
                                التقويم
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activities Grid -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الأنشطة
                    </h5>
                    <div class="btn-group">
                        <button class="btn btn-outline-success btn-sm" @onclick="ExportActivities">
                            <i class="fas fa-file-excel me-2"></i>
                            تصدير
                        </button>
                        <button class="btn btn-outline-primary btn-sm" @onclick="GenerateReport">
                            <i class="fas fa-chart-bar me-2"></i>
                            تقرير
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-4">
                            <div class="spinner-border text-warning" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else if (filteredActivities?.Any() == true)
                    {
                        <div class="row">
                            @foreach (var activity in filteredActivities.Take(12))
                            {
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card h-100 activity-card border-@GetStatusColor(activity.Status)">
                                        <div class="card-header bg-@GetTypeColor(activity.Type) text-white">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">@activity.Title</h6>
                                                <span class="badge bg-light text-dark">@GetTypeName(activity.Type)</span>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <p class="card-text small">@activity.Description</p>
                                            <div class="mb-2">
                                                <i class="fas fa-calendar text-primary me-2"></i>
                                                <small>@activity.StartDate.ToString("dd/MM/yyyy")</small>
                                            </div>
                                            <div class="mb-2">
                                                <i class="fas fa-clock text-info me-2"></i>
                                                <small>@activity.StartTime - @activity.EndTime</small>
                                            </div>
                                            <div class="mb-2">
                                                <i class="fas fa-map-marker-alt text-danger me-2"></i>
                                                <small>@activity.Location</small>
                                            </div>
                                            <div class="mb-2">
                                                <i class="fas fa-user text-success me-2"></i>
                                                <small>@activity.Organizer</small>
                                            </div>
                                            <div class="mb-3">
                                                <span class="badge bg-@GetStatusColor(activity.Status)">
                                                    @GetStatusName(activity.Status)
                                                </span>
                                                <span class="badge bg-secondary ms-1">
                                                    @activity.ParticipantsCount مشارك
                                                </span>
                                            </div>
                                            <div class="progress mb-2" style="height: 8px;">
                                                <div class="progress-bar bg-@GetStatusColor(activity.Status)"
                                                     style="width: @GetProgressPercentage(activity)%"></div>
                                            </div>
                                            <small class="text-muted">@GetProgressText(activity)</small>
                                        </div>
                                        <div class="card-footer">
                                            <div class="btn-group w-100" role="group">
                                                <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewActivityDetails(activity.Id)" title="تفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" @onclick="() => EditActivity(activity.Id)" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-info" @onclick="() => ManageParticipants(activity.Id)" title="المشاركين">
                                                    <i class="fas fa-users"></i>
                                                </button>
                                                @if (activity.Status == "Planned")
                                                {
                                                    <button class="btn btn-sm btn-outline-success" @onclick="() => StartActivity(activity.Id)" title="بدء">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                }
                                                else if (activity.Status == "Active")
                                                {
                                                    <button class="btn btn-sm btn-outline-secondary" @onclick="() => CompleteActivity(activity.Id)" title="إنهاء">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                }
                                                <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteActivity(activity.Id)" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>

                        @if (filteredActivities.Count() > 12)
                        {
                            <div class="text-center mt-3">
                                <p class="text-muted">عرض 12 من أصل @filteredActivities.Count() نشاط</p>
                                <button class="btn btn-outline-warning" @onclick="LoadMoreActivities">
                                    تحميل المزيد
                                </button>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد أنشطة</h5>
                            <p class="text-muted">لم يتم العثور على أنشطة بالفلاتر المحددة</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .activity-card {
        transition: transform 0.2s;
    }

    .activity-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
</style>

@code {
    private List<ActivityDto> activities = new();
    private List<ActivityDto> filteredActivities = new();
    private bool isLoading = true;

    private string searchTerm = string.Empty;
    private string selectedType = string.Empty;
    private string selectedStatus = string.Empty;
    private DateTime? selectedDate;

    private int totalActivities => activities.Count;
    private int upcomingActivities => activities.Count(a => a.Status == "Planned");
    private int activeActivities => activities.Count(a => a.Status == "Active");
    private int totalParticipants => activities.Sum(a => a.ParticipantsCount);

    protected override async Task OnInitializedAsync()
    {
        await LoadActivities();
    }

    private async Task LoadActivities()
    {
        try
        {
            isLoading = true;
            var apiActivities = await ApiService.GetActivitiesAsync();
            activities = apiActivities.Select(a => new ActivityDto
            {
                Id = a.Id,
                Title = a.Title,
                Description = a.Description,
                Type = a.Type,
                Status = a.Status,
                StartDate = a.StartDate,
                EndDate = a.EndDate,
                StartTime = a.StartTime,
                EndTime = a.EndTime,
                Location = a.Location,
                Organizer = a.OrganizerName,
                ParticipantsCount = a.ParticipantsCount,
                MaxParticipants = a.MaxParticipants
            }).ToList();
            FilterActivities();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
            // Fallback to mock data if API fails
            activities = GenerateMockActivities();
            FilterActivities();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private List<ActivityDto> GenerateMockActivities()
    {
        var mockActivities = new List<ActivityDto>();
        var titles = new[] { "مسابقة الرياضيات", "معرض العلوم", "يوم الرياضة", "مهرجان الثقافة", "ورشة البرمجة", "مسابقة القرآن", "يوم البيئة", "معرض الفنون" };
        var types = new[] { "Sports", "Cultural", "Scientific", "Social", "Religious" };
        var statuses = new[] { "Planned", "Active", "Completed", "Cancelled" };
        var locations = new[] { "القاعة الكبرى", "الملعب الرئيسي", "مختبر العلوم", "المكتبة", "الفناء الخارجي" };
        var organizers = new[] { "أ. محمد أحمد", "أ. فاطمة علي", "أ. عبدالله محمد", "أ. مريم أحمد" };
        var random = new Random();

        for (int i = 1; i <= 30; i++)
        {
            var startDate = DateTime.Now.AddDays(random.Next(-30, 60));
            mockActivities.Add(new ActivityDto
            {
                Id = i,
                Title = titles[random.Next(titles.Length)] + $" {i}",
                Description = "وصف تفصيلي للنشاط وأهدافه التعليمية والتربوية",
                Type = types[random.Next(types.Length)],
                Status = statuses[random.Next(statuses.Length)],
                StartDate = startDate,
                EndDate = startDate.AddHours(random.Next(2, 8)),
                StartTime = "09:00",
                EndTime = "12:00",
                Location = locations[random.Next(locations.Length)],
                Organizer = organizers[random.Next(organizers.Length)],
                ParticipantsCount = random.Next(10, 100),
                MaxParticipants = random.Next(50, 150)
            });
        }

        return mockActivities;
    }

    private void FilterActivities()
    {
        var query = activities.AsEnumerable();

        if (!string.IsNullOrEmpty(selectedType))
        {
            query = query.Where(a => a.Type == selectedType);
        }

        if (!string.IsNullOrEmpty(selectedStatus))
        {
            query = query.Where(a => a.Status == selectedStatus);
        }

        if (selectedDate.HasValue)
        {
            query = query.Where(a => a.StartDate.Date == selectedDate.Value.Date);
        }

        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(a =>
                a.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                a.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                a.Organizer.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
        }

        filteredActivities = query.OrderBy(a => a.StartDate).ToList();
        StateHasChanged();
    }

    private async Task OnSearchChanged(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? string.Empty;
        FilterActivities();
    }

    private async Task ShowAddActivityModal()
    {
        Navigation.NavigateTo("/admin/activities/add");
    }

    private async Task ShowCalendarView()
    {
        Navigation.NavigateTo("/admin/activities/calendar");
    }

    private async Task ViewActivityDetails(int activityId)
    {
        Navigation.NavigateTo($"/admin/activities/{activityId}");
    }

    private async Task EditActivity(int activityId)
    {
        Navigation.NavigateTo($"/admin/activities/{activityId}/edit");
    }

    private async Task ManageParticipants(int activityId)
    {
        Navigation.NavigateTo($"/admin/activities/{activityId}/participants");
    }

    private async Task StartActivity(int activityId)
    {
        try
        {
            var activity = activities.FirstOrDefault(a => a.Id == activityId);
            if (activity != null)
            {
                var updateDto = new UpdateActivityDto
                {
                    Title = activity.Title,
                    Description = activity.Description,
                    Type = activity.Type,
                    StartDate = activity.StartDate,
                    EndDate = activity.EndDate,
                    StartTime = TimeSpan.Parse(activity.StartTime),
                    EndTime = TimeSpan.Parse(activity.EndTime),
                    Location = activity.Location,
                    MaxParticipants = activity.MaxParticipants
                };

                var success = await ApiService.UpdateActivityAsync(activityId, updateDto);
                if (success)
                {
                    activity.Status = "Active";
                    FilterActivities();
                    await JSRuntime.InvokeVoidAsync("alert", "تم بدء النشاط بنجاح");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "فشل في تحديث حالة النشاط");
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ: {ex.Message}");
        }
    }

    private async Task CompleteActivity(int activityId)
    {
        try
        {
            var activity = activities.FirstOrDefault(a => a.Id == activityId);
            if (activity != null)
            {
                var updateDto = new UpdateActivityDto
                {
                    Title = activity.Title,
                    Description = activity.Description,
                    Type = activity.Type,
                    StartDate = activity.StartDate,
                    EndDate = activity.EndDate,
                    StartTime = TimeSpan.Parse(activity.StartTime),
                    EndTime = TimeSpan.Parse(activity.EndTime),
                    Location = activity.Location,
                    MaxParticipants = activity.MaxParticipants
                };

                var success = await ApiService.UpdateActivityAsync(activityId, updateDto);
                if (success)
                {
                    activity.Status = "Completed";
                    FilterActivities();
                    await JSRuntime.InvokeVoidAsync("alert", "تم إنهاء النشاط بنجاح");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "فشل في تحديث حالة النشاط");
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ: {ex.Message}");
        }
    }

    private async Task DeleteActivity(int activityId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا النشاط؟"))
        {
            try
            {
                var success = await ApiService.DeleteActivityAsync(activityId);
                if (success)
                {
                    activities.RemoveAll(a => a.Id == activityId);
                    FilterActivities();
                    await JSRuntime.InvokeVoidAsync("alert", "تم حذف النشاط بنجاح");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "فشل في حذف النشاط");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ: {ex.Message}");
            }
        }
    }

    private async Task LoadMoreActivities()
    {
        await JSRuntime.InvokeVoidAsync("alert", "تم تحميل المزيد من الأنشطة");
    }

    private async Task ExportActivities()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم تصدير البيانات إلى Excel");
    }

    private async Task GenerateReport()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم إنشاء تقرير الأنشطة");
    }

    private string GetTypeName(string type)
    {
        return type switch
        {
            "Sports" => "رياضي",
            "Cultural" => "ثقافي",
            "Scientific" => "علمي",
            "Social" => "اجتماعي",
            "Religious" => "ديني",
            _ => type
        };
    }

    private string GetTypeColor(string type)
    {
        return type switch
        {
            "Sports" => "success",
            "Cultural" => "primary",
            "Scientific" => "info",
            "Social" => "warning",
            "Religious" => "secondary",
            _ => "dark"
        };
    }

    private string GetStatusName(string status)
    {
        return status switch
        {
            "Planned" => "مخطط",
            "Active" => "جاري",
            "Completed" => "مكتمل",
            "Cancelled" => "ملغي",
            _ => status
        };
    }

    private string GetStatusColor(string status)
    {
        return status switch
        {
            "Planned" => "primary",
            "Active" => "success",
            "Completed" => "info",
            "Cancelled" => "danger",
            _ => "secondary"
        };
    }

    private int GetProgressPercentage(ActivityDto activity)
    {
        return activity.Status switch
        {
            "Planned" => 25,
            "Active" => 75,
            "Completed" => 100,
            "Cancelled" => 0,
            _ => 0
        };
    }

    private string GetProgressText(ActivityDto activity)
    {
        return activity.Status switch
        {
            "Planned" => "في انتظار البدء",
            "Active" => "جاري التنفيذ",
            "Completed" => "تم الانتهاء",
            "Cancelled" => "تم الإلغاء",
            _ => "غير محدد"
        };
    }

    public class ActivityDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string StartTime { get; set; } = string.Empty;
        public string EndTime { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string Organizer { get; set; } = string.Empty;
        public int ParticipantsCount { get; set; }
        public int MaxParticipants { get; set; }
    }
}
