﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <!-- Core ASP.NET Core packages -->
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Components" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.5" />

    <!-- Entity Framework Core -->
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5" />

    <!-- Authentication & Authorization -->
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.2.1" />

    <!-- Validation -->
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.DataAnnotations.Validation" Version="3.2.0-rc1.20223.4" />

    <!-- HTTP Client -->
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.5" />
    <PackageReference Include="System.Net.Http.Json" Version="9.0.5" />

    <!-- JSON Serialization -->
    <PackageReference Include="System.Text.Json" Version="9.0.5" />

    <!-- Logging -->
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />

    <!-- Configuration -->
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.5" />

    <!-- Dependency Injection -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
  </ItemGroup>

</Project>
