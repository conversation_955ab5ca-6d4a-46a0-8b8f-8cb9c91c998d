using Microsoft.AspNetCore.Identity;
using Schools.Data;
using Schools.Shared.Models;

namespace Schools.API
{
    public static class SeedData
    {
        public static async Task Initialize(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<SchoolsDbContext>();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole>>();

            // Create roles
            await CreateRoles(roleManager);

            // Create admin user
            await CreateAdminUser(userManager);

            // Create sample data
            await CreateSampleData(context, userManager);
        }

        private static async Task CreateRoles(RoleManager<IdentityRole> roleManager)
        {
            string[] roles = { "Admin", "Teacher", "Student", "Parent", "Employee", "Accountant" };

            foreach (var role in roles)
            {
                if (!await roleManager.RoleExistsAsync(role))
                {
                    await roleManager.CreateAsync(new IdentityRole(role));
                }
            }
        }

        private static async Task CreateAdminUser(UserManager<ApplicationUser> userManager)
        {
            var adminEmail = "<EMAIL>";
            var adminUser = await userManager.FindByEmailAsync(adminEmail);

            if (adminUser == null)
            {
                adminUser = new ApplicationUser
                {
                    UserName = adminEmail,
                    Email = adminEmail,
                    FirstName = "مدير",
                    LastName = "النظام",
                    IsActive = true,
                    EmailConfirmed = true
                };

                var result = await userManager.CreateAsync(adminUser, "Admin123!");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(adminUser, "Admin");
                }
            }
        }

        private static async Task CreateSampleData(SchoolsDbContext context, UserManager<ApplicationUser> userManager)
        {
            // Create Academic Year
            if (!context.AcademicYears.Any())
            {
                var academicYear = new AcademicYear
                {
                    Name = "2024-2025",
                    StartDate = new DateTime(2024, 9, 1),
                    EndDate = new DateTime(2025, 6, 30),
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                };
                context.AcademicYears.Add(academicYear);
            }

            // Create Grades
            if (!context.Grades.Any())
            {
                var grades = new[]
                {
                    new Grade { Name = "الصف الأول الابتدائي", Level = GradeLevel.Elementary, CreatedAt = DateTime.UtcNow },
                    new Grade { Name = "الصف الثاني الابتدائي", Level = GradeLevel.Elementary, CreatedAt = DateTime.UtcNow },
                    new Grade { Name = "الصف الثالث الابتدائي", Level = GradeLevel.Elementary, CreatedAt = DateTime.UtcNow },
                    new Grade { Name = "الصف الأول المتوسط", Level = GradeLevel.Middle, CreatedAt = DateTime.UtcNow },
                    new Grade { Name = "الصف الثاني المتوسط", Level = GradeLevel.Middle, CreatedAt = DateTime.UtcNow },
                    new Grade { Name = "الصف الثالث المتوسط", Level = GradeLevel.Middle, CreatedAt = DateTime.UtcNow },
                    new Grade { Name = "الصف الأول الثانوي", Level = GradeLevel.High, CreatedAt = DateTime.UtcNow },
                    new Grade { Name = "الصف الثاني الثانوي", Level = GradeLevel.High, CreatedAt = DateTime.UtcNow },
                    new Grade { Name = "الصف الثالث الثانوي", Level = GradeLevel.High, CreatedAt = DateTime.UtcNow }
                };
                context.Grades.AddRange(grades);
            }

            // Create Subjects
            if (!context.Subjects.Any())
            {
                var subjects = new[]
                {
                    new Subject { Name = "Mathematics", NameAr = "الرياضيات", Code = "MATH", CreditHours = 4, CreatedAt = DateTime.UtcNow },
                    new Subject { Name = "Arabic Language", NameAr = "اللغة العربية", Code = "ARAB", CreditHours = 4, CreatedAt = DateTime.UtcNow },
                    new Subject { Name = "English Language", NameAr = "اللغة الإنجليزية", Code = "ENG", CreditHours = 3, CreatedAt = DateTime.UtcNow },
                    new Subject { Name = "Science", NameAr = "العلوم", Code = "SCI", CreditHours = 3, CreatedAt = DateTime.UtcNow },
                    new Subject { Name = "History", NameAr = "التاريخ", Code = "HIST", CreditHours = 2, CreatedAt = DateTime.UtcNow },
                    new Subject { Name = "Geography", NameAr = "الجغرافيا", Code = "GEO", CreditHours = 2, CreatedAt = DateTime.UtcNow },
                    new Subject { Name = "Islamic Education", NameAr = "التربية الإسلامية", Code = "ISLAM", CreditHours = 2, CreatedAt = DateTime.UtcNow },
                    new Subject { Name = "Physical Education", NameAr = "التربية البدنية", Code = "PE", CreditHours = 1, CreatedAt = DateTime.UtcNow }
                };
                context.Subjects.AddRange(subjects);
            }

            await context.SaveChangesAsync();

            // Create Classes after Grades are saved
            if (!context.Classes.Any())
            {
                var firstGrade = context.Grades.First();
                var classes = new[]
                {
                    new Class { Name = "الصف الأول أ", GradeId = firstGrade.Id, Capacity = 30, CreatedAt = DateTime.UtcNow },
                    new Class { Name = "الصف الأول ب", GradeId = firstGrade.Id, Capacity = 30, CreatedAt = DateTime.UtcNow }
                };
                context.Classes.AddRange(classes);
                await context.SaveChangesAsync();

                // Create Sections
                var firstClass = context.Classes.First();
                var sections = new[]
                {
                    new Section { Name = "شعبة أ", ClassId = firstClass.Id, Capacity = 30, CreatedAt = DateTime.UtcNow },
                    new Section { Name = "شعبة ب", ClassId = firstClass.Id, Capacity = 30, CreatedAt = DateTime.UtcNow }
                };
                context.Sections.AddRange(sections);
            }

            await context.SaveChangesAsync();

            // Create sample teachers and schedules
            await CreateSampleTeachersAndSchedules(context, userManager);
        }

        private static async Task CreateSampleTeachersAndSchedules(SchoolsDbContext context, UserManager<ApplicationUser> userManager)
        {
            // Create sample teachers
            var teacherEmails = new[] { "<EMAIL>", "<EMAIL>", "<EMAIL>" };
            var teacherNames = new[] {
                new { First = "أحمد", Last = "محمد" },
                new { First = "فاطمة", Last = "علي" },
                new { First = "محمد", Last = "حسن" }
            };

            var teachers = new List<ApplicationUser>();
            for (int i = 0; i < teacherEmails.Length; i++)
            {
                var existingTeacher = await userManager.FindByEmailAsync(teacherEmails[i]);
                if (existingTeacher == null)
                {
                    var teacher = new ApplicationUser
                    {
                        UserName = teacherEmails[i],
                        Email = teacherEmails[i],
                        FirstName = teacherNames[i].First,
                        LastName = teacherNames[i].Last,
                        IsActive = true,
                        EmailConfirmed = true
                    };

                    var result = await userManager.CreateAsync(teacher, "Teacher123!");
                    if (result.Succeeded)
                    {
                        await userManager.AddToRoleAsync(teacher, "Teacher");
                        teachers.Add(teacher);
                    }
                }
                else
                {
                    teachers.Add(existingTeacher);
                }
            }

            // Create sample schedules if they don't exist
            if (!context.Schedules.Any())
            {
                var academicYear = context.AcademicYears.First();
                var classes = context.Classes.ToList();
                var subjects = context.Subjects.ToList();

                var schedules = new List<Schedule>();
                var startDate = GetStartOfWeek(DateTime.Today);

                // Create schedules for the current week
                for (int dayOffset = 0; dayOffset < 5; dayOffset++) // Monday to Friday
                {
                    var currentDate = startDate.AddDays(dayOffset);
                    var periods = new[]
                    {
                        new { Start = new TimeSpan(8, 0, 0), End = new TimeSpan(8, 45, 0) },
                        new { Start = new TimeSpan(8, 45, 0), End = new TimeSpan(9, 30, 0) },
                        new { Start = new TimeSpan(9, 45, 0), End = new TimeSpan(10, 30, 0) },
                        new { Start = new TimeSpan(10, 30, 0), End = new TimeSpan(11, 15, 0) },
                        new { Start = new TimeSpan(11, 30, 0), End = new TimeSpan(12, 15, 0) },
                        new { Start = new TimeSpan(12, 15, 0), End = new TimeSpan(13, 0, 0) }
                    };

                    for (int periodIndex = 0; periodIndex < periods.Length; periodIndex++)
                    {
                        foreach (var cls in classes)
                        {
                            var teacher = teachers[periodIndex % teachers.Count];
                            var subject = subjects[periodIndex % subjects.Count];

                            schedules.Add(new Schedule
                            {
                                SubjectId = subject.Id,
                                ClassId = cls.Id,
                                TeacherId = teacher.Id,
                                AcademicYearId = academicYear.Id,
                                DayOfWeek = currentDate.DayOfWeek,
                                StartTime = periods[periodIndex].Start,
                                EndTime = periods[periodIndex].End,
                                Room = $"قاعة {periodIndex + 1}{(char)('أ' + (cls.Id % 3))}",
                                Date = currentDate,
                                Type = ScheduleType.Regular,
                                Status = ScheduleStatus.Scheduled,
                                IsActive = true,
                                CreatedAt = DateTime.UtcNow
                            });
                        }
                    }
                }

                context.Schedules.AddRange(schedules);
                await context.SaveChangesAsync();
            }
        }

        private static DateTime GetStartOfWeek(DateTime date)
        {
            // In Arabic culture, week starts on Saturday
            var diff = (7 + (date.DayOfWeek - DayOfWeek.Saturday)) % 7;
            return date.AddDays(-1 * diff).Date;
        }
    }
}
