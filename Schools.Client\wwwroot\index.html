<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>نظام إدارة المدارس</title>
    <base href="/" />

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="css/app.css" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <link href="Schools.Client.styles.css" rel="stylesheet" />

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            text-align: right;
        }

        .loading-progress {
            position: relative;
            display: block;
            width: 8rem;
            height: 8rem;
            margin: 20vh auto 1rem auto;
        }

        .loading-progress circle {
            fill: none;
            stroke: #e0e0e0;
            stroke-width: 0.6rem;
            transform-origin: 50% 50%;
            transform: rotate(-90deg);
        }

        .loading-progress circle:last-child {
            stroke: #1b6ec2;
            stroke-dasharray: calc(3.141 * var(--blazor-load-percentage, 0%) * 0.8), 500%;
            transition: stroke-dasharray 0.05s ease-in-out;
        }

        .loading-progress-text {
            position: absolute;
            text-align: center;
            font-weight: bold;
            inset: calc(20vh + 3.25rem) 0 auto 0.2rem;
        }

        .loading-progress-text:after {
            content: var(--blazor-load-percentage-text, "جاري التحميل");
        }
    </style>
</head>

<body>
    <div id="app">
        <svg class="loading-progress">
            <circle r="40%" cx="50%" cy="50%" />
            <circle r="40%" cx="50%" cy="50%" />
        </svg>
        <div class="loading-progress-text"></div>
    </div>

    <div id="blazor-error-ui">
        حدث خطأ غير متوقع.
        <a href="." class="reload">إعادة تحميل</a>
        <span class="dismiss">🗙</span>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Chart.js for advanced charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Advanced Features JavaScript -->
    <script src="js/advanced-features.js"></script>

    <script src="_framework/blazor.webassembly.js"></script>
</body>

</html>
