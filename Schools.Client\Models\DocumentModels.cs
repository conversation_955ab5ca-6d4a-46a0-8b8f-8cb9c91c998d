namespace Schools.Client.Models
{
    public class DocumentModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string FileType { get; set; } = "";
        public string Category { get; set; } = "";
        public long Size { get; set; }
        public string FilePath { get; set; } = "";
        public DateTime UploadDate { get; set; }
        public string UploadedBy { get; set; } = "";
        public int ViewCount { get; set; }
        public int DownloadCount { get; set; }
        public bool IsPublic { get; set; }
    }

    public class DocumentUploadModel
    {
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Category { get; set; } = "general";
        public bool IsPublic { get; set; } = true;
        public byte[]? FileContent { get; set; }
        public string FileName { get; set; } = "";
        public string ContentType { get; set; } = "";
    }

    public class DocumentStatisticsModel
    {
        public int TotalDocuments { get; set; }
        public long TotalSize { get; set; }
        public int TotalDownloads { get; set; }
        public int TotalViews { get; set; }
        public Dictionary<string, int> CategoryDistribution { get; set; } = new();
        public Dictionary<string, int> TypeDistribution { get; set; } = new();
        public Dictionary<string, long> TypeSizeDistribution { get; set; } = new();
    }

    public class DocumentFilterModel
    {
        public string? Category { get; set; }
        public string? FileType { get; set; }
        public DateTime? UploadDate { get; set; }
        public string? Search { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class CleanupRequestModel
    {
        public int MonthsOld { get; set; } = 6;
    }

    public class CleanupResultModel
    {
        public int DeletedCount { get; set; }
        public long FreedSpace { get; set; }
        public string Message { get; set; } = "";
    }

    public static class DocumentCategories
    {
        public static readonly Dictionary<string, string> Categories = new()
        {
            { "academic", "أكاديمي" },
            { "administrative", "إداري" },
            { "financial", "مالي" },
            { "reports", "تقارير" },
            { "forms", "نماذج" },
            { "general", "عام" }
        };
    }

    public static class DocumentTypes
    {
        public static readonly Dictionary<string, string> Types = new()
        {
            { "pdf", "PDF" },
            { "doc", "Word" },
            { "docx", "Word" },
            { "xls", "Excel" },
            { "xlsx", "Excel" },
            { "jpg", "صورة" },
            { "jpeg", "صورة" },
            { "png", "صورة" },
            { "gif", "صورة" },
            { "mp4", "فيديو" },
            { "avi", "فيديو" },
            { "mp3", "صوت" },
            { "wav", "صوت" }
        };

        public static readonly Dictionary<string, string> Icons = new()
        {
            { "pdf", "fas fa-file-pdf" },
            { "doc", "fas fa-file-word" },
            { "docx", "fas fa-file-word" },
            { "xls", "fas fa-file-excel" },
            { "xlsx", "fas fa-file-excel" },
            { "jpg", "fas fa-file-image" },
            { "jpeg", "fas fa-file-image" },
            { "png", "fas fa-file-image" },
            { "gif", "fas fa-file-image" },
            { "mp4", "fas fa-file-video" },
            { "avi", "fas fa-file-video" },
            { "mp3", "fas fa-file-audio" },
            { "wav", "fas fa-file-audio" }
        };

        public static readonly Dictionary<string, string> Colors = new()
        {
            { "pdf", "text-danger" },
            { "doc", "text-primary" },
            { "docx", "text-primary" },
            { "xls", "text-success" },
            { "xlsx", "text-success" },
            { "jpg", "text-warning" },
            { "jpeg", "text-warning" },
            { "png", "text-warning" },
            { "gif", "text-warning" },
            { "mp4", "text-info" },
            { "avi", "text-info" },
            { "mp3", "text-secondary" },
            { "wav", "text-secondary" }
        };
    }
}
