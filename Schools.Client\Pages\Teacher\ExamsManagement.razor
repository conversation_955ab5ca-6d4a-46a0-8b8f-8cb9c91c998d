@page "/teacher/exams"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Teacher")]

<PageTitle>إدارة الامتحانات الإلكترونية - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white border-0 shadow-lg">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-1">
                                <i class="fas fa-laptop-code me-2"></i>
                                إدارة الامتحانات الإلكترونية
                            </h2>
                            <p class="mb-0 opacity-75">
                                إنشاء وإدارة الامتحانات الإلكترونية مع التصحيح التلقائي
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-light btn-lg" @onclick="ShowCreateExamModal">
                                <i class="fas fa-plus me-2"></i>
                                إنشاء امتحان جديد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="avatar-md bg-primary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                        <i class="fas fa-file-alt fa-lg"></i>
                    </div>
                    <h4 class="text-primary mb-1">@totalExams</h4>
                    <p class="text-muted mb-0">إجمالي الامتحانات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="avatar-md bg-success text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                        <i class="fas fa-play fa-lg"></i>
                    </div>
                    <h4 class="text-success mb-1">@activeExams</h4>
                    <p class="text-muted mb-0">امتحانات نشطة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="avatar-md bg-info text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                        <i class="fas fa-users fa-lg"></i>
                    </div>
                    <h4 class="text-info mb-1">@totalAttempts</h4>
                    <p class="text-muted mb-0">إجمالي المحاولات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="avatar-md bg-warning text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                        <i class="fas fa-chart-line fa-lg"></i>
                    </div>
                    <h4 class="text-warning mb-1">@averageScore.ToString("F1")%</h4>
                    <p class="text-muted mb-0">متوسط الدرجات</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" placeholder="البحث في الامتحانات..."
                                       @bind="searchTerm" @bind:after="LoadExams" />
                            </div>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" @bind="statusFilter" @bind:after="LoadExams">
                                <option value="">جميع الحالات</option>
                                <option value="Draft">مسودة</option>
                                <option value="Published">منشور</option>
                                <option value="InProgress">قيد التنفيذ</option>
                                <option value="Completed">مكتمل</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" @bind="subjectFilter" @bind:after="LoadExams">
                                <option value="">جميع المواد</option>
                                @if (subjects != null)
                                {
                                    @foreach (var subject in subjects)
                                    {
                                        <option value="@subject.Id">@subject.Name</option>
                                    }
                                }
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" @bind="classFilter" @bind:after="LoadExams">
                                <option value="">جميع الصفوف</option>
                                @if (classes != null)
                                {
                                    @foreach (var classItem in classes)
                                    {
                                        <option value="@classItem.Id">@classItem.Name</option>
                                    }
                                }
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-secondary w-100" @onclick="ResetFilters">
                                <i class="fas fa-undo me-2"></i>
                                إعادة تعيين
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Exams List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2 text-primary"></i>
                                قائمة الامتحانات
                            </h5>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group">
                                <button class="btn btn-outline-primary btn-sm @(viewMode == ViewMode.Grid ? "active" : "")" @onclick="() => viewMode = ViewMode.Grid">
                                    <i class="fas fa-th"></i>
                                </button>
                                <button class="btn btn-outline-primary btn-sm @(viewMode == ViewMode.List ? "active" : "")" @onclick="() => viewMode = ViewMode.List">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2 text-muted">جاري تحميل الامتحانات...</p>
                        </div>
                    }
                    else if (exams?.Any() == true)
                    {
                        @if (viewMode == ViewMode.Grid)
                        {
                            <div class="row g-4">
                                @foreach (var exam in exams)
                                {
                                    <div class="col-lg-4 col-md-6">
                                        <div class="card border h-100 exam-card" @onclick="() => ViewExam(exam.Id)">
                                            <div class="card-header bg-light border-bottom-0">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <span class="badge @GetStatusBadgeClass(exam.Status)">@GetStatusText(exam.Status)</span>
                                                    <div class="dropdown">
                                                        <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                                            <i class="fas fa-ellipsis-v"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <li><a class="dropdown-item" @onclick="() => ViewExam(exam.Id)">
                                                                <i class="fas fa-eye me-2"></i>عرض
                                                            </a></li>
                                                            <li><a class="dropdown-item" @onclick="() => EditExam(exam)">
                                                                <i class="fas fa-edit me-2"></i>تعديل
                                                            </a></li>
                                                            <li><a class="dropdown-item" @onclick="() => ManageQuestions(exam.Id)">
                                                                <i class="fas fa-question-circle me-2"></i>إدارة الأسئلة
                                                            </a></li>
                                                            <li><a class="dropdown-item" @onclick="() => ViewResults(exam.Id)">
                                                                <i class="fas fa-chart-bar me-2"></i>النتائج
                                                            </a></li>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li><a class="dropdown-item text-danger" @onclick="() => DeleteExam(exam.Id)">
                                                                <i class="fas fa-trash me-2"></i>حذف
                                                            </a></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <h6 class="card-title mb-2">@exam.Title</h6>
                                                <p class="card-text text-muted small mb-3">@exam.Description</p>

                                                <div class="row g-2 mb-3">
                                                    <div class="col-6">
                                                        <small class="text-muted d-block">المادة</small>
                                                        <span class="badge bg-primary">@exam.SubjectName</span>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted d-block">الصف</small>
                                                        <span class="badge bg-info">@exam.ClassName</span>
                                                    </div>
                                                </div>

                                                <div class="row g-2 mb-3">
                                                    <div class="col-6">
                                                        <small class="text-muted d-block">الأسئلة</small>
                                                        <strong>@exam.TotalQuestions</strong>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted d-block">الدرجات</small>
                                                        <strong>@exam.TotalMarks</strong>
                                                    </div>
                                                </div>

                                                <div class="row g-2">
                                                    <div class="col-6">
                                                        <small class="text-muted d-block">المدة</small>
                                                        <strong>@exam.DurationMinutes دقيقة</strong>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted d-block">تاريخ البداية</small>
                                                        <strong>@exam.StartDate.ToString("dd/MM")</strong>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>العنوان</th>
                                            <th>المادة</th>
                                            <th>الصف</th>
                                            <th>الحالة</th>
                                            <th>الأسئلة</th>
                                            <th>المدة</th>
                                            <th>تاريخ البداية</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var exam in exams)
                                        {
                                            <tr>
                                                <td>
                                                    <div>
                                                        <strong>@exam.Title</strong>
                                                        <br>
                                                        <small class="text-muted">@exam.Description</small>
                                                    </div>
                                                </td>
                                                <td><span class="badge bg-primary">@exam.SubjectName</span></td>
                                                <td><span class="badge bg-info">@exam.ClassName</span></td>
                                                <td><span class="badge @GetStatusBadgeClass(exam.Status)">@GetStatusText(exam.Status)</span></td>
                                                <td>@exam.TotalQuestions</td>
                                                <td>@exam.DurationMinutes دقيقة</td>
                                                <td>@exam.StartDate.ToString("dd/MM/yyyy HH:mm")</td>
                                                <td>
                                                    <div class="btn-group">
                                                        <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewExam(exam.Id)" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-warning" @onclick="() => EditExam(exam)" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-info" @onclick="() => ManageQuestions(exam.Id)" title="الأسئلة">
                                                            <i class="fas fa-question-circle"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-success" @onclick="() => ViewResults(exam.Id)" title="النتائج">
                                                            <i class="fas fa-chart-bar"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteExam(exam.Id)" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-laptop-code fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد امتحانات</h5>
                            <p class="text-muted">ابدأ بإنشاء امتحان إلكتروني جديد</p>
                            <button class="btn btn-primary" @onclick="ShowCreateExamModal">
                                <i class="fas fa-plus me-2"></i>
                                إنشاء امتحان جديد
                            </button>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Exam Modal -->
@if (showExamModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas @(editingExam == null ? "fa-plus" : "fa-edit") me-2"></i>
                        @(editingExam == null ? "إنشاء امتحان جديد" : "تعديل الامتحان")
                    </h5>
                    <button type="button" class="btn-close btn-close-white" @onclick="CloseExamModal"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="examModel" OnValidSubmit="SaveExam">
                        <DataAnnotationsValidator />

                        <div class="row g-3">
                            <!-- Basic Information -->
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-info-circle me-2"></i>
                                    المعلومات الأساسية
                                </h6>
                            </div>

                            <div class="col-md-8">
                                <label class="form-label">عنوان الامتحان <span class="text-danger">*</span></label>
                                <InputText @bind-Value="examModel.Title" class="form-control" placeholder="أدخل عنوان الامتحان" />
                                <ValidationMessage For="@(() => examModel.Title)" class="text-danger" />
                            </div>

                            <div class="col-md-4">
                                <label class="form-label">المدة بالدقائق <span class="text-danger">*</span></label>
                                <InputNumber @bind-Value="examModel.DurationMinutes" class="form-control" placeholder="60" />
                                <ValidationMessage For="@(() => examModel.DurationMinutes)" class="text-danger" />
                            </div>

                            <div class="col-12">
                                <label class="form-label">وصف الامتحان</label>
                                <InputTextArea @bind-Value="examModel.Description" class="form-control" rows="3" placeholder="وصف مختصر للامتحان" />
                                <ValidationMessage For="@(() => examModel.Description)" class="text-danger" />
                            </div>

                            <!-- Subject and Class -->
                            <div class="col-12 mt-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-book me-2"></i>
                                    المادة والصف
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">المادة <span class="text-danger">*</span></label>
                                <select @bind="examModel.SubjectId" class="form-select">
                                    <option value="">-- اختر المادة --</option>
                                    @if (subjects != null)
                                    {
                                        @foreach (var subject in subjects)
                                        {
                                            <option value="@subject.Id">@subject.Name</option>
                                        }
                                    }
                                </select>
                                <ValidationMessage For="@(() => examModel.SubjectId)" class="text-danger" />
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">الصف <span class="text-danger">*</span></label>
                                <select @bind="examModel.ClassId" class="form-select">
                                    <option value="">-- اختر الصف --</option>
                                    @if (classes != null)
                                    {
                                        @foreach (var classItem in classes)
                                        {
                                            <option value="@classItem.Id">@classItem.Name</option>
                                        }
                                    }
                                </select>
                                <ValidationMessage For="@(() => examModel.ClassId)" class="text-danger" />
                            </div>

                            <!-- Timing -->
                            <div class="col-12 mt-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-clock me-2"></i>
                                    التوقيت
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">تاريخ ووقت البداية <span class="text-danger">*</span></label>
                                <InputDate @bind-Value="examModel.StartDate" class="form-control" />
                                <ValidationMessage For="@(() => examModel.StartDate)" class="text-danger" />
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">تاريخ ووقت النهاية <span class="text-danger">*</span></label>
                                <InputDate @bind-Value="examModel.EndDate" class="form-control" />
                                <ValidationMessage For="@(() => examModel.EndDate)" class="text-danger" />
                            </div>

                            <!-- Grading -->
                            <div class="col-12 mt-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-star me-2"></i>
                                    الدرجات
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">إجمالي الدرجات <span class="text-danger">*</span></label>
                                <InputNumber @bind-Value="examModel.TotalMarks" class="form-control" placeholder="100" />
                                <ValidationMessage For="@(() => examModel.TotalMarks)" class="text-danger" />
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">درجة النجاح <span class="text-danger">*</span></label>
                                <InputNumber @bind-Value="examModel.PassingMarks" class="form-control" placeholder="50" />
                                <ValidationMessage For="@(() => examModel.PassingMarks)" class="text-danger" />
                            </div>

                            <!-- Settings -->
                            <div class="col-12 mt-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-cogs me-2"></i>
                                    إعدادات الامتحان
                                </h6>
                            </div>

                            <div class="col-md-4">
                                <div class="form-check">
                                    <InputCheckbox @bind-Value="examModel.AllowRetake" class="form-check-input" />
                                    <label class="form-check-label">السماح بإعادة المحاولة</label>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-check">
                                    <InputCheckbox @bind-Value="examModel.ShowResultsImmediately" class="form-check-input" />
                                    <label class="form-check-label">عرض النتائج فوراً</label>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-check">
                                    <InputCheckbox @bind-Value="examModel.RandomizeQuestions" class="form-check-input" />
                                    <label class="form-check-label">ترتيب عشوائي للأسئلة</label>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-check">
                                    <InputCheckbox @bind-Value="examModel.RandomizeOptions" class="form-check-input" />
                                    <label class="form-check-label">ترتيب عشوائي للخيارات</label>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <label class="form-label">عدد المحاولات المسموحة</label>
                                <InputNumber @bind-Value="examModel.MaxAttempts" class="form-control" min="1" max="10" />
                                <ValidationMessage For="@(() => examModel.MaxAttempts)" class="text-danger" />
                            </div>
                        </div>
                    </EditForm>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseExamModal">
                        <i class="fas fa-times me-2"></i>
                        إلغاء
                    </button>
                    <button type="button" class="btn btn-primary" @onclick="SaveExam" disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        else
                        {
                            <i class="fas fa-save me-2"></i>
                        }
                        @(editingExam == null ? "إنشاء الامتحان" : "حفظ التغييرات")
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .exam-card {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .exam-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
    }

    .avatar-md {
        width: 48px;
        height: 48px;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }

    .btn-group .btn.active {
        background-color: #007bff;
        color: white;
    }
</style>

@code {
    private List<ExamDto>? exams;
    private List<SubjectDto>? subjects;
    private List<ClassDto>? classes;
    private bool isLoading = true;
    private bool showExamModal = false;
    private bool isSaving = false;
    private ExamDto? editingExam;
    private CreateExamDto examModel = new();

    // Statistics
    private int totalExams = 0;
    private int activeExams = 0;
    private int totalAttempts = 0;
    private double averageScore = 0.0;

    // Filters
    private string searchTerm = "";
    private string statusFilter = "";
    private string subjectFilter = "";
    private string classFilter = "";
    private ViewMode viewMode = ViewMode.Grid;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        await Task.WhenAll(
            LoadExams(),
            LoadSubjects(),
            LoadClasses(),
            LoadStatistics()
        );
    }

    private async Task LoadExams()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            var queryParams = new Dictionary<string, string?>
            {
                ["search"] = string.IsNullOrEmpty(searchTerm) ? null : searchTerm,
                ["status"] = string.IsNullOrEmpty(statusFilter) ? null : statusFilter,
                ["subjectId"] = string.IsNullOrEmpty(subjectFilter) ? null : subjectFilter,
                ["classId"] = string.IsNullOrEmpty(classFilter) ? null : classFilter
            };

            exams = await ApiService.GetExamsAsync(queryParams);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الامتحانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadSubjects()
    {
        try
        {
            var subjectsList = await ApiService.GetSubjectsAsync();
            subjects = subjectsList?.ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading subjects: {ex.Message}");
        }
    }

    private async Task LoadClasses()
    {
        try
        {
            var classesList = await ApiService.GetClassesAsync();
            classes = classesList?.ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading classes: {ex.Message}");
        }
    }

    private async Task LoadStatistics()
    {
        try
        {
            var stats = await ApiService.GetExamStatisticsAsync();
            totalExams = stats.TotalExams;
            activeExams = stats.ActiveExams;
            totalAttempts = stats.TotalAttempts;
            averageScore = stats.AverageScore;
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading statistics: {ex.Message}");
        }
    }

    private void ShowCreateExamModal()
    {
        editingExam = null;
        examModel = new CreateExamDto
        {
            StartDate = DateTime.Now.AddDays(1),
            EndDate = DateTime.Now.AddDays(7),
            DurationMinutes = 60,
            TotalMarks = 100,
            PassingMarks = 50,
            MaxAttempts = 1,
            ShowResultsImmediately = true
        };
        showExamModal = true;
    }

    private void EditExam(ExamDto exam)
    {
        editingExam = exam;
        examModel = new CreateExamDto
        {
            Title = exam.Title,
            Description = exam.Description,
            SubjectId = exam.SubjectId,
            ClassId = exam.ClassId,
            StartDate = exam.StartDate,
            EndDate = exam.EndDate,
            DurationMinutes = exam.DurationMinutes,
            TotalMarks = exam.TotalMarks,
            PassingMarks = exam.PassingMarks,
            AllowRetake = exam.AllowRetake,
            MaxAttempts = exam.MaxAttempts,
            ShowResultsImmediately = exam.ShowResultsImmediately,
            RandomizeQuestions = exam.RandomizeQuestions,
            RandomizeOptions = exam.RandomizeOptions
        };
        showExamModal = true;
    }

    private void CloseExamModal()
    {
        showExamModal = false;
        editingExam = null;
        examModel = new();
    }

    private async Task SaveExam()
    {
        try
        {
            isSaving = true;
            StateHasChanged();

            if (editingExam == null)
            {
                await ApiService.CreateExamAsync(examModel);
                await JSRuntime.InvokeVoidAsync("alert", "تم إنشاء الامتحان بنجاح");
            }
            else
            {
                var updateDto = new UpdateExamDto
                {
                    Title = examModel.Title,
                    Description = examModel.Description,
                    StartDate = examModel.StartDate,
                    EndDate = examModel.EndDate,
                    DurationMinutes = examModel.DurationMinutes,
                    TotalMarks = examModel.TotalMarks,
                    PassingMarks = examModel.PassingMarks,
                    AllowRetake = examModel.AllowRetake,
                    MaxAttempts = examModel.MaxAttempts,
                    ShowResultsImmediately = examModel.ShowResultsImmediately,
                    RandomizeQuestions = examModel.RandomizeQuestions,
                    RandomizeOptions = examModel.RandomizeOptions
                };

                await ApiService.UpdateExamAsync(editingExam.Id, updateDto);
                await JSRuntime.InvokeVoidAsync("alert", "تم تحديث الامتحان بنجاح");
            }

            CloseExamModal();
            await LoadExams();
            await LoadStatistics();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ الامتحان: {ex.Message}");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task DeleteExam(int examId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا الامتحان؟ سيتم حذف جميع الأسئلة والنتائج المرتبطة به."))
        {
            try
            {
                await ApiService.DeleteExamAsync(examId);
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف الامتحان بنجاح");
                await LoadExams();
                await LoadStatistics();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف الامتحان: {ex.Message}");
            }
        }
    }

    private void ViewExam(int examId)
    {
        Navigation.NavigateTo($"/teacher/exams/{examId}");
    }

    private void ManageQuestions(int examId)
    {
        Navigation.NavigateTo($"/teacher/exams/{examId}/questions");
    }

    private void ViewResults(int examId)
    {
        Navigation.NavigateTo($"/teacher/exams/{examId}/results");
    }

    private async Task ResetFilters()
    {
        searchTerm = "";
        statusFilter = "";
        subjectFilter = "";
        classFilter = "";
        await LoadExams();
    }

    private string GetStatusBadgeClass(ExamStatus status)
    {
        return status switch
        {
            ExamStatus.Draft => "bg-secondary",
            ExamStatus.Published => "bg-primary",
            ExamStatus.InProgress => "bg-warning",
            ExamStatus.Completed => "bg-success",
            ExamStatus.Cancelled => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetStatusText(ExamStatus status)
    {
        return status switch
        {
            ExamStatus.Draft => "مسودة",
            ExamStatus.Published => "منشور",
            ExamStatus.InProgress => "قيد التنفيذ",
            ExamStatus.Completed => "مكتمل",
            ExamStatus.Cancelled => "ملغي",
            _ => "غير محدد"
        };
    }

    private enum ViewMode
    {
        Grid,
        List
    }
}
