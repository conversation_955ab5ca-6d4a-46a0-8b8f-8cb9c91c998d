using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.Models;
using Schools.Shared.DTOs;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class GradesController : ControllerBase
    {
        private readonly SchoolsDbContext _context;
        private readonly ILogger<GradesController> _logger;

        public GradesController(SchoolsDbContext context, ILogger<GradesController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<GradeDto>>> GetGrades(
            [FromQuery] string? studentId = null,
            [FromQuery] int? subjectId = null,
            [FromQuery] string? examType = null,
            [FromQuery] string? semester = null,
            [FromQuery] int? academicYearId = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50)
        {
            try
            {
                var query = _context.StudentGrades
                    .Include(g => g.Student)
                    .Include(g => g.Subject)
                    .Include(g => g.AcademicYear)
                    .AsQueryable();

                if (!string.IsNullOrEmpty(studentId))
                    query = query.Where(g => g.StudentId == studentId);

                if (subjectId.HasValue)
                    query = query.Where(g => g.SubjectId == subjectId.Value);

                if (!string.IsNullOrEmpty(examType))
                    query = query.Where(g => g.ExamType == examType);

                if (!string.IsNullOrEmpty(semester))
                    query = query.Where(g => g.Semester == semester);

                if (academicYearId.HasValue)
                    query = query.Where(g => g.AcademicYearId == academicYearId.Value);

                var totalCount = await query.CountAsync();
                var grades = await query
                    .OrderByDescending(g => g.Date)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(g => new GradeDto
                    {
                        Id = g.Id,
                        StudentId = g.StudentId,
                        StudentName = g.Student.FirstName + " " + g.Student.LastName,
                        StudentNumber = g.Student.UserName ?? "",
                        StudentEmail = g.Student.Email ?? "",
                        SubjectId = g.SubjectId,
                        SubjectName = g.Subject.Name,
                        SubjectCode = g.Subject.Code ?? "",
                        AcademicYearId = g.AcademicYearId,
                        AcademicYearName = g.AcademicYear.Name,
                        ExamType = g.ExamType,
                        Score = g.Score,
                        MaxScore = g.MaxScore,
                        Percentage = g.Percentage,
                        Date = g.Date,
                        Semester = g.Semester,
                        Notes = g.Notes,
                        RecordedBy = g.RecordedBy,
                        CreatedAt = g.CreatedAt
                    })
                    .ToListAsync();

                Response.Headers.Append("X-Total-Count", totalCount.ToString());
                return Ok(grades);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving grades");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<GradeDto>> GetGrade(int id)
        {
            try
            {
                var grade = await _context.StudentGrades
                    .Include(g => g.Student)
                    .Include(g => g.Subject)
                    .Include(g => g.AcademicYear)
                    .FirstOrDefaultAsync(g => g.Id == id);

                if (grade == null)
                    return NotFound();

                var gradeDto = new GradeDto
                {
                    Id = grade.Id,
                    StudentId = grade.StudentId,
                    StudentName = grade.Student.FirstName + " " + grade.Student.LastName,
                    StudentNumber = grade.Student.UserName ?? "",
                    StudentEmail = grade.Student.Email ?? "",
                    SubjectId = grade.SubjectId,
                    SubjectName = grade.Subject.Name,
                    SubjectCode = grade.Subject.Code ?? "",
                    AcademicYearId = grade.AcademicYearId,
                    AcademicYearName = grade.AcademicYear.Name,
                    ExamType = grade.ExamType,
                    Score = grade.Score,
                    MaxScore = grade.MaxScore,
                    Percentage = grade.Percentage,
                    Date = grade.Date,
                    Semester = grade.Semester,
                    Notes = grade.Notes,
                    RecordedBy = grade.RecordedBy,
                    CreatedAt = grade.CreatedAt
                };

                return Ok(gradeDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving grade with id {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<ActionResult<GradeDto>> CreateGrade(CreateGradeDto createGradeDto)
        {
            try
            {
                // Validate student exists
                var student = await _context.Users.FindAsync(createGradeDto.StudentId);
                if (student == null)
                    return BadRequest("Student not found");

                // Validate subject exists
                var subject = await _context.Subjects.FindAsync(createGradeDto.SubjectId);
                if (subject == null)
                    return BadRequest("Subject not found");

                // Validate academic year exists
                var academicYear = await _context.AcademicYears.FindAsync(createGradeDto.AcademicYearId);
                if (academicYear == null)
                    return BadRequest("Academic year not found");

                // Check for duplicate grade
                var existingGrade = await _context.StudentGrades
                    .FirstOrDefaultAsync(g => g.StudentId == createGradeDto.StudentId &&
                                            g.SubjectId == createGradeDto.SubjectId &&
                                            g.ExamType == createGradeDto.ExamType &&
                                            g.Semester == createGradeDto.Semester &&
                                            g.AcademicYearId == createGradeDto.AcademicYearId);

                if (existingGrade != null)
                    return BadRequest("Grade already exists for this student, subject, exam type, and semester");

                var grade = new StudentGrade
                {
                    StudentId = createGradeDto.StudentId,
                    SubjectId = createGradeDto.SubjectId,
                    AcademicYearId = createGradeDto.AcademicYearId,
                    ExamType = createGradeDto.ExamType,
                    Score = createGradeDto.Score,
                    MaxScore = createGradeDto.MaxScore,
                    Date = createGradeDto.Date,
                    Semester = createGradeDto.Semester,
                    Notes = createGradeDto.Notes,
                    RecordedBy = User.Identity?.Name,
                    CreatedAt = DateTime.UtcNow
                };

                _context.StudentGrades.Add(grade);
                await _context.SaveChangesAsync();

                // Reload with includes
                grade = await _context.StudentGrades
                    .Include(g => g.Student)
                    .Include(g => g.Subject)
                    .Include(g => g.AcademicYear)
                    .FirstAsync(g => g.Id == grade.Id);

                var gradeDto = new GradeDto
                {
                    Id = grade.Id,
                    StudentId = grade.StudentId,
                    StudentName = grade.Student.FirstName + " " + grade.Student.LastName,
                    StudentNumber = grade.Student.UserName ?? "",
                    StudentEmail = grade.Student.Email ?? "",
                    SubjectId = grade.SubjectId,
                    SubjectName = grade.Subject.Name,
                    SubjectCode = grade.Subject.Code ?? "",
                    AcademicYearId = grade.AcademicYearId,
                    AcademicYearName = grade.AcademicYear.Name,
                    ExamType = grade.ExamType,
                    Score = grade.Score,
                    MaxScore = grade.MaxScore,
                    Percentage = grade.Percentage,
                    Date = grade.Date,
                    Semester = grade.Semester,
                    Notes = grade.Notes,
                    RecordedBy = grade.RecordedBy,
                    CreatedAt = grade.CreatedAt
                };

                return CreatedAtAction(nameof(GetGrade), new { id = grade.Id }, gradeDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating grade");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<IActionResult> UpdateGrade(int id, UpdateGradeDto updateGradeDto)
        {
            try
            {
                var grade = await _context.StudentGrades.FindAsync(id);
                if (grade == null)
                    return NotFound();

                grade.Score = updateGradeDto.Score;
                grade.MaxScore = updateGradeDto.MaxScore;
                grade.ExamType = updateGradeDto.ExamType;
                grade.Date = updateGradeDto.Date;
                grade.Semester = updateGradeDto.Semester;
                grade.Notes = updateGradeDto.Notes;
                grade.UpdatedAt = DateTime.UtcNow;
                grade.UpdatedBy = User.Identity?.Name;

                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating grade with id {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteGrade(int id)
        {
            try
            {
                var grade = await _context.StudentGrades.FindAsync(id);
                if (grade == null)
                    return NotFound();

                _context.StudentGrades.Remove(grade);
                await _context.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting grade with id {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("bulk")]
        [Authorize(Roles = "Admin,Teacher")]
        public async Task<ActionResult> CreateBulkGrades(BulkGradeDto bulkGradeDto)
        {
            try
            {
                // Validate subject exists
                var subject = await _context.Subjects.FindAsync(bulkGradeDto.SubjectId);
                if (subject == null)
                    return BadRequest("Subject not found");

                // Validate academic year exists
                var academicYear = await _context.AcademicYears.FindAsync(bulkGradeDto.AcademicYearId);
                if (academicYear == null)
                    return BadRequest("Academic year not found");

                var gradeRecords = new List<StudentGrade>();

                foreach (var studentGrade in bulkGradeDto.StudentGrades)
                {
                    // Check if student exists
                    var student = await _context.Users.FindAsync(studentGrade.StudentId);
                    if (student == null)
                        continue; // Skip invalid students

                    // Check for duplicate grade
                    var existingGrade = await _context.StudentGrades
                        .FirstOrDefaultAsync(g => g.StudentId == studentGrade.StudentId &&
                                                g.SubjectId == bulkGradeDto.SubjectId &&
                                                g.ExamType == bulkGradeDto.ExamType &&
                                                g.Semester == bulkGradeDto.Semester &&
                                                g.AcademicYearId == bulkGradeDto.AcademicYearId);

                    if (existingGrade != null)
                        continue; // Skip duplicates

                    var grade = new StudentGrade
                    {
                        StudentId = studentGrade.StudentId,
                        SubjectId = bulkGradeDto.SubjectId,
                        AcademicYearId = bulkGradeDto.AcademicYearId,
                        ExamType = bulkGradeDto.ExamType,
                        Score = studentGrade.Score,
                        MaxScore = bulkGradeDto.MaxScore,
                        Date = bulkGradeDto.Date,
                        Semester = bulkGradeDto.Semester,
                        Notes = studentGrade.Notes,
                        RecordedBy = User.Identity?.Name,
                        CreatedAt = DateTime.UtcNow
                    };

                    gradeRecords.Add(grade);
                }

                if (gradeRecords.Any())
                {
                    _context.StudentGrades.AddRange(gradeRecords);
                    await _context.SaveChangesAsync();
                }

                return Ok(new { message = $"Created {gradeRecords.Count} grade records" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating bulk grades");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("statistics")]
        public async Task<ActionResult<GradeStatisticsDto>> GetGradeStatistics(
            [FromQuery] int? subjectId = null,
            [FromQuery] string? examType = null,
            [FromQuery] string? semester = null,
            [FromQuery] int? academicYearId = null)
        {
            try
            {
                var query = _context.StudentGrades.AsQueryable();

                if (subjectId.HasValue)
                    query = query.Where(g => g.SubjectId == subjectId.Value);

                if (!string.IsNullOrEmpty(examType))
                    query = query.Where(g => g.ExamType == examType);

                if (!string.IsNullOrEmpty(semester))
                    query = query.Where(g => g.Semester == semester);

                if (academicYearId.HasValue)
                    query = query.Where(g => g.AcademicYearId == academicYearId.Value);

                var grades = await query.ToListAsync();

                if (!grades.Any())
                {
                    return Ok(new GradeStatisticsDto
                    {
                        TotalStudents = 0,
                        AverageScore = 0,
                        HighestScore = 0,
                        LowestScore = 0,
                        PassRate = 0
                    });
                }

                var statistics = new GradeStatisticsDto
                {
                    TotalStudents = grades.Count,
                    AverageScore = grades.Average(g => g.Percentage),
                    HighestScore = grades.Max(g => g.Percentage),
                    LowestScore = grades.Min(g => g.Percentage),
                    PassRate = grades.Count(g => g.Percentage >= 60) * 100.0m / grades.Count
                };

                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving grade statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("student/{studentId}")]
        public async Task<ActionResult<StudentGradeSummaryDto>> GetStudentGrades(string studentId, [FromQuery] int? academicYearId = null)
        {
            try
            {
                var student = await _context.Users.FindAsync(studentId);
                if (student == null)
                    return NotFound("Student not found");

                var query = _context.StudentGrades
                    .Include(g => g.Subject)
                    .Where(g => g.StudentId == studentId);

                if (academicYearId.HasValue)
                    query = query.Where(g => g.AcademicYearId == academicYearId.Value);

                var grades = await query.ToListAsync();

                var subjectGrades = grades
                    .GroupBy(g => new { g.SubjectId, g.Subject.Name })
                    .Select(g => new SubjectGradeDto
                    {
                        SubjectId = g.Key.SubjectId,
                        SubjectName = g.Key.Name,
                        GradeCount = g.Count(),
                        AverageScore = (double)g.Average(x => x.Percentage)
                    })
                    .ToList();

                var summary = new StudentGradeSummaryDto
                {
                    StudentId = studentId,
                    StudentName = student.FirstName + " " + student.LastName,
                    TotalGrades = grades.Count,
                    OverallAverage = grades.Any() ? (double)grades.Average(g => g.Percentage) : 0,
                    OverallGrade = CalculateLetterGrade(grades.Any() ? grades.Average(g => g.Percentage) : 0),
                    SubjectGrades = subjectGrades
                };

                return Ok(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving student grades for {StudentId}", studentId);
                return StatusCode(500, "Internal server error");
            }
        }

        private string CalculateLetterGrade(decimal percentage)
        {
            return percentage switch
            {
                >= 90 => "A+",
                >= 85 => "A",
                >= 80 => "B+",
                >= 75 => "B",
                >= 70 => "C+",
                >= 65 => "C",
                >= 60 => "D",
                _ => "F"
            };
        }
    }
}
