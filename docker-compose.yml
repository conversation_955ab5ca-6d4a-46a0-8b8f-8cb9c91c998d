version: '3.8'

services:
  # SQL Server Database
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: schools-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong@Password123
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
      - ./scripts:/scripts
    networks:
      - schools-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourStrong@Password123 -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Schools API
  schools-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: schools-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver,1433;Database=SchoolsDB;User Id=sa;Password=YourStrong@Password123;TrustServerCertificate=true;MultipleActiveResultSets=true
      - JwtSettings__SecretKey=YourSuperSecretKeyForProductionThatIsAtLeast32CharactersLong!
      - JwtSettings__Issuer=SchoolsAPI
      - JwtSettings__Audience=SchoolsClient
    ports:
      - "8080:80"
    depends_on:
      sqlserver:
        condition: service_healthy
    volumes:
      - uploads_data:/app/wwwroot/uploads
      - logs_data:/app/logs
    networks:
      - schools-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache (Optional)
  redis:
    image: redis:7-alpine
    container_name: schools-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - schools-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: schools-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - uploads_data:/var/www/uploads:ro
    depends_on:
      - schools-api
    networks:
      - schools-network
    restart: unless-stopped

volumes:
  sqlserver_data:
    driver: local
  uploads_data:
    driver: local
  logs_data:
    driver: local
  redis_data:
    driver: local

networks:
  schools-network:
    driver: bridge
