@page "/admin/executive-dashboard"
@using Schools.Shared.DTOs
@using Schools.Client.Services
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<div class="container-fluid">
    <!-- Header with Real-time Clock -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-1">لوحة التحكم التنفيذية</h2>
                            <p class="mb-0">نظرة شاملة على أداء المؤسسة التعليمية</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div id="real-time-clock" class="h5 mb-0"></div>
                            <small>التوقيت المحلي</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-3">جاري تحميل البيانات التنفيذية...</p>
        </div>
    }
    else
    {
        <!-- Key Performance Indicators -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="d-flex justify-content-center align-items-center mb-3">
                            <div class="bg-primary rounded-circle p-3">
                                <i class="fas fa-users fa-2x text-white"></i>
                            </div>
                        </div>
                        <h3 class="text-primary">@totalStudents</h3>
                        <p class="text-muted mb-1">إجمالي الطلاب</p>
                        <small class="text-success">
                            <i class="fas fa-arrow-up"></i> +@newStudentsThisMonth هذا الشهر
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="d-flex justify-content-center align-items-center mb-3">
                            <div class="bg-success rounded-circle p-3">
                                <i class="fas fa-chalkboard-teacher fa-2x text-white"></i>
                            </div>
                        </div>
                        <h3 class="text-success">@totalTeachers</h3>
                        <p class="text-muted mb-1">إجمالي المعلمين</p>
                        <small class="text-info">
                            <i class="fas fa-star"></i> @teacherSatisfactionRate% رضا المعلمين
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="d-flex justify-content-center align-items-center mb-3">
                            <div class="bg-warning rounded-circle p-3">
                                <i class="fas fa-graduation-cap fa-2x text-white"></i>
                            </div>
                        </div>
                        <h3 class="text-warning">@overallGPA.ToString("F1")</h3>
                        <p class="text-muted mb-1">المعدل العام</p>
                        <small class="text-success">
                            <i class="fas fa-arrow-up"></i> +@gpaImprovement.ToString("F1")% تحسن
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="d-flex justify-content-center align-items-center mb-3">
                            <div class="bg-info rounded-circle p-3">
                                <i class="fas fa-calendar-check fa-2x text-white"></i>
                            </div>
                        </div>
                        <h3 class="text-info">@attendanceRate.ToString("F1")%</h3>
                        <p class="text-muted mb-1">معدل الحضور</p>
                        <small class="text-success">
                            <i class="fas fa-check"></i> ممتاز
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <!-- Academic Performance Chart -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            الأداء الأكاديمي الشهري
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="academicPerformanceChart" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- Attendance Trends Chart -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-area me-2"></i>
                            اتجاهات الحضور
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="attendanceTrendsChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Overview -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0">
                            <i class="fas fa-dollar-sign me-2"></i>
                            النظرة المالية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <div class="border-end">
                                    <h4 class="text-success">@totalRevenue.ToString("C0")</h4>
                                    <p class="text-muted">إجمالي الإيرادات</p>
                                    <small class="text-success">
                                        <i class="fas fa-arrow-up"></i> +@revenueGrowth.ToString("F1")%
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="border-end">
                                    <h4 class="text-primary">@collectedFees.ToString("C0")</h4>
                                    <p class="text-muted">الرسوم المحصلة</p>
                                    <small class="text-info">
                                        @collectionRate.ToString("F1")% معدل التحصيل
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <h4 class="text-warning">@pendingFees.ToString("C0")</h4>
                                <p class="text-muted">الرسوم المعلقة</p>
                                <small class="text-warning">
                                    @pendingCount طالب
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="col-md-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" @onclick="GenerateMonthlyReport">
                                <i class="fas fa-file-pdf me-2"></i>
                                تقرير شهري
                            </button>
                            <button class="btn btn-success" @onclick="SendBulkNotification">
                                <i class="fas fa-bullhorn me-2"></i>
                                إشعار جماعي
                            </button>
                            <button class="btn btn-info" @onclick="BackupData">
                                <i class="fas fa-database me-2"></i>
                                نسخ احتياطي
                            </button>
                            <button class="btn btn-warning" @onclick="ViewSystemHealth">
                                <i class="fas fa-heartbeat me-2"></i>
                                صحة النظام
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities & Alerts -->
        <div class="row mb-4">
            <!-- Recent Activities -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            النشاطات الأخيرة
                        </h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        @foreach (var activity in recentActivities.Take(8))
                        {
                            <div class="d-flex align-items-center mb-3 p-2 rounded" style="background-color: #f8f9fa;">
                                <div class="me-3">
                                    <div class="rounded-circle p-2 @GetActivityBgColor(activity.Type)">
                                        <i class="@GetActivityIcon(activity.Type) text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">@activity.Title</h6>
                                    <p class="mb-1 text-muted small">@activity.Description</p>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        @activity.Timestamp.ToString("HH:mm")
                                    </small>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- System Alerts -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تنبيهات النظام
                        </h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        @foreach (var alert in systemAlerts.Take(6))
                        {
                            <div class="alert @GetAlertClass(alert.Priority) alert-dismissible fade show" role="alert">
                                <div class="d-flex align-items-center">
                                    <i class="@GetAlertIcon(alert.Priority) me-2"></i>
                                    <div>
                                        <strong>@alert.Title</strong>
                                        <p class="mb-0 small">@alert.Message</p>
                                        <small class="text-muted">@alert.Timestamp.ToString("yyyy-MM-dd HH:mm")</small>
                                    </div>
                                </div>
                                <button type="button" class="btn-close" @onclick="@(() => DismissAlert(alert))"></button>
                            </div>
                        }

                        @if (!systemAlerts.Any())
                        {
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <h6 class="text-success">لا توجد تنبيهات</h6>
                                <p class="text-muted small">جميع الأنظمة تعمل بشكل طبيعي</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            مؤشرات الأداء الرئيسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="progress mx-auto mb-3" style="width: 120px; height: 120px;">
                                        <svg class="progress-ring" width="120" height="120">
                                            <circle class="progress-ring-circle" stroke="#e9ecef" stroke-width="8" fill="transparent" r="52" cx="60" cy="60"/>
                                            <circle class="progress-ring-circle" stroke="#28a745" stroke-width="8" fill="transparent" r="52" cx="60" cy="60"
                                                    stroke-dasharray="@GetCircumference()" stroke-dashoffset="@GetDashOffset(studentSatisfaction)"/>
                                        </svg>
                                        <div class="progress-text">@studentSatisfaction%</div>
                                    </div>
                                    <h6>رضا الطلاب</h6>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="progress mx-auto mb-3" style="width: 120px; height: 120px;">
                                        <svg class="progress-ring" width="120" height="120">
                                            <circle class="progress-ring-circle" stroke="#e9ecef" stroke-width="8" fill="transparent" r="52" cx="60" cy="60"/>
                                            <circle class="progress-ring-circle" stroke="#007bff" stroke-width="8" fill="transparent" r="52" cx="60" cy="60"
                                                    stroke-dasharray="@GetCircumference()" stroke-dashoffset="@GetDashOffset(teacherEfficiency)"/>
                                        </svg>
                                        <div class="progress-text">@teacherEfficiency%</div>
                                    </div>
                                    <h6>كفاءة المعلمين</h6>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="progress mx-auto mb-3" style="width: 120px; height: 120px;">
                                        <svg class="progress-ring" width="120" height="120">
                                            <circle class="progress-ring-circle" stroke="#e9ecef" stroke-width="8" fill="transparent" r="52" cx="60" cy="60"/>
                                            <circle class="progress-ring-circle" stroke="#ffc107" stroke-width="8" fill="transparent" r="52" cx="60" cy="60"
                                                    stroke-dasharray="@GetCircumference()" stroke-dashoffset="@GetDashOffset(systemUptime)"/>
                                        </svg>
                                        <div class="progress-text">@systemUptime%</div>
                                    </div>
                                    <h6>وقت تشغيل النظام</h6>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="progress mx-auto mb-3" style="width: 120px; height: 120px;">
                                        <svg class="progress-ring" width="120" height="120">
                                            <circle class="progress-ring-circle" stroke="#e9ecef" stroke-width="8" fill="transparent" r="52" cx="60" cy="60"/>
                                            <circle class="progress-ring-circle" stroke="#dc3545" stroke-width="8" fill="transparent" r="52" cx="60" cy="60"
                                                    stroke-dasharray="@GetCircumference()" stroke-dashoffset="@GetDashOffset(dataAccuracy)"/>
                                        </svg>
                                        <div class="progress-text">@dataAccuracy%</div>
                                    </div>
                                    <h6>دقة البيانات</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .progress-ring {
        transform: rotate(-90deg);
    }

    .progress-ring-circle {
        transition: stroke-dashoffset 0.35s;
        transform-origin: 50% 50%;
    }

    .progress {
        position: relative;
    }

    .progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 18px;
        font-weight: bold;
        color: #333;
    }

    .card {
        transition: transform 0.2s ease-in-out;
    }

    .card:hover {
        transform: translateY(-2px);
    }

    .alert {
        border: none;
        border-radius: 10px;
    }

    .rounded-circle {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>

@code {
    private bool isLoading = true;

    // KPIs
    private int totalStudents = 0;
    private int totalTeachers = 0;
    private double overallGPA = 0;
    private double attendanceRate = 0;
    private int newStudentsThisMonth = 0;
    private int teacherSatisfactionRate = 0;
    private double gpaImprovement = 0;

    // Financial Data
    private decimal totalRevenue = 0;
    private decimal collectedFees = 0;
    private decimal pendingFees = 0;
    private double revenueGrowth = 0;
    private double collectionRate = 0;
    private int pendingCount = 0;

    // Performance Metrics
    private int studentSatisfaction = 88;
    private int teacherEfficiency = 94;
    private int systemUptime = 99;
    private int dataAccuracy = 97;

    // Activities and Alerts
    private List<ActivityItem> recentActivities = new();
    private List<SystemAlert> systemAlerts = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
        await JSRuntime.InvokeVoidAsync("startClock", "real-time-clock");
        await InitializeCharts();
    }

    private async Task LoadDashboardData()
    {
        try
        {
            isLoading = true;

            // Load basic data
            var users = await ApiService.GetUsersAsync();
            totalStudents = users.Count(u => u.Role == "Student");
            totalTeachers = users.Count(u => u.Role == "Teacher");

            // Mock additional data (in real app, these would come from API)
            overallGPA = 3.45;
            attendanceRate = 92.3;
            newStudentsThisMonth = 15;
            teacherSatisfactionRate = 89;
            gpaImprovement = 2.3;

            // Financial data (mock)
            totalRevenue = 2500000;
            collectedFees = 2100000;
            pendingFees = 400000;
            revenueGrowth = 8.5;
            collectionRate = 84.0;
            pendingCount = 45;

            GenerateRecentActivities();
            GenerateSystemAlerts();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void GenerateRecentActivities()
    {
        recentActivities = new List<ActivityItem>
        {
            new() { Type = "grade", Title = "إدخال درجات جديدة", Description = "تم إدخال درجات امتحان الرياضيات للصف الثالث", Timestamp = DateTime.Now.AddMinutes(-5) },
            new() { Type = "attendance", Title = "تسجيل حضور", Description = "تم تسجيل حضور جميع الصفوف لليوم", Timestamp = DateTime.Now.AddMinutes(-15) },
            new() { Type = "user", Title = "مستخدم جديد", Description = "تم تسجيل معلم جديد في النظام", Timestamp = DateTime.Now.AddMinutes(-30) },
            new() { Type = "payment", Title = "دفع رسوم", Description = "تم دفع رسوم 5 طلاب جدد", Timestamp = DateTime.Now.AddHours(-1) },
            new() { Type = "report", Title = "تقرير شهري", Description = "تم إنشاء التقرير الشهري للأداء", Timestamp = DateTime.Now.AddHours(-2) },
            new() { Type = "notification", Title = "إشعار جماعي", Description = "تم إرسال إشعار لجميع أولياء الأمور", Timestamp = DateTime.Now.AddHours(-3) },
            new() { Type = "backup", Title = "نسخ احتياطي", Description = "تم إنشاء نسخة احتياطية من قاعدة البيانات", Timestamp = DateTime.Now.AddHours(-4) },
            new() { Type = "system", Title = "تحديث النظام", Description = "تم تحديث النظام بنجاح", Timestamp = DateTime.Now.AddHours(-5) }
        };
    }

    private void GenerateSystemAlerts()
    {
        systemAlerts = new List<SystemAlert>
        {
            new() { Priority = "warning", Title = "رسوم معلقة", Message = "يوجد 45 طالب لديهم رسوم معلقة", Timestamp = DateTime.Now.AddHours(-1) },
            new() { Priority = "info", Title = "نسخ احتياطي", Message = "موعد النسخ الاحتياطي التالي خلال ساعتين", Timestamp = DateTime.Now.AddHours(-2) },
            new() { Priority = "success", Title = "تحديث مكتمل", Message = "تم تحديث النظام بنجاح", Timestamp = DateTime.Now.AddHours(-3) }
        };
    }

    private async Task InitializeCharts()
    {
        try
        {
            // Academic Performance Chart
            var academicData = new
            {
                labels = new[] { "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو" },
                datasets = new[]
                {
                    new
                    {
                        label = "المعدل العام",
                        data = new[] { 3.2, 3.3, 3.4, 3.5, 3.4, 3.45 },
                        borderColor = "#007bff",
                        backgroundColor = "rgba(0, 123, 255, 0.1)",
                        tension = 0.4
                    }
                }
            };

            await JSRuntime.InvokeVoidAsync("createChart", "academicPerformanceChart", "line", academicData, new
            {
                responsive = true,
                plugins = new
                {
                    legend = new { display = true }
                },
                scales = new
                {
                    y = new { beginAtZero = true, max = 4.0 }
                }
            });

            // Attendance Trends Chart
            var attendanceData = new
            {
                labels = new[] { "الأسبوع 1", "الأسبوع 2", "الأسبوع 3", "الأسبوع 4" },
                datasets = new[]
                {
                    new
                    {
                        label = "معدل الحضور",
                        data = new[] { 89, 92, 94, 92 },
                        backgroundColor = "rgba(40, 167, 69, 0.2)",
                        borderColor = "#28a745",
                        fill = true,
                        tension = 0.4
                    }
                }
            };

            await JSRuntime.InvokeVoidAsync("createChart", "attendanceTrendsChart", "line", attendanceData, new
            {
                responsive = true,
                plugins = new
                {
                    legend = new { display = true }
                },
                scales = new
                {
                    y = new { beginAtZero = true, max = 100 }
                }
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error initializing charts: {ex.Message}");
        }
    }

    private async Task GenerateMonthlyReport()
    {
        await JSRuntime.InvokeVoidAsync("showNotification", "جاري إنشاء التقرير الشهري...", "info");
        await Task.Delay(2000);
        await JSRuntime.InvokeVoidAsync("showNotification", "تم إنشاء التقرير الشهري بنجاح", "success");
    }

    private async Task SendBulkNotification()
    {
        await JSRuntime.InvokeVoidAsync("showNotification", "جاري إرسال الإشعار الجماعي...", "info");
        await Task.Delay(1500);
        await JSRuntime.InvokeVoidAsync("showNotification", "تم إرسال الإشعار لجميع المستخدمين", "success");
    }

    private async Task BackupData()
    {
        await JSRuntime.InvokeVoidAsync("showNotification", "جاري إنشاء النسخة الاحتياطية...", "info");
        await Task.Delay(3000);
        await JSRuntime.InvokeVoidAsync("showNotification", "تم إنشاء النسخة الاحتياطية بنجاح", "success");
    }

    private async Task ViewSystemHealth()
    {
        await JSRuntime.InvokeVoidAsync("showNotification", "جميع الأنظمة تعمل بشكل طبيعي", "success");
    }

    private void DismissAlert(SystemAlert alert)
    {
        systemAlerts.Remove(alert);
        StateHasChanged();
    }

    private string GetActivityIcon(string type)
    {
        return type switch
        {
            "grade" => "fas fa-star",
            "attendance" => "fas fa-calendar-check",
            "user" => "fas fa-user-plus",
            "payment" => "fas fa-credit-card",
            "report" => "fas fa-file-alt",
            "notification" => "fas fa-bell",
            "backup" => "fas fa-database",
            "system" => "fas fa-cog",
            _ => "fas fa-info"
        };
    }

    private string GetActivityBgColor(string type)
    {
        return type switch
        {
            "grade" => "bg-success",
            "attendance" => "bg-info",
            "user" => "bg-primary",
            "payment" => "bg-warning",
            "report" => "bg-secondary",
            "notification" => "bg-danger",
            "backup" => "bg-dark",
            "system" => "bg-primary",
            _ => "bg-secondary"
        };
    }

    private string GetAlertClass(string priority)
    {
        return priority switch
        {
            "error" => "alert-danger",
            "warning" => "alert-warning",
            "info" => "alert-info",
            "success" => "alert-success",
            _ => "alert-secondary"
        };
    }

    private string GetAlertIcon(string priority)
    {
        return priority switch
        {
            "error" => "fas fa-exclamation-circle",
            "warning" => "fas fa-exclamation-triangle",
            "info" => "fas fa-info-circle",
            "success" => "fas fa-check-circle",
            _ => "fas fa-bell"
        };
    }

    private double GetCircumference()
    {
        return 2 * Math.PI * 52; // radius = 52
    }

    private double GetDashOffset(int percentage)
    {
        var circumference = GetCircumference();
        return circumference - (percentage / 100.0) * circumference;
    }

    public class ActivityItem
    {
        public string Type { get; set; } = "";
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public DateTime Timestamp { get; set; }
    }

    public class SystemAlert
    {
        public string Priority { get; set; } = "";
        public string Title { get; set; } = "";
        public string Message { get; set; } = "";
        public DateTime Timestamp { get; set; }
    }
}
