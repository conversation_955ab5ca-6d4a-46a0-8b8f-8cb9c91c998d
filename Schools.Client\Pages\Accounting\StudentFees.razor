@page "/accounting/fees"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Accountant,Admin")]

<PageTitle>إدارة رسوم الطلاب - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-dollar-sign text-warning me-2"></i>
                        إدارة رسوم الطلاب
                    </h2>
                    <p class="text-muted mb-0">إدارة وتتبع رسوم الطلاب والمدفوعات</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-warning" @onclick="CreateNewFeeStructure">
                        <i class="fas fa-plus me-2"></i>
                        هيكل رسوم جديد
                    </button>
                    <button class="btn btn-outline-primary" @onclick="RefreshData">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث
                    </button>
                    <button class="btn btn-outline-success" @onclick="ExportReport">
                        <i class="fas fa-download me-2"></i>
                        تقرير شامل
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل بيانات الرسوم...</p>
        </div>
    }
    else
    {
        <!-- Summary Cards -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm bg-primary text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@totalStudents</h4>
                        <p class="mb-0">إجمالي الطلاب</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm bg-success text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@totalFeesAmount.ToString("C")</h4>
                        <p class="mb-0">إجمالي الرسوم</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm bg-info text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@totalPaidAmount.ToString("C")</h4>
                        <p class="mb-0">المبلغ المحصل</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm bg-danger text-white">
                    <div class="card-body text-center">
                        <h4 class="mb-1">@totalOutstandingAmount.ToString("C")</h4>
                        <p class="mb-0">المبلغ المستحق</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">الصف الدراسي</label>
                                <select @bind="selectedGrade" @bind:after="FilterStudents" class="form-select">
                                    <option value="">جميع الصفوف</option>
                                    @foreach (var grade in grades)
                                    {
                                        <option value="@grade.Id">@grade.GradeName</option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الفصل</label>
                                <select @bind="selectedClass" @bind:after="FilterStudents" class="form-select">
                                    <option value="">جميع الفصول</option>
                                    @foreach (var cls in classes)
                                    {
                                        <option value="@cls.Id">@cls.ClassName</option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">حالة الدفع</label>
                                <select @bind="selectedPaymentStatus" @bind:after="FilterStudents" class="form-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="paid">مدفوع بالكامل</option>
                                    <option value="partial">مدفوع جزئياً</option>
                                    <option value="unpaid">غير مدفوع</option>
                                    <option value="overdue">متأخر</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" placeholder="اسم الطالب أو رقم الهوية..."
                                           @bind="searchTerm" @bind:after="FilterStudents" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Students Fees Table -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-users me-2 text-warning"></i>
                                رسوم الطلاب
                            </h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-success" @onclick="CollectBulkPayments">
                                    <i class="fas fa-money-bill-wave me-1"></i>
                                    تحصيل جماعي
                                </button>
                                <button class="btn btn-outline-info" @onclick="SendPaymentReminders">
                                    <i class="fas fa-bell me-1"></i>
                                    إرسال تذكيرات
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (filteredStudentFees?.Any() == true)
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>
                                                <input type="checkbox" @onchange="SelectAllStudents" />
                                            </th>
                                            <th>الطالب</th>
                                            <th>الصف/الفصل</th>
                                            <th>إجمالي الرسوم</th>
                                            <th>المدفوع</th>
                                            <th>المتبقي</th>
                                            <th>حالة الدفع</th>
                                            <th>آخر دفعة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var studentFee in filteredStudentFees.Skip((currentPage - 1) * pageSize).Take(pageSize))
                                        {
                                            <tr class="@GetRowClass(studentFee.PaymentStatus)">
                                                <td>
                                                    <input type="checkbox" @onchange="(e) => SelectStudent(studentFee.StudentId, e)" />
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                            @studentFee.StudentName.Substring(0, 1)
                                                        </div>
                                                        <div>
                                                            <strong>@studentFee.StudentName</strong>
                                                            <br>
                                                            <small class="text-muted">@studentFee.StudentNumber</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info">@studentFee.GradeName</span>
                                                    <br>
                                                    <small class="text-muted">@studentFee.ClassName</small>
                                                </td>
                                                <td>
                                                    <strong class="text-primary">@studentFee.TotalFees.ToString("C")</strong>
                                                </td>
                                                <td>
                                                    <strong class="text-success">@studentFee.PaidAmount.ToString("C")</strong>
                                                </td>
                                                <td>
                                                    <strong class="@GetAmountClass(studentFee.OutstandingAmount)">
                                                        @studentFee.OutstandingAmount.ToString("C")
                                                    </strong>
                                                </td>
                                                <td>
                                                    <span class="badge @GetPaymentStatusBadge(studentFee.PaymentStatus)">
                                                        @GetPaymentStatusText(studentFee.PaymentStatus)
                                                    </span>
                                                    @if (studentFee.PaymentStatus == "overdue")
                                                    {
                                                        <br>
                                                        <small class="text-danger">
                                                            <i class="fas fa-exclamation-triangle"></i>
                                                            متأخر @GetOverdueDays(studentFee.LastPaymentDate) يوم
                                                        </small>
                                                    }
                                                </td>
                                                <td>
                                                    @if (studentFee.LastPaymentDate.HasValue)
                                                    {
                                                        <span>@studentFee.LastPaymentDate.Value.ToString("dd/MM/yyyy")</span>
                                                        <br>
                                                        <small class="text-muted">@studentFee.LastPaymentAmount.ToString("C")</small>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">لا يوجد</span>
                                                    }
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewStudentDetails(studentFee.StudentId)" title="تفاصيل">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-success" @onclick="() => RecordPayment(studentFee.StudentId)" title="تسجيل دفعة">
                                                            <i class="fas fa-money-bill"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-info" @onclick="() => ViewPaymentHistory(studentFee.StudentId)" title="تاريخ المدفوعات">
                                                            <i class="fas fa-history"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-warning" @onclick="() => SendReminder(studentFee.StudentId)" title="إرسال تذكير">
                                                            <i class="fas fa-bell"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            @if (totalPages > 1)
                            {
                                <nav class="mt-4">
                                    <ul class="pagination justify-content-center">
                                        <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                            <button class="page-link" @onclick="() => ChangePage(currentPage - 1)">السابق</button>
                                        </li>
                                        @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                                        {
                                            <li class="page-item @(i == currentPage ? "active" : "")">
                                                <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                                            </li>
                                        }
                                        <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                                            <button class="page-link" @onclick="() => ChangePage(currentPage + 1)">التالي</button>
                                        </li>
                                    </ul>
                                </nav>
                            }
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-dollar-sign fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد بيانات رسوم</h5>
                                <p class="text-muted">ابدأ بإنشاء هيكل الرسوم للطلاب</p>
                                <button class="btn btn-warning" @onclick="CreateNewFeeStructure">
                                    <i class="fas fa-plus me-2"></i>
                                    إنشاء هيكل رسوم
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private List<StudentFeeDto> allStudentFees = new();
    private List<StudentFeeDto> filteredStudentFees = new();
    private List<GradeDto> grades = new();
    private List<ClassDto> classes = new();

    private bool isLoading = true;
    private string selectedGrade = "";
    private string selectedClass = "";
    private string selectedPaymentStatus = "";
    private string searchTerm = "";

    private int currentPage = 1;
    private int pageSize = 10;
    private int totalPages = 1;

    private HashSet<int> selectedStudents = new();

    // Summary data
    private int totalStudents = 0;
    private decimal totalFeesAmount = 0;
    private decimal totalPaidAmount = 0;
    private decimal totalOutstandingAmount = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // Load student fees data
            allStudentFees = await ApiService.GetStudentFeesAsync() ?? new List<StudentFeeDto>();

            // Load grades and classes for filters
            grades = await ApiService.GetGradesAsync() ?? new List<GradeDto>();
            classes = await ApiService.GetClassesAsync() ?? new List<ClassDto>();

            FilterStudents();
            CalculateSummary();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void FilterStudents()
    {
        filteredStudentFees = allStudentFees.Where(s =>
            (string.IsNullOrEmpty(selectedGrade) || s.GradeId.ToString() == selectedGrade) &&
            (string.IsNullOrEmpty(selectedClass) || s.ClassId.ToString() == selectedClass) &&
            (string.IsNullOrEmpty(selectedPaymentStatus) || s.PaymentStatus == selectedPaymentStatus) &&
            (string.IsNullOrEmpty(searchTerm) ||
             s.StudentName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
             s.StudentNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
        ).OrderBy(s => s.StudentName).ToList();

        UpdatePagination();
        CalculateSummary();
        StateHasChanged();
    }

    private void UpdatePagination()
    {
        totalPages = (int)Math.Ceiling((double)filteredStudentFees.Count / pageSize);
        if (currentPage > totalPages && totalPages > 0)
        {
            currentPage = totalPages;
        }
        else if (currentPage < 1)
        {
            currentPage = 1;
        }
    }

    private void ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            StateHasChanged();
        }
    }

    private void CalculateSummary()
    {
        totalStudents = filteredStudentFees.Count;
        totalFeesAmount = filteredStudentFees.Sum(s => s.TotalFees);
        totalPaidAmount = filteredStudentFees.Sum(s => s.PaidAmount);
        totalOutstandingAmount = filteredStudentFees.Sum(s => s.OutstandingAmount);
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private void CreateNewFeeStructure()
    {
        Navigation.NavigateTo("/accounting/fee-structures/new");
    }

    private void ViewStudentDetails(int studentId)
    {
        Navigation.NavigateTo($"/accounting/students/{studentId}/fees");
    }

    private void RecordPayment(int studentId)
    {
        Navigation.NavigateTo($"/accounting/students/{studentId}/payment");
    }

    private void ViewPaymentHistory(int studentId)
    {
        Navigation.NavigateTo($"/accounting/students/{studentId}/payment-history");
    }

    private async Task SendReminder(int studentId)
    {
        try
        {
            var success = await ApiService.SendPaymentReminderAsync(studentId);
            if (success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "تم إرسال التذكير بنجاح");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "حدث خطأ أثناء إرسال التذكير");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في إرسال التذكير: {ex.Message}");
        }
    }

    private async Task SendPaymentReminders()
    {
        var overdueStudents = filteredStudentFees.Where(s => s.PaymentStatus == "overdue").ToList();
        if (!overdueStudents.Any())
        {
            await JSRuntime.InvokeVoidAsync("alert", "لا يوجد طلاب متأخرين في الدفع");
            return;
        }

        if (await JSRuntime.InvokeAsync<bool>("confirm", $"هل تريد إرسال تذكيرات لـ {overdueStudents.Count} طالب متأخر في الدفع؟"))
        {
            try
            {
                var success = await ApiService.SendBulkPaymentRemindersAsync(overdueStudents.Select(s => s.StudentId).ToList());
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "تم إرسال التذكيرات بنجاح");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في إرسال التذكيرات: {ex.Message}");
            }
        }
    }

    private async Task CollectBulkPayments()
    {
        if (!selectedStudents.Any())
        {
            await JSRuntime.InvokeVoidAsync("alert", "يرجى اختيار الطلاب أولاً");
            return;
        }

        Navigation.NavigateTo($"/accounting/bulk-payment?students={string.Join(",", selectedStudents)}");
    }

    private async Task ExportReport()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة تصدير التقرير ستكون متاحة قريباً");
    }

    private void SelectStudent(int studentId, ChangeEventArgs e)
    {
        if ((bool)e.Value!)
        {
            selectedStudents.Add(studentId);
        }
        else
        {
            selectedStudents.Remove(studentId);
        }
    }

    private void SelectAllStudents(ChangeEventArgs e)
    {
        if ((bool)e.Value!)
        {
            selectedStudents = filteredStudentFees.Select(s => s.StudentId).ToHashSet();
        }
        else
        {
            selectedStudents.Clear();
        }
        StateHasChanged();
    }

    private string GetRowClass(string paymentStatus)
    {
        return paymentStatus switch
        {
            "overdue" => "table-danger",
            "partial" => "table-warning",
            "paid" => "table-success",
            _ => ""
        };
    }

    private string GetPaymentStatusBadge(string status)
    {
        return status switch
        {
            "paid" => "bg-success",
            "partial" => "bg-warning",
            "unpaid" => "bg-secondary",
            "overdue" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetPaymentStatusText(string status)
    {
        return status switch
        {
            "paid" => "مدفوع بالكامل",
            "partial" => "مدفوع جزئياً",
            "unpaid" => "غير مدفوع",
            "overdue" => "متأخر",
            _ => "غير محدد"
        };
    }

    private string GetAmountClass(decimal amount)
    {
        if (amount > 0)
            return "text-danger";
        else
            return "text-success";
    }

    private int GetOverdueDays(DateTime? lastPaymentDate)
    {
        if (!lastPaymentDate.HasValue)
            return (DateTime.Now - DateTime.Now.AddMonths(-1)).Days; // Assume 1 month overdue if no payment

        return Math.Max(0, (DateTime.Now - lastPaymentDate.Value).Days - 30); // 30 days grace period
    }
}
