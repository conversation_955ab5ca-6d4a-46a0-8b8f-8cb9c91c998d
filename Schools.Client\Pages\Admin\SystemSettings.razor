@page "/admin/system-settings"
@using Schools.Shared.DTOs
@using Schools.Client.Services
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        إعدادات النظام
                    </h4>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- General Settings -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-school me-2"></i>
                                        الإعدادات العامة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">اسم المدرسة</label>
                                        <input type="text" class="form-control" @bind="schoolName" placeholder="أدخل اسم المدرسة" />
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">عنوان المدرسة</label>
                                        <textarea class="form-control" rows="3" @bind="schoolAddress" placeholder="أدخل عنوان المدرسة"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" @bind="schoolPhone" placeholder="أدخل رقم الهاتف" />
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" @bind="schoolEmail" placeholder="أدخل البريد الإلكتروني" />
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الموقع الإلكتروني</label>
                                        <input type="url" class="form-control" @bind="schoolWebsite" placeholder="أدخل الموقع الإلكتروني" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Academic Settings -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-graduation-cap me-2"></i>
                                        الإعدادات الأكاديمية
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">الحد الأدنى للنجاح (%)</label>
                                        <input type="number" class="form-control" @bind="passingGrade" min="0" max="100" />
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الحد الأقصى للغياب المسموح</label>
                                        <input type="number" class="form-control" @bind="maxAbsences" min="0" />
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">مدة الحصة (دقيقة)</label>
                                        <input type="number" class="form-control" @bind="classDuration" min="30" max="120" />
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">عدد الحصص اليومية</label>
                                        <input type="number" class="form-control" @bind="dailyPeriods" min="4" max="10" />
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">أيام الدراسة في الأسبوع</label>
                                        <select class="form-select" @bind="schoolDays">
                                            <option value="5">5 أيام (الأحد - الخميس)</option>
                                            <option value="6">6 أيام (الأحد - الجمعة)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notification Settings -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-bell me-2"></i>
                                        إعدادات الإشعارات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" @bind="emailNotifications" id="emailNotifications">
                                        <label class="form-check-label" for="emailNotifications">
                                            إشعارات البريد الإلكتروني
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" @bind="smsNotifications" id="smsNotifications">
                                        <label class="form-check-label" for="smsNotifications">
                                            إشعارات الرسائل النصية
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" @bind="attendanceNotifications" id="attendanceNotifications">
                                        <label class="form-check-label" for="attendanceNotifications">
                                            إشعارات الحضور والغياب
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" @bind="gradeNotifications" id="gradeNotifications">
                                        <label class="form-check-label" for="gradeNotifications">
                                            إشعارات الدرجات
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Security Settings -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-shield-alt me-2"></i>
                                        إعدادات الأمان
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">الحد الأدنى لطول كلمة المرور</label>
                                        <input type="number" class="form-control" @bind="minPasswordLength" min="6" max="20" />
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" @bind="requirePasswordComplexity" id="requirePasswordComplexity">
                                        <label class="form-check-label" for="requirePasswordComplexity">
                                            تطلب تعقيد كلمة المرور
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" @bind="enableTwoFactor" id="enableTwoFactor">
                                        <label class="form-check-label" for="enableTwoFactor">
                                            تفعيل المصادقة الثنائية
                                        </label>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">مدة انتهاء الجلسة (دقيقة)</label>
                                        <input type="number" class="form-control" @bind="sessionTimeout" min="15" max="480" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Information -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        معلومات النظام
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="text-center p-3 border rounded">
                                                <i class="fas fa-code fa-2x text-primary mb-2"></i>
                                                <h6>إصدار النظام</h6>
                                                <p class="mb-0">v2.1.0</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center p-3 border rounded">
                                                <i class="fas fa-calendar fa-2x text-success mb-2"></i>
                                                <h6>تاريخ آخر تحديث</h6>
                                                <p class="mb-0">@DateTime.Now.ToString("yyyy-MM-dd")</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center p-3 border rounded">
                                                <i class="fas fa-users fa-2x text-info mb-2"></i>
                                                <h6>إجمالي المستخدمين</h6>
                                                <p class="mb-0">@totalUsers</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center p-3 border rounded">
                                                <i class="fas fa-database fa-2x text-warning mb-2"></i>
                                                <h6>حالة قاعدة البيانات</h6>
                                                <p class="mb-0 text-success">متصلة</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12 text-center">
                            <button class="btn btn-success btn-lg me-3" @onclick="SaveSettings" disabled="@isSaving">
                                @if (isSaving)
                                {
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                }
                                <i class="fas fa-save me-2"></i>
                                حفظ الإعدادات
                            </button>
                            <button class="btn btn-secondary btn-lg me-3" @onclick="ResetSettings">
                                <i class="fas fa-undo me-2"></i>
                                إعادة تعيين
                            </button>
                            <button class="btn btn-info btn-lg" @onclick="ExportSettings">
                                <i class="fas fa-download me-2"></i>
                                تصدير الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private bool isSaving = false;
    private int totalUsers = 0;

    // General Settings
    private string schoolName = "مدرسة المستقبل";
    private string schoolAddress = "الرياض، المملكة العربية السعودية";
    private string schoolPhone = "+966-11-1234567";
    private string schoolEmail = "<EMAIL>";
    private string schoolWebsite = "https://www.futureschool.edu.sa";

    // Academic Settings
    private int passingGrade = 60;
    private int maxAbsences = 15;
    private int classDuration = 45;
    private int dailyPeriods = 6;
    private int schoolDays = 5;

    // Notification Settings
    private bool emailNotifications = true;
    private bool smsNotifications = false;
    private bool attendanceNotifications = true;
    private bool gradeNotifications = true;

    // Security Settings
    private int minPasswordLength = 8;
    private bool requirePasswordComplexity = true;
    private bool enableTwoFactor = false;
    private int sessionTimeout = 120;

    protected override async Task OnInitializedAsync()
    {
        await LoadSystemInfo();
        await LoadSettings();
    }

    private async Task LoadSystemInfo()
    {
        try
        {
            var users = await ApiService.GetUsersAsync();
            totalUsers = users.Count();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل معلومات النظام: {ex.Message}");
        }
    }

    private async Task LoadSettings()
    {
        try
        {
            // Load settings from API or local storage
            // This is a mock implementation
            await Task.Delay(100);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الإعدادات: {ex.Message}");
        }
    }

    private async Task SaveSettings()
    {
        try
        {
            isSaving = true;

            // Validate settings
            if (string.IsNullOrWhiteSpace(schoolName))
            {
                await JSRuntime.InvokeVoidAsync("alert", "يجب إدخال اسم المدرسة");
                return;
            }

            if (passingGrade < 0 || passingGrade > 100)
            {
                await JSRuntime.InvokeVoidAsync("alert", "الحد الأدنى للنجاح يجب أن يكون بين 0 و 100");
                return;
            }

            if (minPasswordLength < 6 || minPasswordLength > 20)
            {
                await JSRuntime.InvokeVoidAsync("alert", "طول كلمة المرور يجب أن يكون بين 6 و 20 حرف");
                return;
            }

            // Save settings to API or local storage
            await Task.Delay(1000); // Simulate API call

            await JSRuntime.InvokeVoidAsync("alert", "تم حفظ الإعدادات بنجاح");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ الإعدادات: {ex.Message}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task ResetSettings()
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من إعادة تعيين جميع الإعدادات؟");
        if (confirmed)
        {
            // Reset to default values
            schoolName = "مدرسة المستقبل";
            schoolAddress = "الرياض، المملكة العربية السعودية";
            schoolPhone = "+966-11-1234567";
            schoolEmail = "<EMAIL>";
            schoolWebsite = "https://www.futureschool.edu.sa";

            passingGrade = 60;
            maxAbsences = 15;
            classDuration = 45;
            dailyPeriods = 6;
            schoolDays = 5;

            emailNotifications = true;
            smsNotifications = false;
            attendanceNotifications = true;
            gradeNotifications = true;

            minPasswordLength = 8;
            requirePasswordComplexity = true;
            enableTwoFactor = false;
            sessionTimeout = 120;

            StateHasChanged();
            await JSRuntime.InvokeVoidAsync("alert", "تم إعادة تعيين الإعدادات بنجاح");
        }
    }

    private async Task ExportSettings()
    {
        try
        {
            var settings = new
            {
                General = new
                {
                    SchoolName = schoolName,
                    SchoolAddress = schoolAddress,
                    SchoolPhone = schoolPhone,
                    SchoolEmail = schoolEmail,
                    SchoolWebsite = schoolWebsite
                },
                Academic = new
                {
                    PassingGrade = passingGrade,
                    MaxAbsences = maxAbsences,
                    ClassDuration = classDuration,
                    DailyPeriods = dailyPeriods,
                    SchoolDays = schoolDays
                },
                Notifications = new
                {
                    EmailNotifications = emailNotifications,
                    SmsNotifications = smsNotifications,
                    AttendanceNotifications = attendanceNotifications,
                    GradeNotifications = gradeNotifications
                },
                Security = new
                {
                    MinPasswordLength = minPasswordLength,
                    RequirePasswordComplexity = requirePasswordComplexity,
                    EnableTwoFactor = enableTwoFactor,
                    SessionTimeout = sessionTimeout
                }
            };

            var json = System.Text.Json.JsonSerializer.Serialize(settings, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            await JSRuntime.InvokeVoidAsync("downloadFile", "school-settings.json", json);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تصدير الإعدادات: {ex.Message}");
        }
    }
}
