@page "/teacher/question-bank"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Teacher")]

<PageTitle>بنك الأسئلة - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-database text-primary me-2"></i>
                        بنك الأسئلة
                    </h2>
                    <p class="text-muted mb-0">إدارة مجموعة شاملة من الأسئلة لجميع المواد والصفوف</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary" @onclick="ShowAddQuestionModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة سؤال جديد
                    </button>
                    <button class="btn btn-outline-success" @onclick="ImportQuestions">
                        <i class="fas fa-upload me-2"></i>
                        استيراد أسئلة
                    </button>
                    <button class="btn btn-outline-info" @onclick="ExportQuestions">
                        <i class="fas fa-download me-2"></i>
                        تصدير الأسئلة
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل بنك الأسئلة...</p>
        </div>
    }
    else
    {
        <!-- Filters and Search -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">المادة</label>
                                <select @bind="selectedSubjectId" @bind:after="FilterQuestions" class="form-select">
                                    <option value="">جميع المواد</option>
                                    @if (subjects != null)
                                    {
                                        @foreach (var subject in subjects)
                                        {
                                            <option value="@subject.Id">@subject.Name</option>
                                        }
                                    }
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الصف</label>
                                <select @bind="selectedClassId" @bind:after="FilterQuestions" class="form-select">
                                    <option value="">جميع الصفوف</option>
                                    @if (classes != null)
                                    {
                                        @foreach (var classItem in classes)
                                        {
                                            <option value="@classItem.Id">@classItem.Name</option>
                                        }
                                    }
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">نوع السؤال</label>
                                <select @bind="selectedQuestionType" @bind:after="FilterQuestions" class="form-select">
                                    <option value="">جميع الأنواع</option>
                                    <option value="@QuestionType.MultipleChoice">اختيار من متعدد</option>
                                    <option value="@QuestionType.TrueFalse">صح/خطأ</option>
                                    <option value="@QuestionType.ShortAnswer">إجابة قصيرة</option>
                                    <option value="@QuestionType.Essay">مقال</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">مستوى الصعوبة</label>
                                <select @bind="selectedDifficulty" @bind:after="FilterQuestions" class="form-select">
                                    <option value="">جميع المستويات</option>
                                    <option value="Easy">سهل</option>
                                    <option value="Medium">متوسط</option>
                                    <option value="Hard">صعب</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">البحث في النص</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" placeholder="ابحث في نص الأسئلة..."
                                           @bind="searchTerm" @bind:after="FilterQuestions" />
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الكلمات المفتاحية</label>
                                <input type="text" class="form-control" placeholder="كلمات مفتاحية..."
                                       @bind="keywordFilter" @bind:after="FilterQuestions" />
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">ترتيب حسب</label>
                                <select @bind="sortBy" @bind:after="SortQuestions" class="form-select">
                                    <option value="created">تاريخ الإنشاء</option>
                                    <option value="subject">المادة</option>
                                    <option value="difficulty">الصعوبة</option>
                                    <option value="usage">مرات الاستخدام</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-primary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-question-circle fa-lg"></i>
                        </div>
                        <h4 class="text-primary mb-1">@filteredQuestions.Count</h4>
                        <p class="text-muted mb-0">إجمالي الأسئلة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-success text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-list fa-lg"></i>
                        </div>
                        <h4 class="text-success mb-1">@GetQuestionTypeCount(QuestionType.MultipleChoice)</h4>
                        <p class="text-muted mb-0">اختيار متعدد</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-info text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-check-circle fa-lg"></i>
                        </div>
                        <h4 class="text-info mb-1">@GetQuestionTypeCount(QuestionType.TrueFalse)</h4>
                        <p class="text-muted mb-0">صح/خطأ</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-warning text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-edit fa-lg"></i>
                        </div>
                        <h4 class="text-warning mb-1">@(GetQuestionTypeCount(QuestionType.ShortAnswer) + GetQuestionTypeCount(QuestionType.Essay))</h4>
                        <p class="text-muted mb-0">أسئلة مفتوحة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Questions Grid -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2 text-primary"></i>
                                    بنك الأسئلة (@filteredQuestions.Count سؤال)
                                </h5>
                            </div>
                            <div class="col-auto">
                                <div class="btn-group">
                                    <button class="btn btn-sm btn-outline-primary @(viewMode == "grid" ? "active" : "")" @onclick="@(() => viewMode = "grid")">
                                        <i class="fas fa-th me-1"></i>
                                        شبكة
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary @(viewMode == "list" ? "active" : "")" @onclick="@(() => viewMode = "list")">
                                        <i class="fas fa-list me-1"></i>
                                        قائمة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (filteredQuestions?.Any() == true)
                        {
                            @if (viewMode == "grid")
                            {
                                <div class="row g-3">
                                    @foreach (var question in filteredQuestions.Skip((currentPage - 1) * pageSize).Take(pageSize))
                                    {
                                        <div class="col-md-6 col-lg-4">
                                            <div class="question-card card h-100 border">
                                                <div class="card-header bg-light">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <span class="badge @GetQuestionTypeBadgeClass(question.Type)">@GetQuestionTypeText(question.Type)</span>
                                                        <span class="badge @GetDifficultyBadgeClass(question.Difficulty)">@GetDifficultyText(question.Difficulty)</span>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <h6 class="card-title">@TruncateText(question.QuestionText, 100)</h6>
                                                    <div class="question-meta">
                                                        <small class="text-muted d-block">
                                                            <i class="fas fa-book me-1"></i>
                                                            @question.SubjectName
                                                        </small>
                                                        <small class="text-muted d-block">
                                                            <i class="fas fa-users me-1"></i>
                                                            @question.ClassName
                                                        </small>
                                                        <small class="text-muted d-block">
                                                            <i class="fas fa-star me-1"></i>
                                                            @question.Marks درجة
                                                        </small>
                                                        @if (question.Keywords?.Any() == true)
                                                        {
                                                            <div class="mt-2">
                                                                @foreach (var keyword in question.Keywords.Take(3))
                                                                {
                                                                    <span class="badge bg-secondary me-1">@keyword</span>
                                                                }
                                                            </div>
                                                        }
                                                    </div>
                                                </div>
                                                <div class="card-footer bg-white border-top">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <small class="text-muted">
                                                            <i class="fas fa-chart-line me-1"></i>
                                                            استخدم @question.UsageCount مرة
                                                        </small>
                                                        <div class="btn-group">
                                                            <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewQuestion(question)" title="عرض">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-secondary" @onclick="() => EditQuestion(question)" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-success" @onclick="() => DuplicateQuestion(question)" title="نسخ">
                                                                <i class="fas fa-copy"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteQuestion(question.Id)" title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            }
                            else
                            {
                                <div class="questions-list">
                                    @foreach (var question in filteredQuestions.Skip((currentPage - 1) * pageSize).Take(pageSize))
                                    {
                                        <div class="question-list-item card mb-3 border">
                                            <div class="card-body">
                                                <div class="row align-items-center">
                                                    <div class="col-md-8">
                                                        <div class="d-flex align-items-start">
                                                            <div class="question-type-icon me-3">
                                                                <i class="fas @GetQuestionTypeIcon(question.Type) fa-2x @GetQuestionTypeColor(question.Type)"></i>
                                                            </div>
                                                            <div class="flex-grow-1">
                                                                <h6 class="mb-2">@TruncateText(question.QuestionText, 150)</h6>
                                                                <div class="question-meta">
                                                                    <span class="badge @GetQuestionTypeBadgeClass(question.Type) me-2">@GetQuestionTypeText(question.Type)</span>
                                                                    <span class="badge @GetDifficultyBadgeClass(question.Difficulty) me-2">@GetDifficultyText(question.Difficulty)</span>
                                                                    <span class="badge bg-info me-2">@question.SubjectName</span>
                                                                    <span class="badge bg-secondary me-2">@question.ClassName</span>
                                                                    <span class="badge bg-success">@question.Marks درجة</span>
                                                                </div>
                                                                @if (question.Keywords?.Any() == true)
                                                                {
                                                                    <div class="mt-2">
                                                                        <small class="text-muted">الكلمات المفتاحية: </small>
                                                                        @foreach (var keyword in question.Keywords.Take(5))
                                                                        {
                                                                            <span class="badge bg-light text-dark me-1">@keyword</span>
                                                                        }
                                                                    </div>
                                                                }
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4 text-end">
                                                        <div class="question-stats mb-2">
                                                            <small class="text-muted d-block">
                                                                <i class="fas fa-chart-line me-1"></i>
                                                                استخدم @question.UsageCount مرة
                                                            </small>
                                                            <small class="text-muted d-block">
                                                                <i class="fas fa-calendar me-1"></i>
                                                                @question.CreatedDate.ToString("dd/MM/yyyy")
                                                            </small>
                                                        </div>
                                                        <div class="btn-group">
                                                            <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewQuestion(question)" title="عرض">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-secondary" @onclick="() => EditQuestion(question)" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-success" @onclick="() => DuplicateQuestion(question)" title="نسخ">
                                                                <i class="fas fa-copy"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteQuestion(question.Id)" title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            }

                            <!-- Pagination -->
                            @if (totalPages > 1)
                            {
                                <nav class="mt-4">
                                    <ul class="pagination justify-content-center">
                                        <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                            <button class="page-link" @onclick="() => ChangePage(currentPage - 1)">السابق</button>
                                        </li>
                                        @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                                        {
                                            <li class="page-item @(i == currentPage ? "active" : "")">
                                                <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                                            </li>
                                        }
                                        <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                                            <button class="page-link" @onclick="() => ChangePage(currentPage + 1)">التالي</button>
                                        </li>
                                    </ul>
                                </nav>
                            }
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-database fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد أسئلة</h5>
                                <p class="text-muted">ابدأ ببناء بنك الأسئلة الخاص بك</p>
                                <button class="btn btn-primary" @onclick="ShowAddQuestionModal">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة السؤال الأول
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .avatar-md {
        width: 48px;
        height: 48px;
    }

    .question-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .question-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }

    .question-list-item {
        transition: all 0.3s ease;
    }

    .question-list-item:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
    }

    .question-type-icon {
        width: 60px;
        text-align: center;
    }

    .question-meta .badge {
        font-size: 0.75rem;
    }

    .btn-group .btn {
        border-radius: 0.25rem !important;
        margin: 0 1px;
    }
</style>

@code {
    private List<QuestionBankDto> allQuestions = new();
    private List<QuestionBankDto> filteredQuestions = new();
    private List<SubjectDto>? subjects;
    private List<ClassDto>? classes;

    private bool isLoading = true;
    private string selectedSubjectId = "";
    private string selectedClassId = "";
    private string selectedQuestionType = "";
    private string selectedDifficulty = "";
    private string searchTerm = "";
    private string keywordFilter = "";
    private string sortBy = "created";
    private string viewMode = "grid";

    private int currentPage = 1;
    private int pageSize = 12;
    private int totalPages = 1;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            await Task.WhenAll(
                LoadQuestions(),
                LoadSubjects(),
                LoadClasses()
            );

            FilterQuestions();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadQuestions()
    {
        try
        {
            allQuestions = await ApiService.GetQuestionBankAsync() ?? new List<QuestionBankDto>();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading questions: {ex.Message}");
        }
    }

    private async Task LoadSubjects()
    {
        try
        {
            var subjectsList = await ApiService.GetSubjectsAsync();
            subjects = subjectsList?.ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading subjects: {ex.Message}");
        }
    }

    private async Task LoadClasses()
    {
        try
        {
            var classesList = await ApiService.GetClassesAsync();
            classes = classesList?.ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading classes: {ex.Message}");
        }
    }

    private void FilterQuestions()
    {
        filteredQuestions = allQuestions.Where(q =>
            (string.IsNullOrEmpty(selectedSubjectId) || q.SubjectId.ToString() == selectedSubjectId) &&
            (string.IsNullOrEmpty(selectedClassId) || q.ClassId.ToString() == selectedClassId) &&
            (string.IsNullOrEmpty(selectedQuestionType) || q.Type.ToString() == selectedQuestionType) &&
            (string.IsNullOrEmpty(selectedDifficulty) || q.Difficulty == selectedDifficulty) &&
            (string.IsNullOrEmpty(searchTerm) || q.QuestionText.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) &&
            (string.IsNullOrEmpty(keywordFilter) || (q.Keywords?.Any(k => k.Contains(keywordFilter, StringComparison.OrdinalIgnoreCase)) == true))
        ).ToList();

        SortQuestions();
        UpdatePagination();
        StateHasChanged();
    }

    private void SortQuestions()
    {
        filteredQuestions = sortBy switch
        {
            "created" => filteredQuestions.OrderByDescending(q => q.CreatedDate).ToList(),
            "subject" => filteredQuestions.OrderBy(q => q.SubjectName).ToList(),
            "difficulty" => filteredQuestions.OrderBy(q => q.Difficulty).ToList(),
            "usage" => filteredQuestions.OrderByDescending(q => q.UsageCount).ToList(),
            _ => filteredQuestions.OrderByDescending(q => q.CreatedDate).ToList()
        };
    }

    private void UpdatePagination()
    {
        totalPages = (int)Math.Ceiling((double)filteredQuestions.Count / pageSize);
        if (currentPage > totalPages && totalPages > 0)
        {
            currentPage = totalPages;
        }
        else if (currentPage < 1)
        {
            currentPage = 1;
        }
    }

    private void ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            StateHasChanged();
        }
    }

    private int GetQuestionTypeCount(QuestionType type)
    {
        return filteredQuestions.Count(q => q.Type == type);
    }

    private void ShowAddQuestionModal()
    {
        Navigation.NavigateTo("/teacher/question-bank/add");
    }

    private void ViewQuestion(QuestionBankDto question)
    {
        Navigation.NavigateTo($"/teacher/question-bank/view/{question.Id}");
    }

    private void EditQuestion(QuestionBankDto question)
    {
        Navigation.NavigateTo($"/teacher/question-bank/edit/{question.Id}");
    }

    private async Task DuplicateQuestion(QuestionBankDto question)
    {
        try
        {
            var success = await ApiService.DuplicateQuestionAsync(question.Id);
            if (success)
            {
                await LoadQuestions();
                FilterQuestions();
                await JSRuntime.InvokeVoidAsync("alert", "تم نسخ السؤال بنجاح");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في نسخ السؤال: {ex.Message}");
        }
    }

    private async Task DeleteQuestion(int questionId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا السؤال؟"))
        {
            try
            {
                var success = await ApiService.DeleteQuestionFromBankAsync(questionId);
                if (success)
                {
                    allQuestions.RemoveAll(q => q.Id == questionId);
                    FilterQuestions();
                    await JSRuntime.InvokeVoidAsync("alert", "تم حذف السؤال بنجاح");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف السؤال: {ex.Message}");
            }
        }
    }

    private async Task ImportQuestions()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة استيراد الأسئلة ستكون متاحة قريباً");
    }

    private async Task ExportQuestions()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة تصدير الأسئلة ستكون متاحة قريباً");
    }

    private string TruncateText(string text, int maxLength)
    {
        if (string.IsNullOrEmpty(text) || text.Length <= maxLength)
            return text;

        return text.Substring(0, maxLength) + "...";
    }

    private string GetQuestionTypeBadgeClass(QuestionType type)
    {
        return type switch
        {
            QuestionType.MultipleChoice => "bg-primary",
            QuestionType.TrueFalse => "bg-success",
            QuestionType.ShortAnswer => "bg-info",
            QuestionType.Essay => "bg-warning",
            _ => "bg-secondary"
        };
    }

    private string GetQuestionTypeText(QuestionType type)
    {
        return type switch
        {
            QuestionType.MultipleChoice => "اختيار متعدد",
            QuestionType.TrueFalse => "صح/خطأ",
            QuestionType.ShortAnswer => "إجابة قصيرة",
            QuestionType.Essay => "مقال",
            _ => "غير محدد"
        };
    }

    private string GetQuestionTypeIcon(QuestionType type)
    {
        return type switch
        {
            QuestionType.MultipleChoice => "fa-list-ul",
            QuestionType.TrueFalse => "fa-check-circle",
            QuestionType.ShortAnswer => "fa-edit",
            QuestionType.Essay => "fa-file-alt",
            _ => "fa-question"
        };
    }

    private string GetQuestionTypeColor(QuestionType type)
    {
        return type switch
        {
            QuestionType.MultipleChoice => "text-primary",
            QuestionType.TrueFalse => "text-success",
            QuestionType.ShortAnswer => "text-info",
            QuestionType.Essay => "text-warning",
            _ => "text-secondary"
        };
    }

    private string GetDifficultyBadgeClass(string difficulty)
    {
        return difficulty switch
        {
            "Easy" => "bg-success",
            "Medium" => "bg-warning",
            "Hard" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetDifficultyText(string difficulty)
    {
        return difficulty switch
        {
            "Easy" => "سهل",
            "Medium" => "متوسط",
            "Hard" => "صعب",
            _ => "غير محدد"
        };
    }
}
