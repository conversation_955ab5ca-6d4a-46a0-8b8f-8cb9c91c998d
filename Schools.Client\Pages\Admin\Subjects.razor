@page "/admin/subjects"
@using Schools.Shared.Models
@using Schools.Shared.DTOs
@using Schools.Client.Services
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@attribute [Authorize(Roles = "Admin")]

<PageTitle>إدارة المواد الدراسية</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-book me-2"></i>
                        إدارة المواد الدراسية
                    </h4>
                    <button class="btn btn-light" @onclick="ShowAddModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مادة دراسية
                    </button>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-4">
                            <div class="spinner-border text-info" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else if (subjects?.Any() == true)
                    {
                        <div class="row">
                            @foreach (var subject in subjects)
                            {
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card h-100 border-start border-4 border-info">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <div>
                                                    <h5 class="card-title text-info mb-1">@subject.Name</h5>
                                                    <span class="badge bg-secondary">@subject.Code</span>
                                                </div>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="#" @onclick="() => EditSubject(subject)">
                                                            <i class="fas fa-edit me-2"></i>تعديل
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="#" @onclick="() => ViewAssignments(subject.Id)">
                                                            <i class="fas fa-users me-2"></i>المعلمون المكلفون
                                                        </a></li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><a class="dropdown-item text-danger" href="#" @onclick="() => DeleteSubject(subject.Id)">
                                                            <i class="fas fa-trash me-2"></i>حذف
                                                        </a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <small class="text-muted">الساعات المعتمدة:</small>
                                                <span class="badge bg-info ms-2">@subject.CreditHours ساعة</span>
                                            </div>
                                            @if (!string.IsNullOrEmpty(subject.Description))
                                            {
                                                <p class="card-text small text-muted">@subject.Description</p>
                                            }
                                            <small class="text-muted">
                                                تم الإنشاء: @subject.CreatedAt.ToString("dd/MM/yyyy")
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-book fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد مواد دراسية</h5>
                            <p class="text-muted">ابدأ بإضافة المواد الدراسية</p>
                            <button class="btn btn-info" @onclick="ShowAddModal">
                                <i class="fas fa-plus me-2"></i>
                                إضافة مادة دراسية
                            </button>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Modal -->
@if (showModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        @if (editingSubject == null)
                        {
                            <text>إضافة مادة دراسية جديدة</text>
                        }
                        else
                        {
                            <text>تعديل المادة الدراسية</text>
                        }
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseModal"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="subjectModel" OnValidSubmit="SaveSubject">
                        <DataAnnotationsValidator />
                        <div class="mb-3">
                            <label class="form-label">اسم المادة</label>
                            <InputText class="form-control" @bind-Value="subjectModel.Name" placeholder="مثال: الرياضيات" />
                            <ValidationMessage For="() => subjectModel.Name" class="text-danger" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كود المادة</label>
                            <InputText class="form-control" @bind-Value="subjectModel.Code" placeholder="مثال: MATH" />
                            <ValidationMessage For="() => subjectModel.Code" class="text-danger" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الساعات المعتمدة</label>
                            <InputNumber class="form-control" @bind-Value="subjectModel.CreditHours" />
                            <ValidationMessage For="() => subjectModel.CreditHours" class="text-danger" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الوصف (اختياري)</label>
                            <InputTextArea class="form-control" @bind-Value="subjectModel.Description" rows="3" placeholder="وصف المادة الدراسية..." />
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" @onclick="CloseModal">إلغاء</button>
                            <button type="submit" class="btn btn-info" disabled="@isSaving">
                                @if (isSaving)
                                {
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                }
                                حفظ
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<Subject>? subjects;
    private bool isLoading = true;
    private bool showModal = false;
    private bool isSaving = false;
    private Subject? editingSubject;
    private CreateSubjectDto subjectModel = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadSubjects();
    }

    private async Task LoadSubjects()
    {
        try
        {
            isLoading = true;
            var subjectDtos = await ApiService.GetSubjectsAsync();
            subjects = subjectDtos.Select(dto => new Subject
            {
                Id = dto.Id,
                Name = dto.Name,
                Code = dto.Code,
                Description = dto.Description,
                CreditHours = dto.CreditHours,
                CreatedAt = DateTime.Now
            }).ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void ShowAddModal()
    {
        editingSubject = null;
        subjectModel = new CreateSubjectDto();
        showModal = true;
    }

    private void EditSubject(Subject subject)
    {
        editingSubject = subject;
        subjectModel = new CreateSubjectDto
        {
            Name = subject.Name,
            Code = subject.Code,
            CreditHours = subject.CreditHours,
            Description = subject.Description
        };
        showModal = true;
    }

    private void CloseModal()
    {
        showModal = false;
        editingSubject = null;
        subjectModel = new();
    }

    private async Task SaveSubject()
    {
        try
        {
            isSaving = true;

            if (editingSubject == null)
            {
                await ApiService.CreateSubjectAsync(subjectModel);
                await JSRuntime.InvokeVoidAsync("alert", "تم إضافة المادة الدراسية بنجاح");
            }
            else
            {
                await ApiService.UpdateSubjectAsync(editingSubject.Id, subjectModel);
                await JSRuntime.InvokeVoidAsync("alert", "تم تحديث المادة الدراسية بنجاح");
            }

            CloseModal();
            await LoadSubjects();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ البيانات: {ex.Message}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task DeleteSubject(int subjectId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذه المادة الدراسية؟"))
        {
            try
            {
                await ApiService.DeleteSubjectAsync(subjectId);
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف المادة الدراسية بنجاح");
                await LoadSubjects();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف المادة الدراسية: {ex.Message}");
            }
        }
    }

    private async Task ViewAssignments(int subjectId)
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم إضافة هذه الميزة قريباً");
    }
}
