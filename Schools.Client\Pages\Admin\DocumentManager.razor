@page "/admin/documents"
@using Schools.Shared.DTOs
@using Schools.Client.Services
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-folder-open me-2"></i>
                        إدارة الملفات والمستندات
                    </h4>
                </div>

                <div class="card-body">
                    <!-- Upload Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-2 border-dashed border-primary">
                                <div class="card-body text-center py-5">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                    <h5>رفع ملفات جديدة</h5>
                                    <p class="text-muted">اسحب الملفات هنا أو انقر للاختيار</p>
                                    <input type="file" id="fileInput" multiple class="d-none" @onchange="HandleFileSelection" />
                                    <button class="btn btn-primary" @onclick="TriggerFileInput">
                                        <i class="fas fa-plus me-2"></i>
                                        اختيار الملفات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters and Search -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <label class="form-label">نوع الملف</label>
                            <select class="form-select" @bind="selectedFileType" @bind:after="FilterDocuments">
                                <option value="">جميع الأنواع</option>
                                <option value="pdf">PDF</option>
                                <option value="doc">Word</option>
                                <option value="xls">Excel</option>
                                <option value="img">صور</option>
                                <option value="video">فيديو</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الفئة</label>
                            <select class="form-select" @bind="selectedCategory" @bind:after="FilterDocuments">
                                <option value="">جميع الفئات</option>
                                <option value="academic">أكاديمي</option>
                                <option value="administrative">إداري</option>
                                <option value="financial">مالي</option>
                                <option value="reports">تقارير</option>
                                <option value="forms">نماذج</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">تاريخ الرفع</label>
                            <input type="date" class="form-control" @bind="selectedDate" @bind:after="FilterDocuments" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">البحث</label>
                            <input type="text" class="form-control" @bind="searchTerm" @bind:after="FilterDocuments" placeholder="ابحث في الملفات..." />
                        </div>
                    </div>

                    <!-- Storage Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-file fa-2x mb-2"></i>
                                    <h4>@totalFiles</h4>
                                    <p class="mb-0">إجمالي الملفات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-hdd fa-2x mb-2"></i>
                                    <h4>@FormatFileSize(totalSize)</h4>
                                    <p class="mb-0">المساحة المستخدمة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-download fa-2x mb-2"></i>
                                    <h4>@totalDownloads</h4>
                                    <p class="mb-0">إجمالي التحميلات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-eye fa-2x mb-2"></i>
                                    <h4>@totalViews</h4>
                                    <p class="mb-0">إجمالي المشاهدات</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Storage Usage Chart -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-pie me-2"></i>
                                        استخدام المساحة حسب النوع
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="storageUsageChart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        معلومات التخزين
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>المساحة المستخدمة</span>
                                            <strong>@FormatFileSize(usedSpace)</strong>
                                        </div>
                                        <div class="progress mt-1">
                                            <div class="progress-bar bg-primary" style="width: @storagePercentage%"></div>
                                        </div>
                                        <small class="text-muted">@storagePercentage.ToString("F1")% من @FormatFileSize(totalSpace)</small>
                                    </div>
                                    <div class="mb-3">
                                        <h6>أكثر الأنواع استخداماً:</h6>
                                        @foreach (var type in topFileTypes.Take(3))
                                        {
                                            <div class="d-flex justify-content-between mb-1">
                                                <span>@type.Key</span>
                                                <small>@type.Value ملف</small>
                                            </div>
                                        }
                                    </div>
                                    <button class="btn btn-outline-danger btn-sm w-100" @onclick="CleanupOldFiles">
                                        <i class="fas fa-trash me-2"></i>
                                        تنظيف الملفات القديمة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Documents List -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">
                                            <i class="fas fa-list me-2"></i>
                                            قائمة الملفات
                                        </h5>
                                        <div class="btn-group">
                                            <button class="btn btn-outline-secondary btn-sm @(viewMode == "grid" ? "active" : "")" @onclick="@(() => viewMode = "grid")">
                                                <i class="fas fa-th"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm @(viewMode == "list" ? "active" : "")" @onclick="@(() => viewMode = "list")">
                                                <i class="fas fa-list"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    @if (isLoading)
                                    {
                                        <div class="text-center py-5">
                                            <div class="spinner-border text-secondary" role="status">
                                                <span class="visually-hidden">جاري التحميل...</span>
                                            </div>
                                        </div>
                                    }
                                    else if (filteredDocuments?.Any() == true)
                                    {
                                        @if (viewMode == "grid")
                                        {
                                            <div class="row">
                                                @foreach (var doc in filteredDocuments.Take(20))
                                                {
                                                    <div class="col-md-3 mb-3">
                                                        <div class="card h-100 document-card">
                                                            <div class="card-body text-center">
                                                                <div class="mb-3">
                                                                    <i class="@GetFileIcon(doc.FileType) fa-3x @GetFileColor(doc.FileType)"></i>
                                                                </div>
                                                                <h6 class="card-title">@doc.Name</h6>
                                                                <p class="card-text small text-muted">
                                                                    @FormatFileSize(doc.Size)<br>
                                                                    @doc.UploadDate.ToString("yyyy-MM-dd")
                                                                </p>
                                                                <div class="btn-group btn-group-sm w-100">
                                                                    <button class="btn btn-outline-primary" @onclick="@(() => ViewDocument(doc))">
                                                                        <i class="fas fa-eye"></i>
                                                                    </button>
                                                                    <button class="btn btn-outline-success" @onclick="@(() => DownloadDocument(doc))">
                                                                        <i class="fas fa-download"></i>
                                                                    </button>
                                                                    <button class="btn btn-outline-danger" @onclick="@(() => DeleteDocument(doc))">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="table-responsive">
                                                <table class="table table-striped table-hover">
                                                    <thead class="table-dark">
                                                        <tr>
                                                            <th>الملف</th>
                                                            <th>النوع</th>
                                                            <th>الفئة</th>
                                                            <th>الحجم</th>
                                                            <th>تاريخ الرفع</th>
                                                            <th>المشاهدات</th>
                                                            <th>التحميلات</th>
                                                            <th>الإجراءات</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach (var doc in filteredDocuments.Take(20))
                                                        {
                                                            <tr>
                                                                <td>
                                                                    <div class="d-flex align-items-center">
                                                                        <i class="@GetFileIcon(doc.FileType) me-2 @GetFileColor(doc.FileType)"></i>
                                                                        <div>
                                                                            <strong>@doc.Name</strong>
                                                                            <br>
                                                                            <small class="text-muted">@doc.Description</small>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <span class="badge @GetTypeBadgeClass(doc.FileType)">
                                                                        @doc.FileType.ToUpper()
                                                                    </span>
                                                                </td>
                                                                <td>
                                                                    <span class="badge bg-info">@GetCategoryText(doc.Category)</span>
                                                                </td>
                                                                <td>@FormatFileSize(doc.Size)</td>
                                                                <td>@doc.UploadDate.ToString("yyyy-MM-dd HH:mm")</td>
                                                                <td>
                                                                    <span class="badge bg-secondary">@doc.ViewCount</span>
                                                                </td>
                                                                <td>
                                                                    <span class="badge bg-success">@doc.DownloadCount</span>
                                                                </td>
                                                                <td>
                                                                    <div class="btn-group btn-group-sm">
                                                                        <button class="btn btn-outline-primary" @onclick="@(() => ViewDocument(doc))">
                                                                            <i class="fas fa-eye"></i>
                                                                        </button>
                                                                        <button class="btn btn-outline-success" @onclick="@(() => DownloadDocument(doc))">
                                                                            <i class="fas fa-download"></i>
                                                                        </button>
                                                                        <button class="btn btn-outline-warning" @onclick="@(() => EditDocument(doc))">
                                                                            <i class="fas fa-edit"></i>
                                                                        </button>
                                                                        <button class="btn btn-outline-danger" @onclick="@(() => DeleteDocument(doc))">
                                                                            <i class="fas fa-trash"></i>
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                </table>
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <div class="text-center py-5">
                                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">لا توجد ملفات</h5>
                                            <p class="text-muted">لم يتم العثور على ملفات تطابق المعايير المحددة</p>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .document-card {
        transition: transform 0.2s ease-in-out;
        cursor: pointer;
    }

    .document-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .border-dashed {
        border-style: dashed !important;
    }

    .btn-group .btn.active {
        background-color: #007bff;
        color: white;
    }
</style>

@code {
    private bool isLoading = true;
    private string viewMode = "list";

    // Statistics
    private int totalFiles = 0;
    private long totalSize = 0;
    private int totalDownloads = 0;
    private int totalViews = 0;
    private long usedSpace = 0;
    private long totalSpace = 10737418240; // 10GB
    private double storagePercentage = 0;

    // Filters
    private string selectedFileType = "";
    private string selectedCategory = "";
    private DateTime? selectedDate;
    private string searchTerm = "";

    // Data
    private List<DocumentItem> documents = new();
    private List<DocumentItem> filteredDocuments = new();
    private Dictionary<string, int> topFileTypes = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadDocuments();
        await InitializeChart();
    }

    private async Task LoadDocuments()
    {
        try
        {
            isLoading = true;

            // Mock document data (in real app, this would come from API)
            documents = new List<DocumentItem>
            {
                new() { Id = 1, Name = "دليل الطالب 2024.pdf", FileType = "pdf", Category = "academic", Size = 2048576, UploadDate = DateTime.Now.AddDays(-5), ViewCount = 45, DownloadCount = 12, Description = "دليل شامل للطلاب الجدد" },
                new() { Id = 2, Name = "نموذج طلب إجازة.docx", FileType = "doc", Category = "forms", Size = 524288, UploadDate = DateTime.Now.AddDays(-3), ViewCount = 23, DownloadCount = 8, Description = "نموذج طلب إجازة للموظفين" },
                new() { Id = 3, Name = "التقرير المالي الشهري.xlsx", FileType = "xls", Category = "financial", Size = 1048576, UploadDate = DateTime.Now.AddDays(-2), ViewCount = 67, DownloadCount = 15, Description = "التقرير المالي لشهر ديسمبر" },
                new() { Id = 4, Name = "صورة المدرسة.jpg", FileType = "img", Category = "administrative", Size = 3145728, UploadDate = DateTime.Now.AddDays(-1), ViewCount = 89, DownloadCount = 5, Description = "صورة رسمية للمدرسة" },
                new() { Id = 5, Name = "فيديو تعريفي.mp4", FileType = "video", Category = "academic", Size = 52428800, UploadDate = DateTime.Now.AddHours(-6), ViewCount = 156, DownloadCount = 23, Description = "فيديو تعريفي بالمدرسة" },
                new() { Id = 6, Name = "لائحة الامتحانات.pdf", FileType = "pdf", Category = "academic", Size = 1572864, UploadDate = DateTime.Now.AddHours(-3), ViewCount = 78, DownloadCount = 19, Description = "لائحة تنظيم الامتحانات" },
                new() { Id = 7, Name = "تقرير الحضور.xlsx", FileType = "xls", Category = "reports", Size = 786432, UploadDate = DateTime.Now.AddHours(-1), ViewCount = 34, DownloadCount = 7, Description = "تقرير حضور الطلاب الشهري" }
            };

            // Calculate statistics
            totalFiles = documents.Count;
            totalSize = documents.Sum(d => d.Size);
            totalDownloads = documents.Sum(d => d.DownloadCount);
            totalViews = documents.Sum(d => d.ViewCount);
            usedSpace = totalSize;
            storagePercentage = (double)usedSpace / totalSpace * 100;

            // Calculate top file types
            topFileTypes = documents.GroupBy(d => d.FileType)
                                  .ToDictionary(g => g.Key.ToUpper(), g => g.Count())
                                  .OrderByDescending(kvp => kvp.Value)
                                  .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            FilterDocuments();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الملفات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task InitializeChart()
    {
        try
        {
            var chartData = new
            {
                labels = topFileTypes.Keys.ToArray(),
                datasets = new[]
                {
                    new
                    {
                        data = topFileTypes.Values.ToArray(),
                        backgroundColor = new[] { "#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF", "#FF9F40" }
                    }
                }
            };

            await JSRuntime.InvokeVoidAsync("createChart", "storageUsageChart", "doughnut", chartData, new
            {
                responsive = true,
                plugins = new
                {
                    legend = new { position = "bottom" }
                }
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error initializing chart: {ex.Message}");
        }
    }

    private void FilterDocuments()
    {
        filteredDocuments = documents.Where(d =>
            (string.IsNullOrEmpty(selectedFileType) || d.FileType == selectedFileType) &&
            (string.IsNullOrEmpty(selectedCategory) || d.Category == selectedCategory) &&
            (!selectedDate.HasValue || d.UploadDate.Date == selectedDate.Value.Date) &&
            (string.IsNullOrEmpty(searchTerm) || d.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) || d.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
        ).OrderByDescending(d => d.UploadDate).ToList();

        StateHasChanged();
    }

    private async Task TriggerFileInput()
    {
        await JSRuntime.InvokeVoidAsync("eval", "document.getElementById('fileInput').click()");
    }

    private async Task HandleFileSelection(ChangeEventArgs e)
    {
        await JSRuntime.InvokeVoidAsync("showNotification", "جاري رفع الملفات...", "info");
        await Task.Delay(2000);
        await JSRuntime.InvokeVoidAsync("showNotification", "تم رفع الملفات بنجاح", "success");
        await LoadDocuments();
    }

    private async Task ViewDocument(DocumentItem document)
    {
        document.ViewCount++;
        await JSRuntime.InvokeVoidAsync("showNotification", $"عرض الملف: {document.Name}", "info");
    }

    private async Task DownloadDocument(DocumentItem document)
    {
        document.DownloadCount++;
        await JSRuntime.InvokeVoidAsync("showNotification", $"تحميل الملف: {document.Name}", "success");
    }

    private async Task EditDocument(DocumentItem document)
    {
        await JSRuntime.InvokeVoidAsync("showNotification", $"تحرير الملف: {document.Name}", "info");
    }

    private async Task DeleteDocument(DocumentItem document)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"هل تريد حذف الملف: {document.Name}؟");
        if (confirmed)
        {
            documents.Remove(document);
            FilterDocuments();
            await JSRuntime.InvokeVoidAsync("showNotification", "تم حذف الملف بنجاح", "success");
        }
    }

    private async Task CleanupOldFiles()
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "هل تريد حذف الملفات القديمة (أكثر من 6 أشهر)؟");
        if (confirmed)
        {
            var oldFiles = documents.Where(d => d.UploadDate < DateTime.Now.AddMonths(-6)).ToList();
            foreach (var file in oldFiles)
            {
                documents.Remove(file);
            }
            FilterDocuments();
            await JSRuntime.InvokeVoidAsync("showNotification", $"تم حذف {oldFiles.Count} ملف قديم", "success");
        }
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    private string GetFileIcon(string fileType)
    {
        return fileType.ToLower() switch
        {
            "pdf" => "fas fa-file-pdf",
            "doc" or "docx" => "fas fa-file-word",
            "xls" or "xlsx" => "fas fa-file-excel",
            "img" or "jpg" or "png" or "gif" => "fas fa-file-image",
            "video" or "mp4" or "avi" => "fas fa-file-video",
            "audio" or "mp3" or "wav" => "fas fa-file-audio",
            _ => "fas fa-file"
        };
    }

    private string GetFileColor(string fileType)
    {
        return fileType.ToLower() switch
        {
            "pdf" => "text-danger",
            "doc" or "docx" => "text-primary",
            "xls" or "xlsx" => "text-success",
            "img" or "jpg" or "png" or "gif" => "text-warning",
            "video" or "mp4" or "avi" => "text-info",
            "audio" or "mp3" or "wav" => "text-secondary",
            _ => "text-muted"
        };
    }

    private string GetTypeBadgeClass(string fileType)
    {
        return fileType.ToLower() switch
        {
            "pdf" => "bg-danger",
            "doc" or "docx" => "bg-primary",
            "xls" or "xlsx" => "bg-success",
            "img" or "jpg" or "png" or "gif" => "bg-warning",
            "video" or "mp4" or "avi" => "bg-info",
            _ => "bg-secondary"
        };
    }

    private string GetCategoryText(string category)
    {
        return category switch
        {
            "academic" => "أكاديمي",
            "administrative" => "إداري",
            "financial" => "مالي",
            "reports" => "تقارير",
            "forms" => "نماذج",
            _ => category
        };
    }

    public class DocumentItem
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string FileType { get; set; } = "";
        public string Category { get; set; } = "";
        public long Size { get; set; }
        public DateTime UploadDate { get; set; }
        public int ViewCount { get; set; }
        public int DownloadCount { get; set; }
        public string Description { get; set; } = "";
    }
}
