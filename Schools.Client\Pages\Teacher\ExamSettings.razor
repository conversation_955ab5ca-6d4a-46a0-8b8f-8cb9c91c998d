@page "/teacher/exam-settings"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Teacher")]

<PageTitle>إعدادات الامتحانات - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-cogs text-primary me-2"></i>
                        إعدادات الامتحانات
                    </h2>
                    <p class="text-muted mb-0">إدارة الإعدادات العامة للامتحانات الإلكترونية</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-success" @onclick="SaveSettings" disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        else
                        {
                            <i class="fas fa-save me-2"></i>
                        }
                        حفظ الإعدادات
                    </button>
                    <button class="btn btn-outline-secondary" @onclick="ResetSettings">
                        <i class="fas fa-undo me-2"></i>
                        إعادة تعيين
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل الإعدادات...</p>
        </div>
    }
    else
    {
        <div class="row g-4">
            <!-- General Settings -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-sliders-h me-2"></i>
                            الإعدادات العامة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">مدة الامتحان الافتراضية (بالدقائق)</label>
                            <InputNumber @bind-Value="settings.DefaultDurationMinutes" class="form-control" min="5" max="300" />
                            <div class="form-text">المدة الافتراضية للامتحانات الجديدة</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">عدد المحاولات الافتراضي</label>
                            <InputNumber @bind-Value="settings.DefaultMaxAttempts" class="form-control" min="1" max="10" />
                            <div class="form-text">عدد المحاولات المسموحة للطلاب</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">درجة النجاح الافتراضية (%)</label>
                            <InputNumber @bind-Value="settings.DefaultPassingPercentage" class="form-control" min="0" max="100" />
                            <div class="form-text">النسبة المئوية المطلوبة للنجاح</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">فترة الحفظ التلقائي (بالثواني)</label>
                            <InputNumber @bind-Value="settings.AutoSaveIntervalSeconds" class="form-control" min="10" max="300" />
                            <div class="form-text">تكرار حفظ إجابات الطلاب تلقائياً</div>
                        </div>

                        <div class="form-check mb-3">
                            <InputCheckbox @bind-Value="settings.ShowResultsImmediately" class="form-check-input" />
                            <label class="form-check-label">عرض النتائج فور انتهاء الامتحان</label>
                        </div>

                        <div class="form-check mb-3">
                            <InputCheckbox @bind-Value="settings.AllowRetakeByDefault" class="form-check-input" />
                            <label class="form-check-label">السماح بإعادة المحاولة افتراضياً</label>
                        </div>

                        <div class="form-check mb-3">
                            <InputCheckbox @bind-Value="settings.RandomizeQuestions" class="form-check-input" />
                            <label class="form-check-label">ترتيب الأسئلة عشوائياً</label>
                        </div>

                        <div class="form-check">
                            <InputCheckbox @bind-Value="settings.RandomizeOptions" class="form-check-input" />
                            <label class="form-check-label">ترتيب خيارات الإجابة عشوائياً</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            إعدادات الأمان
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">مهلة انتهاء الجلسة (بالدقائق)</label>
                            <InputNumber @bind-Value="settings.SessionTimeoutMinutes" class="form-control" min="5" max="120" />
                            <div class="form-text">مدة بقاء الطالب متصلاً بدون نشاط</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">عدد محاولات تسجيل الدخول الفاشلة</label>
                            <InputNumber @bind-Value="settings.MaxLoginAttempts" class="form-control" min="3" max="10" />
                            <div class="form-text">عدد المحاولات قبل حظر الحساب مؤقتاً</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">مدة الحظر المؤقت (بالدقائق)</label>
                            <InputNumber @bind-Value="settings.LockoutDurationMinutes" class="form-control" min="5" max="60" />
                            <div class="form-text">مدة حظر الحساب بعد المحاولات الفاشلة</div>
                        </div>

                        <div class="form-check mb-3">
                            <InputCheckbox @bind-Value="settings.PreventTabSwitching" class="form-check-input" />
                            <label class="form-check-label">منع تبديل التبويبات أثناء الامتحان</label>
                        </div>

                        <div class="form-check mb-3">
                            <InputCheckbox @bind-Value="settings.PreventCopyPaste" class="form-check-input" />
                            <label class="form-check-label">منع النسخ واللصق</label>
                        </div>

                        <div class="form-check mb-3">
                            <InputCheckbox @bind-Value="settings.PreventRightClick" class="form-check-input" />
                            <label class="form-check-label">منع النقر بالزر الأيمن</label>
                        </div>

                        <div class="form-check mb-3">
                            <InputCheckbox @bind-Value="settings.EnableFullScreen" class="form-check-input" />
                            <label class="form-check-label">فرض وضع الشاشة الكاملة</label>
                        </div>

                        <div class="form-check">
                            <InputCheckbox @bind-Value="settings.LogUserActivity" class="form-check-input" />
                            <label class="form-check-label">تسجيل نشاط المستخدم</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notification Settings -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-bell me-2"></i>
                            إعدادات التنبيهات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">تنبيه قبل انتهاء الوقت (بالدقائق)</label>
                            <InputNumber @bind-Value="settings.TimeWarningMinutes" class="form-control" min="1" max="30" />
                            <div class="form-text">إظهار تحذير قبل انتهاء وقت الامتحان</div>
                        </div>

                        <div class="form-check mb-3">
                            <InputCheckbox @bind-Value="settings.EmailNotifications" class="form-check-input" />
                            <label class="form-check-label">إرسال تنبيهات بالبريد الإلكتروني</label>
                        </div>

                        <div class="form-check mb-3">
                            <InputCheckbox @bind-Value="settings.SmsNotifications" class="form-check-input" />
                            <label class="form-check-label">إرسال تنبيهات برسائل SMS</label>
                        </div>

                        <div class="form-check mb-3">
                            <InputCheckbox @bind-Value="settings.NotifyOnExamStart" class="form-check-input" />
                            <label class="form-check-label">تنبيه عند بداية الامتحان</label>
                        </div>

                        <div class="form-check mb-3">
                            <InputCheckbox @bind-Value="settings.NotifyOnExamEnd" class="form-check-input" />
                            <label class="form-check-label">تنبيه عند انتهاء الامتحان</label>
                        </div>

                        <div class="form-check">
                            <InputCheckbox @bind-Value="settings.NotifyOnResultsAvailable" class="form-check-input" />
                            <label class="form-check-label">تنبيه عند توفر النتائج</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Grading Settings -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-graduation-cap me-2"></i>
                            إعدادات التقدير
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">نظام التقدير</label>
                            <select @bind="settings.GradingSystem" class="form-select">
                                <option value="Percentage">نسبة مئوية</option>
                                <option value="Letter">أحرف (A, B, C, D, F)</option>
                                <option value="Arabic">عربي (ممتاز، جيد جداً، جيد، مقبول، راسب)</option>
                                <option value="Points">نقاط (من 4.0)</option>
                            </select>
                        </div>

                        <div class="grade-scale">
                            <h6 class="mb-3">مقياس التقديرات</h6>

                            <div class="row g-2 mb-2">
                                <div class="col-6">
                                    <label class="form-label small">ممتاز (من %)</label>
                                    <InputNumber @bind-Value="settings.ExcellentThreshold" class="form-control form-control-sm" min="0" max="100" />
                                </div>
                                <div class="col-6">
                                    <label class="form-label small">جيد جداً (من %)</label>
                                    <InputNumber @bind-Value="settings.VeryGoodThreshold" class="form-control form-control-sm" min="0" max="100" />
                                </div>
                            </div>

                            <div class="row g-2 mb-2">
                                <div class="col-6">
                                    <label class="form-label small">جيد (من %)</label>
                                    <InputNumber @bind-Value="settings.GoodThreshold" class="form-control form-control-sm" min="0" max="100" />
                                </div>
                                <div class="col-6">
                                    <label class="form-label small">مقبول (من %)</label>
                                    <InputNumber @bind-Value="settings.AcceptableThreshold" class="form-control form-control-sm" min="0" max="100" />
                                </div>
                            </div>
                        </div>

                        <div class="form-check mb-3">
                            <InputCheckbox @bind-Value="settings.ShowDetailedGrades" class="form-check-input" />
                            <label class="form-check-label">عرض التقديرات التفصيلية</label>
                        </div>

                        <div class="form-check">
                            <InputCheckbox @bind-Value="settings.AllowPartialCredit" class="form-check-input" />
                            <label class="form-check-label">السماح بالدرجات الجزئية</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <div class="btn-group">
                    <button class="btn btn-success btn-lg" @onclick="SaveSettings" disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        else
                        {
                            <i class="fas fa-save me-2"></i>
                        }
                        حفظ جميع الإعدادات
                    </button>
                    <button class="btn btn-outline-secondary btn-lg" @onclick="ResetSettings">
                        <i class="fas fa-undo me-2"></i>
                        إعادة تعيين
                    </button>
                    <button class="btn btn-outline-info btn-lg" @onclick="ExportSettings">
                        <i class="fas fa-download me-2"></i>
                        تصدير الإعدادات
                    </button>
                    <button class="btn btn-outline-warning btn-lg" @onclick="ImportSettings">
                        <i class="fas fa-upload me-2"></i>
                        استيراد الإعدادات
                    </button>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .grade-scale {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .card-header {
        font-weight: 600;
    }

    .form-text {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .btn-group .btn {
        margin: 0 2px;
    }
</style>

@code {
    private ExamSettingsDto settings = new();
    private bool isLoading = true;
    private bool isSaving = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadSettings();
    }

    private async Task LoadSettings()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            settings = await ApiService.GetExamSettingsAsync() ?? new ExamSettingsDto();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الإعدادات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task SaveSettings()
    {
        try
        {
            isSaving = true;
            StateHasChanged();

            var success = await ApiService.UpdateExamSettingsAsync(settings);
            if (success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "تم حفظ الإعدادات بنجاح");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "فشل في حفظ الإعدادات");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ الإعدادات: {ex.Message}");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task ResetSettings()
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟"))
        {
            settings = new ExamSettingsDto
            {
                DefaultDurationMinutes = 60,
                DefaultMaxAttempts = 1,
                DefaultPassingPercentage = 50,
                AutoSaveIntervalSeconds = 60,
                ShowResultsImmediately = true,
                AllowRetakeByDefault = false,
                RandomizeQuestions = false,
                RandomizeOptions = false,
                SessionTimeoutMinutes = 30,
                MaxLoginAttempts = 5,
                LockoutDurationMinutes = 15,
                PreventTabSwitching = false,
                PreventCopyPaste = false,
                PreventRightClick = false,
                EnableFullScreen = false,
                LogUserActivity = true,
                TimeWarningMinutes = 5,
                EmailNotifications = true,
                SmsNotifications = false,
                NotifyOnExamStart = true,
                NotifyOnExamEnd = true,
                NotifyOnResultsAvailable = true,
                GradingSystem = "Arabic",
                ExcellentThreshold = 90,
                VeryGoodThreshold = 80,
                GoodThreshold = 70,
                AcceptableThreshold = 60,
                ShowDetailedGrades = true,
                AllowPartialCredit = false
            };

            StateHasChanged();
            await JSRuntime.InvokeVoidAsync("alert", "تم إعادة تعيين الإعدادات إلى القيم الافتراضية");
        }
    }

    private async Task ExportSettings()
    {
        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(settings, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            await JSRuntime.InvokeVoidAsync("downloadFile", "exam-settings.json", "application/json", json);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تصدير الإعدادات: {ex.Message}");
        }
    }

    private async Task ImportSettings()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة استيراد الإعدادات ستكون متاحة قريباً");
    }
}
