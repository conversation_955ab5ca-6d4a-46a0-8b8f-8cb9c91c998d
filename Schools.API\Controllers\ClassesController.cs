using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Schools.Data;
using Schools.Shared.DTOs;
using Schools.Shared.Models;
using System.Security.Claims;

namespace Schools.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Policy = "AdminOnly")]
    public class ClassesController : ControllerBase
    {
        private readonly SchoolsDbContext _context;

        public ClassesController(SchoolsDbContext context)
        {
            _context = context;
        }

        #region Classes Management

        [HttpGet]
        public async Task<ActionResult<IEnumerable<ClassDto>>> GetClasses()
        {
            var classes = await _context.Classes
                .Include(c => c.Grade)
                .Where(c => !c.IsDeleted)
                .Select(c => new ClassDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    GradeId = c.GradeId,
                    GradeName = c.Grade.Name,
                    Capacity = c.Capacity,
                    Description = c.Description,
                    StudentCount = c.StudentEnrollments.Count(se => se.IsActive),
                    SectionCount = c.Sections.Count(s => !s.IsDeleted)
                })
                .ToListAsync();

            return Ok(classes);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ClassDto>> GetClass(int id)
        {
            var classEntity = await _context.Classes
                .Include(c => c.Grade)
                .Where(c => c.Id == id && !c.IsDeleted)
                .Select(c => new ClassDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    GradeId = c.GradeId,
                    GradeName = c.Grade.Name,
                    Capacity = c.Capacity,
                    Description = c.Description,
                    StudentCount = c.StudentEnrollments.Count(se => se.IsActive),
                    SectionCount = c.Sections.Count(s => !s.IsDeleted)
                })
                .FirstOrDefaultAsync();

            if (classEntity == null)
            {
                return NotFound(new { message = "الصف غير موجود" });
            }

            return Ok(classEntity);
        }

        [HttpPost]
        public async Task<ActionResult<ClassDto>> CreateClass(CreateClassDto model)
        {
            try
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                // Check if grade exists
                var grade = await _context.Grades.FindAsync(model.GradeId);
                if (grade == null || grade.IsDeleted)
                {
                    return BadRequest(new { message = "المرحلة الدراسية غير موجودة" });
                }

                var classEntity = new Class
                {
                    Name = model.Name,
                    GradeId = model.GradeId,
                    Capacity = model.Capacity,
                    Description = model.Description,
                    CreatedBy = userId
                };

                _context.Classes.Add(classEntity);
                await _context.SaveChangesAsync();

                var result = new ClassDto
                {
                    Id = classEntity.Id,
                    Name = classEntity.Name,
                    GradeId = classEntity.GradeId,
                    GradeName = grade.Name,
                    Capacity = classEntity.Capacity,
                    Description = classEntity.Description,
                    StudentCount = 0,
                    SectionCount = 0
                };

                return CreatedAtAction(nameof(GetClass), new { id = classEntity.Id }, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء إنشاء الصف" });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateClass(int id, CreateClassDto model)
        {
            try
            {
                var classEntity = await _context.Classes.FindAsync(id);
                if (classEntity == null || classEntity.IsDeleted)
                {
                    return NotFound(new { message = "الصف غير موجود" });
                }

                // Check if grade exists
                var grade = await _context.Grades.FindAsync(model.GradeId);
                if (grade == null || grade.IsDeleted)
                {
                    return BadRequest(new { message = "المرحلة الدراسية غير موجودة" });
                }

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                classEntity.Name = model.Name;
                classEntity.GradeId = model.GradeId;
                classEntity.Capacity = model.Capacity;
                classEntity.Description = model.Description;
                classEntity.UpdatedAt = DateTime.UtcNow;
                classEntity.UpdatedBy = userId;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تحديث الصف بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء تحديث الصف" });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteClass(int id)
        {
            try
            {
                var classEntity = await _context.Classes.FindAsync(id);
                if (classEntity == null || classEntity.IsDeleted)
                {
                    return NotFound(new { message = "الصف غير موجود" });
                }

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                classEntity.IsDeleted = true;
                classEntity.UpdatedAt = DateTime.UtcNow;
                classEntity.UpdatedBy = userId;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم حذف الصف بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء حذف الصف" });
            }
        }

        #endregion

        #region Sections Management

        [HttpGet("{classId}/sections")]
        public async Task<ActionResult<IEnumerable<SectionDto>>> GetSections(int classId)
        {
            var sections = await _context.Sections
                .Include(s => s.Class)
                .Where(s => s.ClassId == classId && !s.IsDeleted)
                .Select(s => new SectionDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    ClassId = s.ClassId,
                    ClassName = s.Class.Name,
                    Capacity = s.Capacity,
                    StudentCount = s.StudentEnrollments.Count(se => se.IsActive)
                })
                .ToListAsync();

            return Ok(sections);
        }

        [HttpPost("{classId}/sections")]
        public async Task<ActionResult<SectionDto>> CreateSection(int classId, CreateSectionDto model)
        {
            try
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                // Check if class exists
                var classEntity = await _context.Classes.FindAsync(classId);
                if (classEntity == null || classEntity.IsDeleted)
                {
                    return BadRequest(new { message = "الصف غير موجود" });
                }

                var section = new Section
                {
                    Name = model.Name,
                    ClassId = classId,
                    Capacity = model.Capacity,
                    CreatedBy = userId
                };

                _context.Sections.Add(section);
                await _context.SaveChangesAsync();

                var result = new SectionDto
                {
                    Id = section.Id,
                    Name = section.Name,
                    ClassId = section.ClassId,
                    ClassName = classEntity.Name,
                    Capacity = section.Capacity,
                    StudentCount = 0
                };

                return CreatedAtAction(nameof(GetSections), new { classId = classId, id = section.Id }, result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء إنشاء القسم" });
            }
        }

        [HttpPut("sections/{id}")]
        public async Task<IActionResult> UpdateSection(int id, CreateSectionDto model)
        {
            try
            {
                var section = await _context.Sections.FindAsync(id);
                if (section == null || section.IsDeleted)
                {
                    return NotFound(new { message = "القسم غير موجود" });
                }

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                section.Name = model.Name;
                section.Capacity = model.Capacity;
                section.UpdatedAt = DateTime.UtcNow;
                section.UpdatedBy = userId;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم تحديث القسم بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء تحديث القسم" });
            }
        }

        [HttpDelete("sections/{id}")]
        public async Task<IActionResult> DeleteSection(int id)
        {
            try
            {
                var section = await _context.Sections.FindAsync(id);
                if (section == null || section.IsDeleted)
                {
                    return NotFound(new { message = "القسم غير موجود" });
                }

                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

                section.IsDeleted = true;
                section.UpdatedAt = DateTime.UtcNow;
                section.UpdatedBy = userId;

                await _context.SaveChangesAsync();

                return Ok(new { message = "تم حذف القسم بنجاح" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "حدث خطأ أثناء حذف القسم" });
            }
        }

        #endregion

        #region Class Students

        [HttpGet("{id}/students")]
        public async Task<ActionResult<IEnumerable<UserDto>>> GetClassStudents(int id)
        {
            try
            {
                var classEntity = await _context.Classes
                    .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);

                if (classEntity == null)
                    return NotFound();

                // For now, return all users (this would need proper class-student relationship)
                var students = await _context.Users
                    .Take(50) // Limit for performance
                    .Select(s => new UserDto
                    {
                        Id = s.Id,
                        FirstName = s.FirstName,
                        LastName = s.LastName,
                        Email = s.Email ?? "",
                        PhoneNumber = s.PhoneNumber ?? "",
                        Role = "Student"
                    })
                    .ToListAsync();

                return Ok(students);
            }
            catch (Exception ex)
            {
                return StatusCode(500, "Internal server error");
            }
        }

        #endregion
    }
}
