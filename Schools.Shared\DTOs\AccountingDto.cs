namespace Schools.Shared.DTOs;

public class AccountDto
{
    public int Id { get; set; }
    public string AccountCode { get; set; } = string.Empty;
    public string AccountName { get; set; } = string.Empty;
    public string AccountNameEn { get; set; } = string.Empty;
    public AccountType AccountType { get; set; }
    public int? ParentAccountId { get; set; }
    public string? ParentAccountName { get; set; }
    public int Level { get; set; } = 1;
    public decimal Balance { get; set; }
    public decimal DebitBalance { get; set; }
    public decimal CreditBalance { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedDate { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public List<AccountDto> SubAccounts { get; set; } = new();
}

public class ReceiptVoucherDto
{
    public int Id { get; set; }
    public string VoucherNumber { get; set; } = string.Empty;
    public DateTime VoucherDate { get; set; }
    public string ReceivedFrom { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public string AmountInWords { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string PaymentMethod { get; set; } = string.Empty; // Cash, Bank, Check, Card, Online
    public string? CheckNumber { get; set; }
    public string? BankName { get; set; }
    public string? AccountNumber { get; set; }
    public DateTime? CheckDate { get; set; }
    public VoucherStatus Status { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public string? ApprovedBy { get; set; }
    public DateTime? ApprovedDate { get; set; }
    public string? Reference { get; set; }
    public string? Notes { get; set; }
    public List<ReceiptVoucherDetailDto> Details { get; set; } = new();
    public List<JournalEntryDto> JournalEntries { get; set; } = new();
}

public class ReceiptVoucherDetailDto
{
    public int Id { get; set; }
    public int ReceiptVoucherId { get; set; }
    public int AccountId { get; set; }
    public string AccountName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
}

public class PaymentVoucherDto
{
    public int Id { get; set; }
    public string VoucherNumber { get; set; } = string.Empty;
    public DateTime VoucherDate { get; set; }
    public string PaidTo { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public string Description { get; set; } = string.Empty;
    public string PaymentMethod { get; set; } = string.Empty; // Cash, Bank, Check
    public string? CheckNumber { get; set; }
    public string? BankName { get; set; }
    public DateTime? CheckDate { get; set; }
    public VoucherStatus Status { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public string? ApprovedBy { get; set; }
    public DateTime? ApprovedDate { get; set; }
    public List<PaymentVoucherDetailDto> Details { get; set; } = new();
    public List<JournalEntryDto> JournalEntries { get; set; } = new();
}

public class PaymentVoucherDetailDto
{
    public int Id { get; set; }
    public int PaymentVoucherId { get; set; }
    public int AccountId { get; set; }
    public string AccountName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
}

public class JournalEntryDto
{
    public int Id { get; set; }
    public string EntryNumber { get; set; } = string.Empty;
    public DateTime EntryDate { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Reference { get; set; } = string.Empty;
    public decimal TotalDebit { get; set; }
    public decimal TotalCredit { get; set; }
    public JournalEntryStatus Status { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public string? ApprovedBy { get; set; }
    public DateTime? ApprovedDate { get; set; }
    public List<JournalEntryDetailDto> Details { get; set; } = new();
}

public class JournalEntryDetailDto
{
    public int Id { get; set; }
    public int JournalEntryId { get; set; }
    public int AccountId { get; set; }
    public string AccountCode { get; set; } = string.Empty;
    public string AccountName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal DebitAmount { get; set; }
    public decimal CreditAmount { get; set; }
}

public class AccountStatementDto
{
    public int AccountId { get; set; }
    public string AccountCode { get; set; } = string.Empty;
    public string AccountName { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public decimal OpeningBalance { get; set; }
    public decimal TotalDebits { get; set; }
    public decimal TotalCredits { get; set; }
    public decimal ClosingBalance { get; set; }
    public List<AccountTransactionDto> Transactions { get; set; } = new();
}

public class AccountTransactionDto
{
    public int Id { get; set; }
    public DateTime TransactionDate { get; set; }
    public string Reference { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal DebitAmount { get; set; }
    public decimal CreditAmount { get; set; }
    public decimal Balance { get; set; }
    public string TransactionType { get; set; } = string.Empty;
}

public class TrialBalanceDto
{
    public DateTime AsOfDate { get; set; }
    public List<TrialBalanceItemDto> Items { get; set; } = new();
    public decimal TotalDebits { get; set; }
    public decimal TotalCredits { get; set; }
}

public class TrialBalanceItemDto
{
    public int AccountId { get; set; }
    public string AccountCode { get; set; } = string.Empty;
    public string AccountName { get; set; } = string.Empty;
    public AccountType AccountType { get; set; }
    public decimal DebitBalance { get; set; }
    public decimal CreditBalance { get; set; }
}

public class IncomeStatementDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<IncomeStatementItemDto> Revenues { get; set; } = new();
    public List<IncomeStatementItemDto> Expenses { get; set; } = new();
    public decimal TotalRevenues { get; set; }
    public decimal TotalExpenses { get; set; }
    public decimal NetIncome { get; set; }
}

public class IncomeStatementItemDto
{
    public int AccountId { get; set; }
    public string AccountCode { get; set; } = string.Empty;
    public string AccountName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
}

public class BalanceSheetDto
{
    public DateTime AsOfDate { get; set; }
    public List<BalanceSheetItemDto> Assets { get; set; } = new();
    public List<BalanceSheetItemDto> Liabilities { get; set; } = new();
    public List<BalanceSheetItemDto> Equity { get; set; } = new();
    public decimal TotalAssets { get; set; }
    public decimal TotalLiabilities { get; set; }
    public decimal TotalEquity { get; set; }
}

public class BalanceSheetItemDto
{
    public int AccountId { get; set; }
    public string AccountCode { get; set; } = string.Empty;
    public string AccountName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public List<BalanceSheetItemDto> SubItems { get; set; } = new();
}

public class CashFlowStatementDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public List<CashFlowItemDto> OperatingActivities { get; set; } = new();
    public List<CashFlowItemDto> InvestingActivities { get; set; } = new();
    public List<CashFlowItemDto> FinancingActivities { get; set; } = new();
    public decimal NetCashFromOperating { get; set; }
    public decimal NetCashFromInvesting { get; set; }
    public decimal NetCashFromFinancing { get; set; }
    public decimal NetCashFlow { get; set; }
    public decimal BeginningCash { get; set; }
    public decimal EndingCash { get; set; }
}

public class CashFlowItemDto
{
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
}

public enum AccountType
{
    Asset = 1,
    Liability = 2,
    Equity = 3,
    Revenue = 4,
    Expense = 5
}

public enum VoucherStatus
{
    Draft = 1,
    Approved = 2,
    Posted = 3,
    Cancelled = 4
}

public enum JournalEntryStatus
{
    Draft = 1,
    Posted = 2,
    Cancelled = 3
}

public class AccountingDashboardDto
{
    public decimal TotalAssets { get; set; }
    public decimal TotalLiabilities { get; set; }
    public decimal TotalEquity { get; set; }
    public decimal MonthlyRevenue { get; set; }
    public decimal MonthlyExpenses { get; set; }
    public decimal NetIncome { get; set; }
    public decimal CashBalance { get; set; }
    public decimal BankBalance { get; set; }
    public int PendingVouchers { get; set; }
    public int UnpostedEntries { get; set; }

    // Student Fees Properties
    public decimal StudentFeesCollected { get; set; }
    public decimal StudentFeesOutstanding { get; set; }
    public int StudentsWithFullPayment { get; set; }
    public int StudentsWithPartialPayment { get; set; }
    public int StudentsWithNoPayment { get; set; }
    public int StudentsOverdue { get; set; }

    public List<RecentTransactionDto> RecentTransactions { get; set; } = new();
    public List<TopExpenseDto> TopExpenses { get; set; } = new();
}

public class RecentTransactionDto
{
    public DateTime Date { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public decimal Amount { get; set; }
}

public class TopExpenseDto
{
    public string AccountName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public decimal Percentage { get; set; }
}

// Student Fees DTOs
public class StudentFeeDto
{
    public int StudentId { get; set; }
    public string StudentName { get; set; } = string.Empty;
    public string StudentNumber { get; set; } = string.Empty;
    public int GradeId { get; set; }
    public string GradeName { get; set; } = string.Empty;
    public int ClassId { get; set; }
    public string ClassName { get; set; } = string.Empty;
    public decimal TotalFees { get; set; }
    public decimal PaidAmount { get; set; }
    public decimal OutstandingAmount { get; set; }
    public string PaymentStatus { get; set; } = string.Empty; // paid, partial, unpaid, overdue
    public DateTime? LastPaymentDate { get; set; }
    public decimal LastPaymentAmount { get; set; }
    public List<FeeItemDto> FeeItems { get; set; } = new();
    public List<PaymentHistoryDto> PaymentHistory { get; set; } = new();
}

public class FeeItemDto
{
    public int Id { get; set; }
    public string FeeType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public DateTime DueDate { get; set; }
    public bool IsPaid { get; set; }
    public DateTime? PaidDate { get; set; }
}

public class PaymentHistoryDto
{
    public int Id { get; set; }
    public DateTime PaymentDate { get; set; }
    public decimal Amount { get; set; }
    public string PaymentMethod { get; set; } = string.Empty;
    public string Reference { get; set; } = string.Empty;
    public string Notes { get; set; } = string.Empty;
    public int? ReceiptVoucherId { get; set; }
}

public class FeeStructureDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int GradeId { get; set; }
    public string GradeName { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public bool IsActive { get; set; }
    public List<FeeComponentDto> Components { get; set; } = new();
}

public class FeeComponentDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public bool IsMandatory { get; set; }
    public DateTime? DueDate { get; set; }
    public int AccountId { get; set; }
    public string AccountName { get; set; } = string.Empty;
}

public class StudentPaymentDto
{
    public int StudentId { get; set; }
    public decimal Amount { get; set; }
    public string PaymentMethod { get; set; } = string.Empty;
    public string Reference { get; set; } = string.Empty;
    public string Notes { get; set; } = string.Empty;
    public List<int> FeeItemIds { get; set; } = new();
    public DateTime PaymentDate { get; set; } = DateTime.Now;
}

// Voucher Detail DTO (Generic for both Receipt and Payment vouchers)
public class VoucherDetailDto
{
    public int Id { get; set; }
    public int VoucherId { get; set; }
    public int AccountId { get; set; }
    public string AccountCode { get; set; } = string.Empty;
    public string AccountName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public decimal DebitAmount { get; set; }
    public decimal CreditAmount { get; set; }
    public string? Reference { get; set; }
    public string VoucherType { get; set; } = string.Empty; // Receipt, Payment
}
