# PowerShell script to start the Schools Management System

Write-Host "🏫 بدء تشغيل نظام إدارة المدارس..." -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Cyan

# Check if .NET is installed
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET SDK Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ .NET SDK غير مثبت. يرجى تثبيت .NET 9 SDK أولاً" -ForegroundColor Red
    exit 1
}

# Build the solution
Write-Host "🔨 بناء المشروع..." -ForegroundColor Yellow
dotnet build
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في بناء المشروع" -ForegroundColor Red
    exit 1
}

Write-Host "✅ تم بناء المشروع بنجاح" -ForegroundColor Green

# Start API in background
Write-Host "🚀 تشغيل API..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD'; dotnet run --project Schools.API" -WindowStyle Normal

# Wait a bit for API to start
Start-Sleep -Seconds 5

# Start Client in background
Write-Host "🌐 تشغيل العميل..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD'; dotnet run --project Schools.Client" -WindowStyle Normal

# Wait a bit for Client to start
Start-Sleep -Seconds 8

Write-Host "================================================" -ForegroundColor Cyan
Write-Host "🎉 تم تشغيل النظام بنجاح!" -ForegroundColor Green
Write-Host ""
Write-Host "📍 الروابط المتاحة:" -ForegroundColor Cyan
Write-Host "   🌐 العميل: http://localhost:5131" -ForegroundColor White
Write-Host "   🔧 API: http://localhost:5261" -ForegroundColor White
Write-Host "   📚 Swagger: http://localhost:5261/swagger" -ForegroundColor White
Write-Host ""
Write-Host "🔐 بيانات تسجيل الدخول للمدير:" -ForegroundColor Cyan
Write-Host "   📧 البريد: <EMAIL>" -ForegroundColor White
Write-Host "   🔑 كلمة المرور: Admin123!" -ForegroundColor White
Write-Host ""
Write-Host "💡 نصائح:" -ForegroundColor Yellow
Write-Host "   • سجل دخولك كمدير أولاً" -ForegroundColor White
Write-Host "   • أنشئ حسابات للمستخدمين الآخرين" -ForegroundColor White
Write-Host "   • وافق على طلبات المستخدمين الجدد" -ForegroundColor White
Write-Host "   • استكشف جميع لوحات التحكم" -ForegroundColor White
Write-Host ""

# Open browser automatically
Write-Host "🌐 فتح المتصفح..." -ForegroundColor Yellow
Start-Process "http://localhost:5131"

Write-Host "✨ استمتع باستخدام نظام إدارة المدارس!" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Cyan

# Keep the script window open
Write-Host "اضغط أي مفتاح للخروج..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
