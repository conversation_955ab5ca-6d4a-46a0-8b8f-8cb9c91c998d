using Schools.Shared.DTOs;
using Schools.Shared.Models;

namespace Schools.API.Services;

public interface IStudentService
{
    Task<PagedResult<StudentDto>> GetStudentsAsync(
        string? search = null,
        string? grade = null,
        string? className = null,
        bool? isActive = null,
        int page = 1,
        int pageSize = 20);

    Task<StudentDto?> GetStudentByIdAsync(string id);
    Task<StudentDto> CreateStudentAsync(CreateStudentDto createDto);
    Task<StudentDto?> UpdateStudentAsync(string id, UpdateStudentDto updateDto);
    Task<bool> DeleteStudentAsync(string id);
    Task<bool> ActivateStudentAsync(string id);
    Task<bool> DeactivateStudentAsync(string id);
    
    // Academic related
    Task<List<GradeDto>> GetStudentGradesAsync(string studentId, int? subjectId = null, DateTime? startDate = null, DateTime? endDate = null);
    Task<List<AttendanceRecordDto>> GetStudentAttendanceAsync(string studentId, DateTime? startDate = null, DateTime? endDate = null);
    Task<StudentPerformanceSummaryDto> GetStudentPerformanceSummaryAsync(string studentId);
    
    // Enrollment
    Task<List<StudentEnrollmentDto>> GetStudentEnrollmentsAsync(string studentId);
    Task<StudentEnrollmentDto> EnrollStudentInClassAsync(string studentId, int classId);
    Task<bool> UnenrollStudentFromClassAsync(string studentId, int classId);
    
    // Reports
    Task<StudentReportCardDto> GenerateReportCardAsync(string studentId, int academicYearId, int? termId = null);
    Task<List<StudentDto>> GetStudentsByClassAsync(int classId);
    Task<List<StudentDto>> GetStudentsByGradeAsync(int gradeId);
}
