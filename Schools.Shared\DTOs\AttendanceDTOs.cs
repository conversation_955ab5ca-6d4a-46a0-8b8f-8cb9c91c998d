using System.ComponentModel.DataAnnotations;

namespace Schools.Shared.DTOs
{
    // Attendance DTOs
    public class AttendanceDto
    {
        public int Id { get; set; }
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public string StudentEmail { get; set; } = string.Empty;
        public int ClassId { get; set; }
        public string ClassName { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? ArrivalTime { get; set; }
        public string? Notes { get; set; }
    }

    public class CreateAttendanceDto
    {
        public string StudentId { get; set; } = string.Empty;
        public int ClassId { get; set; }
        public DateTime Date { get; set; }
        public string Status { get; set; } = string.Empty;
        public TimeSpan? ArrivalTime { get; set; }
        public string? Notes { get; set; }
    }

    public class UpdateAttendanceDto
    {
        public string Status { get; set; } = string.Empty;
        public TimeSpan? ArrivalTime { get; set; }
        public string? Notes { get; set; }
    }

    public class BulkAttendanceDto
    {
        public int ClassId { get; set; }
        public DateTime Date { get; set; }
        public List<StudentAttendanceDto> StudentAttendances { get; set; } = new();
    }

    public class StudentAttendanceDto
    {
        public string StudentId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public TimeSpan? ArrivalTime { get; set; }
        public string? Notes { get; set; }
    }

    public class AttendanceStatisticsDto
    {
        public int TotalRecords { get; set; }
        public int PresentCount { get; set; }
        public int AbsentCount { get; set; }
        public int LateCount { get; set; }
        public int ExcusedCount { get; set; }
        public double AttendanceRate { get; set; }
        public List<DailyAttendanceStatDto> DailyStatistics { get; set; } = new();
    }



    public class DailyAttendanceStatDto
    {
        public DateTime Date { get; set; }
        public int TotalStudents { get; set; }
        public int PresentCount { get; set; }
        public int AbsentCount { get; set; }
        public int LateCount { get; set; }
        public int ExcusedCount { get; set; }
    }

    public class StudentAttendanceSummaryDto
    {
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public int TotalDays { get; set; }
        public int PresentDays { get; set; }
        public int AbsentDays { get; set; }
        public int LateDays { get; set; }
        public int ExcusedDays { get; set; }
        public double AttendanceRate { get; set; }
    }
}
