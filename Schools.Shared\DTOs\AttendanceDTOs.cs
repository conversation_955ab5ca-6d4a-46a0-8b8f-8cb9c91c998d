using System.ComponentModel.DataAnnotations;
using Schools.Shared.Models;

namespace Schools.Shared.DTOs
{
    // Enhanced Attendance Record DTOs
    public class AttendanceRecordDto
    {
        public int Id { get; set; }
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public string StudentEmail { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public int? ClassId { get; set; }
        public string? ClassName { get; set; }
        public int? SubjectId { get; set; }
        public string? SubjectName { get; set; }
        public AttendanceStatus Status { get; set; }
        public bool IsPresent { get; set; }
        public bool IsLate { get; set; }
        public bool IsExcused { get; set; }
        public TimeSpan? CheckInTime { get; set; }
        public TimeSpan? CheckOutTime { get; set; }
        public TimeSpan? ArrivalTime { get; set; }
        public TimeSpan? DepartureTime { get; set; }
        public string? Reason { get; set; }
        public string? Notes { get; set; }
        public string? RecordedBy { get; set; }
        public string? RecordedByName { get; set; }
        public DateTime? RecordedAt { get; set; }
        public decimal? Temperature { get; set; }
        public bool HealthCheckPassed { get; set; }
        public string? HealthNotes { get; set; }
        public AttendanceMethod Method { get; set; }
        public bool IsVerified { get; set; }
        public string? VerifiedBy { get; set; }
        public string? VerifiedByName { get; set; }
        public DateTime? VerifiedAt { get; set; }
        public bool ParentNotified { get; set; }
        public DateTime? ParentNotificationSent { get; set; }
        public bool RequiresFollowUp { get; set; }
        public DateTime? FollowUpDate { get; set; }
        public string? FollowUpNotes { get; set; }
    }

    // Legacy Attendance DTO for backward compatibility
    public class AttendanceDto
    {
        public int Id { get; set; }
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public string StudentEmail { get; set; } = string.Empty;
        public int ClassId { get; set; }
        public string ClassName { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? ArrivalTime { get; set; }
        public string? Notes { get; set; }
    }

    // Create Attendance Record DTO
    public class CreateAttendanceRecordDto
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public DateTime Date { get; set; }

        public int? ClassId { get; set; }

        public int? SubjectId { get; set; }

        public int? ScheduleId { get; set; }

        [Required]
        public AttendanceStatus Status { get; set; } = AttendanceStatus.Present;

        public bool IsPresent { get; set; } = true;

        public bool IsLate { get; set; } = false;

        public bool IsExcused { get; set; } = false;

        public TimeSpan? CheckInTime { get; set; }

        public TimeSpan? CheckOutTime { get; set; }

        public TimeSpan? ArrivalTime { get; set; }

        public TimeSpan? DepartureTime { get; set; }

        [StringLength(500)]
        public string? Reason { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public decimal? Temperature { get; set; }

        public bool HealthCheckPassed { get; set; } = true;

        [StringLength(500)]
        public string? HealthNotes { get; set; }

        [StringLength(200)]
        public string? CheckInLocation { get; set; }

        [StringLength(200)]
        public string? CheckOutLocation { get; set; }

        public AttendanceMethod Method { get; set; } = AttendanceMethod.Manual;

        public decimal? Latitude { get; set; }

        public decimal? Longitude { get; set; }

        public int? AcademicYearId { get; set; }

        public int? SemesterId { get; set; }

        public bool RequiresFollowUp { get; set; } = false;

        public DateTime? FollowUpDate { get; set; }

        [StringLength(1000)]
        public string? FollowUpNotes { get; set; }
    }

    // Update Attendance Record DTO
    public class UpdateAttendanceRecordDto
    {
        public AttendanceStatus? Status { get; set; }

        public bool? IsPresent { get; set; }

        public bool? IsLate { get; set; }

        public bool? IsExcused { get; set; }

        public TimeSpan? CheckInTime { get; set; }

        public TimeSpan? CheckOutTime { get; set; }

        public TimeSpan? ArrivalTime { get; set; }

        public TimeSpan? DepartureTime { get; set; }

        [StringLength(500)]
        public string? Reason { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public decimal? Temperature { get; set; }

        public bool? HealthCheckPassed { get; set; }

        [StringLength(500)]
        public string? HealthNotes { get; set; }

        public bool? RequiresFollowUp { get; set; }

        public DateTime? FollowUpDate { get; set; }

        [StringLength(1000)]
        public string? FollowUpNotes { get; set; }

        public bool? IsVerified { get; set; }
    }

    // Legacy Create Attendance DTO for backward compatibility
    public class CreateAttendanceDto
    {
        public string StudentId { get; set; } = string.Empty;
        public int ClassId { get; set; }
        public DateTime Date { get; set; }
        public string Status { get; set; } = string.Empty;
        public TimeSpan? ArrivalTime { get; set; }
        public string? Notes { get; set; }
    }

    public class UpdateAttendanceDto
    {
        public string Status { get; set; } = string.Empty;
        public TimeSpan? ArrivalTime { get; set; }
        public string? Notes { get; set; }
    }

    public class BulkAttendanceDto
    {
        public int ClassId { get; set; }
        public DateTime Date { get; set; }
        public List<StudentAttendanceDto> StudentAttendances { get; set; } = new();
    }

    public class StudentAttendanceDto
    {
        public string StudentId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public TimeSpan? ArrivalTime { get; set; }
        public string? Notes { get; set; }
    }

    public class AttendanceStatisticsDto
    {
        public int TotalRecords { get; set; }
        public int PresentCount { get; set; }
        public int AbsentCount { get; set; }
        public int LateCount { get; set; }
        public int ExcusedCount { get; set; }
        public double AttendanceRate { get; set; }
        public List<DailyAttendanceStatDto> DailyStatistics { get; set; } = new();
    }



    public class DailyAttendanceStatDto
    {
        public DateTime Date { get; set; }
        public int TotalStudents { get; set; }
        public int PresentCount { get; set; }
        public int AbsentCount { get; set; }
        public int LateCount { get; set; }
        public int ExcusedCount { get; set; }
    }

    public class StudentAttendanceSummaryDto
    {
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public int TotalDays { get; set; }
        public int PresentDays { get; set; }
        public int AbsentDays { get; set; }
        public int LateDays { get; set; }
        public int ExcusedDays { get; set; }
        public double AttendanceRate { get; set; }
    }

    // Report DTOs
    public class AttendanceReportDto
    {
        public int ClassId { get; set; }
        public string ClassName { get; set; } = string.Empty;
        public string GradeName { get; set; } = string.Empty;
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int TotalStudents { get; set; }
        public int TotalSchoolDays { get; set; }
        public double OverallAttendanceRate { get; set; }
        public List<StudentAttendanceReportDto> StudentAttendance { get; set; } = new();
    }

    public class StudentAttendanceReportDto
    {
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public int TotalPresent { get; set; }
        public int TotalAbsent { get; set; }
        public int TotalLate { get; set; }
        public double AttendanceRate { get; set; }
    }

    // Enhanced Attendance Summary DTO
    public class EnhancedAttendanceSummaryDto
    {
        public int Id { get; set; }
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public int AcademicYearId { get; set; }
        public string AcademicYearName { get; set; } = string.Empty;
        public int? SemesterId { get; set; }
        public string? SemesterName { get; set; }
        public int? ClassId { get; set; }
        public string? ClassName { get; set; }
        public int TotalDays { get; set; }
        public int PresentDays { get; set; }
        public int AbsentDays { get; set; }
        public int LateDays { get; set; }
        public int ExcusedDays { get; set; }
        public decimal AttendanceRate { get; set; }
        public decimal LateRate { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    // Attendance Alert DTO
    public class AttendanceAlertDto
    {
        public int Id { get; set; }
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public AttendanceAlertType AlertType { get; set; }
        public AttendanceAlertPriority Priority { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime TriggerDate { get; set; }
        public bool IsResolved { get; set; }
        public DateTime? ResolvedDate { get; set; }
        public string? ResolvedBy { get; set; }
        public string? ResolvedByName { get; set; }
        public string? ResolutionNotes { get; set; }
        public bool NotificationSent { get; set; }
        public DateTime? NotificationSentAt { get; set; }
        public int? ConsecutiveAbsences { get; set; }
        public decimal? AttendanceRateThreshold { get; set; }
        public int? LateCountThreshold { get; set; }
    }

    // Enhanced Attendance Statistics DTO
    public class EnhancedAttendanceStatisticsDto
    {
        public int TotalStudents { get; set; }
        public int TotalRecords { get; set; }
        public int PresentToday { get; set; }
        public int AbsentToday { get; set; }
        public int LateToday { get; set; }
        public decimal OverallAttendanceRate { get; set; }
        public decimal TodayAttendanceRate { get; set; }
        public int ActiveAlerts { get; set; }
        public int PendingFollowUps { get; set; }
        public Dictionary<string, int> StatusBreakdown { get; set; } = new();
        public Dictionary<string, decimal> MonthlyTrends { get; set; } = new();
        public List<AttendanceAlertDto> RecentAlerts { get; set; } = new();
        public List<EnhancedAttendanceSummaryDto> TopAbsentees { get; set; } = new();
    }

    // Attendance Search DTO
    public class AttendanceSearchDto
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? StudentId { get; set; }
        public int? ClassId { get; set; }
        public int? SubjectId { get; set; }
        public AttendanceStatus? Status { get; set; }
        public bool? IsLate { get; set; }
        public bool? IsExcused { get; set; }
        public bool? RequiresFollowUp { get; set; }
        public bool? IsVerified { get; set; }
        public AttendanceMethod? Method { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; } = false;
    }
}
