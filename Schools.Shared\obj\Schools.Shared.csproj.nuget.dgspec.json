{"format": 1, "restore": {"D:\\Shools App\\Schools4\\Schools.Shared\\Schools.Shared.csproj": {}}, "projects": {"D:\\Shools App\\Schools4\\Schools.Shared\\Schools.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Shools App\\Schools4\\Schools.Shared\\Schools.Shared.csproj", "projectName": "Schools.Shared", "projectPath": "D:\\Shools App\\Schools4\\Schools.Shared\\Schools.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Shools App\\Schools4\\Schools.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.AspNetCore.Components": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.AspNetCore.Components.Authorization": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.AspNetCore.Components.DataAnnotations.Validation": {"target": "Package", "version": "[3.2.0-rc1.20223.4, )"}, "Microsoft.AspNetCore.Components.Web": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.AspNetCore.Components.WebAssembly": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[9.0.5, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.2.1, )"}, "System.Net.Http.Json": {"target": "Package", "version": "[9.0.5, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}