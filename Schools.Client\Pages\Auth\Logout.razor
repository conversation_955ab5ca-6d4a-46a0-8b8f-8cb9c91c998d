@page "/logout"
@using Microsoft.AspNetCore.Components.Authorization
@using Schools.Client.Services
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>تسجيل الخروج - نظام إدارة المدارس</PageTitle>

<div class="container-fluid vh-100 d-flex align-items-center justify-content-center bg-light">
    <div class="row w-100">
        <div class="col-md-4 mx-auto">
            <div class="card shadow border-0">
                <div class="card-body text-center p-5">
                    <div class="mb-4">
                        <i class="fas fa-sign-out-alt fa-4x text-warning"></i>
                    </div>
                    <h3 class="mb-3">تسجيل الخروج</h3>
                    <p class="text-muted mb-4">جاري تسجيل الخروج من النظام...</p>
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Clear session storage
            await JSRuntime.InvokeVoidAsync("sessionStorage.clear");

            // Notify authentication state provider
            if (AuthenticationStateProvider is CustomAuthenticationStateProvider customProvider)
            {
                await customProvider.NotifyUserLogout();
            }

            // Wait a moment for visual feedback
            await Task.Delay(1500);

            // Redirect to home page
            Navigation.NavigateTo("/", true);
        }
        catch (Exception)
        {
            // If something goes wrong, still redirect
            Navigation.NavigateTo("/", true);
        }
    }
}
