using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using System.Security.Claims;
using System.Text.Json;
using System.IdentityModel.Tokens.Jwt;
using System.Text;

namespace Schools.Client.Services
{
    public class CustomAuthenticationStateProvider : AuthenticationStateProvider
    {
        private readonly IJSRuntime _jsRuntime;
        private readonly HttpClient _httpClient;
        private ClaimsPrincipal _currentUser = new(new ClaimsIdentity());

        public CustomAuthenticationStateProvider(IJSRuntime jsRuntime, HttpClient httpClient)
        {
            _jsRuntime = jsRuntime;
            _httpClient = httpClient;
        }

        public override async Task<AuthenticationState> GetAuthenticationStateAsync()
        {
            try
            {
                var userClaimsJson = await _jsRuntime.InvokeAsync<string>("sessionStorage.getItem", "userClaims");
                var token = await _jsRuntime.InvokeAsync<string>("sessionStorage.getItem", "authToken");

                if (string.IsNullOrEmpty(userClaimsJson))
                {
                    return new AuthenticationState(_currentUser);
                }

                var userClaims = JsonSerializer.Deserialize<ClaimData[]>(userClaimsJson);
                if (userClaims?.Length > 0)
                {
                    var claims = userClaims.Select(c => new Claim(c.Type, c.Value)).ToList();
                    var identity = new ClaimsIdentity(claims, "sessionStorage");
                    _currentUser = new ClaimsPrincipal(identity);

                    // Set Authorization header if token exists
                    if (!string.IsNullOrEmpty(token))
                    {
                        _httpClient.DefaultRequestHeaders.Authorization =
                            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                    }
                }

                return new AuthenticationState(_currentUser);
            }
            catch (Exception)
            {
                return new AuthenticationState(_currentUser);
            }
        }

        public async Task NotifyUserAuthentication(IEnumerable<Claim> claims)
        {
            var identity = new ClaimsIdentity(claims, "sessionStorage");
            _currentUser = new ClaimsPrincipal(identity);

            // Generate a simple JWT token for API calls
            var token = GenerateJwtToken(claims);
            await _jsRuntime.InvokeVoidAsync("sessionStorage.setItem", "authToken", token);

            // Set Authorization header for HttpClient
            _httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

            NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(_currentUser)));
        }

        public async Task NotifyUserLogout()
        {
            await _jsRuntime.InvokeVoidAsync("sessionStorage.removeItem", "userClaims");
            await _jsRuntime.InvokeVoidAsync("sessionStorage.removeItem", "authToken");

            // Clear Authorization header
            _httpClient.DefaultRequestHeaders.Authorization = null;

            _currentUser = new ClaimsPrincipal(new ClaimsIdentity());
            NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(_currentUser)));
        }

        private string GenerateJwtToken(IEnumerable<Claim> claims)
        {
            // Create a simple JWT token for demo purposes
            var header = new { alg = "HS256", typ = "JWT" };
            var payload = new
            {
                sub = claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value ?? Guid.NewGuid().ToString(),
                name = claims.FirstOrDefault(c => c.Type == ClaimTypes.Name)?.Value ?? "User",
                email = claims.FirstOrDefault(c => c.Type == ClaimTypes.Email)?.Value ?? "<EMAIL>",
                role = claims.FirstOrDefault(c => c.Type == ClaimTypes.Role)?.Value ?? "User",
                iat = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                exp = DateTimeOffset.UtcNow.AddHours(24).ToUnixTimeSeconds()
            };

            var headerJson = JsonSerializer.Serialize(header);
            var payloadJson = JsonSerializer.Serialize(payload);

            var headerBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(headerJson))
                .TrimEnd('=').Replace('+', '-').Replace('/', '_');
            var payloadBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(payloadJson))
                .TrimEnd('=').Replace('+', '-').Replace('/', '_');

            // For demo purposes, we'll use a simple signature
            var signature = Convert.ToBase64String(Encoding.UTF8.GetBytes("demo-signature"))
                .TrimEnd('=').Replace('+', '-').Replace('/', '_');

            return $"{headerBase64}.{payloadBase64}.{signature}";
        }

        public async Task<string?> GetTokenAsync()
        {
            try
            {
                return await _jsRuntime.InvokeAsync<string>("sessionStorage.getItem", "authToken");
            }
            catch
            {
                return null;
            }
        }

        private class ClaimData
        {
            public string Type { get; set; } = "";
            public string Value { get; set; } = "";
        }
    }
}
