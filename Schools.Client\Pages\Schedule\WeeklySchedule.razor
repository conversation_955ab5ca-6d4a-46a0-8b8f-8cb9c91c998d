@page "/schedule/weekly"
@using Schools.Client.Models
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Components.Authorization
@inject HttpClient Http
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthStateProvider

<PageTitle>الجدول الأسبوعي - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4 class="mb-0">
                                <i class="fas fa-calendar-week me-2"></i>
                                الجدول الأسبوعي للحصص
                            </h4>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group">
                                <button class="btn btn-light btn-sm" @onclick="PreviousWeek">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                                <button class="btn btn-light btn-sm" @onclick="CurrentWeek">
                                    الأسبوع الحالي
                                </button>
                                <button class="btn btn-light btn-sm" @onclick="NextWeek">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body p-0">
                    <!-- Filters -->
                    <div class="p-3 border-bottom bg-light">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">الصف</label>
                                <select class="form-select" @bind="selectedClassId" @bind:after="LoadSchedule">
                                    <option value="">جميع الصفوف</option>
                                    @if (classes != null)
                                    {
                                        @foreach (var cls in classes)
                                        {
                                            <option value="@cls.Id">@cls.Name</option>
                                        }
                                    }
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">المعلم</label>
                                <select class="form-select" @bind="selectedTeacherId" @bind:after="LoadSchedule">
                                    <option value="">جميع المعلمين</option>
                                    @if (teachers != null)
                                    {
                                        @foreach (var teacher in teachers)
                                        {
                                            <option value="@teacher.Id">@teacher.Name</option>
                                        }
                                    }
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الأسبوع</label>
                                <input type="date" class="form-control" @bind="weekStart" @bind:after="LoadSchedule" />
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button class="btn btn-primary w-100" @onclick="LoadSchedule">
                                    <i class="fas fa-search me-2"></i>
                                    بحث
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Week Header -->
                    @if (weeklySchedule != null)
                    {
                        <div class="p-3 bg-primary text-white">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h5 class="mb-0">
                                        الأسبوع من @weeklySchedule.WeekStart.ToString("dd/MM/yyyy")
                                        إلى @weeklySchedule.WeekEnd.ToString("dd/MM/yyyy")
                                    </h5>
                                </div>
                                <div class="col-auto">
                                    <div class="d-flex gap-3">
                                        <div class="text-center">
                                            <div class="fw-bold">@weeklySchedule.TotalPeriods</div>
                                            <small>إجمالي الحصص</small>
                                        </div>
                                        <div class="text-center">
                                            <div class="fw-bold">@weeklySchedule.TotalHours.ToString("F1")</div>
                                            <small>إجمالي الساعات</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Loading -->
                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2 text-muted">جاري تحميل الجدول الأسبوعي...</p>
                        </div>
                    }
                    else if (weeklySchedule?.Days != null)
                    {
                        <!-- Schedule Table -->
                        <div class="table-responsive">
                            <table class="table table-bordered mb-0 schedule-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 100px;">الوقت</th>
                                        @foreach (var day in weeklySchedule.Days)
                                        {
                                            <th class="text-center @(day.IsToday ? "bg-warning text-dark" : "") @(day.IsWeekend ? "bg-secondary" : "")">
                                                <div class="fw-bold">@day.DayName</div>
                                                <small>@day.Date.ToString("dd/MM")</small>
                                                @if (day.IsToday)
                                                {
                                                    <div><small class="badge bg-warning text-dark">اليوم</small></div>
                                                }
                                                @if (day.IsHoliday)
                                                {
                                                    <div><small class="badge bg-danger">عطلة</small></div>
                                                }
                                            </th>
                                        }
                                    </tr>
                                </thead>
                                <tbody>
                                    @{
                                        var allTimes = GetAllTimeSlots();
                                    }
                                    @foreach (var timeSlot in allTimes)
                                    {
                                        <tr>
                                            <td class="time-slot bg-light fw-bold text-center">
                                                @timeSlot
                                            </td>
                                            @foreach (var day in weeklySchedule.Days)
                                            {
                                                var period = day.Periods.FirstOrDefault(p => p.TimeSlot == timeSlot);
                                                <td class="period-cell @(day.IsWeekend ? "weekend-cell" : "") @(period?.IsCancelled == true ? "cancelled-cell" : "")">
                                                    @if (period != null)
                                                    {
                                                        <div class="period-card @GetPeriodClass(period)">
                                                            <div class="period-subject fw-bold">@period.SubjectNameAr</div>
                                                            <div class="period-teacher">@period.TeacherName</div>
                                                            <div class="period-class">@period.ClassName</div>
                                                            @if (!string.IsNullOrEmpty(period.Room))
                                                            {
                                                                <div class="period-room">
                                                                    <i class="fas fa-door-open"></i> @period.Room
                                                                </div>
                                                            }
                                                            @if (period.IsSubstitute)
                                                            {
                                                                <div class="period-substitute">
                                                                    <i class="fas fa-exchange-alt"></i> بديل
                                                                </div>
                                                            }
                                                            @if (period.IsCancelled)
                                                            {
                                                                <div class="period-cancelled">
                                                                    <i class="fas fa-times"></i> ملغية
                                                                </div>
                                                            }
                                                        </div>
                                                    }
                                                    else if (day.IsWeekend)
                                                    {
                                                        <div class="text-center text-muted">
                                                            <i class="fas fa-calendar-times"></i>
                                                        </div>
                                                    }
                                                </td>
                                            }
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Legend -->
                        <div class="p-3 border-top bg-light">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="mb-2">دليل الألوان:</h6>
                                    <div class="d-flex flex-wrap gap-3">
                                        <div class="d-flex align-items-center">
                                            <div class="legend-box bg-primary"></div>
                                            <span class="ms-2">حصة عادية</span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div class="legend-box bg-warning"></div>
                                            <span class="ms-2">حصة بديلة</span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div class="legend-box bg-danger"></div>
                                            <span class="ms-2">حصة ملغية</span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div class="legend-box bg-success"></div>
                                            <span class="ms-2">حصة مكتملة</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <button class="btn btn-outline-primary me-2" @onclick="ExportSchedule">
                                        <i class="fas fa-download me-2"></i>
                                        تصدير الجدول
                                    </button>
                                    <button class="btn btn-outline-success" @onclick="PrintSchedule">
                                        <i class="fas fa-print me-2"></i>
                                        طباعة
                                    </button>
                                </div>
                            </div>
                        </div>
                    }
                    else if (!isLoading)
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد حصص مجدولة</h5>
                            <p class="text-muted">لا توجد حصص مجدولة للفترة المحددة</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .schedule-table {
        font-size: 0.85rem;
    }

    .time-slot {
        writing-mode: vertical-rl;
        text-orientation: mixed;
        min-width: 80px;
    }

    .period-cell {
        padding: 4px;
        vertical-align: top;
        min-height: 80px;
        position: relative;
    }

    .weekend-cell {
        background-color: #f8f9fa;
    }

    .cancelled-cell {
        background-color: #ffe6e6;
    }

    .period-card {
        padding: 8px;
        border-radius: 6px;
        font-size: 0.75rem;
        line-height: 1.2;
        height: 100%;
        min-height: 70px;
        position: relative;
        overflow: hidden;
    }

    .period-card.regular {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
    }

    .period-card.substitute {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
    }

    .period-card.cancelled {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        opacity: 0.7;
    }

    .period-card.completed {
        background: linear-gradient(135deg, #28a745, #1e7e34);
        color: white;
    }

    .period-subject {
        font-size: 0.8rem;
        margin-bottom: 2px;
    }

    .period-teacher,
    .period-class,
    .period-room {
        font-size: 0.7rem;
        opacity: 0.9;
        margin-bottom: 1px;
    }

    .period-substitute,
    .period-cancelled {
        position: absolute;
        top: 2px;
        right: 2px;
        font-size: 0.6rem;
        padding: 1px 3px;
        border-radius: 2px;
        background: rgba(255, 255, 255, 0.2);
    }

    .legend-box {
        width: 20px;
        height: 15px;
        border-radius: 3px;
        display: inline-block;
    }

    @@media (max-width: 768px) {
        .schedule-table {
            font-size: 0.7rem;
        }

        .period-card {
            min-height: 60px;
            padding: 4px;
        }

        .time-slot {
            writing-mode: horizontal-tb;
            min-width: 60px;
        }
    }
</style>

@code {
    private WeeklyScheduleDto? weeklySchedule;
    private List<Schools.Client.Models.ClassDto> classes = new();
    private List<Schools.Client.Models.TeacherDto> teachers = new();
    private bool isLoading = false;

    private int? selectedClassId;
    private string? selectedTeacherId;
    private DateTime weekStart = GetStartOfWeek(DateTime.Today);

    protected override async Task OnInitializedAsync()
    {
        await LoadClasses();
        await LoadTeachers();
        await LoadSchedule();
    }

    private async Task LoadClasses()
    {
        try
        {
            var response = await Http.GetAsync("api/classes");
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<List<Schools.Client.Models.ClassDto>>();
                classes = result ?? new List<Schools.Client.Models.ClassDto>();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الصفوف: {ex.Message}");
        }
    }

    private async Task LoadTeachers()
    {
        try
        {
            var response = await Http.GetAsync("api/admin/teachers");
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<List<Schools.Client.Models.TeacherDto>>();
                teachers = result ?? new List<Schools.Client.Models.TeacherDto>();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل المعلمين: {ex.Message}");
        }
    }

    private async Task LoadSchedule()
    {
        isLoading = true;
        try
        {
            var queryParams = new List<string>();

            if (selectedClassId.HasValue)
                queryParams.Add($"classId={selectedClassId}");

            if (!string.IsNullOrEmpty(selectedTeacherId))
                queryParams.Add($"teacherId={selectedTeacherId}");

            queryParams.Add($"weekStart={weekStart:yyyy-MM-dd}");

            var queryString = string.Join("&", queryParams);
            var response = await Http.GetAsync($"api/schedule/weekly?{queryString}");

            if (response.IsSuccessStatusCode)
            {
                weeklySchedule = await response.Content.ReadFromJsonAsync<WeeklyScheduleDto>();

                // Calculate totals
                if (weeklySchedule != null)
                {
                    weeklySchedule.TotalPeriods = weeklySchedule.Days.Sum(d => d.Periods.Count);
                    weeklySchedule.TotalHours = weeklySchedule.Days.Sum(d => d.Periods.Sum(p => p.Duration.TotalHours));
                }
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "فشل في تحميل الجدول الأسبوعي");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الجدول: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private List<string> GetAllTimeSlots()
    {
        if (weeklySchedule?.Days == null) return new List<string>();

        var allSlots = weeklySchedule.Days
            .SelectMany(d => d.Periods.Select(p => p.TimeSlot))
            .Distinct()
            .OrderBy(slot => TimeSpan.Parse(slot.Split(" - ")[0]))
            .ToList();

        return allSlots;
    }

    private string GetPeriodClass(SchedulePeriodDto period)
    {
        if (period.IsCancelled) return "cancelled";
        if (period.IsCompleted) return "completed";
        if (period.IsSubstitute) return "substitute";
        return "regular";
    }

    private async Task PreviousWeek()
    {
        weekStart = weekStart.AddDays(-7);
        await LoadSchedule();
    }

    private async Task NextWeek()
    {
        weekStart = weekStart.AddDays(7);
        await LoadSchedule();
    }

    private async Task CurrentWeek()
    {
        weekStart = GetStartOfWeek(DateTime.Today);
        await LoadSchedule();
    }

    private static DateTime GetStartOfWeek(DateTime date)
    {
        // In Arabic culture, week starts on Saturday
        var diff = (7 + (date.DayOfWeek - DayOfWeek.Saturday)) % 7;
        return date.AddDays(-1 * diff).Date;
    }

    private async Task ExportSchedule()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("alert", "ميزة التصدير ستكون متاحة قريباً");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في التصدير: {ex.Message}");
        }
    }

    private async Task PrintSchedule()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("window.print");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في الطباعة: {ex.Message}");
        }
    }
}
