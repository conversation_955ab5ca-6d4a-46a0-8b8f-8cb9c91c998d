namespace Schools.Shared.DTOs
{
    /// <summary>
    /// Common DTOs used across multiple modules
    /// </summary>
    
    // Notification DTO
    public class NotificationDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public bool IsRead { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? ActionUrl { get; set; }
        public string? Icon { get; set; }
        public string? Color { get; set; }
        public string? StudentName { get; set; }
        public string? UserId { get; set; }
        public string? UserName { get; set; }
    }

    // Upcoming Event DTO
    public class UpcomingEventDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Location { get; set; }
        public bool IsAllDay { get; set; }
        public string? Color { get; set; }
        public string? Icon { get; set; }
        public List<string> Participants { get; set; } = new();
        public string Category { get; set; } = string.Empty;
    }

    // Recent Activity DTO
    public class RecentActivityDto
    {
        public int Id { get; set; }
        public string Action { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string EntityType { get; set; } = string.Empty;
        public string EntityId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string? Icon { get; set; }
        public string? Color { get; set; }
    }

    // File Upload DTO
    public class FileUploadDto
    {
        public string? FileName { get; set; }
        public string? FilePath { get; set; }
        public string? FileUrl { get; set; }
        public long FileSize { get; set; }
        public string? ContentType { get; set; }
        public string? FileId { get; set; }
        public DateTime UploadedAt { get; set; }
        public string UploadedBy { get; set; } = string.Empty;
    }

    // Search Filter DTO
    public class SearchFilterDto
    {
        public string? SearchTerm { get; set; }
        public Dictionary<string, object> Filters { get; set; } = new();
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; } = false;
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    // Pagination DTO
    public class PaginationDto
    {
        public int CurrentPage { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public int TotalCount { get; set; }
        public bool HasPrevious { get; set; }
        public bool HasNext { get; set; }
        public int? PreviousPage { get; set; }
        public int? NextPage { get; set; }
    }

    // Statistics DTO
    public class StatisticsDto
    {
        public string Label { get; set; } = string.Empty;
        public object Value { get; set; } = new();
        public string? Unit { get; set; }
        public string? Icon { get; set; }
        public string? Color { get; set; }
        public string? Trend { get; set; } // up, down, stable
        public double? ChangePercentage { get; set; }
        public DateTime? LastUpdated { get; set; }
    }

    // Chart Data DTO
    public class ChartDataDto
    {
        public string Label { get; set; } = string.Empty;
        public List<object> Data { get; set; } = new();
        public List<string> Labels { get; set; } = new();
        public string? BackgroundColor { get; set; }
        public string? BorderColor { get; set; }
        public string ChartType { get; set; } = "line"; // line, bar, pie, doughnut
    }

    // Key-Value Pair DTO
    public class KeyValueDto
    {
        public string Key { get; set; } = string.Empty;
        public object Value { get; set; } = new();
        public string? DisplayName { get; set; }
        public string? Description { get; set; }
        public string? Type { get; set; }
    }

    // Option DTO for dropdowns
    public class OptionDto
    {
        public string Value { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
        public bool IsSelected { get; set; } = false;
        public bool IsDisabled { get; set; } = false;
        public string? Group { get; set; }
        public Dictionary<string, object> Data { get; set; } = new();
    }

    // Address DTO
    public class AddressDto
    {
        public string? Street { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? Country { get; set; }
        public string? PostalCode { get; set; }
        public string? FullAddress { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
    }

    // Contact Information DTO
    public class ContactInfoDto
    {
        public string? PrimaryPhone { get; set; }
        public string? SecondaryPhone { get; set; }
        public string? Email { get; set; }
        public string? AlternateEmail { get; set; }
        public string? Website { get; set; }
        public AddressDto? Address { get; set; }
        public string? EmergencyContact { get; set; }
        public string? EmergencyPhone { get; set; }
    }

    // Audit Trail DTO
    public class AuditTrailDto
    {
        public int Id { get; set; }
        public string Action { get; set; } = string.Empty;
        public string EntityType { get; set; } = string.Empty;
        public string EntityId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string? OldValues { get; set; }
        public string? NewValues { get; set; }
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; } = new();
    }

    // System Information DTO
    public class SystemInfoDto
    {
        public string Version { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public TimeSpan Uptime { get; set; }
        public string Environment { get; set; } = string.Empty;
        public Dictionary<string, object> Statistics { get; set; } = new();
        public List<string> Features { get; set; } = new();
        public Dictionary<string, string> Configuration { get; set; } = new();
    }

    // Error DTO
    public class ErrorDto
    {
        public string Code { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? Details { get; set; }
        public string? StackTrace { get; set; }
        public DateTime Timestamp { get; set; }
        public string? RequestId { get; set; }
        public Dictionary<string, object> Data { get; set; } = new();
    }

    // Success DTO
    public class SuccessDto
    {
        public string Message { get; set; } = string.Empty;
        public object? Data { get; set; }
        public DateTime Timestamp { get; set; }
        public string? RequestId { get; set; }
    }

    // Configuration DTO
    public class ConfigurationDto
    {
        public string Key { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Category { get; set; }
        public string DataType { get; set; } = "string";
        public bool IsReadOnly { get; set; } = false;
        public bool IsRequired { get; set; } = false;
        public string? DefaultValue { get; set; }
        public List<string> AllowedValues { get; set; } = new();
    }

    // Backup DTO
    public class BackupDto
    {
        public string? BackupId { get; set; }
        public string? FileName { get; set; }
        public string? FilePath { get; set; }
        public long FileSize { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public string BackupType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    // Health Check DTO
    public class HealthCheckDto
    {
        public string Name { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string? Description { get; set; }
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> Data { get; set; } = new();
        public DateTime CheckedAt { get; set; }
    }
}
