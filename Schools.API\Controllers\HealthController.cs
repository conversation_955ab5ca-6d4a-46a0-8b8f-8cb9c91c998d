using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Schools.Shared.DTOs;

namespace Schools.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class HealthController : ControllerBase
{
    private readonly ILogger<HealthController> _logger;

    public HealthController(ILogger<HealthController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Get student health records
    /// </summary>
    [HttpGet("students/{studentId}/records")]
    public async Task<ActionResult<List<HealthRecordDto>>> GetStudentHealthRecords(string studentId)
    {
        try
        {
            var records = new List<HealthRecordDto>
            {
                new() { Id = 1, StudentId = studentId, StudentName = "أحمد محمد", Date = DateTime.Now.AddDays(-30), 
                        Type = "Checkup", Description = "فحص دوري", Diagnosis = "سليم", 
                        Treatment = "لا يوجد", NurseId = "nurse1", NurseName = "فاطمة أحمد" },
                new() { Id = 2, StudentId = studentId, StudentName = "أحمد محمد", Date = DateTime.Now.AddDays(-15), 
                        Type = "Illness", Description = "صداع", Diagnosis = "صداع نصفي", 
                        Treatment = "راحة ومسكن", NurseId = "nurse1", NurseName = "فاطمة أحمد" }
            };

            return Ok(records);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting health records for student {StudentId}", studentId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create health record
    /// </summary>
    [HttpPost("records")]
    [Authorize(Roles = "Admin,Nurse")]
    public async Task<ActionResult<HealthRecordDto>> CreateHealthRecord([FromBody] CreateHealthRecordDto createDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var record = new HealthRecordDto
            {
                Id = new Random().Next(1000, 9999),
                StudentId = createDto.StudentId,
                StudentName = "أحمد محمد",
                Date = createDto.Date,
                Type = createDto.Type,
                Description = createDto.Description,
                Diagnosis = createDto.Diagnosis,
                Treatment = createDto.Treatment,
                Medications = createDto.Medications,
                FollowUpRequired = createDto.FollowUpRequired,
                FollowUpDate = createDto.FollowUpDate,
                NurseId = "current-nurse-id",
                NurseName = "فاطمة أحمد",
                Notes = createDto.Notes
            };

            return CreatedAtAction(nameof(GetHealthRecord), new { id = record.Id }, record);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating health record");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get health record by ID
    /// </summary>
    [HttpGet("records/{id}")]
    public async Task<ActionResult<HealthRecordDto>> GetHealthRecord(int id)
    {
        try
        {
            var record = new HealthRecordDto
            {
                Id = id,
                StudentId = "student1",
                StudentName = "أحمد محمد",
                Date = DateTime.Now.AddDays(-15),
                Type = "Illness",
                Description = "صداع وغثيان",
                Diagnosis = "صداع نصفي",
                Treatment = "راحة ومسكن",
                Medications = new List<string> { "باراسيتامول 500mg" },
                FollowUpRequired = true,
                FollowUpDate = DateTime.Now.AddDays(7),
                NurseId = "nurse1",
                NurseName = "فاطمة أحمد",
                Notes = "يحتاج متابعة بعد أسبوع"
            };

            return Ok(record);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting health record {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get vaccination records for student
    /// </summary>
    [HttpGet("students/{studentId}/vaccinations")]
    public async Task<ActionResult<List<VaccinationRecordDto>>> GetStudentVaccinations(string studentId)
    {
        try
        {
            var vaccinations = new List<VaccinationRecordDto>
            {
                new() { Id = 1, StudentId = studentId, StudentName = "أحمد محمد", 
                        VaccineName = "لقاح الإنفلونزا", Date = DateTime.Now.AddMonths(-6), 
                        DoseNumber = 1, NextDueDate = DateTime.Now.AddMonths(6), 
                        AdministeredBy = "د. محمد علي", Location = "العيادة المدرسية" },
                new() { Id = 2, StudentId = studentId, StudentName = "أحمد محمد", 
                        VaccineName = "لقاح كوفيد-19", Date = DateTime.Now.AddMonths(-3), 
                        DoseNumber = 2, AdministeredBy = "د. فاطمة سالم", Location = "مركز التطعيم" }
            };

            return Ok(vaccinations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vaccinations for student {StudentId}", studentId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Record vaccination
    /// </summary>
    [HttpPost("vaccinations")]
    [Authorize(Roles = "Admin,Nurse,Doctor")]
    public async Task<ActionResult<VaccinationRecordDto>> RecordVaccination([FromBody] CreateVaccinationDto createDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var vaccination = new VaccinationRecordDto
            {
                Id = new Random().Next(1000, 9999),
                StudentId = createDto.StudentId,
                StudentName = "أحمد محمد",
                VaccineName = createDto.VaccineName,
                Date = createDto.Date,
                DoseNumber = createDto.DoseNumber,
                NextDueDate = createDto.NextDueDate,
                AdministeredBy = createDto.AdministeredBy,
                Location = createDto.Location,
                BatchNumber = createDto.BatchNumber,
                Manufacturer = createDto.Manufacturer,
                SideEffects = createDto.SideEffects,
                Notes = createDto.Notes
            };

            return CreatedAtAction(nameof(GetVaccination), new { id = vaccination.Id }, vaccination);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording vaccination");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get vaccination record by ID
    /// </summary>
    [HttpGet("vaccinations/{id}")]
    public async Task<ActionResult<VaccinationRecordDto>> GetVaccination(int id)
    {
        try
        {
            var vaccination = new VaccinationRecordDto
            {
                Id = id,
                StudentId = "student1",
                StudentName = "أحمد محمد",
                VaccineName = "لقاح الإنفلونزا",
                Date = DateTime.Now.AddMonths(-6),
                DoseNumber = 1,
                NextDueDate = DateTime.Now.AddMonths(6),
                AdministeredBy = "د. محمد علي",
                Location = "العيادة المدرسية"
            };

            return Ok(vaccination);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vaccination {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get health alerts and notifications
    /// </summary>
    [HttpGet("alerts")]
    [Authorize(Roles = "Admin,Nurse,Doctor")]
    public async Task<ActionResult<List<HealthAlertDto>>> GetHealthAlerts()
    {
        try
        {
            var alerts = new List<HealthAlertDto>
            {
                new() { Id = 1, Type = "Vaccination Due", Priority = "Medium", 
                        Message = "5 طلاب يحتاجون لقاح الإنفلونزا", 
                        StudentCount = 5, DueDate = DateTime.Now.AddDays(7) },
                new() { Id = 2, Type = "Follow-up Required", Priority = "High", 
                        Message = "3 طلاب يحتاجون متابعة طبية", 
                        StudentCount = 3, DueDate = DateTime.Now.AddDays(2) },
                new() { Id = 3, Type = "Medication Reminder", Priority = "Low", 
                        Message = "تذكير بإعطاء الدواء لطالب واحد", 
                        StudentCount = 1, DueDate = DateTime.Now }
            };

            return Ok(alerts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting health alerts");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get health statistics
    /// </summary>
    [HttpGet("statistics")]
    [Authorize(Roles = "Admin,Nurse,Doctor")]
    public async Task<ActionResult<HealthStatisticsDto>> GetHealthStatistics()
    {
        try
        {
            var statistics = new HealthStatisticsDto
            {
                TotalHealthRecords = 450,
                TotalVaccinations = 1200,
                ActiveFollowUps = 15,
                OverdueVaccinations = 8,
                CommonIllnesses = new Dictionary<string, int>
                {
                    { "نزلة برد", 45 },
                    { "صداع", 32 },
                    { "حمى", 28 },
                    { "آلام معدة", 20 }
                },
                VaccinationCoverage = new Dictionary<string, double>
                {
                    { "الإنفلونزا", 85.5 },
                    { "كوفيد-19", 92.3 },
                    { "الحصبة", 98.7 }
                },
                MonthlyVisits = new Dictionary<string, int>
                {
                    { "يناير", 35 },
                    { "فبراير", 42 },
                    { "مارس", 38 },
                    { "أبريل", 29 }
                }
            };

            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting health statistics");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get students with medical conditions
    /// </summary>
    [HttpGet("medical-conditions")]
    [Authorize(Roles = "Admin,Nurse,Doctor,Teacher")]
    public async Task<ActionResult<List<MedicalConditionDto>>> GetStudentsWithMedicalConditions()
    {
        try
        {
            var conditions = new List<MedicalConditionDto>
            {
                new() { Id = 1, StudentId = "student1", StudentName = "أحمد محمد", 
                        Condition = "ربو", Severity = "متوسط", Medications = new List<string> { "بخاخ الربو" }, 
                        EmergencyContact = "والده - **********", SpecialInstructions = "تجنب الأنشطة الشاقة" },
                new() { Id = 2, StudentId = "student2", StudentName = "فاطمة علي", 
                        Condition = "حساسية الطعام", Severity = "عالي", Medications = new List<string> { "إبينفرين" }, 
                        EmergencyContact = "والدتها - **********", SpecialInstructions = "تجنب المكسرات والبيض" }
            };

            return Ok(conditions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting medical conditions");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Create medical condition record
    /// </summary>
    [HttpPost("medical-conditions")]
    [Authorize(Roles = "Admin,Nurse,Doctor")]
    public async Task<ActionResult<MedicalConditionDto>> CreateMedicalCondition([FromBody] CreateMedicalConditionDto createDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var condition = new MedicalConditionDto
            {
                Id = new Random().Next(1000, 9999),
                StudentId = createDto.StudentId,
                StudentName = "أحمد محمد",
                Condition = createDto.Condition,
                Severity = createDto.Severity,
                Medications = createDto.Medications,
                EmergencyContact = createDto.EmergencyContact,
                SpecialInstructions = createDto.SpecialInstructions,
                DiagnosisDate = createDto.DiagnosisDate,
                DoctorName = createDto.DoctorName,
                IsActive = true
            };

            return CreatedAtAction(nameof(GetMedicalCondition), new { id = condition.Id }, condition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating medical condition");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get medical condition by ID
    /// </summary>
    [HttpGet("medical-conditions/{id}")]
    public async Task<ActionResult<MedicalConditionDto>> GetMedicalCondition(int id)
    {
        try
        {
            var condition = new MedicalConditionDto
            {
                Id = id,
                StudentId = "student1",
                StudentName = "أحمد محمد",
                Condition = "ربو",
                Severity = "متوسط",
                Medications = new List<string> { "بخاخ الربو", "مضاد الهيستامين" },
                EmergencyContact = "والده - **********",
                SpecialInstructions = "تجنب الأنشطة الشاقة، يحتاج بخاخ الربو عند الحاجة",
                DiagnosisDate = DateTime.Now.AddYears(-2),
                DoctorName = "د. محمد علي",
                IsActive = true
            };

            return Ok(condition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting medical condition {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Generate health report
    /// </summary>
    [HttpPost("reports")]
    [Authorize(Roles = "Admin,Nurse,Doctor")]
    public async Task<ActionResult<HealthReportDto>> GenerateHealthReport([FromBody] HealthReportRequestDto requestDto)
    {
        try
        {
            var report = new HealthReportDto
            {
                ReportType = requestDto.ReportType,
                GeneratedDate = DateTime.Now,
                StartDate = requestDto.StartDate,
                EndDate = requestDto.EndDate,
                TotalRecords = 125,
                Summary = "تقرير صحي شامل للفترة المحددة",
                Data = new Dictionary<string, object>
                {
                    { "total_visits", 125 },
                    { "common_illnesses", new[] { "نزلة برد", "صداع", "حمى" } },
                    { "vaccination_rate", 89.5 }
                }
            };

            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating health report");
            return StatusCode(500, "Internal server error");
        }
    }
}
