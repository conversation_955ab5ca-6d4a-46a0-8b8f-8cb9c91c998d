using System.ComponentModel.DataAnnotations;
using Schools.Shared.Models;

namespace Schools.Data.Entities
{
    public class EventParticipant
    {
        public int Id { get; set; }

        public int EventId { get; set; }

        public int UserId { get; set; }

        [Required]
        [MaxLength(100)]
        public string UserName { get; set; } = "";

        public DateTime RegistrationDate { get; set; }

        [Required]
        [MaxLength(20)]
        public string Status { get; set; } = "confirmed";

        // Navigation properties
        public virtual Event Event { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
    }
}
