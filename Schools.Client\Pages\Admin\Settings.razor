@page "/admin/settings"
@using Schools.Shared.Models
@using Schools.Shared.DTOs
@using Schools.Client.Services
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@attribute [Authorize(Roles = "Admin")]

<PageTitle>إعدادات النظام</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        إعدادات النظام
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- General Settings -->
                        <div class="col-lg-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-school me-2"></i>
                                        إعدادات المدرسة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <EditForm Model="schoolSettings" OnValidSubmit="SaveSchoolSettings">
                                        <DataAnnotationsValidator />
                                        <div class="mb-3">
                                            <label class="form-label">اسم المدرسة</label>
                                            <InputText class="form-control" @bind-Value="schoolSettings.SchoolName" />
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">عنوان المدرسة</label>
                                            <InputTextArea class="form-control" @bind-Value="schoolSettings.Address" rows="3" />
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف</label>
                                            <InputText class="form-control" @bind-Value="schoolSettings.PhoneNumber" />
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني</label>
                                            <InputText class="form-control" @bind-Value="schoolSettings.Email" />
                                        </div>
                                        <button type="submit" class="btn btn-primary" disabled="@isSavingSchool">
                                            @if (isSavingSchool)
                                            {
                                                <span class="spinner-border spinner-border-sm me-2"></span>
                                            }
                                            حفظ إعدادات المدرسة
                                        </button>
                                    </EditForm>
                                </div>
                            </div>
                        </div>

                        <!-- Academic Settings -->
                        <div class="col-lg-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-graduation-cap me-2"></i>
                                        الإعدادات الأكاديمية
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <EditForm Model="academicSettings" OnValidSubmit="SaveAcademicSettings">
                                        <DataAnnotationsValidator />
                                        <div class="mb-3">
                                            <label class="form-label">مدة الحصة (بالدقائق)</label>
                                            <InputNumber class="form-control" @bind-Value="academicSettings.ClassDuration" />
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">عدد الحصص في اليوم</label>
                                            <InputNumber class="form-control" @bind-Value="academicSettings.ClassesPerDay" />
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">أيام الدراسة في الأسبوع</label>
                                            <InputNumber class="form-control" @bind-Value="academicSettings.StudyDaysPerWeek" />
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">الدرجة النهائية للمادة</label>
                                            <InputNumber class="form-control" @bind-Value="academicSettings.MaxGrade" />
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">درجة النجاح</label>
                                            <InputNumber class="form-control" @bind-Value="academicSettings.PassingGrade" />
                                        </div>
                                        <button type="submit" class="btn btn-success" disabled="@isSavingAcademic">
                                            @if (isSavingAcademic)
                                            {
                                                <span class="spinner-border spinner-border-sm me-2"></span>
                                            }
                                            حفظ الإعدادات الأكاديمية
                                        </button>
                                    </EditForm>
                                </div>
                            </div>
                        </div>

                        <!-- System Settings -->
                        <div class="col-lg-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-server me-2"></i>
                                        إعدادات النظام
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <EditForm Model="systemSettings" OnValidSubmit="SaveSystemSettings">
                                        <DataAnnotationsValidator />
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <InputCheckbox class="form-check-input" @bind-Value="systemSettings.AllowSelfRegistration" />
                                                <label class="form-check-label">
                                                    السماح بالتسجيل الذاتي
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <InputCheckbox class="form-check-input" @bind-Value="systemSettings.RequireEmailVerification" />
                                                <label class="form-check-label">
                                                    تتطلب تأكيد البريد الإلكتروني
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <InputCheckbox class="form-check-input" @bind-Value="systemSettings.EnableNotifications" />
                                                <label class="form-check-label">
                                                    تفعيل الإشعارات
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">مهلة انتهاء الجلسة (بالدقائق)</label>
                                            <InputNumber class="form-control" @bind-Value="systemSettings.SessionTimeout" />
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">الحد الأقصى لحجم الملف (MB)</label>
                                            <InputNumber class="form-control" @bind-Value="systemSettings.MaxFileSize" />
                                        </div>
                                        <button type="submit" class="btn btn-info" disabled="@isSavingSystem">
                                            @if (isSavingSystem)
                                            {
                                                <span class="spinner-border spinner-border-sm me-2"></span>
                                            }
                                            حفظ إعدادات النظام
                                        </button>
                                    </EditForm>
                                </div>
                            </div>
                        </div>

                        <!-- Backup & Maintenance -->
                        <div class="col-lg-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">
                                        <i class="fas fa-tools me-2"></i>
                                        النسخ الاحتياطي والصيانة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <h6>النسخ الاحتياطي</h6>
                                        <p class="text-muted small">إنشاء نسخة احتياطية من قاعدة البيانات</p>
                                        <button class="btn btn-outline-primary" @onclick="CreateBackup" disabled="@isCreatingBackup">
                                            @if (isCreatingBackup)
                                            {
                                                <span class="spinner-border spinner-border-sm me-2"></span>
                                            }
                                            <i class="fas fa-download me-2"></i>
                                            إنشاء نسخة احتياطية
                                        </button>
                                    </div>
                                    <hr>
                                    <div class="mb-3">
                                        <h6>تنظيف النظام</h6>
                                        <p class="text-muted small">حذف الملفات المؤقتة والسجلات القديمة</p>
                                        <button class="btn btn-outline-warning" @onclick="CleanupSystem" disabled="@isCleaningUp">
                                            @if (isCleaningUp)
                                            {
                                                <span class="spinner-border spinner-border-sm me-2"></span>
                                            }
                                            <i class="fas fa-broom me-2"></i>
                                            تنظيف النظام
                                        </button>
                                    </div>
                                    <hr>
                                    <div class="mb-3">
                                        <h6>إعادة تشغيل النظام</h6>
                                        <p class="text-muted small">إعادة تشغيل الخدمات (استخدم بحذر)</p>
                                        <button class="btn btn-outline-danger" @onclick="RestartSystem" disabled="@isRestarting">
                                            @if (isRestarting)
                                            {
                                                <span class="spinner-border spinner-border-sm me-2"></span>
                                            }
                                            <i class="fas fa-redo me-2"></i>
                                            إعادة تشغيل
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private SchoolSettingsDto schoolSettings = new();
    private AcademicSettingsDto academicSettings = new();
    private SystemSettingsDto systemSettings = new();
    
    private bool isSavingSchool = false;
    private bool isSavingAcademic = false;
    private bool isSavingSystem = false;
    private bool isCreatingBackup = false;
    private bool isCleaningUp = false;
    private bool isRestarting = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadSettings();
    }

    private async Task LoadSettings()
    {
        try
        {
            // Load settings from API (mock data for now)
            schoolSettings = new SchoolSettingsDto
            {
                SchoolName = "مدرسة المستقبل النموذجية",
                Address = "الرياض، المملكة العربية السعودية",
                PhoneNumber = "+966 11 123 4567",
                Email = "<EMAIL>"
            };

            academicSettings = new AcademicSettingsDto
            {
                ClassDuration = 45,
                ClassesPerDay = 7,
                StudyDaysPerWeek = 5,
                MaxGrade = 100,
                PassingGrade = 50
            };

            systemSettings = new SystemSettingsDto
            {
                AllowSelfRegistration = true,
                RequireEmailVerification = false,
                EnableNotifications = true,
                SessionTimeout = 30,
                MaxFileSize = 10
            };
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الإعدادات: {ex.Message}");
        }
    }

    private async Task SaveSchoolSettings()
    {
        try
        {
            isSavingSchool = true;
            // Save to API
            await Task.Delay(1000); // Simulate API call
            await JSRuntime.InvokeVoidAsync("alert", "تم حفظ إعدادات المدرسة بنجاح");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ إعدادات المدرسة: {ex.Message}");
        }
        finally
        {
            isSavingSchool = false;
        }
    }

    private async Task SaveAcademicSettings()
    {
        try
        {
            isSavingAcademic = true;
            // Save to API
            await Task.Delay(1000); // Simulate API call
            await JSRuntime.InvokeVoidAsync("alert", "تم حفظ الإعدادات الأكاديمية بنجاح");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ الإعدادات الأكاديمية: {ex.Message}");
        }
        finally
        {
            isSavingAcademic = false;
        }
    }

    private async Task SaveSystemSettings()
    {
        try
        {
            isSavingSystem = true;
            // Save to API
            await Task.Delay(1000); // Simulate API call
            await JSRuntime.InvokeVoidAsync("alert", "تم حفظ إعدادات النظام بنجاح");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ إعدادات النظام: {ex.Message}");
        }
        finally
        {
            isSavingSystem = false;
        }
    }

    private async Task CreateBackup()
    {
        try
        {
            isCreatingBackup = true;
            await Task.Delay(3000); // Simulate backup creation
            await JSRuntime.InvokeVoidAsync("alert", "تم إنشاء النسخة الاحتياطية بنجاح");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}");
        }
        finally
        {
            isCreatingBackup = false;
        }
    }

    private async Task CleanupSystem()
    {
        try
        {
            isCleaningUp = true;
            await Task.Delay(2000); // Simulate cleanup
            await JSRuntime.InvokeVoidAsync("alert", "تم تنظيف النظام بنجاح");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تنظيف النظام: {ex.Message}");
        }
        finally
        {
            isCleaningUp = false;
        }
    }

    private async Task RestartSystem()
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من إعادة تشغيل النظام؟ سيتم قطع الاتصال مؤقتاً."))
        {
            try
            {
                isRestarting = true;
                await Task.Delay(1000); // Simulate restart
                await JSRuntime.InvokeVoidAsync("alert", "سيتم إعادة تشغيل النظام خلال دقائق قليلة");
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في إعادة تشغيل النظام: {ex.Message}");
            }
            finally
            {
                isRestarting = false;
            }
        }
    }

    public class SchoolSettingsDto
    {
        public string SchoolName { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
    }

    public class AcademicSettingsDto
    {
        public int ClassDuration { get; set; }
        public int ClassesPerDay { get; set; }
        public int StudyDaysPerWeek { get; set; }
        public int MaxGrade { get; set; }
        public int PassingGrade { get; set; }
    }

    public class SystemSettingsDto
    {
        public bool AllowSelfRegistration { get; set; }
        public bool RequireEmailVerification { get; set; }
        public bool EnableNotifications { get; set; }
        public int SessionTimeout { get; set; }
        public int MaxFileSize { get; set; }
    }
}
