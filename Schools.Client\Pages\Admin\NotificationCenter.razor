@page "/admin/notifications"
@using Schools.Shared.DTOs
@using Schools.Client.Services
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-bell me-2"></i>
                        مركز الإشعارات
                    </h4>
                </div>

                <div class="card-body">
                    <!-- Quick Actions -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <button class="btn btn-success w-100 h-100 py-3" @onclick="ShowSendNotificationModal">
                                <i class="fas fa-paper-plane fa-2x mb-2"></i>
                                <br>إرسال إشعار جديد
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-info w-100 h-100 py-3" @onclick="SendBulkNotification">
                                <i class="fas fa-bullhorn fa-2x mb-2"></i>
                                <br>إشعار جماعي
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-warning w-100 h-100 py-3" @onclick="ScheduleNotification">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <br>جدولة إشعار
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-secondary w-100 h-100 py-3" @onclick="ViewTemplates">
                                <i class="fas fa-file-alt fa-2x mb-2"></i>
                                <br>قوالب الإشعارات
                            </button>
                        </div>
                    </div>

                    <!-- Notification Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-envelope fa-2x mb-2"></i>
                                    <h4>@totalNotifications</h4>
                                    <p class="mb-0">إجمالي الإشعارات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                                    <h4>@sentNotifications</h4>
                                    <p class="mb-0">تم الإرسال</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-clock fa-2x mb-2"></i>
                                    <h4>@pendingNotifications</h4>
                                    <p class="mb-0">في الانتظار</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-eye fa-2x mb-2"></i>
                                    <h4>@readNotifications</h4>
                                    <p class="mb-0">تم القراءة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <label class="form-label">نوع الإشعار</label>
                            <select class="form-select" @bind="selectedType" @bind:after="FilterNotifications">
                                <option value="">جميع الأنواع</option>
                                <option value="General">عام</option>
                                <option value="Academic">أكاديمي</option>
                                <option value="Attendance">حضور</option>
                                <option value="Grades">درجات</option>
                                <option value="Emergency">طوارئ</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" @bind="selectedStatus" @bind:after="FilterNotifications">
                                <option value="">جميع الحالات</option>
                                <option value="Sent">تم الإرسال</option>
                                <option value="Pending">في الانتظار</option>
                                <option value="Failed">فشل</option>
                                <option value="Scheduled">مجدول</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">المستلم</label>
                            <select class="form-select" @bind="selectedRecipient" @bind:after="FilterNotifications">
                                <option value="">جميع المستلمين</option>
                                <option value="Students">الطلاب</option>
                                <option value="Teachers">المعلمين</option>
                                <option value="Parents">أولياء الأمور</option>
                                <option value="Staff">الموظفين</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">البحث</label>
                            <input type="text" class="form-control" @bind="searchTerm" @bind:after="FilterNotifications" placeholder="ابحث في الإشعارات..." />
                        </div>
                    </div>

                    <!-- Notifications List -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-list me-2"></i>
                                        قائمة الإشعارات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    @if (isLoading)
                                    {
                                        <div class="text-center py-5">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">جاري التحميل...</span>
                                            </div>
                                        </div>
                                    }
                                    else if (filteredNotifications?.Any() == true)
                                    {
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead class="table-dark">
                                                    <tr>
                                                        <th>العنوان</th>
                                                        <th>النوع</th>
                                                        <th>المستلم</th>
                                                        <th>تاريخ الإرسال</th>
                                                        <th>الحالة</th>
                                                        <th>معدل القراءة</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach (var notification in filteredNotifications.Take(20))
                                                    {
                                                        <tr>
                                                            <td>
                                                                <strong>@notification.Title</strong>
                                                                <br>
                                                                <small class="text-muted">@notification.Message.Substring(0, Math.Min(50, notification.Message.Length))...</small>
                                                            </td>
                                                            <td>
                                                                <span class="badge @GetTypeClass(notification.Type)">
                                                                    @GetTypeText(notification.Type)
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <span class="badge bg-info">@notification.RecipientType</span>
                                                                <br>
                                                                <small class="text-muted">@notification.RecipientCount مستلم</small>
                                                            </td>
                                                            <td>@notification.SentDate.ToString("yyyy-MM-dd HH:mm")</td>
                                                            <td>
                                                                <span class="badge @GetStatusClass(notification.Status)">
                                                                    @GetStatusText(notification.Status)
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <div class="progress" style="height: 20px;">
                                                                    <div class="progress-bar bg-success"
                                                                         style="width: @notification.ReadRate%">
                                                                        @notification.ReadRate%
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <div class="btn-group btn-group-sm">
                                                                    <button class="btn btn-outline-primary" @onclick="@(() => ViewNotification(notification))">
                                                                        <i class="fas fa-eye"></i>
                                                                    </button>
                                                                    <button class="btn btn-outline-success" @onclick="@(() => ResendNotification(notification))">
                                                                        <i class="fas fa-redo"></i>
                                                                    </button>
                                                                    <button class="btn btn-outline-danger" @onclick="@(() => DeleteNotification(notification))">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="text-center py-5">
                                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">لا توجد إشعارات</h5>
                                            <p class="text-muted">لم يتم العثور على إشعارات تطابق المعايير المحددة</p>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-history me-2"></i>
                                        النشاط الأخير
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="timeline">
                                        @foreach (var activity in recentActivity.Take(5))
                                        {
                                            <div class="timeline-item">
                                                <div class="timeline-marker @GetActivityColor(activity.Type)">
                                                    <i class="@GetActivityIcon(activity.Type)"></i>
                                                </div>
                                                <div class="timeline-content">
                                                    <h6 class="mb-1">@activity.Title</h6>
                                                    <p class="mb-1 text-muted">@activity.Description</p>
                                                    <small class="text-muted">
                                                        <i class="fas fa-clock me-1"></i>
                                                        @activity.Timestamp.ToString("yyyy-MM-dd HH:mm")
                                                    </small>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }

    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 0;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
    }

    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #007bff;
    }
</style>

@code {
    private bool isLoading = true;

    // Statistics
    private int totalNotifications = 0;
    private int sentNotifications = 0;
    private int pendingNotifications = 0;
    private int readNotifications = 0;

    // Filters
    private string selectedType = "";
    private string selectedStatus = "";
    private string selectedRecipient = "";
    private string searchTerm = "";

    // Data
    private List<NotificationItem> notifications = new();
    private List<NotificationItem> filteredNotifications = new();
    private List<ActivityItem> recentActivity = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadNotifications();
        GenerateRecentActivity();
    }

    private async Task LoadNotifications()
    {
        try
        {
            isLoading = true;

            // Mock notification data (in real app, this would come from API)
            notifications = new List<NotificationItem>
            {
                new() { Id = 1, Title = "بداية العام الدراسي", Message = "نرحب بجميع الطلاب في العام الدراسي الجديد", Type = "General", RecipientType = "Students", RecipientCount = 150, SentDate = DateTime.Now.AddDays(-5), Status = "Sent", ReadRate = 85 },
                new() { Id = 2, Title = "موعد الامتحانات النهائية", Message = "تبدأ الامتحانات النهائية يوم الأحد القادم", Type = "Academic", RecipientType = "Students", RecipientCount = 150, SentDate = DateTime.Now.AddDays(-3), Status = "Sent", ReadRate = 92 },
                new() { Id = 3, Title = "اجتماع أولياء الأمور", Message = "يرجى حضور اجتماع أولياء الأمور يوم الخميس", Type = "General", RecipientType = "Parents", RecipientCount = 120, SentDate = DateTime.Now.AddDays(-2), Status = "Sent", ReadRate = 78 },
                new() { Id = 4, Title = "تحديث درجات الطلاب", Message = "تم تحديث درجات امتحان الرياضيات", Type = "Grades", RecipientType = "Students", RecipientCount = 30, SentDate = DateTime.Now.AddDays(-1), Status = "Sent", ReadRate = 95 },
                new() { Id = 5, Title = "تنبيه غياب", Message = "تنبيه بخصوص غياب الطالب أحمد محمد", Type = "Attendance", RecipientType = "Parents", RecipientCount = 1, SentDate = DateTime.Now.AddHours(-2), Status = "Sent", ReadRate = 100 },
                new() { Id = 6, Title = "إشعار طوارئ", Message = "تأجيل الدوام بسبب الأحوال الجوية", Type = "Emergency", RecipientType = "All", RecipientCount = 300, SentDate = DateTime.Now.AddHours(-1), Status = "Pending", ReadRate = 0 }
            };

            // Calculate statistics
            totalNotifications = notifications.Count;
            sentNotifications = notifications.Count(n => n.Status == "Sent");
            pendingNotifications = notifications.Count(n => n.Status == "Pending");
            readNotifications = notifications.Where(n => n.Status == "Sent").Sum(n => (int)(n.RecipientCount * n.ReadRate / 100));

            FilterNotifications();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الإشعارات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void FilterNotifications()
    {
        filteredNotifications = notifications.Where(n =>
            (string.IsNullOrEmpty(selectedType) || n.Type == selectedType) &&
            (string.IsNullOrEmpty(selectedStatus) || n.Status == selectedStatus) &&
            (string.IsNullOrEmpty(selectedRecipient) || n.RecipientType == selectedRecipient) &&
            (string.IsNullOrEmpty(searchTerm) || n.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) || n.Message.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
        ).OrderByDescending(n => n.SentDate).ToList();

        StateHasChanged();
    }

    private void GenerateRecentActivity()
    {
        recentActivity = new List<ActivityItem>
        {
            new() { Type = "send", Title = "إرسال إشعار جماعي", Description = "تم إرسال إشعار لجميع الطلاب بخصوص الامتحانات", Timestamp = DateTime.Now.AddMinutes(-15) },
            new() { Type = "schedule", Title = "جدولة إشعار", Description = "تم جدولة إشعار تذكير بموعد الرسوم", Timestamp = DateTime.Now.AddMinutes(-30) },
            new() { Type = "template", Title = "إنشاء قالب جديد", Description = "تم إنشاء قالب جديد للإشعارات الأكاديمية", Timestamp = DateTime.Now.AddHours(-1) },
            new() { Type = "read", Title = "قراءة إشعار", Description = "تم قراءة إشعار الغياب من قبل ولي الأمر", Timestamp = DateTime.Now.AddHours(-2) },
            new() { Type = "failed", Title = "فشل إرسال", Description = "فشل في إرسال إشعار لعدم توفر بريد إلكتروني", Timestamp = DateTime.Now.AddHours(-3) }
        };
    }

    private async Task ShowSendNotificationModal()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم فتح نافذة إرسال إشعار جديد");
    }

    private async Task SendBulkNotification()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم فتح نافذة الإشعار الجماعي");
    }

    private async Task ScheduleNotification()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم فتح نافذة جدولة الإشعارات");
    }

    private async Task ViewTemplates()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم فتح صفحة قوالب الإشعارات");
    }

    private async Task ViewNotification(NotificationItem notification)
    {
        await JSRuntime.InvokeVoidAsync("alert", $"عرض تفاصيل الإشعار: {notification.Title}");
    }

    private async Task ResendNotification(NotificationItem notification)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"هل تريد إعادة إرسال الإشعار: {notification.Title}؟");
        if (confirmed)
        {
            await JSRuntime.InvokeVoidAsync("alert", "تم إعادة إرسال الإشعار بنجاح");
        }
    }

    private async Task DeleteNotification(NotificationItem notification)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"هل تريد حذف الإشعار: {notification.Title}؟");
        if (confirmed)
        {
            notifications.Remove(notification);
            FilterNotifications();
            await JSRuntime.InvokeVoidAsync("alert", "تم حذف الإشعار بنجاح");
        }
    }

    private string GetTypeClass(string type)
    {
        return type switch
        {
            "General" => "bg-primary",
            "Academic" => "bg-success",
            "Attendance" => "bg-warning",
            "Grades" => "bg-info",
            "Emergency" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetTypeText(string type)
    {
        return type switch
        {
            "General" => "عام",
            "Academic" => "أكاديمي",
            "Attendance" => "حضور",
            "Grades" => "درجات",
            "Emergency" => "طوارئ",
            _ => type
        };
    }

    private string GetStatusClass(string status)
    {
        return status switch
        {
            "Sent" => "bg-success",
            "Pending" => "bg-warning",
            "Failed" => "bg-danger",
            "Scheduled" => "bg-info",
            _ => "bg-secondary"
        };
    }

    private string GetStatusText(string status)
    {
        return status switch
        {
            "Sent" => "تم الإرسال",
            "Pending" => "في الانتظار",
            "Failed" => "فشل",
            "Scheduled" => "مجدول",
            _ => status
        };
    }

    private string GetActivityIcon(string type)
    {
        return type switch
        {
            "send" => "fas fa-paper-plane",
            "schedule" => "fas fa-clock",
            "template" => "fas fa-file-alt",
            "read" => "fas fa-eye",
            "failed" => "fas fa-exclamation-triangle",
            _ => "fas fa-info"
        };
    }

    private string GetActivityColor(string type)
    {
        return type switch
        {
            "send" => "bg-success",
            "schedule" => "bg-info",
            "template" => "bg-primary",
            "read" => "bg-warning",
            "failed" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    public class NotificationItem
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string Message { get; set; } = "";
        public string Type { get; set; } = "";
        public string RecipientType { get; set; } = "";
        public int RecipientCount { get; set; }
        public DateTime SentDate { get; set; }
        public string Status { get; set; } = "";
        public int ReadRate { get; set; }
    }

    public class ActivityItem
    {
        public string Type { get; set; } = "";
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public DateTime Timestamp { get; set; }
    }
}
