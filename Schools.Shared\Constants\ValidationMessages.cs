namespace Schools.Shared.Constants
{
    /// <summary>
    /// Validation Messages for the School Management System
    /// Contains all validation error messages in Arabic and English
    /// </summary>
    public static class ValidationMessages
    {
        // General Messages
        public static class General
        {
            public const string Required_AR = "هذا الحقل مطلوب";
            public const string Required_EN = "This field is required";
            
            public const string InvalidFormat_AR = "تنسيق البيانات غير صحيح";
            public const string InvalidFormat_EN = "Invalid data format";
            
            public const string NotFound_AR = "العنصر غير موجود";
            public const string NotFound_EN = "Item not found";
            
            public const string AccessDenied_AR = "ليس لديك صلاحية للوصول";
            public const string AccessDenied_EN = "Access denied";
            
            public const string OperationFailed_AR = "فشلت العملية";
            public const string OperationFailed_EN = "Operation failed";
            
            public const string OperationSuccessful_AR = "تمت العملية بنجاح";
            public const string OperationSuccessful_EN = "Operation completed successfully";
        }
        
        // Authentication Messages
        public static class Authentication
        {
            public const string InvalidCredentials_AR = "بيانات الدخول غير صحيحة";
            public const string InvalidCredentials_EN = "Invalid credentials";
            
            public const string AccountLocked_AR = "الحساب مقفل";
            public const string AccountLocked_EN = "Account is locked";
            
            public const string AccountNotActivated_AR = "الحساب غير مفعل";
            public const string AccountNotActivated_EN = "Account is not activated";
            
            public const string TokenExpired_AR = "انتهت صلاحية الجلسة";
            public const string TokenExpired_EN = "Session expired";
            
            public const string LoginSuccessful_AR = "تم تسجيل الدخول بنجاح";
            public const string LoginSuccessful_EN = "Login successful";
            
            public const string LogoutSuccessful_AR = "تم تسجيل الخروج بنجاح";
            public const string LogoutSuccessful_EN = "Logout successful";
        }
        
        // Email Validation
        public static class Email
        {
            public const string InvalidEmail_AR = "البريد الإلكتروني غير صحيح";
            public const string InvalidEmail_EN = "Invalid email address";
            
            public const string EmailRequired_AR = "البريد الإلكتروني مطلوب";
            public const string EmailRequired_EN = "Email is required";
            
            public const string EmailExists_AR = "البريد الإلكتروني مستخدم بالفعل";
            public const string EmailExists_EN = "Email already exists";
            
            public const string EmailTooLong_AR = "البريد الإلكتروني طويل جداً";
            public const string EmailTooLong_EN = "Email is too long";
        }
        
        // Phone Validation
        public static class Phone
        {
            public const string InvalidPhone_AR = "رقم الهاتف غير صحيح";
            public const string InvalidPhone_EN = "Invalid phone number";
            
            public const string PhoneRequired_AR = "رقم الهاتف مطلوب";
            public const string PhoneRequired_EN = "Phone number is required";
            
            public const string PhoneTooShort_AR = "رقم الهاتف قصير جداً";
            public const string PhoneTooShort_EN = "Phone number is too short";
            
            public const string PhoneTooLong_AR = "رقم الهاتف طويل جداً";
            public const string PhoneTooLong_EN = "Phone number is too long";
        }
        
        // Password Validation
        public static class Password
        {
            public const string PasswordRequired_AR = "كلمة المرور مطلوبة";
            public const string PasswordRequired_EN = "Password is required";
            
            public const string PasswordTooShort_AR = "كلمة المرور قصيرة جداً (الحد الأدنى 6 أحرف)";
            public const string PasswordTooShort_EN = "Password is too short (minimum 6 characters)";
            
            public const string PasswordTooLong_AR = "كلمة المرور طويلة جداً";
            public const string PasswordTooLong_EN = "Password is too long";
            
            public const string PasswordMismatch_AR = "كلمات المرور غير متطابقة";
            public const string PasswordMismatch_EN = "Passwords do not match";
            
            public const string PasswordWeak_AR = "كلمة المرور ضعيفة";
            public const string PasswordWeak_EN = "Password is weak";
            
            public const string CurrentPasswordIncorrect_AR = "كلمة المرور الحالية غير صحيحة";
            public const string CurrentPasswordIncorrect_EN = "Current password is incorrect";
        }
        
        // Date Validation
        public static class Date
        {
            public const string InvalidDate_AR = "التاريخ غير صحيح";
            public const string InvalidDate_EN = "Invalid date";
            
            public const string DateRequired_AR = "التاريخ مطلوب";
            public const string DateRequired_EN = "Date is required";
            
            public const string DateInPast_AR = "التاريخ في الماضي";
            public const string DateInPast_EN = "Date is in the past";
            
            public const string DateInFuture_AR = "التاريخ في المستقبل";
            public const string DateInFuture_EN = "Date is in the future";
            
            public const string StartDateAfterEndDate_AR = "تاريخ البداية بعد تاريخ النهاية";
            public const string StartDateAfterEndDate_EN = "Start date is after end date";
        }
        
        // Amount/Number Validation
        public static class Amount
        {
            public const string InvalidAmount_AR = "المبلغ غير صحيح";
            public const string InvalidAmount_EN = "Invalid amount";
            
            public const string AmountRequired_AR = "المبلغ مطلوب";
            public const string AmountRequired_EN = "Amount is required";
            
            public const string AmountTooLow_AR = "المبلغ منخفض جداً";
            public const string AmountTooLow_EN = "Amount is too low";
            
            public const string AmountTooHigh_AR = "المبلغ مرتفع جداً";
            public const string AmountTooHigh_EN = "Amount is too high";
            
            public const string NegativeAmount_AR = "المبلغ لا يمكن أن يكون سالباً";
            public const string NegativeAmount_EN = "Amount cannot be negative";
            
            public const string InvalidNumber_AR = "الرقم غير صحيح";
            public const string InvalidNumber_EN = "Invalid number";
        }
        
        // File Validation
        public static class File
        {
            public const string FileRequired_AR = "الملف مطلوب";
            public const string FileRequired_EN = "File is required";
            
            public const string FileTooLarge_AR = "حجم الملف كبير جداً";
            public const string FileTooLarge_EN = "File size is too large";
            
            public const string InvalidFileType_AR = "نوع الملف غير مدعوم";
            public const string InvalidFileType_EN = "File type is not supported";
            
            public const string FileUploadFailed_AR = "فشل في رفع الملف";
            public const string FileUploadFailed_EN = "File upload failed";
        }
        
        // Student Validation
        public static class Student
        {
            public const string StudentNotFound_AR = "الطالب غير موجود";
            public const string StudentNotFound_EN = "Student not found";
            
            public const string StudentNumberExists_AR = "رقم الطالب موجود بالفعل";
            public const string StudentNumberExists_EN = "Student number already exists";
            
            public const string StudentNumberRequired_AR = "رقم الطالب مطلوب";
            public const string StudentNumberRequired_EN = "Student number is required";
            
            public const string InvalidStudentNumber_AR = "رقم الطالب غير صحيح";
            public const string InvalidStudentNumber_EN = "Invalid student number";
        }
        
        // Teacher Validation
        public static class Teacher
        {
            public const string TeacherNotFound_AR = "المعلم غير موجود";
            public const string TeacherNotFound_EN = "Teacher not found";
            
            public const string EmployeeNumberExists_AR = "رقم الموظف موجود بالفعل";
            public const string EmployeeNumberExists_EN = "Employee number already exists";
            
            public const string EmployeeNumberRequired_AR = "رقم الموظف مطلوب";
            public const string EmployeeNumberRequired_EN = "Employee number is required";
        }
        
        // Academic Validation
        public static class Academic
        {
            public const string GradeRequired_AR = "الصف مطلوب";
            public const string GradeRequired_EN = "Grade is required";
            
            public const string ClassRequired_AR = "الفصل مطلوب";
            public const string ClassRequired_EN = "Class is required";
            
            public const string SubjectRequired_AR = "المادة مطلوبة";
            public const string SubjectRequired_EN = "Subject is required";
            
            public const string AcademicYearRequired_AR = "السنة الدراسية مطلوبة";
            public const string AcademicYearRequired_EN = "Academic year is required";
            
            public const string InvalidGrade_AR = "الدرجة غير صحيحة";
            public const string InvalidGrade_EN = "Invalid grade";
            
            public const string GradeOutOfRange_AR = "الدرجة خارج النطاق المسموح";
            public const string GradeOutOfRange_EN = "Grade is out of allowed range";
        }
        
        // Exam Validation
        public static class Exam
        {
            public const string ExamNotFound_AR = "الامتحان غير موجود";
            public const string ExamNotFound_EN = "Exam not found";
            
            public const string ExamTitleRequired_AR = "عنوان الامتحان مطلوب";
            public const string ExamTitleRequired_EN = "Exam title is required";
            
            public const string ExamAlreadyStarted_AR = "الامتحان بدأ بالفعل";
            public const string ExamAlreadyStarted_EN = "Exam has already started";
            
            public const string ExamNotStarted_AR = "الامتحان لم يبدأ بعد";
            public const string ExamNotStarted_EN = "Exam has not started yet";
            
            public const string ExamExpired_AR = "انتهت مدة الامتحان";
            public const string ExamExpired_EN = "Exam time has expired";
            
            public const string MaxAttemptsReached_AR = "تم الوصول للحد الأقصى من المحاولات";
            public const string MaxAttemptsReached_EN = "Maximum attempts reached";
        }
        
        // Financial Validation
        public static class Financial
        {
            public const string InsufficientBalance_AR = "الرصيد غير كافي";
            public const string InsufficientBalance_EN = "Insufficient balance";
            
            public const string PaymentMethodRequired_AR = "طريقة الدفع مطلوبة";
            public const string PaymentMethodRequired_EN = "Payment method is required";
            
            public const string InvalidPaymentMethod_AR = "طريقة الدفع غير صحيحة";
            public const string InvalidPaymentMethod_EN = "Invalid payment method";
            
            public const string PaymentFailed_AR = "فشل في الدفع";
            public const string PaymentFailed_EN = "Payment failed";
            
            public const string PaymentSuccessful_AR = "تم الدفع بنجاح";
            public const string PaymentSuccessful_EN = "Payment successful";
            
            public const string VoucherNotFound_AR = "السند غير موجود";
            public const string VoucherNotFound_EN = "Voucher not found";
            
            public const string AccountNotFound_AR = "الحساب غير موجود";
            public const string AccountNotFound_EN = "Account not found";
        }
        
        // System Messages
        public static class System
        {
            public const string SystemError_AR = "خطأ في النظام";
            public const string SystemError_EN = "System error";
            
            public const string DatabaseError_AR = "خطأ في قاعدة البيانات";
            public const string DatabaseError_EN = "Database error";
            
            public const string NetworkError_AR = "خطأ في الشبكة";
            public const string NetworkError_EN = "Network error";
            
            public const string ServiceUnavailable_AR = "الخدمة غير متاحة";
            public const string ServiceUnavailable_EN = "Service unavailable";
            
            public const string MaintenanceMode_AR = "النظام في وضع الصيانة";
            public const string MaintenanceMode_EN = "System is in maintenance mode";
        }
    }
}
