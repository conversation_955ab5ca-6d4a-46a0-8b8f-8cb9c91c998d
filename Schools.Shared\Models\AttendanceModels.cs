using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Schools.Shared.Models
{
    // Attendance Record Entity - Enhanced version for detailed tracking
    public class AttendanceRecord : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public DateTime Date { get; set; }

        public int? ClassId { get; set; }

        public int? SubjectId { get; set; }

        public int? ScheduleId { get; set; }

        [Required]
        public AttendanceStatus Status { get; set; } = AttendanceStatus.Present;

        public bool IsPresent { get; set; } = true;

        public bool IsLate { get; set; } = false;

        public bool IsExcused { get; set; } = false;

        public TimeSpan? CheckInTime { get; set; }

        public TimeSpan? CheckOutTime { get; set; }

        public TimeSpan? ArrivalTime { get; set; }

        public TimeSpan? DepartureTime { get; set; }

        [StringLength(500)]
        public string? Reason { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public string? RecordedBy { get; set; }

        public DateTime? RecordedAt { get; set; }

        public string? ModifiedBy { get; set; }

        public DateTime? ModifiedAt { get; set; }

        // Temperature check (for health protocols)
        [Column(TypeName = "decimal(4,1)")]
        public decimal? Temperature { get; set; }

        // Health status
        public bool HealthCheckPassed { get; set; } = true;

        [StringLength(500)]
        public string? HealthNotes { get; set; }

        // Location tracking
        [StringLength(200)]
        public string? CheckInLocation { get; set; }

        [StringLength(200)]
        public string? CheckOutLocation { get; set; }

        // Device information
        [StringLength(100)]
        public string? DeviceId { get; set; }

        [StringLength(50)]
        public string? DeviceType { get; set; }

        // IP Address for security
        [StringLength(45)]
        public string? IpAddress { get; set; }

        // Geolocation
        [Column(TypeName = "decimal(10,8)")]
        public decimal? Latitude { get; set; }

        [Column(TypeName = "decimal(11,8)")]
        public decimal? Longitude { get; set; }

        // Academic period
        public int? AcademicYearId { get; set; }

        public int? SemesterId { get; set; }

        // Notification flags
        public bool ParentNotified { get; set; } = false;

        public DateTime? ParentNotificationSent { get; set; }

        public bool RequiresFollowUp { get; set; } = false;

        public DateTime? FollowUpDate { get; set; }

        [StringLength(1000)]
        public string? FollowUpNotes { get; set; }

        // Attendance method
        public AttendanceMethod Method { get; set; } = AttendanceMethod.Manual;

        // Verification status
        public bool IsVerified { get; set; } = false;

        public string? VerifiedBy { get; set; }

        public DateTime? VerifiedAt { get; set; }

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual Class? Class { get; set; }
        public virtual Subject? Subject { get; set; }
        public virtual Schedule? Schedule { get; set; }
        public virtual AcademicYear? AcademicYear { get; set; }
        public virtual ApplicationUser? RecordedByUser { get; set; }
        public virtual ApplicationUser? ModifiedByUser { get; set; }
        public virtual ApplicationUser? VerifiedByUser { get; set; }
    }

    // Attendance Summary for quick reporting
    public class AttendanceSummary : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int AcademicYearId { get; set; }

        public int? SemesterId { get; set; }

        public int? ClassId { get; set; }

        public int TotalDays { get; set; } = 0;

        public int PresentDays { get; set; } = 0;

        public int AbsentDays { get; set; } = 0;

        public int LateDays { get; set; } = 0;

        public int ExcusedDays { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal AttendanceRate { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal LateRate { get; set; } = 0;

        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual AcademicYear AcademicYear { get; set; } = null!;
        public virtual Class? Class { get; set; }
    }

    // Attendance Alert for notifications
    public class AttendanceAlert : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public AttendanceAlertType AlertType { get; set; }

        [Required]
        public AttendanceAlertPriority Priority { get; set; }

        [Required]
        [StringLength(500)]
        public string Message { get; set; } = string.Empty;

        [Required]
        public DateTime TriggerDate { get; set; }

        public bool IsResolved { get; set; } = false;

        public DateTime? ResolvedDate { get; set; }

        public string? ResolvedBy { get; set; }

        [StringLength(1000)]
        public string? ResolutionNotes { get; set; }

        public bool NotificationSent { get; set; } = false;

        public DateTime? NotificationSentAt { get; set; }

        // Threshold values that triggered the alert
        public int? ConsecutiveAbsences { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? AttendanceRateThreshold { get; set; }

        public int? LateCountThreshold { get; set; }

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual ApplicationUser? ResolvedByUser { get; set; }
    }

    // Enums for Attendance
    public enum AttendanceMethod
    {
        Manual = 1,
        Biometric = 2,
        QRCode = 3,
        RFID = 4,
        Mobile = 5,
        Web = 6,
        Import = 7
    }

    public enum AttendanceAlertType
    {
        ConsecutiveAbsences = 1,
        LowAttendanceRate = 2,
        FrequentLateness = 3,
        UnexcusedAbsences = 4,
        HealthConcern = 5,
        PatternChange = 6
    }

    public enum AttendanceAlertPriority
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }
}
