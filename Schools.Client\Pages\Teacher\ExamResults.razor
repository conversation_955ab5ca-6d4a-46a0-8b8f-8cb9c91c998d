@page "/teacher/exams/{ExamId:int}/results"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Teacher")]

<PageTitle>نتائج الامتحان - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/teacher/exams">الامتحانات</a></li>
                    <li class="breadcrumb-item active">نتائج الامتحان</li>
                </ol>
            </nav>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل نتائج الامتحان...</p>
        </div>
    }
    else if (exam != null)
    {
        <!-- Exam Info Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-gradient-primary text-white border-0 shadow-lg">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="mb-1">@exam.Title</h2>
                                <p class="mb-0 opacity-75">@exam.Description</p>
                                <div class="mt-2">
                                    <span class="badge bg-light text-dark me-2">@exam.SubjectName</span>
                                    <span class="badge bg-light text-dark me-2">@exam.ClassName</span>
                                    <span class="badge bg-light text-dark">@exam.TotalMarks درجة</span>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-light" @onclick="ExportResults">
                                        <i class="fas fa-download me-2"></i>
                                        تصدير النتائج
                                    </button>
                                    <button class="btn btn-light" @onclick="RefreshResults">
                                        <i class="fas fa-sync-alt me-2"></i>
                                        تحديث
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-primary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-users fa-lg"></i>
                        </div>
                        <h4 class="text-primary mb-1">@statistics.TotalAttempts</h4>
                        <p class="text-muted mb-0">إجمالي المحاولات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-success text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-check-circle fa-lg"></i>
                        </div>
                        <h4 class="text-success mb-1">@statistics.TotalPassed</h4>
                        <p class="text-muted mb-0">ناجح</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-info text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-star fa-lg"></i>
                        </div>
                        <h4 class="text-info mb-1">@statistics.AverageScore.ToString("F1")</h4>
                        <p class="text-muted mb-0">المتوسط العام</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="avatar-md bg-warning text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                            <i class="fas fa-percentage fa-lg"></i>
                        </div>
                        <h4 class="text-warning mb-1">@statistics.PassRate.ToString("F1")%</h4>
                        <p class="text-muted mb-0">معدل النجاح</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Grade Distribution Chart -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie me-2 text-primary"></i>
                            توزيع التقديرات
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (statistics.GradeDistribution?.Any() == true)
                        {
                            <div class="grade-distribution">
                                @foreach (var grade in statistics.GradeDistribution)
                                {
                                    var percentage = statistics.TotalAttempts > 0 ? (double)grade.Value / statistics.TotalAttempts * 100 : 0;
                                    <div class="grade-item mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <span class="fw-bold">@grade.Key</span>
                                            <span class="text-muted">@grade.Value طالب (@percentage.ToString("F1")%)</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar @GetGradeColor(grade.Key)" style="width: @percentage%"></div>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="fas fa-chart-pie fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">لا توجد بيانات للعرض</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2 text-success"></i>
                            إحصائيات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="stat-item text-center">
                                    <h3 class="text-success mb-1">@statistics.HighestScore.ToString("F1")</h3>
                                    <small class="text-muted">أعلى درجة</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item text-center">
                                    <h3 class="text-danger mb-1">@statistics.LowestScore.ToString("F1")</h3>
                                    <small class="text-muted">أقل درجة</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item text-center">
                                    <h3 class="text-primary mb-1">@statistics.TotalPassed</h3>
                                    <small class="text-muted">عدد الناجحين</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item text-center">
                                    <h3 class="text-warning mb-1">@statistics.TotalFailed</h3>
                                    <small class="text-muted">عدد الراسبين</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Table -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0">
                                    <i class="fas fa-table me-2 text-primary"></i>
                                    تفاصيل النتائج
                                </h5>
                            </div>
                            <div class="col-auto">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" placeholder="البحث في النتائج..."
                                           @bind="searchTerm" @bind:after="FilterResults" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (filteredResults?.Any() == true)
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>الطالب</th>
                                            <th>تاريخ الامتحان</th>
                                            <th>الوقت المستغرق</th>
                                            <th>الدرجة</th>
                                            <th>النسبة المئوية</th>
                                            <th>التقدير</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var result in filteredResults.OrderByDescending(r => r.Percentage))
                                        {
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center">
                                                            @result.StudentName.Substring(0, 1)
                                                        </div>
                                                        <div>
                                                            <strong>@result.StudentName</strong>
                                                            <br>
                                                            <small class="text-muted">المحاولة #@result.AttemptId</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        @result.EndTime.ToString("dd/MM/yyyy")
                                                        <br>
                                                        <small class="text-muted">@result.EndTime.ToString("HH:mm")</small>
                                                    </div>
                                                </td>
                                                <td>@result.DurationTaken دقيقة</td>
                                                <td>
                                                    <strong>@result.MarksObtained / @result.TotalMarks</strong>
                                                </td>
                                                <td>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar @GetResultColor(result.Percentage)"
                                                             style="width: @result.Percentage%">
                                                            @result.Percentage.ToString("F1")%
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge @GetGradeBadgeColor(result.Percentage) fs-6">
                                                        @result.Grade
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge @(result.IsPassed ? "bg-success" : "bg-danger")">
                                                        @(result.IsPassed ? "ناجح" : "راسب")
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewDetailedResult(result.AttemptId)" title="عرض التفاصيل">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-info" @onclick="() => ViewAnswers(result.AttemptId)" title="عرض الإجابات">
                                                            <i class="fas fa-list-alt"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد نتائج</h5>
                                <p class="text-muted">لم يقم أي طالب بأداء هذا الامتحان بعد</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h5 class="text-muted">لم يتم العثور على الامتحان</h5>
            <p class="text-muted">تأكد من صحة الرابط أو تواصل مع الإدارة</p>
            <a href="/teacher/exams" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للامتحانات
            </a>
        </div>
    }
</div>

<style>
    .avatar-md {
        width: 48px;
        height: 48px;
    }

    .avatar-sm {
        width: 32px;
        height: 32px;
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }

    .stat-item {
        padding: 15px;
        border-radius: 8px;
        background-color: #f8f9fa;
    }

    .grade-item {
        padding: 10px;
        border-radius: 6px;
        background-color: #f8f9fa;
    }
</style>

@code {
    [Parameter] public int ExamId { get; set; }

    private ExamDto? exam;
    private List<ExamResultDto> results = new();
    private List<ExamResultDto> filteredResults = new();
    private ExamStatisticsDto statistics = new();
    private bool isLoading = true;
    private string searchTerm = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadExamResults();
    }

    private async Task LoadExamResults()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            exam = await ApiService.GetExamAsync(ExamId);
            if (exam != null)
            {
                results = await ApiService.GetExamResultsAsync(ExamId) ?? new List<ExamResultDto>();
                statistics = await ApiService.GetExamStatisticsDetailedAsync(ExamId) ?? new ExamStatisticsDto();
                filteredResults = results.ToList();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل نتائج الامتحان: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshResults()
    {
        await LoadExamResults();
        await JSRuntime.InvokeVoidAsync("alert", "تم تحديث النتائج بنجاح");
    }

    private void FilterResults()
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            filteredResults = results.ToList();
        }
        else
        {
            filteredResults = results.Where(r =>
                r.StudentName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                r.Grade.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
            ).ToList();
        }
        StateHasChanged();
    }

    private async Task ExportResults()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة تصدير النتائج ستكون متاحة قريباً");
    }

    private void ViewDetailedResult(int attemptId)
    {
        Navigation.NavigateTo($"/teacher/exam-result/{attemptId}");
    }

    private void ViewAnswers(int attemptId)
    {
        Navigation.NavigateTo($"/teacher/exam-answers/{attemptId}");
    }

    private string GetResultColor(double percentage)
    {
        return percentage switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-info",
            >= 70 => "bg-warning",
            >= 60 => "bg-secondary",
            _ => "bg-danger"
        };
    }

    private string GetGradeBadgeColor(double percentage)
    {
        return percentage switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-info",
            >= 70 => "bg-warning",
            >= 60 => "bg-secondary",
            _ => "bg-danger"
        };
    }

    private string GetGradeColor(string grade)
    {
        return grade switch
        {
            "ممتاز" => "bg-success",
            "جيد جداً" => "bg-info",
            "جيد" => "bg-warning",
            "مقبول" => "bg-secondary",
            _ => "bg-danger"
        };
    }
}
