using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Schools.Shared.Models
{
    // Base Entity for all models
    public abstract class BaseEntity
    {
        [Key]
        public int Id { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? UpdatedAt { get; set; }
        
        public string CreatedBy { get; set; } = string.Empty;
        
        public string? UpdatedBy { get; set; }
        
        public bool IsDeleted { get; set; } = false;
        
        public DateTime? DeletedAt { get; set; }
        
        public string? DeletedBy { get; set; }
    }

    // Academic Year Model
    public class AcademicYear : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        public bool IsActive { get; set; } = true;
        
        public bool IsCurrent { get; set; } = false;

        public string? Description { get; set; }

        // Navigation properties
        public virtual ICollection<StudentEnrollment> StudentEnrollments { get; set; } = new List<StudentEnrollment>();
        public virtual ICollection<Grade> Grades { get; set; } = new List<Grade>();
        public virtual ICollection<Exam> Exams { get; set; } = new List<Exam>();
        public virtual ICollection<FeeStructure> FeeStructures { get; set; } = new List<FeeStructure>();
    }

    // Academic Grade/Level Model
    public class AcademicGrade : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        public string? Description { get; set; }

        public int SortOrder { get; set; }
        
        public int Level { get; set; } = 1;

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<Class> Classes { get; set; } = new List<Class>();
        public virtual ICollection<StudentEnrollment> StudentEnrollments { get; set; } = new List<StudentEnrollment>();
        public virtual ICollection<FeeStructure> FeeStructures { get; set; } = new List<FeeStructure>();
    }

    // Class Model
    public class Class : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public int AcademicGradeId { get; set; }

        public int Capacity { get; set; } = 30;

        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public string? ClassTeacherId { get; set; }

        // Navigation properties
        public virtual AcademicGrade AcademicGrade { get; set; } = null!;
        public virtual ApplicationUser? ClassTeacher { get; set; }
        public virtual ICollection<Section> Sections { get; set; } = new List<Section>();
        public virtual ICollection<StudentEnrollment> StudentEnrollments { get; set; } = new List<StudentEnrollment>();
        public virtual ICollection<TeacherAssignment> TeacherAssignments { get; set; } = new List<TeacherAssignment>();
        public virtual ICollection<Exam> Exams { get; set; } = new List<Exam>();
        public virtual ICollection<Schedule> Schedules { get; set; } = new List<Schedule>();
    }

    // Section Model
    public class Section : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public int ClassId { get; set; }

        public int Capacity { get; set; } = 30;

        public bool IsActive { get; set; } = true;

        public string? SectionTeacherId { get; set; }

        // Navigation properties
        public virtual Class Class { get; set; } = null!;
        public virtual ApplicationUser? SectionTeacher { get; set; }
        public virtual ICollection<StudentEnrollment> StudentEnrollments { get; set; } = new List<StudentEnrollment>();
        public virtual ICollection<Schedule> Schedules { get; set; } = new List<Schedule>();
    }

    // Subject Model
    public class Subject : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NameAr { get; set; }

        [Required]
        [StringLength(10)]
        public string Code { get; set; } = string.Empty;

        public string? Description { get; set; }

        public int CreditHours { get; set; } = 1;

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<TeacherAssignment> TeacherAssignments { get; set; } = new List<TeacherAssignment>();
        public virtual ICollection<Grade> Grades { get; set; } = new List<Grade>();
        public virtual ICollection<Exam> Exams { get; set; } = new List<Exam>();
        public virtual ICollection<Schedule> Schedules { get; set; } = new List<Schedule>();
    }

    // Student Enrollment Model
    public class StudentEnrollment : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int AcademicYearId { get; set; }

        [Required]
        public int AcademicGradeId { get; set; }

        [Required]
        public int ClassId { get; set; }

        public int? SectionId { get; set; }

        [Required]
        public DateTime EnrollmentDate { get; set; }

        public DateTime? WithdrawalDate { get; set; }

        public string? WithdrawalReason { get; set; }

        public bool IsActive { get; set; } = true;

        public string Status { get; set; } = "Active"; // Active, Withdrawn, Transferred, Graduated

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual AcademicYear AcademicYear { get; set; } = null!;
        public virtual AcademicGrade AcademicGrade { get; set; } = null!;
        public virtual Class Class { get; set; } = null!;
        public virtual Section? Section { get; set; }
        public virtual ICollection<Grade> Grades { get; set; } = new List<Grade>();
        public virtual ICollection<Attendance> Attendances { get; set; } = new List<Attendance>();
        public virtual ICollection<StudentFee> StudentFees { get; set; } = new List<StudentFee>();
    }

    // Teacher Assignment Model
    public class TeacherAssignment : BaseEntity
    {
        [Required]
        public string TeacherId { get; set; } = string.Empty;

        [Required]
        public int ClassId { get; set; }

        [Required]
        public int SubjectId { get; set; }

        public bool IsClassTeacher { get; set; } = false;

        [Required]
        public DateTime AssignedDate { get; set; }

        public DateTime? UnassignedDate { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ApplicationUser Teacher { get; set; } = null!;
        public virtual Class Class { get; set; } = null!;
        public virtual Subject Subject { get; set; } = null!;
    }

    // Grade Model (Student Grades/Marks)
    public class Grade : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int SubjectId { get; set; }

        [Required]
        public int AcademicYearId { get; set; }

        [Required]
        public int StudentEnrollmentId { get; set; }

        [Required]
        [StringLength(50)]
        public string ExamType { get; set; } = string.Empty; // Midterm, Final, Quiz, Assignment

        [Required]
        [Range(0, 1000)]
        public decimal Score { get; set; }

        [Required]
        [Range(0.1, 1000)]
        public decimal MaxScore { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal Percentage => MaxScore > 0 ? (Score / MaxScore) * 100 : 0;

        [Required]
        public DateTime Date { get; set; }

        [Required]
        [StringLength(20)]
        public string Semester { get; set; } = string.Empty; // First, Second

        public string? Notes { get; set; }

        public string? RecordedBy { get; set; }

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual Subject Subject { get; set; } = null!;
        public virtual AcademicYear AcademicYear { get; set; } = null!;
        public virtual StudentEnrollment StudentEnrollment { get; set; } = null!;
    }

    // Attendance Model
    public class Attendance : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int StudentEnrollmentId { get; set; }

        [Required]
        public DateTime Date { get; set; }

        public int? SubjectId { get; set; }

        public int? ScheduleId { get; set; }

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = string.Empty; // Present, Absent, Late, Excused

        public TimeSpan? CheckInTime { get; set; }

        public TimeSpan? CheckOutTime { get; set; }

        public string? Reason { get; set; }

        public string? Notes { get; set; }

        public string? RecordedBy { get; set; }

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual StudentEnrollment StudentEnrollment { get; set; } = null!;
        public virtual Subject? Subject { get; set; }
        public virtual Schedule? Schedule { get; set; }
    }

    // Enums
    public enum UserRole
    {
        Admin = 1,
        Teacher = 2,
        Student = 3,
        Parent = 4,
        Accountant = 5,
        Librarian = 6,
        Staff = 7
    }

    public enum GradeLevel
    {
        PreKindergarten = 1,
        Kindergarten = 2,
        Grade1 = 3,
        Grade2 = 4,
        Grade3 = 5,
        Grade4 = 6,
        Grade5 = 7,
        Grade6 = 8,
        Grade7 = 9,
        Grade8 = 10,
        Grade9 = 11,
        Grade10 = 12,
        Grade11 = 13,
        Grade12 = 14
    }

    public enum AttendanceStatus
    {
        Present = 1,
        Absent = 2,
        Late = 3,
        Excused = 4
    }

    public enum ExamType
    {
        Midterm = 1,
        Final = 2,
        Quiz = 3,
        Assignment = 4,
        Project = 5,
        Oral = 6
    }

    public enum Semester
    {
        First = 1,
        Second = 2,
        Summer = 3
    }
}
