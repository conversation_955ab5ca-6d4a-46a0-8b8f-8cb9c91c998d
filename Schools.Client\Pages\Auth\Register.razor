@page "/register"
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@using System.ComponentModel.DataAnnotations
@using Schools.Client.Services
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>إنشاء حساب جديد - نظام إدارة المدارس</PageTitle>

<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left side - Registration Form -->
        <div class="col-md-8 d-flex align-items-center justify-content-center">
            <div class="card shadow-lg border-0" style="width: 100%; max-width: 600px;">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                        <h3 class="card-title">إنشاء حساب جديد</h3>
                        <p class="text-muted">املأ البيانات التالية لإنشاء حسابك</p>
                    </div>

                    <EditForm Model="@registerModel" OnValidSubmit="@HandleRegister">
                        <DataAnnotationsValidator />

                        @if (!string.IsNullOrEmpty(errorMessage))
                        {
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                @errorMessage
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(successMessage))
                        {
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                @successMessage
                            </div>
                        }

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="firstName" class="form-label">الاسم الأول *</label>
                                <input id="firstName" class="form-control" @bind="registerModel.FirstName" placeholder="الاسم الأول" />
                                <ValidationMessage For="@(() => registerModel.FirstName)" class="text-danger" />
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="lastName" class="form-label">اسم العائلة *</label>
                                <input id="lastName" class="form-control" @bind="registerModel.LastName" placeholder="اسم العائلة" />
                                <ValidationMessage For="@(() => registerModel.LastName)" class="text-danger" />
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني *</label>
                            <input id="email" type="email" class="form-control" @bind="registerModel.Email" placeholder="<EMAIL>" />
                            <ValidationMessage For="@(() => registerModel.Email)" class="text-danger" />
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">كلمة المرور *</label>
                                <input id="password" type="password" class="form-control" @bind="registerModel.Password" placeholder="كلمة المرور" />
                                <ValidationMessage For="@(() => registerModel.Password)" class="text-danger" />
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="confirmPassword" class="form-label">تأكيد كلمة المرور *</label>
                                <input id="confirmPassword" type="password" class="form-control" @bind="registerModel.ConfirmPassword" placeholder="تأكيد كلمة المرور" />
                                <ValidationMessage For="@(() => registerModel.ConfirmPassword)" class="text-danger" />
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phoneNumber" class="form-label">رقم الهاتف</label>
                                <input id="phoneNumber" class="form-control" @bind="registerModel.PhoneNumber" placeholder="05xxxxxxxx" />
                                <ValidationMessage For="@(() => registerModel.PhoneNumber)" class="text-danger" />
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">نوع الحساب *</label>
                                <select id="role" class="form-select" @bind="registerModel.Role">
                                    <option value="">اختر نوع الحساب</option>
                                    <option value="Teacher">معلم</option>
                                    <option value="Student">طالب</option>
                                    <option value="Parent">ولي أمر</option>
                                    <option value="Employee">موظف</option>
                                </select>
                                <ValidationMessage For="@(() => registerModel.Role)" class="text-danger" />
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" disabled="@isLoading">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    <span>جاري إنشاء الحساب...</span>
                                }
                                else
                                {
                                    <i class="fas fa-user-plus me-2"></i>
                                    <span>إنشاء الحساب</span>
                                }
                            </button>
                        </div>
                    </EditForm>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="mb-2">لديك حساب بالفعل؟</p>
                        <a href="/login" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right side - Info -->
        <div class="col-md-4 d-none d-md-flex align-items-center justify-content-center bg-gradient" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="text-center text-white p-4">
                <i class="fas fa-users fa-4x mb-4"></i>
                <h3 class="mb-3">انضم إلى مجتمعنا التعليمي</h3>
                <p class="lead mb-4">سجل الآن واستمتع بتجربة تعليمية متميزة</p>
                
                <div class="mb-4">
                    <div class="mb-3">
                        <i class="fas fa-check-circle me-2"></i>
                        <span>واجهة سهلة الاستخدام</span>
                    </div>
                    <div class="mb-3">
                        <i class="fas fa-check-circle me-2"></i>
                        <span>متابعة شاملة للأداء</span>
                    </div>
                    <div class="mb-3">
                        <i class="fas fa-check-circle me-2"></i>
                        <span>تواصل فعال مع الإدارة</span>
                    </div>
                </div>

                <small class="text-light">
                    <i class="fas fa-info-circle me-1"></i>
                    سيتم مراجعة طلبك من قبل الإدارة قبل تفعيل الحساب
                </small>
            </div>
        </div>
    </div>
</div>

@code {
    private RegisterModel registerModel = new();
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool isLoading = false;

    public class RegisterModel
    {
        [Required(ErrorMessage = "الاسم الأول مطلوب")]
        public string FirstName { get; set; } = "";

        [Required(ErrorMessage = "اسم العائلة مطلوب")]
        public string LastName { get; set; } = "";

        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        public string Email { get; set; } = "";

        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [MinLength(6, ErrorMessage = "كلمة المرور يجب أن تكون 6 أحرف على الأقل")]
        public string Password { get; set; } = "";

        [Required(ErrorMessage = "تأكيد كلمة المرور مطلوب")]
        [Compare(nameof(Password), ErrorMessage = "كلمة المرور غير متطابقة")]
        public string ConfirmPassword { get; set; } = "";

        public string PhoneNumber { get; set; } = "";

        [Required(ErrorMessage = "نوع الحساب مطلوب")]
        public string Role { get; set; } = "";
    }

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            Navigation.NavigateTo("/");
        }
    }

    private async Task HandleRegister()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;

            // Simulate registration process
            await Task.Delay(2000);

            // For demo purposes, just show success message
            successMessage = "تم إنشاء الحساب بنجاح! سيتم مراجعة طلبك من قبل الإدارة.";
            registerModel = new RegisterModel(); // Reset form
            
            // Redirect to login after 3 seconds
            await Task.Delay(3000);
            Navigation.NavigateTo("/login");
        }
        catch (Exception)
        {
            errorMessage = "حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.";
        }
        finally
        {
            isLoading = false;
        }
    }
}
