@using Schools.Client.Services
@using Schools.Shared.DTOs
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<div class="container-fluid py-4">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-info text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="text-white mb-1">مرحباً أ. عبدالله محمد</h2>
                            <p class="text-white-75 mb-0">لوحة تحكم ولي الأمر - متابعة الأبناء - @DateTime.Now.ToString("dddd، dd MMMM yyyy", new System.Globalization.CultureInfo("ar-SA"))</p>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-3x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Children Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-child me-2"></i>
                        نظرة عامة على الأبناء
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach (var child in children)
                        {
                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card border-@GetGradeColor(child.Grade)">
                                    <div class="card-header bg-@GetGradeColor(child.Grade) text-white">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">@child.Name</h6>
                                            <span class="badge bg-light text-dark">@child.Grade</span>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <div class="border-end">
                                                    <h5 class="text-@GetGradeColor(child.Average)">@child.Average%</h5>
                                                    <small class="text-muted">المعدل</small>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="border-end">
                                                    <h5 class="text-@GetAttendanceColor(child.Attendance)">@child.Attendance%</h5>
                                                    <small class="text-muted">الحضور</small>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <h5 class="text-warning">@child.PendingAssignments</h5>
                                                <small class="text-muted">واجبات</small>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="d-grid">
                                            <button class="btn btn-outline-@GetGradeColor(child.Grade)" @onclick="@(() => ViewChildDetails(child.Id))">
                                                <i class="fas fa-eye me-2"></i>
                                                عرض التفاصيل
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">متوسط الدرجات</div>
                            <div class="text-lg fw-bold">@overallAverage%</div>
                        </div>
                        <i class="fas fa-chart-line fa-2x text-white-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="#" @onclick="@(() => NavigateToPage("/parent/grades"))">عرض التفاصيل</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">متوسط الحضور</div>
                            <div class="text-lg fw-bold">@overallAttendance%</div>
                        </div>
                        <i class="fas fa-user-check fa-2x text-white-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="#" @onclick="@(() => NavigateToPage("/parent/attendance"))">سجل الحضور</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">الواجبات المعلقة</div>
                            <div class="text-lg fw-bold">@totalPendingAssignments</div>
                        </div>
                        <i class="fas fa-tasks fa-2x text-white-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="#" @onclick="@(() => NavigateToPage("/parent/assignments"))">عرض الواجبات</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card bg-danger text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-75 small">الامتحانات القادمة</div>
                            <div class="text-lg fw-bold">@upcomingExams</div>
                        </div>
                        <i class="fas fa-clipboard-list fa-2x text-white-50"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="#" @onclick="@(() => NavigateToPage("/parent/exams"))">جدول الامتحانات</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Recent Grades -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        آخر الدرجات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الطالب</th>
                                    <th>المادة</th>
                                    <th>الدرجة</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>أحمد عبدالله</td>
                                    <td>الرياضيات</td>
                                    <td><span class="badge bg-success">92%</span></td>
                                    <td>أمس</td>
                                </tr>
                                <tr>
                                    <td>فاطمة عبدالله</td>
                                    <td>العلوم</td>
                                    <td><span class="badge bg-primary">88%</span></td>
                                    <td>أمس</td>
                                </tr>
                                <tr>
                                    <td>أحمد عبدالله</td>
                                    <td>اللغة العربية</td>
                                    <td><span class="badge bg-warning">75%</span></td>
                                    <td>منذ يومين</td>
                                </tr>
                                <tr>
                                    <td>فاطمة عبدالله</td>
                                    <td>الإنجليزية</td>
                                    <td><span class="badge bg-info">95%</span></td>
                                    <td>منذ 3 أيام</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance Summary -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-check me-2"></i>
                        ملخص الحضور هذا الأسبوع
                    </h5>
                </div>
                <div class="card-body">
                    @foreach (var child in children)
                    {
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <strong>@child.Name</strong>
                                <span class="badge bg-@GetAttendanceColor(child.WeeklyAttendance)">@child.WeeklyAttendance%</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-@GetAttendanceColor(child.WeeklyAttendance)" 
                                     style="width: @child.WeeklyAttendance%"></div>
                            </div>
                            <small class="text-muted">@child.PresentDays من @child.TotalDays أيام</small>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-primary w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/parent/grades"))">
                                <i class="fas fa-chart-line fa-2x mb-2"></i>
                                <br>عرض الدرجات
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-success w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/parent/attendance"))">
                                <i class="fas fa-calendar-check fa-2x mb-2"></i>
                                <br>سجل الحضور
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-info w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/parent/schedule"))">
                                <i class="fas fa-calendar fa-2x mb-2"></i>
                                <br>الجدول الدراسي
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <button class="btn btn-outline-warning w-100 h-100 py-3" @onclick="@(() => NavigateToPage("/parent/messages"))">
                                <i class="fas fa-envelope fa-2x mb-2"></i>
                                <br>الرسائل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Announcements and Messages -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-bullhorn me-2"></i>
                        الإعلانات المدرسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex align-items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-circle text-warning fa-lg"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">امتحانات نصف الفصل</h6>
                                    <p class="mb-1 small">ستبدأ امتحانات نصف الفصل الدراسي الأسبوع القادم</p>
                                    <small class="text-muted">منذ ساعتين</small>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex align-items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle text-info fa-lg"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">اجتماع أولياء الأمور</h6>
                                    <p class="mb-1 small">اجتماع دوري لأولياء الأمور يوم الخميس القادم</p>
                                    <small class="text-muted">أمس</small>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex align-items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-calendar text-success fa-lg"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">يوم الرياضة المدرسي</h6>
                                    <p class="mb-1 small">فعاليات رياضية متنوعة لجميع الطلاب</p>
                                    <small class="text-muted">منذ 3 أيام</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-comments me-2"></i>
                        رسائل المعلمين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">أ. محمد أحمد - معلم الرياضيات</h6>
                                    <p class="mb-1 small">أحمد يحتاج إلى مراجعة إضافية في الجبر</p>
                                    <small class="text-muted">منذ ساعة</small>
                                </div>
                                <span class="badge bg-primary">جديد</span>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">أ. فاطمة علي - معلمة العلوم</h6>
                                    <p class="mb-1 small">فاطمة تظهر تحسناً ملحوظاً في الفيزياء</p>
                                    <small class="text-muted">أمس</small>
                                </div>
                                <span class="badge bg-success">مقروء</span>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">أ. عبدالله محمد - معلم العربية</h6>
                                    <p class="mb-1 small">يرجى متابعة حفظ النصوص في المنزل</p>
                                    <small class="text-muted">منذ يومين</small>
                                </div>
                                <span class="badge bg-secondary">مقروء</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private List<ChildDto> children = new();
    
    private int overallAverage => children.Any() ? (int)children.Average(c => c.Average) : 0;
    private int overallAttendance => children.Any() ? (int)children.Average(c => c.Attendance) : 0;
    private int totalPendingAssignments => children.Sum(c => c.PendingAssignments);
    private int upcomingExams = 5;

    protected override async Task OnInitializedAsync()
    {
        LoadMockData();
    }

    private void LoadMockData()
    {
        children = new List<ChildDto>
        {
            new ChildDto
            {
                Id = 1,
                Name = "أحمد عبدالله",
                Grade = "الصف الثالث أ",
                Average = 87,
                Attendance = 95,
                PendingAssignments = 2,
                WeeklyAttendance = 100,
                PresentDays = 5,
                TotalDays = 5
            },
            new ChildDto
            {
                Id = 2,
                Name = "فاطمة عبدالله",
                Grade = "الصف الأول ب",
                Average = 92,
                Attendance = 98,
                PendingAssignments = 1,
                WeeklyAttendance = 80,
                PresentDays = 4,
                TotalDays = 5
            }
        };
    }

    private void NavigateToPage(string url)
    {
        Navigation.NavigateTo(url);
    }

    private async Task ViewChildDetails(int childId)
    {
        Navigation.NavigateTo($"/parent/child/{childId}");
    }

    private string GetGradeColor(int grade)
    {
        return grade switch
        {
            >= 90 => "success",
            >= 80 => "primary",
            >= 70 => "info",
            >= 60 => "warning",
            _ => "danger"
        };
    }

    private string GetGradeColor(string grade)
    {
        return grade.Contains("الأول") ? "primary" : 
               grade.Contains("الثاني") ? "success" : 
               grade.Contains("الثالث") ? "info" : "secondary";
    }

    private string GetAttendanceColor(int attendance)
    {
        return attendance switch
        {
            >= 95 => "success",
            >= 85 => "primary",
            >= 75 => "warning",
            _ => "danger"
        };
    }

    public class ChildDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Grade { get; set; } = string.Empty;
        public int Average { get; set; }
        public int Attendance { get; set; }
        public int PendingAssignments { get; set; }
        public int WeeklyAttendance { get; set; }
        public int PresentDays { get; set; }
        public int TotalDays { get; set; }
    }
}
