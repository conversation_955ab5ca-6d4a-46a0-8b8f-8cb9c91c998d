# PowerShell script to fix quote issues in Razor files

$files = @(
    "Schools.Client\Shared\TeacherDashboard.razor",
    "Schools.Client\Shared\StudentDashboard.razor",
    "Schools.Client\Shared\ParentDashboard.razor",
    "Schools.Client\Shared\EmployeeDashboard.razor",
    "Schools.Client\Shared\AccountantDashboard.razor",
    "Schools.Client\Shared\WelcomeComponent.razor",
    "Schools.Client\Shared\AdminDashboard.razor"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Fixing quotes in $file"
        $content = Get-Content $file -Raw
        # Fix escaped quotes
        $content = $content -replace '@onclick="[^"]*NavigateToPage\(\\"([^"]+)\\"\)"', '@onclick="() => NavigateToPage(\"$1\")"'
        # Fix single quotes
        $content = $content -replace "NavigateToPage\('([^']+)'\)", 'NavigateToPage("$1")'
        Set-Content $file $content -Encoding UTF8
    }
}

Write-Host "Quote fixes completed!"
