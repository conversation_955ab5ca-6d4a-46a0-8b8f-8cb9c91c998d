@page "/admin/library"
@using Schools.Shared.Models
@using Schools.Shared.DTOs
@using Schools.Client.Services
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Admin")]

<PageTitle>إدارة المكتبة الرقمية</PageTitle>

<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-info text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="text-white mb-1">
                                <i class="fas fa-book me-2"></i>
                                إدارة المكتبة الرقمية
                            </h2>
                            <p class="text-white-75 mb-0">إدارة الكتب والمواد التعليمية الرقمية</p>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-books fa-3x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@totalBooks</h4>
                            <p class="mb-0">إجمالي الكتب</p>
                        </div>
                        <i class="fas fa-book fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@availableBooks</h4>
                            <p class="mb-0">كتب متاحة</p>
                        </div>
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@borrowedBooks</h4>
                            <p class="mb-0">كتب مستعارة</p>
                        </div>
                        <i class="fas fa-hand-holding fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@overdueBooks</h4>
                            <p class="mb-0">كتب متأخرة</p>
                        </div>
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Controls -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        البحث والفلترة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" placeholder="البحث عن كتاب..." @bind="searchTerm" @oninput="OnSearchChanged">
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <select class="form-select" @bind="selectedCategory" @bind:after="FilterBooks">
                                <option value="">جميع الفئات</option>
                                <option value="Academic">أكاديمي</option>
                                <option value="Literature">أدب</option>
                                <option value="Science">علوم</option>
                                <option value="History">تاريخ</option>
                                <option value="Religion">ديني</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <select class="form-select" @bind="selectedStatus" @bind:after="FilterBooks">
                                <option value="">جميع الحالات</option>
                                <option value="Available">متاح</option>
                                <option value="Borrowed">مستعار</option>
                                <option value="Reserved">محجوز</option>
                                <option value="Maintenance">صيانة</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <select class="form-select" @bind="selectedGrade" @bind:after="FilterBooks">
                                <option value="">جميع المراحل</option>
                                <option value="Elementary">ابتدائي</option>
                                <option value="Middle">متوسط</option>
                                <option value="High">ثانوي</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <button class="btn btn-primary w-100" @onclick="ShowAddBookModal">
                                <i class="fas fa-plus me-2"></i>
                                إضافة كتاب
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Books Grid -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-books me-2"></i>
                        مكتبة الكتب
                    </h5>
                    <div class="btn-group">
                        <button class="btn btn-outline-success btn-sm" @onclick="ExportBooks">
                            <i class="fas fa-file-excel me-2"></i>
                            تصدير
                        </button>
                        <button class="btn btn-outline-primary btn-sm" @onclick="GenerateReport">
                            <i class="fas fa-chart-bar me-2"></i>
                            تقرير
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-4">
                            <div class="spinner-border text-info" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else if (filteredBooks?.Any() == true)
                    {
                        <div class="row">
                            @foreach (var book in filteredBooks.Take(12))
                            {
                                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                    <div class="card h-100 book-card">
                                        <div class="card-img-top book-cover d-flex align-items-center justify-content-center bg-@GetCategoryColor(book.Category)">
                                            <i class="fas fa-book fa-3x text-white"></i>
                                        </div>
                                        <div class="card-body">
                                            <h6 class="card-title">@book.Title</h6>
                                            <p class="card-text small text-muted mb-2">@book.Author</p>
                                            <div class="mb-2">
                                                <span class="badge bg-@GetCategoryColor(book.Category) me-1">@GetCategoryName(book.Category)</span>
                                                <span class="badge bg-@GetStatusColor(book.Status)">@GetStatusName(book.Status)</span>
                                            </div>
                                            <div class="small text-muted mb-2">
                                                <i class="fas fa-barcode me-1"></i>
                                                @book.ISBN
                                            </div>
                                            <div class="small text-muted mb-2">
                                                <i class="fas fa-calendar me-1"></i>
                                                @book.PublishYear
                                            </div>
                                            @if (book.Status == "Borrowed")
                                            {
                                                <div class="small text-warning mb-2">
                                                    <i class="fas fa-user me-1"></i>
                                                    مستعار من: @book.BorrowerName
                                                </div>
                                                <div class="small text-danger">
                                                    <i class="fas fa-clock me-1"></i>
                                                    تاريخ الإرجاع: @book.DueDate?.ToString("dd/MM/yyyy")
                                                </div>
                                            }
                                        </div>
                                        <div class="card-footer">
                                            <div class="btn-group w-100" role="group">
                                                <button class="btn btn-sm btn-outline-primary" @onclick="() => ViewBookDetails(book.Id)" title="تفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning" @onclick="() => EditBook(book.Id)" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                @if (book.Status == "Available")
                                                {
                                                    <button class="btn btn-sm btn-outline-success" @onclick="() => BorrowBook(book.Id)" title="إعارة">
                                                        <i class="fas fa-hand-holding"></i>
                                                    </button>
                                                }
                                                else if (book.Status == "Borrowed")
                                                {
                                                    <button class="btn btn-sm btn-outline-info" @onclick="() => ReturnBook(book.Id)" title="إرجاع">
                                                        <i class="fas fa-undo"></i>
                                                    </button>
                                                }
                                                <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteBook(book.Id)" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>

                        @if (filteredBooks.Count() > 12)
                        {
                            <div class="text-center mt-3">
                                <p class="text-muted">عرض 12 من أصل @filteredBooks.Count() كتاب</p>
                                <button class="btn btn-outline-info" @onclick="LoadMoreBooks">
                                    تحميل المزيد
                                </button>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-book fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد كتب</h5>
                            <p class="text-muted">لم يتم العثور على كتب بالفلاتر المحددة</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .book-card {
        transition: transform 0.2s;
    }

    .book-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .book-cover {
        height: 150px;
    }
</style>

@code {
    private List<BookDto> books = new();
    private List<BookDto> filteredBooks = new();
    private bool isLoading = true;

    private string searchTerm = string.Empty;
    private string selectedCategory = string.Empty;
    private string selectedStatus = string.Empty;
    private string selectedGrade = string.Empty;

    private int totalBooks => books.Count;
    private int availableBooks => books.Count(b => b.Status == "Available");
    private int borrowedBooks => books.Count(b => b.Status == "Borrowed");
    private int overdueBooks => books.Count(b => b.Status == "Borrowed" && b.DueDate < DateTime.Now);

    protected override async Task OnInitializedAsync()
    {
        await LoadBooks();
    }

    private async Task LoadBooks()
    {
        try
        {
            isLoading = true;
            var apiBooks = await ApiService.GetBooksAsync();
            books = apiBooks.ToList();
            FilterBooks();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
            // Fallback to mock data if API fails
            books = GenerateMockBooks();
            FilterBooks();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private List<BookDto> GenerateMockBooks()
    {
        var mockBooks = new List<BookDto>();
        var titles = new[] { "الرياضيات المتقدمة", "علوم الطبيعة", "تاريخ الحضارات", "الأدب العربي", "الفيزياء الحديثة", "الكيمياء العضوية", "الجغرافيا الطبيعية", "التربية الإسلامية" };
        var authors = new[] { "د. محمد أحمد", "د. فاطمة علي", "د. عبدالله محمد", "د. مريم أحمد", "د. يوسف علي" };
        var categories = new[] { "Academic", "Literature", "Science", "History", "Religion" };
        var statuses = new[] { "Available", "Borrowed", "Reserved", "Maintenance" };
        var grades = new[] { "Elementary", "Middle", "High" };
        var random = new Random();

        for (int i = 1; i <= 50; i++)
        {
            var status = statuses[random.Next(statuses.Length)];
            var book = new BookDto
            {
                Id = i,
                Title = titles[random.Next(titles.Length)] + $" - الجزء {i % 3 + 1}",
                Author = authors[random.Next(authors.Length)],
                ISBN = $"978-{random.Next(1000, 9999)}-{random.Next(100, 999)}-{random.Next(10, 99)}",
                Category = categories[random.Next(categories.Length)],
                Status = status,
                Grade = grades[random.Next(grades.Length)],
                PublishYear = random.Next(2010, 2024),
                Pages = random.Next(100, 500),
                Publisher = "دار النشر العربية"
            };

            if (status == "Borrowed")
            {
                book.BorrowerName = $"الطالب {random.Next(1, 100)}";
                book.BorrowDate = DateTime.Now.AddDays(-random.Next(1, 30));
                book.DueDate = book.BorrowDate?.AddDays(14);
            }

            mockBooks.Add(book);
        }

        return mockBooks;
    }

    private void FilterBooks()
    {
        var query = books.AsEnumerable();

        if (!string.IsNullOrEmpty(selectedCategory))
        {
            query = query.Where(b => b.Category == selectedCategory);
        }

        if (!string.IsNullOrEmpty(selectedStatus))
        {
            query = query.Where(b => b.Status == selectedStatus);
        }

        if (!string.IsNullOrEmpty(selectedGrade))
        {
            query = query.Where(b => b.Grade == selectedGrade);
        }

        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(b =>
                b.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                b.Author.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                b.ISBN.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
        }

        filteredBooks = query.ToList();
        StateHasChanged();
    }

    private async Task OnSearchChanged(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? string.Empty;
        FilterBooks();
    }

    private async Task ShowAddBookModal()
    {
        Navigation.NavigateTo("/admin/library/add");
    }

    private async Task ViewBookDetails(int bookId)
    {
        Navigation.NavigateTo($"/admin/library/{bookId}");
    }

    private async Task EditBook(int bookId)
    {
        Navigation.NavigateTo($"/admin/library/{bookId}/edit");
    }

    private async Task BorrowBook(int bookId)
    {
        Navigation.NavigateTo($"/admin/library/{bookId}/borrow");
    }

    private async Task ReturnBook(int bookId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من إرجاع هذا الكتاب؟"))
        {
            var book = books.FirstOrDefault(b => b.Id == bookId);
            if (book != null)
            {
                book.Status = "Available";
                book.BorrowerName = null;
                book.BorrowDate = null;
                book.DueDate = null;
                FilterBooks();
                await JSRuntime.InvokeVoidAsync("alert", "تم إرجاع الكتاب بنجاح");
            }
        }
    }

    private async Task DeleteBook(int bookId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا الكتاب؟"))
        {
            books.RemoveAll(b => b.Id == bookId);
            FilterBooks();
            await JSRuntime.InvokeVoidAsync("alert", "تم حذف الكتاب بنجاح");
        }
    }

    private async Task LoadMoreBooks()
    {
        await JSRuntime.InvokeVoidAsync("alert", "تم تحميل المزيد من الكتب");
    }

    private async Task ExportBooks()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم تصدير البيانات إلى Excel");
    }

    private async Task GenerateReport()
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم إنشاء تقرير المكتبة");
    }

    private string GetCategoryName(string category)
    {
        return category switch
        {
            "Academic" => "أكاديمي",
            "Literature" => "أدب",
            "Science" => "علوم",
            "History" => "تاريخ",
            "Religion" => "ديني",
            _ => category
        };
    }

    private string GetCategoryColor(string category)
    {
        return category switch
        {
            "Academic" => "primary",
            "Literature" => "success",
            "Science" => "info",
            "History" => "warning",
            "Religion" => "secondary",
            _ => "dark"
        };
    }

    private string GetStatusName(string status)
    {
        return status switch
        {
            "Available" => "متاح",
            "Borrowed" => "مستعار",
            "Reserved" => "محجوز",
            "Maintenance" => "صيانة",
            _ => status
        };
    }

    private string GetStatusColor(string status)
    {
        return status switch
        {
            "Available" => "success",
            "Borrowed" => "warning",
            "Reserved" => "info",
            "Maintenance" => "danger",
            _ => "secondary"
        };
    }

}
