@page "/schedule/teacher"
@page "/schedule/teacher/{TeacherId}"
@using Schools.Client.Models
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Components.Authorization
@inject HttpClient Http
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthStateProvider

<PageTitle>جدول المعلم - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient-success text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4 class="mb-0">
                                <i class="fas fa-chalkboard-teacher me-2"></i>
                                جدول المعلم
                            </h4>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group">
                                <button class="btn btn-light btn-sm" @onclick="PreviousWeek">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                                <button class="btn btn-light btn-sm" @onclick="CurrentWeek">
                                    الأسبوع الحالي
                                </button>
                                <button class="btn btn-light btn-sm" @onclick="NextWeek">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body p-0">
                    <!-- Teacher Selection -->
                    <div class="p-3 border-bottom bg-light">
                        <div class="row g-3 align-items-end">
                            <div class="col-md-4">
                                <label class="form-label">اختر المعلم</label>
                                <select class="form-select" @bind="selectedTeacherId" @bind:after="LoadTeacherSchedule">
                                    <option value="">اختر المعلم</option>
                                    @if (teachers != null)
                                    {
                                        @foreach (var teacher in teachers)
                                        {
                                            <option value="@teacher.Id">@teacher.Name</option>
                                        }
                                    }
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الأسبوع</label>
                                <input type="date" class="form-control" @bind="weekStart" @bind:after="LoadTeacherSchedule" />
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-success w-100" @onclick="LoadTeacherSchedule">
                                    <i class="fas fa-search me-2"></i>
                                    عرض الجدول
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Loading -->
                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-success" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2 text-muted">جاري تحميل جدول المعلم...</p>
                        </div>
                    }
                    else if (teacherSchedule != null)
                    {
                        <!-- Teacher Info -->
                        <div class="p-3 bg-success text-white">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h5 class="mb-1">
                                        <i class="fas fa-user-tie me-2"></i>
                                        @teacherSchedule.TeacherName
                                    </h5>
                                    <p class="mb-0 opacity-75">
                                        الأسبوع من @teacherSchedule.WeekStart.ToString("dd/MM/yyyy")
                                        إلى @teacherSchedule.WeekEnd.ToString("dd/MM/yyyy")
                                    </p>
                                </div>
                                <div class="col-auto">
                                    <div class="row g-3 text-center">
                                        <div class="col">
                                            <div class="fw-bold fs-4">@teacherSchedule.TotalClasses</div>
                                            <small>إجمالي الحصص</small>
                                        </div>
                                        <div class="col">
                                            <div class="fw-bold fs-4">@teacherSchedule.TotalHours.ToString("F1")</div>
                                            <small>إجمالي الساعات</small>
                                        </div>
                                        <div class="col">
                                            <div class="fw-bold fs-4">@teacherSchedule.SubjectCount</div>
                                            <small>عدد المواد</small>
                                        </div>
                                        <div class="col">
                                            <div class="fw-bold fs-4">@teacherSchedule.ClassCount</div>
                                            <small>عدد الصفوف</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Schedule Table -->
                        <div class="table-responsive">
                            <table class="table table-bordered mb-0 teacher-schedule-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 100px;">الوقت</th>
                                        @foreach (var day in teacherSchedule.WeeklySchedule.Days)
                                        {
                                            <th class="text-center @(day.IsToday ? "bg-warning text-dark" : "") @(day.IsWeekend ? "bg-secondary" : "")">
                                                <div class="fw-bold">@day.DayName</div>
                                                <small>@day.Date.ToString("dd/MM")</small>
                                                @if (day.IsToday)
                                                {
                                                    <div><small class="badge bg-warning text-dark">اليوم</small></div>
                                                }
                                                @if (day.IsWeekend)
                                                {
                                                    <div><small class="badge bg-secondary">عطلة</small></div>
                                                }
                                            </th>
                                        }
                                    </tr>
                                </thead>
                                <tbody>
                                    @{
                                        var allTimes = GetAllTimeSlots();
                                    }
                                    @foreach (var timeSlot in allTimes)
                                    {
                                        <tr>
                                            <td class="time-slot bg-light fw-bold text-center">
                                                @timeSlot
                                            </td>
                                            @foreach (var day in teacherSchedule.WeeklySchedule.Days)
                                            {
                                                var period = day.Periods.FirstOrDefault(p => p.TimeSlot == timeSlot);
                                                <td class="period-cell @(day.IsWeekend ? "weekend-cell" : "") @(period?.IsCancelled == true ? "cancelled-cell" : "")">
                                                    @if (period != null)
                                                    {
                                                        <div class="teacher-period-card @GetPeriodClass(period)">
                                                            <div class="period-subject fw-bold">
                                                                @period.SubjectNameAr
                                                                @if (period.IsSubstitute)
                                                                {
                                                                    <span class="badge bg-warning text-dark ms-1">بديل</span>
                                                                }
                                                            </div>
                                                            <div class="period-class">
                                                                <i class="fas fa-users"></i> @period.ClassName
                                                                @if (!string.IsNullOrEmpty(period.SectionName))
                                                                {
                                                                    <span> - @period.SectionName</span>
                                                                }
                                                            </div>
                                                            @if (!string.IsNullOrEmpty(period.Room))
                                                            {
                                                                <div class="period-room">
                                                                    <i class="fas fa-door-open"></i> @period.Room
                                                                </div>
                                                            }
                                                            @if (period.IsCancelled)
                                                            {
                                                                <div class="period-status cancelled">
                                                                    <i class="fas fa-times"></i> ملغية
                                                                </div>
                                                            }
                                                            else if (period.IsCompleted)
                                                            {
                                                                <div class="period-status completed">
                                                                    <i class="fas fa-check"></i> مكتملة
                                                                </div>
                                                            }
                                                        </div>
                                                    }
                                                    else if (day.IsWeekend)
                                                    {
                                                        <div class="text-center text-muted">
                                                            <i class="fas fa-calendar-times"></i>
                                                        </div>
                                                    }
                                                </td>
                                            }
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Daily Workload Summary -->
                        <div class="p-3 border-top">
                            <h6 class="mb-3">ملخص الأعباء اليومية:</h6>
                            <div class="row g-3">
                                @foreach (var day in teacherSchedule.WeeklySchedule.Days.Where(d => !d.IsWeekend))
                                {
                                    var dayWorkload = GetDayWorkload(day);
                                    <div class="col-md-2">
                                        <div class="card @(day.IsToday ? "border-warning" : "")">
                                            <div class="card-body text-center p-2">
                                                <h6 class="card-title mb-1 @(day.IsToday ? "text-warning" : "")">@day.DayName</h6>
                                                <div class="mb-1">
                                                    <span class="badge bg-primary">@dayWorkload.PeriodCount حصة</span>
                                                </div>
                                                <div class="mb-1">
                                                    <span class="badge bg-info">@dayWorkload.HoursCount.ToString("F1") ساعة</span>
                                                </div>
                                                @if (dayWorkload.SubstituteCount > 0)
                                                {
                                                    <div>
                                                        <span class="badge bg-warning text-dark">@dayWorkload.SubstituteCount بديل</span>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="p-3 border-top bg-light">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="mb-2">الإجراءات:</h6>
                                    <div class="btn-group">
                                        <button class="btn btn-outline-primary btn-sm" @onclick="ExportSchedule">
                                            <i class="fas fa-download me-1"></i>
                                            تصدير
                                        </button>
                                        <button class="btn btn-outline-success btn-sm" @onclick="PrintSchedule">
                                            <i class="fas fa-print me-1"></i>
                                            طباعة
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" @onclick="SendEmail">
                                            <i class="fas fa-envelope me-1"></i>
                                            إرسال بالبريد
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <div class="d-flex justify-content-end gap-2">
                                        <div class="d-flex align-items-center">
                                            <div class="legend-box bg-primary me-1"></div>
                                            <span class="small">حصة عادية</span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div class="legend-box bg-warning me-1"></div>
                                            <span class="small">حصة بديلة</span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div class="legend-box bg-danger me-1"></div>
                                            <span class="small">حصة ملغية</span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div class="legend-box bg-success me-1"></div>
                                            <span class="small">حصة مكتملة</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    else if (!isLoading && !string.IsNullOrEmpty(selectedTeacherId))
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد حصص مجدولة</h5>
                            <p class="text-muted">لا توجد حصص مجدولة لهذا المعلم في الفترة المحددة</p>
                        </div>
                    }
                    else if (!isLoading)
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-chalkboard-teacher fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">اختر معلماً لعرض جدوله</h5>
                            <p class="text-muted">حدد المعلم من القائمة أعلاه لعرض جدوله الأسبوعي</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .teacher-schedule-table {
        font-size: 0.85rem;
    }

    .time-slot {
        writing-mode: vertical-rl;
        text-orientation: mixed;
        min-width: 80px;
    }

    .period-cell {
        padding: 4px;
        vertical-align: top;
        min-height: 80px;
        position: relative;
    }

    .weekend-cell {
        background-color: #f8f9fa;
    }

    .cancelled-cell {
        background-color: #ffe6e6;
    }

    .teacher-period-card {
        padding: 8px;
        border-radius: 6px;
        font-size: 0.75rem;
        line-height: 1.3;
        height: 100%;
        min-height: 70px;
        position: relative;
        overflow: hidden;
        border: 2px solid transparent;
    }

    .teacher-period-card.regular {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border-color: #0056b3;
    }

    .teacher-period-card.substitute {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        border-color: #e0a800;
    }

    .teacher-period-card.cancelled {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        opacity: 0.7;
        border-color: #c82333;
    }

    .teacher-period-card.completed {
        background: linear-gradient(135deg, #28a745, #1e7e34);
        color: white;
        border-color: #1e7e34;
    }

    .period-subject {
        font-size: 0.8rem;
        margin-bottom: 3px;
        font-weight: bold;
    }

    .period-class,
    .period-room {
        font-size: 0.7rem;
        opacity: 0.9;
        margin-bottom: 2px;
    }

    .period-status {
        position: absolute;
        bottom: 2px;
        right: 2px;
        font-size: 0.6rem;
        padding: 1px 4px;
        border-radius: 3px;
        background: rgba(255, 255, 255, 0.2);
    }

    .legend-box {
        width: 15px;
        height: 15px;
        border-radius: 3px;
        display: inline-block;
    }

    @@media (max-width: 768px) {
        .teacher-schedule-table {
            font-size: 0.7rem;
        }

        .teacher-period-card {
            min-height: 60px;
            padding: 4px;
        }

        .time-slot {
            writing-mode: horizontal-tb;
            min-width: 60px;
        }
    }
</style>

@code {
    [Parameter] public string? TeacherId { get; set; }

    private TeacherScheduleDto? teacherSchedule;
    private List<Schools.Client.Models.TeacherDto> teachers = new();
    private bool isLoading = false;

    private string? selectedTeacherId;
    private DateTime weekStart = GetStartOfWeek(DateTime.Today);

    protected override async Task OnInitializedAsync()
    {
        await LoadTeachers();

        // If TeacherId is provided in URL, use it
        if (!string.IsNullOrEmpty(TeacherId))
        {
            selectedTeacherId = TeacherId;
            await LoadTeacherSchedule();
        }
    }

    private async Task LoadTeachers()
    {
        try
        {
            var response = await Http.GetAsync("api/admin/teachers");
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<List<Schools.Client.Models.TeacherDto>>();
                teachers = result ?? new List<Schools.Client.Models.TeacherDto>();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل المعلمين: {ex.Message}");
        }
    }

    private async Task LoadTeacherSchedule()
    {
        if (string.IsNullOrEmpty(selectedTeacherId)) return;

        isLoading = true;
        try
        {
            var response = await Http.GetAsync($"api/schedule/teacher/{selectedTeacherId}?weekStart={weekStart:yyyy-MM-dd}");

            if (response.IsSuccessStatusCode)
            {
                teacherSchedule = await response.Content.ReadFromJsonAsync<TeacherScheduleDto>();
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                await JSRuntime.InvokeVoidAsync("alert", "المعلم غير موجود");
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.Forbidden)
            {
                await JSRuntime.InvokeVoidAsync("alert", "ليس لديك صلاحية لعرض جدول هذا المعلم");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "فشل في تحميل جدول المعلم");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل جدول المعلم: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private List<string> GetAllTimeSlots()
    {
        if (teacherSchedule?.WeeklySchedule?.Days == null) return new List<string>();

        var allSlots = teacherSchedule.WeeklySchedule.Days
            .SelectMany(d => d.Periods.Select(p => p.TimeSlot))
            .Distinct()
            .OrderBy(slot => TimeSpan.Parse(slot.Split(" - ")[0]))
            .ToList();

        return allSlots;
    }

    private string GetPeriodClass(SchedulePeriodDto period)
    {
        if (period.IsCancelled) return "cancelled";
        if (period.IsCompleted) return "completed";
        if (period.IsSubstitute) return "substitute";
        return "regular";
    }

    private TeacherWorkloadDto GetDayWorkload(DayScheduleDto day)
    {
        return new TeacherWorkloadDto
        {
            Date = day.Date,
            DayName = day.DayName,
            PeriodCount = day.Periods.Count,
            HoursCount = day.Periods.Sum(p => p.Duration.TotalHours),
            SubstituteCount = day.Periods.Count(p => p.IsSubstitute),
            IsOverloaded = day.Periods.Count > 6, // More than 6 periods is considered overloaded
            Subjects = day.Periods.Select(p => p.SubjectNameAr ?? p.SubjectName).Distinct().ToList(),
            Classes = day.Periods.Select(p => p.ClassName).Distinct().ToList()
        };
    }

    private async Task PreviousWeek()
    {
        weekStart = weekStart.AddDays(-7);
        await LoadTeacherSchedule();
    }

    private async Task NextWeek()
    {
        weekStart = weekStart.AddDays(7);
        await LoadTeacherSchedule();
    }

    private async Task CurrentWeek()
    {
        weekStart = GetStartOfWeek(DateTime.Today);
        await LoadTeacherSchedule();
    }

    private static DateTime GetStartOfWeek(DateTime date)
    {
        // In Arabic culture, week starts on Saturday
        var diff = (7 + (date.DayOfWeek - DayOfWeek.Saturday)) % 7;
        return date.AddDays(-1 * diff).Date;
    }

    private async Task ExportSchedule()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("alert", "ميزة التصدير ستكون متاحة قريباً");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في التصدير: {ex.Message}");
        }
    }

    private async Task PrintSchedule()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("window.print");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في الطباعة: {ex.Message}");
        }
    }

    private async Task SendEmail()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("alert", "ميزة إرسال البريد الإلكتروني ستكون متاحة قريباً");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في إرسال البريد: {ex.Message}");
        }
    }
}
