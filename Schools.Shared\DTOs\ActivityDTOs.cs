namespace Schools.Shared.DTOs
{
    // Activity DTOs
    public class ActivityDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string StartTime { get; set; } = string.Empty;
        public string EndTime { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string OrganizerId { get; set; } = string.Empty;
        public string OrganizerName { get; set; } = string.Empty;
        public int MaxParticipants { get; set; }
        public int ParticipantsCount { get; set; }
        public string? Requirements { get; set; }
        public string? Notes { get; set; }
        public List<ActivityParticipantDto> Participants { get; set; } = new();
    }

    public class CreateActivityDto
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public string Location { get; set; } = string.Empty;
        public string OrganizerId { get; set; } = string.Empty;
        public int MaxParticipants { get; set; }
        public string? Requirements { get; set; }
        public string? Notes { get; set; }
    }

    public class UpdateActivityDto
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public string Location { get; set; } = string.Empty;
        public int MaxParticipants { get; set; }
        public string? Requirements { get; set; }
        public string? Notes { get; set; }
    }

    public class UpdateActivityStatusDto
    {
        public string Status { get; set; } = string.Empty;
    }

    public class ActivityParticipantDto
    {
        public int Id { get; set; }
        public int ActivityId { get; set; }
        public string StudentId { get; set; } = string.Empty;
        public string StudentName { get; set; } = string.Empty;
        public DateTime RegistrationDate { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    public class RegisterParticipantDto
    {
        public string StudentId { get; set; } = string.Empty;
    }

    public class ActivityStatisticsDto
    {
        public int TotalActivities { get; set; }
        public int UpcomingActivities { get; set; }
        public int ActiveActivities { get; set; }
        public int CompletedActivities { get; set; }
        public int TotalParticipants { get; set; }
        public List<ActivityTypeStatDto> TypeStatistics { get; set; } = new();
    }

    public class ActivityTypeStatDto
    {
        public string Type { get; set; } = string.Empty;
        public int Count { get; set; }
    }

    public class ActivityCalendarDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public string Location { get; set; } = string.Empty;
    }
}
