@echo off
chcp 65001 >nul
title نظام إدارة المدارس

echo.
echo ================================================
echo 🏫 نظام إدارة المدارس - Schools Management System
echo ================================================
echo.

echo 🔨 بناء المشروع...
dotnet build
if errorlevel 1 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo.

echo 🚀 تشغيل API...
start "Schools API" cmd /k "echo API يعمل على http://localhost:5261 && echo Swagger: http://localhost:5261/swagger && dotnet run --project Schools.API"

echo ⏳ انتظار تشغيل API...
timeout /t 8 /nobreak >nul

echo 🌐 تشغيل العميل...
start "Schools Client" cmd /k "echo العميل يعمل على http://localhost:5131 && dotnet run --project Schools.Client"

echo ⏳ انتظار تشغيل العميل...
timeout /t 10 /nobreak >nul

echo.
echo ================================================
echo 🎉 تم تشغيل النظام بنجاح!
echo ================================================
echo.
echo 📍 الروابط:
echo    🌐 العميل: http://localhost:5131
echo    🔧 API: http://localhost:5261
echo    📚 Swagger: http://localhost:5261/swagger
echo.
echo 🔐 بيانات المدير:
echo    📧 البريد: <EMAIL>
echo    🔑 كلمة المرور: Admin123!
echo.

echo 🌐 فتح المتصفح...
start http://localhost:5131

echo.
echo ✨ استمتع باستخدام النظام!
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
