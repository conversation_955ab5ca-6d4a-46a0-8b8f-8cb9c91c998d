@page "/admin/classes"
@using Schools.Shared.Models
@using Schools.Shared.DTOs
@using Schools.Client.Services
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@attribute [Authorize(Roles = "Admin")]

<PageTitle>إدارة الصفوف</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-chalkboard me-2"></i>
                        إدارة الصفوف
                    </h4>
                    <button class="btn btn-dark" @onclick="ShowAddModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة صف جديد
                    </button>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-4">
                            <div class="spinner-border text-warning" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else if (classes?.Any() == true)
                    {
                        <!-- Filter by Grade -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <select class="form-select" @onchange="FilterByGrade">
                                    <option value="">جميع المراحل</option>
                                    @if (grades != null)
                                    {
                                        @foreach (var grade in grades)
                                        {
                                            <option value="@grade.Id">@grade.Name</option>
                                        }
                                    }
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            @foreach (var classItem in filteredClasses)
                            {
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card h-100 border-start border-4 border-warning">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <div>
                                                    <h5 class="card-title text-warning mb-1">@classItem.Name</h5>
                                                    <span class="badge bg-secondary">@classItem.Grade?.Name</span>
                                                </div>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="#" @onclick="() => EditClass(classItem)">
                                                            <i class="fas fa-edit me-2"></i>تعديل
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="#" @onclick="() => ViewStudents(classItem.Id)">
                                                            <i class="fas fa-users me-2"></i>الطلاب
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="#" @onclick="() => ViewSchedule(classItem.Id)">
                                                            <i class="fas fa-calendar me-2"></i>الجدول
                                                        </a></li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><a class="dropdown-item text-danger" href="#" @onclick="() => DeleteClass(classItem.Id)">
                                                            <i class="fas fa-trash me-2"></i>حذف
                                                        </a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">السعة:</small>
                                                    <span class="badge bg-warning text-dark">@classItem.Capacity طالب</span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center mt-2">
                                                    <small class="text-muted">الطلاب الحاليون:</small>
                                                    <span class="badge bg-info">0 طالب</span>
                                                </div>
                                            </div>
                                            <div class="progress mb-2" style="height: 8px;">
                                                <div class="progress-bar bg-info" role="progressbar"
                                                     style="width: @(GetOccupancyPercentage(classItem))%">
                                                </div>
                                            </div>
                                            <small class="text-muted">
                                                تم الإنشاء: @classItem.CreatedAt.ToString("dd/MM/yyyy")
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-chalkboard fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد صفوف</h5>
                            <p class="text-muted">ابدأ بإضافة الصفوف الدراسية</p>
                            <button class="btn btn-warning" @onclick="ShowAddModal">
                                <i class="fas fa-plus me-2"></i>
                                إضافة صف جديد
                            </button>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Modal -->
@if (showModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        @if (editingClass == null)
                        {
                            <text>إضافة صف جديد</text>
                        }
                        else
                        {
                            <text>تعديل الصف</text>
                        }
                    </h5>
                    <button type="button" class="btn-close" @onclick="CloseModal"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="classModel" OnValidSubmit="SaveClass">
                        <DataAnnotationsValidator />
                        <div class="mb-3">
                            <label class="form-label">اسم الصف</label>
                            <InputText class="form-control" @bind-Value="classModel.Name" placeholder="مثال: الصف الأول أ" />
                            <ValidationMessage For="() => classModel.Name" class="text-danger" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المرحلة الدراسية</label>
                            <InputSelect class="form-select" @bind-Value="classModel.GradeId">
                                <option value="">اختر المرحلة الدراسية</option>
                                @if (grades != null)
                                {
                                    @foreach (var grade in grades)
                                    {
                                        <option value="@grade.Id">@grade.Name</option>
                                    }
                                }
                            </InputSelect>
                            <ValidationMessage For="() => classModel.GradeId" class="text-danger" />
                        </div>
                        <div class="mb-3">
                            <label class="form-label">سعة الصف</label>
                            <InputNumber class="form-control" @bind-Value="classModel.Capacity" />
                            <ValidationMessage For="() => classModel.Capacity" class="text-danger" />
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" @onclick="CloseModal">إلغاء</button>
                            <button type="submit" class="btn btn-warning" disabled="@isSaving">
                                @if (isSaving)
                                {
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                }
                                حفظ
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<Class>? classes;
    private List<Class> filteredClasses = new();
    private List<Grade>? grades;
    private bool isLoading = true;
    private bool showModal = false;
    private bool isSaving = false;
    private Class? editingClass;
    private CreateClassDto classModel = new();
    private int selectedGradeFilter = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            var classesTask = ApiService.GetClassesAsync();
            var gradesTask = ApiService.GetGradesAsync();

            await Task.WhenAll(classesTask, gradesTask);

            var classDtos = await classesTask;
            var gradeDtos = await gradesTask;

            grades = gradeDtos.Select(dto => new Grade
            {
                Id = dto.Id,
                Name = dto.Name,
                Level = dto.Level,
                Description = dto.Description,
                CreatedAt = DateTime.Now
            }).ToList();

            classes = classDtos.Select(dto => new Class
            {
                Id = dto.Id,
                Name = dto.Name,
                GradeId = dto.GradeId,
                Capacity = dto.Capacity,
                CreatedAt = DateTime.Now
            }).ToList();

            // ربط المراحل بالصفوف
            foreach (var classItem in classes)
            {
                classItem.Grade = grades?.FirstOrDefault(g => g.Id == classItem.GradeId);
            }

            filteredClasses = classes?.ToList() ?? new();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void FilterByGrade(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out selectedGradeFilter) && selectedGradeFilter > 0)
        {
            filteredClasses = classes?.Where(c => c.GradeId == selectedGradeFilter).ToList() ?? new();
        }
        else
        {
            filteredClasses = classes?.ToList() ?? new();
        }
        StateHasChanged();
    }

    private void ShowAddModal()
    {
        editingClass = null;
        classModel = new CreateClassDto();
        showModal = true;
    }

    private void EditClass(Class classItem)
    {
        editingClass = classItem;
        classModel = new CreateClassDto
        {
            Name = classItem.Name,
            GradeId = classItem.GradeId,
            Capacity = classItem.Capacity
        };
        showModal = true;
    }

    private void CloseModal()
    {
        showModal = false;
        editingClass = null;
        classModel = new();
    }

    private async Task SaveClass()
    {
        try
        {
            isSaving = true;

            if (editingClass == null)
            {
                await ApiService.CreateClassAsync(classModel);
                await JSRuntime.InvokeVoidAsync("alert", "تم إضافة الصف بنجاح");
            }
            else
            {
                await ApiService.UpdateClassAsync(editingClass.Id, classModel);
                await JSRuntime.InvokeVoidAsync("alert", "تم تحديث الصف بنجاح");
            }

            CloseModal();
            await LoadData();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حفظ البيانات: {ex.Message}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task DeleteClass(int classId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا الصف؟"))
        {
            try
            {
                await ApiService.DeleteClassAsync(classId);
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف الصف بنجاح");
                await LoadData();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف الصف: {ex.Message}");
            }
        }
    }

    private async Task ViewStudents(int classId)
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم إضافة هذه الميزة قريباً");
    }

    private async Task ViewSchedule(int classId)
    {
        await JSRuntime.InvokeVoidAsync("alert", "سيتم إضافة هذه الميزة قريباً");
    }

    private double GetOccupancyPercentage(Class classItem)
    {
        if (classItem.Capacity == 0) return 0;
        return 0; // سيتم تحديثه لاحقاً عند إضافة الطلاب
    }
}
