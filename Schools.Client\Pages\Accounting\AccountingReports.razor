@page "/accounting/reports"
@using Schools.Client.Services
@using Schools.Shared.DTOs
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Accountant,Admin")]

<PageTitle>التقارير المحاسبية - نظام إدارة المدارس</PageTitle>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-chart-line text-primary me-2"></i>
                        التقارير المحاسبية
                    </h2>
                    <p class="text-muted mb-0">تقارير مالية شاملة ومفصلة للمدرسة</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary" @onclick="RefreshReports">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث التقارير
                    </button>
                    <button class="btn btn-outline-success" @onclick="ExportAllReports">
                        <i class="fas fa-download me-2"></i>
                        تصدير جميع التقارير
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 text-muted">جاري تحميل التقارير المحاسبية...</p>
        </div>
    }
    else
    {
        <!-- Report Type Selection -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            اختيار نوع التقرير والفترة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">نوع التقرير</label>
                                <select @bind="selectedReportType" @bind:after="LoadSelectedReport" class="form-select">
                                    <option value="trial-balance">ميزان المراجعة</option>
                                    <option value="income-statement">قائمة الدخل</option>
                                    <option value="balance-sheet">الميزانية العمومية</option>
                                    <option value="cash-flow">قائمة التدفقات النقدية</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" @bind="fromDate" @bind:after="LoadSelectedReport" />
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" @bind="toDate" @bind:after="LoadSelectedReport" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Content -->
        @if (selectedReportType == "trial-balance")
        {
            <!-- Trial Balance Report -->
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-bottom">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-balance-scale me-2 text-primary"></i>
                                    ميزان المراجعة كما في @toDate.ToString("dd/MM/yyyy")
                                </h5>
                                <button class="btn btn-sm btn-outline-success" @onclick="ExportTrialBalance">
                                    <i class="fas fa-download me-1"></i>
                                    تصدير
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            @if (trialBalance?.Items?.Any() == true)
                            {
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>رمز الحساب</th>
                                                <th>اسم الحساب</th>
                                                <th>نوع الحساب</th>
                                                <th class="text-end">رصيد مدين</th>
                                                <th class="text-end">رصيد دائن</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var item in trialBalance.Items)
                                            {
                                                <tr>
                                                    <td><strong>@item.AccountCode</strong></td>
                                                    <td>@item.AccountName</td>
                                                    <td>
                                                        <span class="badge @GetAccountTypeBadge(item.AccountType)">
                                                            @GetAccountTypeText(item.AccountType)
                                                        </span>
                                                    </td>
                                                    <td class="text-end">
                                                        @if (item.DebitBalance > 0)
                                                        {
                                                            <strong class="text-primary">@item.DebitBalance.ToString("C")</strong>
                                                        }
                                                    </td>
                                                    <td class="text-end">
                                                        @if (item.CreditBalance > 0)
                                                        {
                                                            <strong class="text-success">@item.CreditBalance.ToString("C")</strong>
                                                        }
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                        <tfoot class="table-secondary">
                                            <tr>
                                                <th colspan="3">الإجمالي</th>
                                                <th class="text-end">
                                                    <strong class="text-primary">@trialBalance.TotalDebits.ToString("C")</strong>
                                                </th>
                                                <th class="text-end">
                                                    <strong class="text-success">@trialBalance.TotalCredits.ToString("C")</strong>
                                                </th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-balance-scale fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">لا توجد بيانات لميزان المراجعة</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (selectedReportType == "income-statement")
        {
            <!-- Income Statement Report -->
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-bottom">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-bar me-2 text-success"></i>
                                    قائمة الدخل من @fromDate.ToString("dd/MM/yyyy") إلى @toDate.ToString("dd/MM/yyyy")
                                </h5>
                                <button class="btn btn-sm btn-outline-success" @onclick="ExportIncomeStatement">
                                    <i class="fas fa-download me-1"></i>
                                    تصدير
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            @if (incomeStatement != null)
                            {
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-success mb-3">
                                            <i class="fas fa-arrow-up me-2"></i>
                                            الإيرادات
                                        </h6>
                                        @if (incomeStatement.Revenues?.Any() == true)
                                        {
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <tbody>
                                                        @foreach (var revenue in incomeStatement.Revenues)
                                                        {
                                                            <tr>
                                                                <td>@revenue.AccountName</td>
                                                                <td class="text-end">
                                                                    <strong class="text-success">@revenue.Amount.ToString("C")</strong>
                                                                </td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                    <tfoot class="table-light">
                                                        <tr>
                                                            <th>إجمالي الإيرادات</th>
                                                            <th class="text-end">
                                                                <strong class="text-success">@incomeStatement.TotalRevenues.ToString("C")</strong>
                                                            </th>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                        }
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-danger mb-3">
                                            <i class="fas fa-arrow-down me-2"></i>
                                            المصروفات
                                        </h6>
                                        @if (incomeStatement.Expenses?.Any() == true)
                                        {
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <tbody>
                                                        @foreach (var expense in incomeStatement.Expenses)
                                                        {
                                                            <tr>
                                                                <td>@expense.AccountName</td>
                                                                <td class="text-end">
                                                                    <strong class="text-danger">@expense.Amount.ToString("C")</strong>
                                                                </td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                    <tfoot class="table-light">
                                                        <tr>
                                                            <th>إجمالي المصروفات</th>
                                                            <th class="text-end">
                                                                <strong class="text-danger">@incomeStatement.TotalExpenses.ToString("C")</strong>
                                                            </th>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                        }
                                    </div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="card @(incomeStatement.NetIncome >= 0 ? "bg-success" : "bg-danger") text-white">
                                            <div class="card-body text-center">
                                                <h4 class="mb-1">
                                                    @(incomeStatement.NetIncome >= 0 ? "صافي الربح" : "صافي الخسارة")
                                                </h4>
                                                <h2 class="mb-0">@incomeStatement.NetIncome.ToString("C")</h2>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">لا توجد بيانات لقائمة الدخل</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (selectedReportType == "balance-sheet")
        {
            <!-- Balance Sheet Report -->
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-bottom">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-building me-2 text-info"></i>
                                    الميزانية العمومية كما في @toDate.ToString("dd/MM/yyyy")
                                </h5>
                                <button class="btn btn-sm btn-outline-success" @onclick="ExportBalanceSheet">
                                    <i class="fas fa-download me-1"></i>
                                    تصدير
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            @if (balanceSheet != null)
                            {
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-chart-line me-2"></i>
                                            الأصول
                                        </h6>
                                        @if (balanceSheet.Assets?.Any() == true)
                                        {
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <tbody>
                                                        @foreach (var asset in balanceSheet.Assets)
                                                        {
                                                            <tr>
                                                                <td>@asset.AccountName</td>
                                                                <td class="text-end">
                                                                    <strong class="text-primary">@asset.Amount.ToString("C")</strong>
                                                                </td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                    <tfoot class="table-light">
                                                        <tr>
                                                            <th>إجمالي الأصول</th>
                                                            <th class="text-end">
                                                                <strong class="text-primary">@balanceSheet.TotalAssets.ToString("C")</strong>
                                                            </th>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                        }
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-danger mb-3">
                                            <i class="fas fa-credit-card me-2"></i>
                                            الخصوم وحقوق الملكية
                                        </h6>
                                        @if (balanceSheet.Liabilities?.Any() == true)
                                        {
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <tbody>
                                                        @foreach (var liability in balanceSheet.Liabilities)
                                                        {
                                                            <tr>
                                                                <td>@liability.AccountName</td>
                                                                <td class="text-end">
                                                                    <strong class="text-danger">@liability.Amount.ToString("C")</strong>
                                                                </td>
                                                            </tr>
                                                        }
                                                        @foreach (var equity in balanceSheet.Equity)
                                                        {
                                                            <tr>
                                                                <td>@equity.AccountName</td>
                                                                <td class="text-end">
                                                                    <strong class="text-success">@equity.Amount.ToString("C")</strong>
                                                                </td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                    <tfoot class="table-light">
                                                        <tr>
                                                            <th>إجمالي الخصوم وحقوق الملكية</th>
                                                            <th class="text-end">
                                                                <strong class="text-info">@((balanceSheet.TotalLiabilities + balanceSheet.TotalEquity).ToString("C"))</strong>
                                                            </th>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                        }
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">لا توجد بيانات للميزانية العمومية</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (selectedReportType == "cash-flow")
        {
            <!-- Cash Flow Statement -->
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-bottom">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-water me-2 text-info"></i>
                                    قائمة التدفقات النقدية من @fromDate.ToString("dd/MM/yyyy") إلى @toDate.ToString("dd/MM/yyyy")
                                </h5>
                                <button class="btn btn-sm btn-outline-success" @onclick="ExportCashFlow">
                                    <i class="fas fa-download me-1"></i>
                                    تصدير
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="text-center py-4">
                                <i class="fas fa-water fa-3x text-muted mb-3"></i>
                                <p class="text-muted">قائمة التدفقات النقدية ستكون متاحة قريباً</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    }
</div>

@code {
    private bool isLoading = true;
    private string selectedReportType = "trial-balance";
    private DateTime fromDate = DateTime.Now.AddMonths(-1);
    private DateTime toDate = DateTime.Now;

    private TrialBalanceDto? trialBalance;
    private IncomeStatementDto? incomeStatement;
    private BalanceSheetDto? balanceSheet;
    private CashFlowStatementDto? cashFlowStatement;

    protected override async Task OnInitializedAsync()
    {
        await LoadSelectedReport();
    }

    private async Task LoadSelectedReport()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            switch (selectedReportType)
            {
                case "trial-balance":
                    trialBalance = await ApiService.GetTrialBalanceAsync(toDate);
                    break;
                case "income-statement":
                    incomeStatement = await ApiService.GetIncomeStatementAsync(fromDate, toDate);
                    break;
                case "balance-sheet":
                    balanceSheet = await ApiService.GetBalanceSheetAsync(toDate);
                    break;
                case "cash-flow":
                    // cashFlowStatement = await ApiService.GetCashFlowStatementAsync(fromDate, toDate);
                    break;
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل التقرير: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshReports()
    {
        await LoadSelectedReport();
        await JSRuntime.InvokeVoidAsync("alert", "تم تحديث التقارير بنجاح");
    }

    private async Task ExportAllReports()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة تصدير جميع التقارير ستكون متاحة قريباً");
    }

    private async Task ExportTrialBalance()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة تصدير ميزان المراجعة ستكون متاحة قريباً");
    }

    private async Task ExportIncomeStatement()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة تصدير قائمة الدخل ستكون متاحة قريباً");
    }

    private async Task ExportBalanceSheet()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة تصدير الميزانية العمومية ستكون متاحة قريباً");
    }

    private async Task ExportCashFlow()
    {
        await JSRuntime.InvokeVoidAsync("alert", "ميزة تصدير قائمة التدفقات النقدية ستكون متاحة قريباً");
    }

    private string GetAccountTypeBadge(AccountType accountType)
    {
        return accountType switch
        {
            AccountType.Asset => "bg-primary",
            AccountType.Liability => "bg-danger",
            AccountType.Equity => "bg-success",
            AccountType.Revenue => "bg-info",
            AccountType.Expense => "bg-warning",
            _ => "bg-secondary"
        };
    }

    private string GetAccountTypeText(AccountType accountType)
    {
        return accountType switch
        {
            AccountType.Asset => "أصول",
            AccountType.Liability => "خصوم",
            AccountType.Equity => "حقوق ملكية",
            AccountType.Revenue => "إيرادات",
            AccountType.Expense => "مصروفات",
            _ => "غير محدد"
        };
    }
}
