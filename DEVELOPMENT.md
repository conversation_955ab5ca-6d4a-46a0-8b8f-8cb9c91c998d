# 🛠️ دليل التطوير - نظام إدارة المدارس

## 🏗️ بنية المشروع

```
Schools4/
├── Schools.API/              # Backend API
│   ├── Controllers/          # API Controllers
│   ├── Services/            # Business Logic Services
│   ├── Program.cs           # API Configuration
│   └── SeedData.cs          # Initial Data
├── Schools.Client/          # Frontend Blazor WebAssembly
│   ├── Pages/               # Razor Pages
│   ├── Shared/              # Shared Components
│   ├── Services/            # Client Services
│   ├── wwwroot/             # Static Files
│   └── Program.cs           # Client Configuration
├── Schools.Data/            # Data Layer
│   ├── Migrations/          # EF Migrations
│   ├── SchoolsDbContext.cs  # Database Context
│   └── Schools.Data.csproj  # Data Project
├── Schools.Shared/          # Shared Models
│   ├── Models/              # Entity Models
│   ├── DTOs/                # Data Transfer Objects
│   └── Enums/               # Enumerations
└── README.md
```

## 🔧 إعداد بيئة التطوير

### المتطلبات:
- .NET 9 SDK
- Visual Studio 2022 أو VS Code
- SQL Server (LocalDB أو Express)
- Git

### إعداد IDE:

#### Visual Studio 2022:
1. تثبيت workloads:
   - ASP.NET and web development
   - .NET desktop development
2. Extensions مفيدة:
   - Blazor WASM Debugging
   - Entity Framework Power Tools

#### VS Code:
1. Extensions مطلوبة:
   - C# Dev Kit
   - Blazor WASM Debugger
   - SQL Server (mssql)

## 📊 قاعدة البيانات

### إدارة Migrations:

#### إنشاء Migration جديد:
```bash
dotnet ef migrations add MigrationName --project Schools.Data --startup-project Schools.API
```

#### تطبيق Migration:
```bash
dotnet ef database update --project Schools.Data --startup-project Schools.API
```

#### حذف آخر Migration:
```bash
dotnet ef migrations remove --project Schools.Data --startup-project Schools.API
```

#### إنشاء Script للإنتاج:
```bash
dotnet ef script --project Schools.Data --startup-project Schools.API
```

### إضافة Entity جديد:

1. **أنشئ Model** في `Schools.Shared/Models/`
2. **أضف DbSet** في `SchoolsDbContext.cs`
3. **أنشئ Migration** باستخدام الأمر أعلاه
4. **طبق Migration** على قاعدة البيانات

## 🎯 إضافة ميزات جديدة

### إضافة API Endpoint:

1. **أنشئ Controller** في `Schools.API/Controllers/`
```csharp
[ApiController]
[Route("api/[controller]")]
public class NewFeatureController : ControllerBase
{
    // Actions here
}
```

2. **أضف Service** إذا لزم الأمر في `Schools.API/Services/`
3. **سجل Service** في `Program.cs`

### إضافة صفحة Blazor:

1. **أنشئ Razor Page** في `Schools.Client/Pages/`
```razor
@page "/new-feature"
@using Schools.Shared.Models

<h3>New Feature</h3>

@code {
    // Component logic
}
```

2. **أضف Navigation** في المكونات المناسبة
3. **أضف Authorization** إذا لزم الأمر

### إضافة مكون مشترك:

1. **أنشئ Component** في `Schools.Client/Shared/`
2. **استخدمه** في الصفحات المطلوبة
3. **أضف CSS** إذا لزم الأمر

## 🔐 الأمان والتفويض

### إضافة دور جديد:

1. **أضف الدور** في `SeedData.cs`
2. **استخدم Authorize** في Controllers:
```csharp
[Authorize(Roles = "NewRole")]
public class SecureController : ControllerBase
```

3. **أضف AuthorizeView** في Blazor:
```razor
<AuthorizeView Roles="NewRole">
    <Authorized>
        <!-- Content for authorized users -->
    </Authorized>
    <NotAuthorized>
        <!-- Content for unauthorized users -->
    </NotAuthorized>
</AuthorizeView>
```

### تخصيص Claims:

1. **أضف Claims** في `AuthService.cs`
2. **استخدم Policy-based Authorization**
3. **أنشئ Custom Authorization Handlers**

## 🎨 التصميم والواجهة

### إضافة CSS مخصص:

1. **أضف ملف CSS** في `wwwroot/css/`
2. **اربطه** في `index.html`
3. **استخدم CSS Classes** في المكونات

### استخدام Bootstrap:

```html
<!-- أمثلة على Classes مفيدة -->
<div class="container-fluid">
<div class="row g-3">
<div class="col-md-6">
<button class="btn btn-primary">
<i class="fas fa-icon"></i> Text
```

### إضافة أيقونات:

```html
<!-- Font Awesome -->
<i class="fas fa-icon-name"></i>
<i class="far fa-icon-name"></i>
<i class="fab fa-icon-name"></i>
```

## 🧪 الاختبار

### Unit Tests:

1. **أنشئ مشروع اختبار**:
```bash
dotnet new xunit -n Schools.Tests
```

2. **أضف مراجع**:
```bash
dotnet add reference ../Schools.API/Schools.API.csproj
```

3. **اكتب اختبارات** للـ Controllers والـ Services

### Integration Tests:

1. **استخدم TestServer** لاختبار API
2. **اختبر Blazor Components** باستخدام bUnit
3. **اختبر قاعدة البيانات** باستخدام InMemory Provider

## 📦 النشر والإنتاج

### إعداد للإنتاج:

1. **حدث Connection Strings** في `appsettings.Production.json`
2. **فعّل HTTPS** والـ Security Headers
3. **أضف Logging** المناسب
4. **اختبر الأداء**

### نشر على IIS:

1. **انشر API** كـ Self-Contained
2. **انشر Client** كـ Static Files
3. **أعد كتابة URLs** للـ Blazor Routing

### نشر على Azure:

1. **استخدم Azure App Service** للـ API
2. **استخدم Azure Static Web Apps** للـ Client
3. **استخدم Azure SQL Database**

## 🔍 استكشاف الأخطاء

### أدوات مفيدة:

1. **Browser DevTools** للـ Client-side debugging
2. **Visual Studio Debugger** للـ Server-side
3. **SQL Server Profiler** لمراقبة قاعدة البيانات
4. **Application Insights** للمراقبة في الإنتاج

### مشاكل شائعة:

#### CORS Issues:
```csharp
// في Program.cs
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", builder =>
    {
        builder.AllowAnyOrigin()
               .AllowAnyMethod()
               .AllowAnyHeader();
    });
});
```

#### SignalR Connection:
```csharp
// تأكد من إعداد SignalR بشكل صحيح
builder.Services.AddSignalR();
app.MapHub<NotificationHub>("/notificationHub");
```

## 📈 تحسين الأداء

### Client-side:

1. **استخدم Lazy Loading** للمكونات الكبيرة
2. **فعّل Prerendering** إذا أمكن
3. **ضغط الـ Assets** (CSS, JS)
4. **استخدم CDN** للملفات الثابتة

### Server-side:

1. **فعّل Response Caching**
2. **استخدم Database Indexing**
3. **طبق Pagination** للبيانات الكبيرة
4. **استخدم Async/Await** بشكل صحيح

## 🔄 إدارة الإصدارات

### Git Workflow:

1. **main branch** للإنتاج
2. **develop branch** للتطوير
3. **feature branches** للميزات الجديدة
4. **hotfix branches** للإصلاحات العاجلة

### Semantic Versioning:

- **MAJOR.MINOR.PATCH**
- **1.0.0** → إصدار أولي
- **1.1.0** → ميزة جديدة
- **1.1.1** → إصلاح خطأ

---

**هذا الدليل يساعدك في تطوير وتحسين نظام إدارة المدارس بشكل احترافي 🚀**
