using System.ComponentModel.DataAnnotations;

namespace Schools.Shared.Models
{

    // Schedule
    public class Schedule : BaseEntity
    {
        [Required]
        public int SubjectId { get; set; }

        [Required]
        public int ClassId { get; set; }

        public int? SectionId { get; set; }

        [Required]
        public string TeacherId { get; set; } = string.Empty;

        [Required]
        public int AcademicYearId { get; set; }

        [Required]
        public DayOfWeek DayOfWeek { get; set; }

        [Required]
        public TimeSpan StartTime { get; set; }

        [Required]
        public TimeSpan EndTime { get; set; }

        [StringLength(50)]
        public string? Room { get; set; }

        [Required]
        public DateTime Date { get; set; }

        public ScheduleType Type { get; set; } = ScheduleType.Regular;

        public ScheduleStatus Status { get; set; } = ScheduleStatus.Scheduled;

        public bool IsActive { get; set; } = true;

        public string? Notes { get; set; }

        public string? SubstituteTeacherId { get; set; }

        public string? CancellationReason { get; set; }

        public DateTime? CancelledAt { get; set; }

        public string? CancelledBy { get; set; }

        // Computed properties
        public string TimeSlot => $"{StartTime:hh\\:mm} - {EndTime:hh\\:mm}";

        public TimeSpan Duration => EndTime - StartTime;

        public bool IsToday => Date.Date == DateTime.Today;

        public bool IsUpcoming => Date > DateTime.Now;

        public bool IsPast => Date < DateTime.Now;

        // Navigation properties
        public virtual Subject Subject { get; set; } = null!;
        public virtual Class Class { get; set; } = null!;
        public virtual Section? Section { get; set; }
        public virtual ApplicationUser Teacher { get; set; } = null!;
        public virtual ApplicationUser? SubstituteTeacher { get; set; }
        public virtual AcademicYear AcademicYear { get; set; } = null!;
        public virtual ICollection<Attendance> Attendances { get; set; } = new List<Attendance>();
        public virtual ICollection<StudentParticipation> StudentParticipations { get; set; } = new List<StudentParticipation>();
        public virtual ICollection<ScheduleChange> ScheduleChanges { get; set; } = new List<ScheduleChange>();
    }

    // Attendance moved to AcademicModels.cs to avoid duplication

    // Student Behavior
    public class StudentBehavior : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public DateTime Date { get; set; }

        [Required]
        public BehaviorType Type { get; set; }

        [Required]
        public int Points { get; set; }

        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [StringLength(200)]
        public string? Category { get; set; }

        public string? RecordedBy { get; set; }

        public string? ActionTaken { get; set; }

        public bool IsResolved { get; set; } = false;

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
    }

    // Student Participation
    public class StudentParticipation : BaseEntity
    {
        [Required]
        public string StudentId { get; set; } = string.Empty;

        [Required]
        public int ScheduleId { get; set; }

        [Required]
        public DateTime Date { get; set; }

        [Required]
        [Range(1, 10)]
        public int ParticipationScore { get; set; } // 1-10

        [StringLength(20)]
        public string? ParticipationType { get; set; } // Active, Passive, Excellent, etc.

        public string? Notes { get; set; }

        public string? RecordedBy { get; set; }

        // Navigation properties
        public virtual ApplicationUser Student { get; set; } = null!;
        public virtual Schedule Schedule { get; set; } = null!;
    }



    // Time Table Template
    public class TimeTableTemplate : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public int AcademicYearId { get; set; }

        public int? GradeId { get; set; }

        public int? ClassId { get; set; }

        public DateTime? EffectiveFrom { get; set; }

        public DateTime? EffectiveTo { get; set; }

        public bool IsActive { get; set; } = true;

        public string? Description { get; set; }

        // Navigation properties
        public virtual AcademicYear AcademicYear { get; set; } = null!;
        public virtual Grade? Grade { get; set; }
        public virtual Class? Class { get; set; }
        public virtual ICollection<TimeTableSlot> TimeTableSlots { get; set; } = new List<TimeTableSlot>();
    }

    // Time Table Slot
    public class TimeTableSlot : BaseEntity
    {
        [Required]
        public int TimeTableTemplateId { get; set; }

        [Required]
        public DayOfWeek DayOfWeek { get; set; }

        [Required]
        public TimeSpan StartTime { get; set; }

        [Required]
        public TimeSpan EndTime { get; set; }

        public int? SubjectId { get; set; }

        public string? TeacherId { get; set; }

        [StringLength(50)]
        public string? Room { get; set; }

        [StringLength(100)]
        public string? SlotType { get; set; } // Class, Break, Lunch, Assembly, etc.

        public bool IsBreak { get; set; } = false;

        public string? Notes { get; set; }

        // Navigation properties
        public virtual TimeTableTemplate TimeTableTemplate { get; set; } = null!;
        public virtual Subject? Subject { get; set; }
        public virtual ApplicationUser? Teacher { get; set; }
    }

    // Holiday
    public class Holiday : BaseEntity
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        [Required]
        public HolidayType Type { get; set; }

        public string? Description { get; set; }

        public bool IsRecurring { get; set; } = false;

        public string? RecurrencePattern { get; set; }

        public bool IsActive { get; set; } = true;
    }

    // Schedule Change (for tracking modifications)
    public class ScheduleChange : BaseEntity
    {
        [Required]
        public int ScheduleId { get; set; }

        [Required]
        public ScheduleChangeType ChangeType { get; set; }

        [Required]
        public DateTime OriginalDate { get; set; }

        public DateTime? NewDate { get; set; }

        [Required]
        public TimeSpan OriginalStartTime { get; set; }

        public TimeSpan? NewStartTime { get; set; }

        [Required]
        public TimeSpan OriginalEndTime { get; set; }

        public TimeSpan? NewEndTime { get; set; }

        public string? OriginalRoom { get; set; }

        public string? NewRoom { get; set; }

        public string? OriginalTeacherId { get; set; }

        public string? NewTeacherId { get; set; }

        [Required]
        [StringLength(500)]
        public string Reason { get; set; } = string.Empty;

        public string? RequestedBy { get; set; }

        public string? ApprovedBy { get; set; }

        public DateTime? ApprovedAt { get; set; }

        public ScheduleChangeStatus Status { get; set; } = ScheduleChangeStatus.Pending;

        public string? Notes { get; set; }

        // Navigation properties
        public virtual Schedule Schedule { get; set; } = null!;
        public virtual ApplicationUser? RequestedByUser { get; set; }
        public virtual ApplicationUser? ApprovedByUser { get; set; }
        public virtual ApplicationUser? OriginalTeacher { get; set; }
        public virtual ApplicationUser? NewTeacher { get; set; }
    }

    // Class Period (for defining standard periods)
    public class ClassPeriod : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public int PeriodNumber { get; set; }

        [Required]
        public TimeSpan StartTime { get; set; }

        [Required]
        public TimeSpan EndTime { get; set; }

        public bool IsBreak { get; set; } = false;

        public bool IsActive { get; set; } = true;

        public string? Description { get; set; }

        // Computed property
        public string TimeSlot => $"{StartTime:hh\\:mm} - {EndTime:hh\\:mm}";

        public TimeSpan Duration => EndTime - StartTime;
    }

    // TeacherAssignment and StudentEnrollment moved to AcademicModels.cs to avoid duplication

}
