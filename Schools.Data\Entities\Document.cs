using System.ComponentModel.DataAnnotations;

namespace Schools.Data.Entities
{
    public class Document
    {
        public int Id { get; set; }
        
        [Required]
        [MaxLength(255)]
        public string Name { get; set; } = "";
        
        [MaxLength(1000)]
        public string Description { get; set; } = "";
        
        [Required]
        [MaxLength(10)]
        public string FileType { get; set; } = "";
        
        [Required]
        [MaxLength(50)]
        public string Category { get; set; } = "";
        
        public long Size { get; set; }
        
        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = "";
        
        public DateTime UploadDate { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string UploadedBy { get; set; } = "";
        
        public int ViewCount { get; set; } = 0;
        
        public int DownloadCount { get; set; } = 0;
        
        public bool IsPublic { get; set; } = true;
    }
}
