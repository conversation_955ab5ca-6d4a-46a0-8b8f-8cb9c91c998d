@page "/reports/grades"
@using Schools.Shared.DTOs
@using Schools.Client.Services
@inject IApiService ApiService
@inject IJSRuntime JSRuntime

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        تقارير الدرجات والإحصائيات
                    </h4>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <label class="form-label">المادة</label>
                            <select class="form-select" @bind="selectedSubjectId">
                                <option value="">جميع المواد</option>
                                @if (subjects != null)
                                {
                                    @foreach (var subject in subjects)
                                    {
                                        <option value="@subject.Id">@subject.Name</option>
                                    }
                                }
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">نوع الامتحان</label>
                            <select class="form-select" @bind="selectedExamType">
                                <option value="">جميع الأنواع</option>
                                <option value="Quiz">اختبار قصير</option>
                                <option value="Midterm">امتحان نصفي</option>
                                <option value="Final">امتحان نهائي</option>
                                <option value="Assignment">واجب</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الفصل الدراسي</label>
                            <select class="form-select" @bind="selectedSemester">
                                <option value="">جميع الفصول</option>
                                <option value="First">الفصل الأول</option>
                                <option value="Second">الفصل الثاني</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-info d-block w-100" @onclick="LoadStatistics">
                                <i class="fas fa-chart-line me-2"></i>
                                تحديث الإحصائيات
                            </button>
                        </div>
                    </div>

                    @if (isLoading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-info" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else if (statistics != null)
                    {
                        <!-- Main Statistics Cards -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-users fa-2x mb-2"></i>
                                        <h4>@statistics.TotalStudents</h4>
                                        <p class="mb-0">إجمالي الطلاب</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chart-line fa-2x mb-2"></i>
                                        <h4>@statistics.AverageScore.ToString("F1")%</h4>
                                        <p class="mb-0">المتوسط العام</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-trophy fa-2x mb-2"></i>
                                        <h4>@statistics.HighestScore.ToString("F1")%</h4>
                                        <p class="mb-0">أعلى درجة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                                        <h4>@statistics.PassRate.ToString("F1")%</h4>
                                        <p class="mb-0">معدل النجاح</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Grade Distribution Chart -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">توزيع الدرجات</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span>ممتاز (90-100%)</span>
                                                    <span class="badge bg-success">@GetGradeCount(90, 100)</span>
                                                </div>
                                                <div class="progress mb-3">
                                                    <div class="progress-bar bg-success" style="width: @GetGradePercentage(90, 100)%"></div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span>جيد جداً (80-89%)</span>
                                                    <span class="badge bg-info">@GetGradeCount(80, 89)</span>
                                                </div>
                                                <div class="progress mb-3">
                                                    <div class="progress-bar bg-info" style="width: @GetGradePercentage(80, 89)%"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span>جيد (70-79%)</span>
                                                    <span class="badge bg-warning">@GetGradeCount(70, 79)</span>
                                                </div>
                                                <div class="progress mb-3">
                                                    <div class="progress-bar bg-warning" style="width: @GetGradePercentage(70, 79)%"></div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span>مقبول (60-69%)</span>
                                                    <span class="badge bg-secondary">@GetGradeCount(60, 69)</span>
                                                </div>
                                                <div class="progress mb-3">
                                                    <div class="progress-bar bg-secondary" style="width: @GetGradePercentage(60, 69)%"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span>راسب (أقل من 60%)</span>
                                                    <span class="badge bg-danger">@GetGradeCount(0, 59)</span>
                                                </div>
                                                <div class="progress mb-3">
                                                    <div class="progress-bar bg-danger" style="width: @GetGradePercentage(0, 59)%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">إحصائيات أنواع الامتحانات</h5>
                                    </div>
                                    <div class="card-body">
                                        @if (statistics.ExamTypeStats?.Any() == true)
                                        {
                                            @foreach (var examStat in statistics.ExamTypeStats)
                                            {
                                                <div class="d-flex justify-content-between align-items-center mb-3">
                                                    <div>
                                                        <strong>@GetExamTypeText(examStat.ExamType)</strong>
                                                        <br>
                                                        <small class="text-muted">@examStat.Count امتحان</small>
                                                    </div>
                                                    <div class="text-end">
                                                        <span class="badge bg-primary">@examStat.AverageScore.ToString("F1")%</span>
                                                    </div>
                                                </div>
                                                <div class="progress mb-3">
                                                    <div class="progress-bar" style="width: @examStat.AverageScore%"></div>
                                                </div>
                                            }
                                        }
                                        else
                                        {
                                            <p class="text-muted text-center">لا توجد بيانات</p>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Subject Statistics -->
                        @if (statistics.SubjectStats?.Any() == true)
                        {
                            <div class="row">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">إحصائيات المواد</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>المادة</th>
                                                            <th>عدد الدرجات</th>
                                                            <th>المتوسط</th>
                                                            <th>الأداء</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach (var subjectStat in statistics.SubjectStats.OrderByDescending(s => (decimal)s.AverageScore))
                                                        {
                                                            <tr>
                                                                <td>
                                                                    <strong>@subjectStat.SubjectName</strong>
                                                                </td>
                                                                <td>
                                                                    <span class="badge bg-info">@subjectStat.GradeCount</span>
                                                                </td>
                                                                <td>
                                                                    <span class="badge @GetPerformanceClass((decimal)subjectStat.AverageScore)">
                                                                        @subjectStat.AverageScore.ToString("F1")%
                                                                    </span>
                                                                </td>
                                                                <td>
                                                                    <div class="progress" style="height: 20px;">
                                                                        <div class="progress-bar @GetPerformanceClass((decimal)subjectStat.AverageScore)"
                                                                             style="width: @subjectStat.AverageScore%">
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد إحصائيات</h5>
                            <p class="text-muted">لم يتم العثور على بيانات للمعايير المحددة</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private List<SubjectDto>? subjects;
    private List<AcademicYearDto>? academicYears;
    private GradeStatisticsDto? statistics;
    private List<GradeDto>? allGrades;

    private bool isLoading = true;

    // Filters
    private int? selectedSubjectId;
    private string selectedExamType = "";
    private string selectedSemester = "";
    private int? selectedAcademicYearId;

    protected override async Task OnInitializedAsync()
    {
        await LoadInitialData();
        await LoadStatistics();
    }

    private async Task LoadInitialData()
    {
        try
        {
            var subjectsTask = ApiService.GetSubjectsAsync();
            var academicYearsTask = ApiService.GetAcademicYearsAsync();

            await Task.WhenAll(subjectsTask, academicYearsTask);

            subjects = (await subjectsTask).ToList();
            academicYears = (await academicYearsTask).ToList();

            // Set default academic year
            selectedAcademicYearId = academicYears.FirstOrDefault(y => y.IsActive)?.Id;
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
    }

    private async Task LoadStatistics()
    {
        try
        {
            isLoading = true;

            var statisticsTask = ApiService.GetGradeStatisticsAsync(
                selectedSubjectId,
                selectedExamType,
                selectedSemester,
                selectedAcademicYearId);

            var gradesTask = ApiService.GetStudentGradesAsync(
                null,
                selectedSubjectId,
                selectedExamType,
                selectedSemester,
                selectedAcademicYearId,
                1,
                1000);

            await Task.WhenAll(statisticsTask, gradesTask);

            statistics = await statisticsTask;
            allGrades = (await gradesTask).ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل الإحصائيات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private int GetGradeCount(int minPercentage, int maxPercentage)
    {
        if (allGrades == null) return 0;
        return allGrades.Count(g => g.Percentage >= minPercentage && g.Percentage <= maxPercentage);
    }

    private double GetGradePercentage(int minPercentage, int maxPercentage)
    {
        if (allGrades == null || !allGrades.Any()) return 0;
        var count = GetGradeCount(minPercentage, maxPercentage);
        return (double)count / allGrades.Count * 100;
    }

    private string GetExamTypeText(string examType)
    {
        return examType switch
        {
            "Quiz" => "اختبار قصير",
            "Midterm" => "امتحان نصفي",
            "Final" => "امتحان نهائي",
            "Assignment" => "واجب",
            _ => examType
        };
    }

    private string GetPerformanceClass(decimal average)
    {
        return average switch
        {
            >= 90 => "bg-success",
            >= 80 => "bg-info",
            >= 70 => "bg-warning",
            >= 60 => "bg-secondary",
            _ => "bg-danger"
        };
    }
}
