// Advanced Features JavaScript for Schools Management System

// Download file functionality
window.downloadFile = (filename, content) => {
    const element = document.createElement('a');
    const file = new Blob([content], { type: 'application/json' });
    element.href = URL.createObjectURL(file);
    element.download = filename;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
};

// Print functionality
window.printElement = (elementId) => {
    const element = document.getElementById(elementId);
    if (element) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>طباعة</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background-color: #f2f2f2; }
                        .no-print { display: none; }
                        @media print {
                            .no-print { display: none !important; }
                        }
                    </style>
                </head>
                <body>
                    ${element.innerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
        printWindow.close();
    }
};

// Export to Excel functionality
window.exportToExcel = (tableId, filename) => {
    const table = document.getElementById(tableId);
    if (table) {
        let csv = '';
        const rows = table.querySelectorAll('tr');
        
        for (let i = 0; i < rows.length; i++) {
            const cells = rows[i].querySelectorAll('th, td');
            const rowData = [];
            
            for (let j = 0; j < cells.length; j++) {
                let cellData = cells[j].innerText.replace(/"/g, '""');
                rowData.push('"' + cellData + '"');
            }
            
            csv += rowData.join(',') + '\n';
        }
        
        const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename + '.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
};

// Chart functionality using Chart.js (if available)
window.createChart = (canvasId, type, data, options) => {
    const canvas = document.getElementById(canvasId);
    if (canvas && typeof Chart !== 'undefined') {
        const ctx = canvas.getContext('2d');
        return new Chart(ctx, {
            type: type,
            data: data,
            options: options || {}
        });
    }
    return null;
};

// Notification system
window.showNotification = (message, type = 'info', duration = 5000) => {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, duration);
};

// Local storage helpers
window.saveToLocalStorage = (key, data) => {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (e) {
        console.error('Error saving to localStorage:', e);
        return false;
    }
};

window.loadFromLocalStorage = (key) => {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (e) {
        console.error('Error loading from localStorage:', e);
        return null;
    }
};

// Form validation helpers
window.validateForm = (formId) => {
    const form = document.getElementById(formId);
    if (form) {
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                input.classList.add('is-invalid');
                isValid = false;
            } else {
                input.classList.remove('is-invalid');
            }
        });
        
        return isValid;
    }
    return false;
};

// Auto-save functionality
window.enableAutoSave = (formId, interval = 30000) => {
    const form = document.getElementById(formId);
    if (form) {
        setInterval(() => {
            const formData = new FormData(form);
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            saveToLocalStorage(`autosave_${formId}`, data);
        }, interval);
    }
};

// Theme management
window.setTheme = (theme) => {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
};

window.getTheme = () => {
    return localStorage.getItem('theme') || 'light';
};

// Initialize theme on page load
document.addEventListener('DOMContentLoaded', () => {
    const savedTheme = getTheme();
    setTheme(savedTheme);
});

// Search and filter helpers
window.filterTable = (tableId, searchTerm, columnIndex = -1) => {
    const table = document.getElementById(tableId);
    if (table) {
        const rows = table.querySelectorAll('tbody tr');
        const term = searchTerm.toLowerCase();
        
        rows.forEach(row => {
            let shouldShow = false;
            const cells = row.querySelectorAll('td');
            
            if (columnIndex >= 0 && columnIndex < cells.length) {
                // Search in specific column
                shouldShow = cells[columnIndex].textContent.toLowerCase().includes(term);
            } else {
                // Search in all columns
                cells.forEach(cell => {
                    if (cell.textContent.toLowerCase().includes(term)) {
                        shouldShow = true;
                    }
                });
            }
            
            row.style.display = shouldShow ? '' : 'none';
        });
    }
};

// Pagination helpers
window.setupPagination = (tableId, itemsPerPage = 10) => {
    const table = document.getElementById(tableId);
    if (table) {
        const rows = Array.from(table.querySelectorAll('tbody tr'));
        const totalPages = Math.ceil(rows.length / itemsPerPage);
        let currentPage = 1;
        
        const showPage = (page) => {
            const start = (page - 1) * itemsPerPage;
            const end = start + itemsPerPage;
            
            rows.forEach((row, index) => {
                row.style.display = (index >= start && index < end) ? '' : 'none';
            });
        };
        
        showPage(1);
        
        return {
            totalPages,
            currentPage,
            showPage,
            nextPage: () => {
                if (currentPage < totalPages) {
                    currentPage++;
                    showPage(currentPage);
                }
            },
            prevPage: () => {
                if (currentPage > 1) {
                    currentPage--;
                    showPage(currentPage);
                }
            }
        };
    }
    return null;
};

// Loading spinner
window.showLoading = (elementId) => {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
            </div>
        `;
    }
};

window.hideLoading = (elementId, content = '') => {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = content;
    }
};

// Confirmation dialogs
window.confirmAction = (message, callback) => {
    if (confirm(message)) {
        callback();
    }
};

// Copy to clipboard
window.copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('تم نسخ النص بنجاح', 'success', 2000);
    }).catch(() => {
        showNotification('فشل في نسخ النص', 'danger', 2000);
    });
};

// Initialize tooltips and popovers (Bootstrap)
document.addEventListener('DOMContentLoaded', () => {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(tooltipTriggerEl => {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(popoverTriggerEl => {
        return new bootstrap.Popover(popoverTriggerEl);
    });
});

// Real-time clock
window.startClock = (elementId) => {
    const updateClock = () => {
        const now = new Date();
        const timeString = now.toLocaleString('ar-SA', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = timeString;
        }
    };
    
    updateClock();
    return setInterval(updateClock, 1000);
};

console.log('Advanced Features JavaScript loaded successfully');
