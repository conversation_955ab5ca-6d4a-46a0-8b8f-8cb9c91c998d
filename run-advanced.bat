@echo off
chcp 65001 >nul
title نظام إدارة المدارس المتقدم - Advanced Schools Management System

echo.
echo ================================================
echo 🏫 نظام إدارة المدارس المتقدم
echo ================================================
echo.
echo ✨ الميزات الجديدة:
echo    📅 إدارة الأعوام الدراسية
echo    🎓 إدارة المراحل الدراسية  
echo    📚 إدارة المواد الدراسية
echo    🏛️ إدارة الصفوف الدراسية
echo    📊 التقارير والإحصائيات
echo.

echo 🔍 فحص المتطلبات...
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ .NET SDK غير مثبت
    pause
    exit /b 1
)

echo ✅ .NET SDK متوفر
echo.

echo 🔨 بناء المشروع المحدث...
dotnet build
if errorlevel 1 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo.

echo 🛑 إيقاف العمليات السابقة...
taskkill /f /im dotnet.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo 🚀 تشغيل API المحدث...
start "Schools API - Advanced" cmd /k "echo ================================================ && echo 🔧 API المحدث يعمل على: http://localhost:5261 && echo 📚 Swagger: http://localhost:5261/swagger && echo 🆕 يدعم الآن جميع العمليات الإدارية المتقدمة && echo ================================================ && echo. && dotnet run --project Schools.API"

echo ⏳ انتظار تشغيل API...
timeout /t 10 /nobreak >nul

echo 🌐 تشغيل العميل المحدث...
start "Schools Client - Advanced" cmd /k "echo ================================================ && echo 🌐 العميل المحدث يعمل على: http://localhost:5131 && echo 🆕 يحتوي على صفحات إدارية جديدة && echo ================================================ && echo. && dotnet run --project Schools.Client"

echo ⏳ انتظار تشغيل العميل...
timeout /t 12 /nobreak >nul

echo.
echo ================================================
echo 🎉 تم تشغيل النظام المتقدم بنجاح!
echo ================================================
echo.
echo 📍 الروابط المتاحة:
echo    🌐 العميل: http://localhost:5131
echo    🔧 API: http://localhost:5261
echo    📚 Swagger: http://localhost:5261/swagger
echo.
echo 🆕 الصفحات الجديدة:
echo    📅 /admin/academic-years - إدارة الأعوام الدراسية
echo    🎓 /admin/grades - إدارة المراحل الدراسية
echo    📚 /admin/subjects - إدارة المواد الدراسية
echo    🏛️ /admin/classes - إدارة الصفوف الدراسية
echo    📊 /admin/reports - التقارير والإحصائيات
echo.
echo 🔐 بيانات المدير:
echo    📧 البريد: <EMAIL>
echo    🔑 كلمة المرور: Admin123!
echo.
echo 💡 نصائح للاستخدام:
echo    • سجل دخولك كمدير لرؤية جميع الميزات الجديدة
echo    • استخدم لوحة التحكم للوصول السريع للصفحات
echo    • جرب إضافة أعوام ومراحل ومواد دراسية جديدة
echo    • استكشف صفحة التقارير للإحصائيات
echo.

echo 🌐 فتح المتصفح...
timeout /t 2 /nobreak >nul
start http://localhost:5131

echo.
echo ✨ استمتع بالنظام المتقدم!
echo ================================================
echo.
echo اضغط أي مفتاح للخروج من هذه النافذة...
echo (نوافذ API والعميل ستبقى مفتوحة)
pause >nul
