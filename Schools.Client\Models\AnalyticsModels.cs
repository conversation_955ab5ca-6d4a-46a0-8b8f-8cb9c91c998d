namespace Schools.Client.Models
{
    public class AnalyticsOverviewModel
    {
        public int TotalStudents { get; set; }
        public int TotalTeachers { get; set; }
        public int TotalClasses { get; set; }
        public int TotalSubjects { get; set; }
        public double AverageGPA { get; set; }
        public double AttendanceRate { get; set; }
        public double StudentGrowthRate { get; set; }
        public double SatisfactionScore { get; set; }
        public double GPAImprovement { get; set; }
    }

    public class PerformanceTrendsModel
    {
        public string TimeRange { get; set; } = "";
        public List<MonthlyPerformanceModel> MonthlyData { get; set; } = new();
    }

    public class MonthlyPerformanceModel
    {
        public string Month { get; set; } = "";
        public string MonthName { get; set; } = "";
        public double AverageGPA { get; set; }
        public double AttendanceRate { get; set; }
    }

    public class SubjectPerformanceModel
    {
        public List<SubjectPerformanceItemModel> Subjects { get; set; } = new();
    }

    public class SubjectPerformanceItemModel
    {
        public string SubjectName { get; set; } = "";
        public double AverageScore { get; set; }
        public int TotalStudents { get; set; }
        public double PassingRate { get; set; }
    }

    public class GradeDistributionModel
    {
        public Dictionary<string, int> Distribution { get; set; } = new();
        public int TotalGrades { get; set; }
    }

    public class AttendanceHeatmapModel
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<DailyAttendanceModel> DailyData { get; set; } = new();
    }

    public class DailyAttendanceModel
    {
        public DateTime Date { get; set; }
        public double AttendanceRate { get; set; }
        public int TotalStudents { get; set; }
        public int PresentStudents { get; set; }
    }

    public class PredictiveAnalyticsModel
    {
        public double PredictedStudentGrowth { get; set; }
        public double PredictedPerformanceImprovement { get; set; }
        public double PredictedAttendanceRate { get; set; }
        public double PredictedGPA { get; set; }
        public double ConfidenceLevel { get; set; }
        public DateTime PredictionDate { get; set; }
    }

    public class RecommendationModel
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Priority { get; set; } = "";
        public string Category { get; set; } = "";
        public string EstimatedImpact { get; set; } = "";
        public string Icon { get; set; } = "";
        public string Color { get; set; } = "";
    }

    public class RiskAnalysisModel
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Severity { get; set; } = "";
        public int RiskLevel { get; set; }
        public string Category { get; set; } = "";
        public string[] RecommendedActions { get; set; } = Array.Empty<string>();
        public string Icon { get; set; } = "";
    }

    public class ChartDataModel
    {
        public string[] Labels { get; set; } = Array.Empty<string>();
        public List<ChartDatasetModel> Datasets { get; set; } = new();
    }

    public class ChartDatasetModel
    {
        public string Label { get; set; } = "";
        public double[] Data { get; set; } = Array.Empty<double>();
        public string BorderColor { get; set; } = "";
        public string BackgroundColor { get; set; } = "";
        public double Tension { get; set; } = 0.4;
        public bool Fill { get; set; } = false;
    }

    public static class AnalyticsConstants
    {
        public static readonly Dictionary<string, string> TimeRanges = new()
        {
            { "6months", "6 أشهر" },
            { "1year", "سنة واحدة" },
            { "2years", "سنتان" }
        };

        public static readonly Dictionary<string, string> PriorityColors = new()
        {
            { "high", "danger" },
            { "medium", "warning" },
            { "low", "info" }
        };

        public static readonly Dictionary<string, string> SeverityColors = new()
        {
            { "high", "danger" },
            { "medium", "warning" },
            { "low", "info" }
        };

        public static readonly Dictionary<string, string> CategoryIcons = new()
        {
            { "academic", "fas fa-graduation-cap" },
            { "attendance", "fas fa-calendar-check" },
            { "engagement", "fas fa-users" },
            { "resources", "fas fa-tools" },
            { "financial", "fas fa-dollar-sign" }
        };

        public static readonly string[] ChartColors = new[]
        {
            "#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF", "#FF9F40",
            "#FF6384", "#C9CBCF", "#4BC0C0", "#FF6384", "#36A2EB", "#FFCE56"
        };
    }

    public class AnalyticsFilterModel
    {
        public string TimeRange { get; set; } = "6months";
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Category { get; set; }
        public string? SubjectId { get; set; }
        public string? ClassId { get; set; }
    }

    public class KPIModel
    {
        public string Title { get; set; } = "";
        public string Value { get; set; } = "";
        public string Change { get; set; } = "";
        public string ChangeType { get; set; } = ""; // positive, negative, neutral
        public string Icon { get; set; } = "";
        public string Color { get; set; } = "";
        public string Description { get; set; } = "";
    }

    public class TrendModel
    {
        public string Period { get; set; } = "";
        public double Value { get; set; }
        public double Change { get; set; }
        public string ChangeDirection { get; set; } = ""; // up, down, stable
    }
}
