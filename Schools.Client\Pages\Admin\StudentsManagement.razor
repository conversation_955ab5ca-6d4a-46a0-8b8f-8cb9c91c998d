@page "/admin/students"
@using Schools.Shared.Models
@using Schools.Shared.DTOs
@using Schools.Client.Services
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Admin")]

<PageTitle>إدارة الطلاب</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-user-graduate me-2"></i>
                        إدارة الطلاب
                    </h4>
                    <div class="d-flex gap-2">
                        <button class="btn btn-light" @onclick="ShowAddStudentModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة طالب جديد
                        </button>
                        <div class="btn-group">
                            <button class="btn btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-filter me-2"></i>
                                فلترة: @GetGradeDisplayName(selectedGrade)
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" @onclick="@(() => FilterByGrade(0))">جميع المراحل</a></li>
                                <li><a class="dropdown-item" href="#" @onclick="@(() => FilterByGrade(1))">الابتدائية</a></li>
                                <li><a class="dropdown-item" href="#" @onclick="@(() => FilterByGrade(2))">المتوسطة</a></li>
                                <li><a class="dropdown-item" href="#" @onclick="@(() => FilterByGrade(3))">الثانوية</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else
                    {
                        <!-- Statistics Cards -->
                        <div class="row mb-4">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>@totalStudents</h4>
                                                <p class="mb-0">إجمالي الطلاب</p>
                                            </div>
                                            <i class="fas fa-users fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>@activeStudents</h4>
                                                <p class="mb-0">طلاب نشطين</p>
                                            </div>
                                            <i class="fas fa-user-check fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>@maleStudents</h4>
                                                <p class="mb-0">طلاب ذكور</p>
                                            </div>
                                            <i class="fas fa-male fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>@femaleStudents</h4>
                                                <p class="mb-0">طلاب إناث</p>
                                            </div>
                                            <i class="fas fa-female fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Search Bar -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" placeholder="البحث عن طالب..." @bind="searchTerm" @oninput="OnSearchChanged">
                                </div>
                            </div>
                        </div>

                        <!-- Students Table -->
                        @if (filteredStudents?.Any() == true)
                        {
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>الطالب</th>
                                            <th>رقم الطالب</th>
                                            <th>المرحلة</th>
                                            <th>الصف</th>
                                            <th>الجنس</th>
                                            <th>تاريخ الميلاد</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var student in filteredStudents.Take(20))
                                        {
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                                            <i class="fas fa-user-graduate text-white"></i>
                                                        </div>
                                                        <div>
                                                            <strong>@student.FirstName @student.LastName</strong>
                                                            <br><small class="text-muted">@student.Email</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>@student.StudentNumber</td>
                                                <td>
                                                    <span class="badge bg-@GetGradeColor(student.GradeLevel)">
                                                        @GetGradeLevelName(student.GradeLevel)
                                                    </span>
                                                </td>
                                                <td>@student.ClassName</td>
                                                <td>
                                                    <i class="fas @(student.Gender == "Male" ? "fa-male text-primary" : "fa-female text-danger")"></i>
                                                    @(student.Gender == "Male" ? "ذكر" : "أنثى")
                                                </td>
                                                <td>@student.DateOfBirth?.ToString("dd/MM/yyyy")</td>
                                                <td>
                                                    @if (student.IsActive)
                                                    {
                                                        <span class="badge bg-success">نشط</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-danger">غير نشط</span>
                                                    }
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-primary" @onclick="() => ViewStudentDetails(student.Id)" title="عرض التفاصيل">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-warning" @onclick="() => EditStudent(student.Id)" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        @if (student.IsActive)
                                                        {
                                                            <button class="btn btn-sm btn-secondary" @onclick="() => DeactivateStudent(student.Id)" title="إلغاء تفعيل">
                                                                <i class="fas fa-pause"></i>
                                                            </button>
                                                        }
                                                        else
                                                        {
                                                            <button class="btn btn-sm btn-success" @onclick="() => ActivateStudent(student.Id)" title="تفعيل">
                                                                <i class="fas fa-play"></i>
                                                            </button>
                                                        }
                                                        <button class="btn btn-sm btn-danger" @onclick="() => DeleteStudent(student.Id)" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>

                            @if (filteredStudents.Count() > 20)
                            {
                                <div class="text-center mt-3">
                                    <p class="text-muted">عرض 20 من أصل @filteredStudents.Count() طالب</p>
                                    <button class="btn btn-outline-primary" @onclick="LoadMoreStudents">
                                        تحميل المزيد
                                    </button>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد طلاب</h5>
                                <p class="text-muted">لم يتم العثور على طلاب بالفلتر المحدد</p>
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .avatar-sm {
        width: 40px;
        height: 40px;
    }
</style>

@code {
    private List<StudentDto> students = new();
    private List<StudentDto> filteredStudents = new();
    private bool isLoading = true;
    private int selectedGrade = 0;
    private string searchTerm = string.Empty;

    private int totalStudents => students.Count;
    private int activeStudents => students.Count(s => s.IsActive);
    private int maleStudents => students.Count(s => s.Gender == "Male");
    private int femaleStudents => students.Count(s => s.Gender == "Female");

    protected override async Task OnInitializedAsync()
    {
        await LoadStudents();
    }

    private async Task LoadStudents()
    {
        try
        {
            isLoading = true;
            // Mock data for demonstration
            students = GenerateMockStudents();
            FilterStudents();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private List<StudentDto> GenerateMockStudents()
    {
        var mockStudents = new List<StudentDto>();
        var random = new Random();
        var firstNames = new[] { "أحمد", "محمد", "فاطمة", "عائشة", "علي", "خديجة", "عبدالله", "مريم", "يوسف", "زينب" };
        var lastNames = new[] { "الأحمد", "المحمد", "العلي", "الحسن", "الزهراني", "القحطاني", "الغامدي", "العتيبي" };
        var grades = new[] { GradeLevel.Elementary, GradeLevel.Middle, GradeLevel.High };
        var classes = new[] { "الأول أ", "الثاني ب", "الثالث ج", "الرابع أ", "الخامس ب", "السادس ج" };

        for (int i = 1; i <= 150; i++)
        {
            var gender = random.Next(2) == 0 ? "Male" : "Female";
            var gradeLevel = grades[random.Next(grades.Length)];

            mockStudents.Add(new StudentDto
            {
                Id = i.ToString(),
                FirstName = firstNames[random.Next(firstNames.Length)],
                LastName = lastNames[random.Next(lastNames.Length)],
                Email = $"student{i}@school.com",
                StudentNumber = $"STU{i:D4}",
                GradeLevel = gradeLevel,
                ClassName = classes[random.Next(classes.Length)],
                Gender = gender,
                DateOfBirth = DateTime.Now.AddYears(-random.Next(6, 18)),
                IsActive = random.Next(10) > 1 // 90% active
            });
        }

        return mockStudents;
    }

    private void FilterByGrade(int grade)
    {
        selectedGrade = grade;
        FilterStudents();
    }

    private void FilterStudents()
    {
        var query = students.AsEnumerable();

        if (selectedGrade > 0)
        {
            query = query.Where(s => (int)s.GradeLevel == selectedGrade);
        }

        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(s =>
                s.FirstName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                s.LastName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                s.StudentNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                s.Email.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
        }

        filteredStudents = query.ToList();
        StateHasChanged();
    }

    private async Task OnSearchChanged(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? string.Empty;
        FilterStudents();
    }

    private void ShowAddStudentModal()
    {
        // Navigate to add student page
        Navigation.NavigateTo("/admin/students/add");
    }

    private void ViewStudentDetails(string studentId)
    {
        Navigation.NavigateTo($"/admin/students/{studentId}");
    }

    private void EditStudent(string studentId)
    {
        Navigation.NavigateTo($"/admin/students/{studentId}/edit");
    }

    private async Task ActivateStudent(string studentId)
    {
        try
        {
            // Call API to activate student
            await JSRuntime.InvokeVoidAsync("alert", "تم تفعيل الطالب بنجاح");
            await LoadStudents();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تفعيل الطالب: {ex.Message}");
        }
    }

    private async Task DeactivateStudent(string studentId)
    {
        try
        {
            // Call API to deactivate student
            await JSRuntime.InvokeVoidAsync("alert", "تم إلغاء تفعيل الطالب بنجاح");
            await LoadStudents();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في إلغاء تفعيل الطالب: {ex.Message}");
        }
    }

    private async Task DeleteStudent(string studentId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا الطالب؟"))
        {
            try
            {
                // Call API to delete student
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف الطالب بنجاح");
                await LoadStudents();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف الطالب: {ex.Message}");
            }
        }
    }

    private async Task LoadMoreStudents()
    {
        // Implement pagination
        await JSRuntime.InvokeVoidAsync("alert", "تم تحميل المزيد من الطلاب");
    }

    private string GetGradeDisplayName(int grade)
    {
        return grade switch
        {
            1 => "الابتدائية",
            2 => "المتوسطة",
            3 => "الثانوية",
            _ => "جميع المراحل"
        };
    }

    private string GetGradeLevelName(GradeLevel level)
    {
        return level switch
        {
            GradeLevel.Elementary => "ابتدائي",
            GradeLevel.Middle => "متوسط",
            GradeLevel.High => "ثانوي",
            _ => "غير محدد"
        };
    }

    private string GetGradeColor(GradeLevel level)
    {
        return level switch
        {
            GradeLevel.Elementary => "success",
            GradeLevel.Middle => "warning",
            GradeLevel.High => "danger",
            _ => "secondary"
        };
    }

    public class StudentDto
    {
        public string Id { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string StudentNumber { get; set; } = string.Empty;
        public GradeLevel GradeLevel { get; set; }
        public string ClassName { get; set; } = string.Empty;
        public string Gender { get; set; } = string.Empty;
        public DateTime? DateOfBirth { get; set; }
        public bool IsActive { get; set; }
    }
}
