@echo off
chcp 65001 >nul
title نظام إدارة المدارس - تشغيل محسن

echo.
echo ================================================
echo 🏫 نظام إدارة المدارس - Schools Management System
echo ================================================
echo.

echo 🔍 فحص المتطلبات...
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ .NET SDK غير مثبت
    echo يرجى تثبيت .NET 9 SDK من: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo ✅ .NET SDK متوفر
echo.

echo 🔨 بناء المشروع...
dotnet build
if errorlevel 1 (
    echo ❌ فشل في بناء المشروع
    echo تحقق من الأخطاء أعلاه
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo.

echo 🛑 إيقاف العمليات السابقة...
taskkill /f /im dotnet.exe >nul 2>&1

echo ⏳ انتظار...
timeout /t 2 /nobreak >nul

echo 🚀 تشغيل API...
start "Schools API - لا تغلق هذه النافذة" cmd /k "echo ================================================ && echo 🔧 API يعمل على: http://localhost:5261 && echo 📚 Swagger: http://localhost:5261/swagger && echo ================================================ && echo. && dotnet run --project Schools.API"

echo ⏳ انتظار تشغيل API...
timeout /t 10 /nobreak >nul

echo 🌐 تشغيل العميل...
start "Schools Client - لا تغلق هذه النافذة" cmd /k "echo ================================================ && echo 🌐 العميل يعمل على: http://localhost:5131 && echo ================================================ && echo. && dotnet run --project Schools.Client"

echo ⏳ انتظار تشغيل العميل...
timeout /t 12 /nobreak >nul

echo.
echo ================================================
echo 🎉 تم تشغيل النظام بنجاح!
echo ================================================
echo.
echo 📍 الروابط المتاحة:
echo    🌐 العميل: http://localhost:5131
echo    🔧 API: http://localhost:5261
echo    📚 Swagger: http://localhost:5261/swagger
echo.
echo 🔐 بيانات المدير:
echo    📧 البريد: <EMAIL>
echo    🔑 كلمة المرور: Admin123!
echo.
echo 💡 نصائح:
echo    • لا تغلق نوافذ API أو العميل
echo    • إذا واجهت مشاكل، أعد تشغيل هذا الملف
echo    • راجع TROUBLESHOOTING.md للمساعدة
echo.

echo 🌐 فتح المتصفح...
timeout /t 2 /nobreak >nul
start http://localhost:5131

echo.
echo ✨ استمتع باستخدام النظام!
echo.
echo اضغط أي مفتاح للخروج من هذه النافذة...
echo (نوافذ API والعميل ستبقى مفتوحة)
pause >nul
