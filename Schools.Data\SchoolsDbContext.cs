﻿using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Schools.Shared.Models;
using Schools.Data.Entities;

namespace Schools.Data
{
    public class SchoolsDbContext : IdentityDbContext<ApplicationUser>
    {
        public SchoolsDbContext(DbContextOptions<SchoolsDbContext> options) : base(options)
        {
        }

        // Academic Entities
        public DbSet<AcademicYear> AcademicYears { get; set; }
        public DbSet<Grade> Grades { get; set; }
        public DbSet<Class> Classes { get; set; }
        public DbSet<Section> Sections { get; set; }
        public DbSet<Subject> Subjects { get; set; }

        // User Related Entities
        public DbSet<StudentEnrollment> StudentEnrollments { get; set; }
        public DbSet<TeacherAssignment> TeacherAssignments { get; set; }
        public DbSet<ParentStudent> ParentStudents { get; set; }

        // Schedule and Attendance
        public DbSet<Schedule> Schedules { get; set; }
        public DbSet<Attendance> Attendances { get; set; }
        public DbSet<EmployeeAttendance> EmployeeAttendances { get; set; }

        // Behavior and Participation
        public DbSet<StudentBehavior> StudentBehaviors { get; set; }
        public DbSet<StudentParticipation> StudentParticipations { get; set; }

        // Exams and Assignments
        public DbSet<Exam> Exams { get; set; }
        public DbSet<ExamQuestion> ExamQuestions { get; set; }
        public DbSet<ExamQuestionOption> ExamQuestionOptions { get; set; }
        public DbSet<StudentExamResult> StudentExamResults { get; set; }
        public DbSet<StudentExamAnswer> StudentExamAnswers { get; set; }
        public DbSet<Assignment> Assignments { get; set; }
        public DbSet<StudentAssignmentSubmission> StudentAssignmentSubmissions { get; set; }

        // Requests and Notifications
        public DbSet<Request> Requests { get; set; }
        public DbSet<Notification> Notifications { get; set; }

        // Financial
        public DbSet<FeeStructure> FeeStructures { get; set; }
        public DbSet<StudentFeePayment> StudentFeePayments { get; set; }
        public DbSet<EmployeeSalary> EmployeeSalaries { get; set; }

        // Reports
        public DbSet<Report> Reports { get; set; }

        // Library
        public DbSet<Book> Books { get; set; }
        public DbSet<BorrowRecord> BorrowRecords { get; set; }

        // Activities
        public DbSet<Activity> Activities { get; set; }
        public DbSet<ActivityParticipant> ActivityParticipants { get; set; }

        // Student Grades
        public DbSet<StudentGrade> StudentGrades { get; set; }
        public DbSet<GradeScale> GradeScales { get; set; }
        public DbSet<StudentReportCard> StudentReportCards { get; set; }

        // Library Extended
        public DbSet<BookReservation> BookReservations { get; set; }
        public DbSet<BookReview> BookReviews { get; set; }
        public DbSet<LibraryCard> LibraryCards { get; set; }
        public DbSet<DigitalResource> DigitalResources { get; set; }
        public DbSet<DigitalResourceAccess> DigitalResourceAccesses { get; set; }

        // Activities Extended
        public DbSet<ActivityResource> ActivityResources { get; set; }
        public DbSet<ActivityFeedback> ActivityFeedbacks { get; set; }
        public DbSet<Schools.Shared.Models.Event> Events { get; set; }
        public DbSet<EventRegistration> EventRegistrations { get; set; }
        public DbSet<Schools.Data.Entities.EventParticipant> EventParticipants { get; set; }

        // Document Management
        public DbSet<Document> Documents { get; set; }

        // Schedule Extended
        public DbSet<TimeTableTemplate> TimeTableTemplates { get; set; }
        public DbSet<TimeTableSlot> TimeTableSlots { get; set; }
        public DbSet<Holiday> Holidays { get; set; }
        public DbSet<ScheduleChange> ScheduleChanges { get; set; }
        public DbSet<ClassPeriod> ClassPeriods { get; set; }


        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure relationships and constraints
            ConfigureAcademicEntities(builder);
            ConfigureUserRelatedEntities(builder);
            ConfigureExamEntities(builder);
            ConfigureFinancialEntities(builder);
            ConfigureOtherEntities(builder);
            ConfigureLibraryEntities(builder);
            ConfigureActivityEntities(builder);
            ConfigureGradeEntities(builder);
            ConfigureExtendedEntities(builder);
        }

        private void ConfigureAcademicEntities(ModelBuilder builder)
        {
            // AcademicGrade -> Classes (One-to-Many)
            builder.Entity<Class>()
                .HasOne(c => c.AcademicGrade)
                .WithMany(g => g.Classes)
                .HasForeignKey(c => c.AcademicGradeId)
                .OnDelete(DeleteBehavior.Restrict);

            // Class -> Sections (One-to-Many)
            builder.Entity<Section>()
                .HasOne(s => s.Class)
                .WithMany(c => c.Sections)
                .HasForeignKey(s => s.ClassId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure decimal properties
            builder.Entity<StudentFeePayment>()
                .Property(p => p.AmountPaid)
                .HasColumnType("decimal(18,2)");

            builder.Entity<StudentFeePayment>()
                .Property(p => p.Discount)
                .HasColumnType("decimal(18,2)");

            builder.Entity<StudentFeePayment>()
                .Property(p => p.Fine)
                .HasColumnType("decimal(18,2)");

            builder.Entity<FeeStructure>()
                .Property(f => f.TotalAmount)
                .HasColumnType("decimal(18,2)");

            builder.Entity<EmployeeSalary>()
                .Property(s => s.BasicSalary)
                .HasColumnType("decimal(18,2)");

            builder.Entity<EmployeeSalary>()
                .Property(s => s.Allowances)
                .HasColumnType("decimal(18,2)");

            builder.Entity<EmployeeSalary>()
                .Property(s => s.Deductions)
                .HasColumnType("decimal(18,2)");

            builder.Entity<EmployeeSalary>()
                .Property(s => s.NetSalary)
                .HasColumnType("decimal(18,2)");

            builder.Entity<StudentExamResult>()
                .Property(r => r.Percentage)
                .HasColumnType("decimal(5,2)");
        }

        private void ConfigureUserRelatedEntities(ModelBuilder builder)
        {
            // Student Enrollment relationships
            builder.Entity<StudentEnrollment>()
                .HasOne(se => se.Student)
                .WithMany(u => u.StudentEnrollments)
                .HasForeignKey(se => se.StudentId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<StudentEnrollment>()
                .HasOne(se => se.AcademicYear)
                .WithMany(ay => ay.StudentEnrollments)
                .HasForeignKey(se => se.AcademicYearId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<StudentEnrollment>()
                .HasOne(se => se.Class)
                .WithMany(c => c.StudentEnrollments)
                .HasForeignKey(se => se.ClassId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<StudentEnrollment>()
                .HasOne(se => se.Section)
                .WithMany(s => s.StudentEnrollments)
                .HasForeignKey(se => se.SectionId)
                .OnDelete(DeleteBehavior.SetNull);

            // Teacher Assignment relationships
            builder.Entity<TeacherAssignment>()
                .HasOne(ta => ta.Teacher)
                .WithMany(u => u.TeacherAssignments)
                .HasForeignKey(ta => ta.TeacherId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<TeacherAssignment>()
                .HasOne(ta => ta.Subject)
                .WithMany(s => s.TeacherAssignments)
                .HasForeignKey(ta => ta.SubjectId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<TeacherAssignment>()
                .HasOne(ta => ta.Class)
                .WithMany(c => c.TeacherAssignments)
                .HasForeignKey(ta => ta.ClassId)
                .OnDelete(DeleteBehavior.Restrict);

            // Parent-Student relationships
            builder.Entity<ParentStudent>()
                .HasOne(ps => ps.Parent)
                .WithMany(u => u.ParentStudents)
                .HasForeignKey(ps => ps.ParentId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<ParentStudent>()
                .HasOne(ps => ps.Student)
                .WithMany(u => u.StudentParents)
                .HasForeignKey(ps => ps.StudentId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigureExamEntities(ModelBuilder builder)
        {
            // Exam relationships
            builder.Entity<Exam>()
                .HasOne(e => e.Subject)
                .WithMany(s => s.Exams)
                .HasForeignKey(e => e.SubjectId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Exam>()
                .HasOne(e => e.Class)
                .WithMany(c => c.Exams)
                .HasForeignKey(e => e.ClassId)
                .OnDelete(DeleteBehavior.Restrict);

            // Exam Question relationships
            builder.Entity<ExamQuestion>()
                .HasOne(eq => eq.Exam)
                .WithMany(e => e.Questions)
                .HasForeignKey(eq => eq.ExamId)
                .OnDelete(DeleteBehavior.Cascade);

            // Question Option relationships
            builder.Entity<QuestionOption>()
                .HasOne(qo => qo.Question)
                .WithMany(q => q.Options)
                .HasForeignKey(qo => qo.QuestionId)
                .OnDelete(DeleteBehavior.Cascade);

            // Exam Attempt relationships
            builder.Entity<ExamAttempt>()
                .HasOne(ea => ea.Student)
                .WithMany()
                .HasForeignKey(ea => ea.StudentId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<ExamAttempt>()
                .HasOne(ea => ea.Exam)
                .WithMany(e => e.ExamAttempts)
                .HasForeignKey(ea => ea.ExamId)
                .OnDelete(DeleteBehavior.Restrict);

            // Student Answer relationships
            builder.Entity<StudentAnswer>()
                .HasOne(sa => sa.Attempt)
                .WithMany(ea => ea.StudentAnswers)
                .HasForeignKey(sa => sa.AttemptId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<StudentAnswer>()
                .HasOne(sa => sa.Question)
                .WithMany()
                .HasForeignKey(sa => sa.QuestionId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigureFinancialEntities(ModelBuilder builder)
        {
            // Fee Structure relationships
            builder.Entity<FeeStructure>()
                .HasOne(fs => fs.AcademicYear)
                .WithMany()
                .HasForeignKey(fs => fs.AcademicYearId)
                .OnDelete(DeleteBehavior.Restrict);

            // Student Fee Payment relationships
            builder.Entity<StudentFeePayment>()
                .HasOne(sfp => sfp.Student)
                .WithMany()
                .HasForeignKey(sfp => sfp.StudentId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<StudentFeePayment>()
                .HasOne(sfp => sfp.FeeStructure)
                .WithMany(fs => fs.StudentFeePayments)
                .HasForeignKey(sfp => sfp.FeeStructureId)
                .OnDelete(DeleteBehavior.Restrict);

            // Employee Salary relationships
            builder.Entity<EmployeeSalary>()
                .HasOne(es => es.Employee)
                .WithMany()
                .HasForeignKey(es => es.EmployeeId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigureOtherEntities(ModelBuilder builder)
        {
            // Assignment relationships
            builder.Entity<Assignment>()
                .HasOne(a => a.Subject)
                .WithMany(s => s.Assignments)
                .HasForeignKey(a => a.SubjectId)
                .OnDelete(DeleteBehavior.Restrict);

            // Student Assignment Submission relationships
            builder.Entity<StudentAssignmentSubmission>()
                .HasOne(sas => sas.Student)
                .WithMany()
                .HasForeignKey(sas => sas.StudentId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<StudentAssignmentSubmission>()
                .HasOne(sas => sas.Assignment)
                .WithMany(a => a.StudentAssignmentSubmissions)
                .HasForeignKey(sas => sas.AssignmentId)
                .OnDelete(DeleteBehavior.Restrict);

            // Schedule relationships
            builder.Entity<Schedule>()
                .HasOne(s => s.Subject)
                .WithMany(sub => sub.Schedules)
                .HasForeignKey(s => s.SubjectId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Schedule>()
                .HasOne(s => s.Class)
                .WithMany(c => c.Schedules)
                .HasForeignKey(s => s.ClassId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Schedule>()
                .HasOne(s => s.Teacher)
                .WithMany()
                .HasForeignKey(s => s.TeacherId)
                .OnDelete(DeleteBehavior.SetNull);

            // Attendance relationships
            builder.Entity<Attendance>()
                .HasOne(a => a.Student)
                .WithMany()
                .HasForeignKey(a => a.StudentId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Attendance>()
                .HasOne(a => a.Subject)
                .WithMany()
                .HasForeignKey(a => a.SubjectId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.Entity<Attendance>()
                .HasOne(a => a.Schedule)
                .WithMany()
                .HasForeignKey(a => a.ScheduleId)
                .OnDelete(DeleteBehavior.SetNull);

            // Request relationships
            builder.Entity<Request>()
                .HasOne(r => r.Requester)
                .WithMany(u => u.Requests)
                .HasForeignKey(r => r.RequesterId)
                .OnDelete(DeleteBehavior.Restrict);

            // Notification relationships
            builder.Entity<Notification>()
                .HasOne(n => n.User)
                .WithMany()
                .HasForeignKey(n => n.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // Report relationships
            builder.Entity<Report>()
                .HasOne(r => r.GeneratedByUser)
                .WithMany()
                .HasForeignKey(r => r.GeneratedBy)
                .OnDelete(DeleteBehavior.Restrict);

            // Student Behavior relationships
            builder.Entity<StudentBehavior>()
                .HasOne(sb => sb.Student)
                .WithMany()
                .HasForeignKey(sb => sb.StudentId)
                .OnDelete(DeleteBehavior.Restrict);

            // Student Participation relationships
            builder.Entity<StudentParticipation>()
                .HasOne(sp => sp.Student)
                .WithMany()
                .HasForeignKey(sp => sp.StudentId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<StudentParticipation>()
                .HasOne(sp => sp.Schedule)
                .WithMany()
                .HasForeignKey(sp => sp.ScheduleId)
                .OnDelete(DeleteBehavior.Restrict);

            // Employee Attendance relationships
            builder.Entity<EmployeeAttendance>()
                .HasOne(ea => ea.Employee)
                .WithMany()
                .HasForeignKey(ea => ea.EmployeeId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes for better performance
            builder.Entity<StudentEnrollment>()
                .HasIndex(se => new { se.StudentId, se.AcademicYearId })
                .IsUnique();

            builder.Entity<TeacherAssignment>()
                .HasIndex(ta => new { ta.TeacherId, ta.SubjectId, ta.ClassId });

            builder.Entity<Attendance>()
                .HasIndex(a => new { a.StudentId, a.Date });

            builder.Entity<EmployeeAttendance>()
                .HasIndex(ea => new { ea.EmployeeId, ea.Date })
                .IsUnique();

            builder.Entity<StudentFeePayment>()
                .HasIndex(sfp => new { sfp.StudentId, sfp.FeeStructureId });
        }

        private void ConfigureLibraryEntities(ModelBuilder builder)
        {
            // Book configuration
            builder.Entity<Book>()
                .HasIndex(b => b.ISBN)
                .IsUnique();

            // BorrowRecord relationships
            builder.Entity<BorrowRecord>()
                .HasOne(br => br.Book)
                .WithMany(b => b.BorrowRecords)
                .HasForeignKey(br => br.BookId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<BorrowRecord>()
                .HasOne(br => br.Student)
                .WithMany()
                .HasForeignKey(br => br.StudentId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes for better performance
            builder.Entity<BorrowRecord>()
                .HasIndex(br => new { br.StudentId, br.BorrowDate });

            builder.Entity<BorrowRecord>()
                .HasIndex(br => br.DueDate);
        }

        private void ConfigureActivityEntities(ModelBuilder builder)
        {
            // Activity relationships
            builder.Entity<Activity>()
                .HasOne(a => a.Organizer)
                .WithMany()
                .HasForeignKey(a => a.OrganizerId)
                .OnDelete(DeleteBehavior.Restrict);

            // ActivityParticipant relationships
            builder.Entity<ActivityParticipant>()
                .HasOne(ap => ap.Activity)
                .WithMany(a => a.Participants)
                .HasForeignKey(ap => ap.ActivityId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<ActivityParticipant>()
                .HasOne(ap => ap.Student)
                .WithMany()
                .HasForeignKey(ap => ap.StudentId)
                .OnDelete(DeleteBehavior.Restrict);

            // Unique constraint for student-activity participation
            builder.Entity<ActivityParticipant>()
                .HasIndex(ap => new { ap.ActivityId, ap.StudentId })
                .IsUnique();

            // Indexes for better performance
            builder.Entity<Activity>()
                .HasIndex(a => a.StartDate);

            builder.Entity<Activity>()
                .HasIndex(a => a.Status);
        }

        private void ConfigureGradeEntities(ModelBuilder builder)
        {
            // Grade relationships
            builder.Entity<Grade>()
                .HasOne(g => g.Student)
                .WithMany()
                .HasForeignKey(g => g.StudentId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Grade>()
                .HasOne(g => g.Subject)
                .WithMany()
                .HasForeignKey(g => g.SubjectId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Grade>()
                .HasOne(g => g.AcademicYear)
                .WithMany()
                .HasForeignKey(g => g.AcademicYearId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure decimal properties for grades
            builder.Entity<Grade>()
                .Property(g => g.Score)
                .HasColumnType("decimal(5,2)");

            builder.Entity<Grade>()
                .Property(g => g.MaxScore)
                .HasColumnType("decimal(5,2)");

            builder.Entity<Grade>()
                .Property(g => g.Percentage)
                .HasColumnType("decimal(5,2)");

            builder.Entity<GradeScale>()
                .Property(gs => gs.MinPercentage)
                .HasColumnType("decimal(5,2)");

            builder.Entity<GradeScale>()
                .Property(gs => gs.MaxPercentage)
                .HasColumnType("decimal(5,2)");

            builder.Entity<GradeScale>()
                .Property(gs => gs.GradePoint)
                .HasColumnType("decimal(3,2)");

            // StudentReportCard relationships
            builder.Entity<StudentReportCard>()
                .HasOne(src => src.Student)
                .WithMany()
                .HasForeignKey(src => src.StudentId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<StudentReportCard>()
                .HasOne(src => src.AcademicYear)
                .WithMany()
                .HasForeignKey(src => src.AcademicYearId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure decimal properties for report cards
            builder.Entity<StudentReportCard>()
                .Property(src => src.TotalMarks)
                .HasColumnType("decimal(8,2)");

            builder.Entity<StudentReportCard>()
                .Property(src => src.ObtainedMarks)
                .HasColumnType("decimal(8,2)");

            builder.Entity<StudentReportCard>()
                .Property(src => src.Percentage)
                .HasColumnType("decimal(5,2)");

            builder.Entity<StudentReportCard>()
                .Property(src => src.GPA)
                .HasColumnType("decimal(3,2)");

            // Indexes for better performance
            builder.Entity<Grade>()
                .HasIndex(g => new { g.StudentId, g.SubjectId, g.Date });

            builder.Entity<StudentReportCard>()
                .HasIndex(src => new { src.StudentId, src.AcademicYearId, src.Semester })
                .IsUnique();
        }

        private void ConfigureExtendedEntities(ModelBuilder builder)
        {
            // Library Extended Entities
            builder.Entity<BookReservation>()
                .HasOne(br => br.Book)
                .WithMany(b => b.BookReservations)
                .HasForeignKey(br => br.BookId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<BookReservation>()
                .HasOne(br => br.Student)
                .WithMany()
                .HasForeignKey(br => br.StudentId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<BookReview>()
                .HasOne(br => br.Book)
                .WithMany(b => b.BookReviews)
                .HasForeignKey(br => br.BookId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<BookReview>()
                .HasOne(br => br.Student)
                .WithMany()
                .HasForeignKey(br => br.StudentId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<LibraryCard>()
                .HasOne(lc => lc.User)
                .WithMany()
                .HasForeignKey(lc => lc.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<LibraryCard>()
                .HasIndex(lc => lc.CardNumber)
                .IsUnique();

            builder.Entity<DigitalResourceAccess>()
                .HasOne(dra => dra.DigitalResource)
                .WithMany(dr => dr.DigitalResourceAccesses)
                .HasForeignKey(dra => dra.DigitalResourceId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<DigitalResourceAccess>()
                .HasOne(dra => dra.User)
                .WithMany()
                .HasForeignKey(dra => dra.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Activity Extended Entities
            builder.Entity<ActivityResource>()
                .HasOne(ar => ar.Activity)
                .WithMany(a => a.ActivityResources)
                .HasForeignKey(ar => ar.ActivityId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<ActivityFeedback>()
                .HasOne(af => af.Activity)
                .WithMany(a => a.ActivityFeedbacks)
                .HasForeignKey(af => af.ActivityId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<ActivityFeedback>()
                .HasOne(af => af.Participant)
                .WithMany()
                .HasForeignKey(af => af.ParticipantId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<EventRegistration>()
                .HasOne(er => er.Event)
                .WithMany(e => e.EventRegistrations)
                .HasForeignKey(er => er.EventId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<EventRegistration>()
                .HasOne(er => er.User)
                .WithMany()
                .HasForeignKey(er => er.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Schedule Extended Entities
            builder.Entity<TimeTableSlot>()
                .HasOne(tts => tts.TimeTableTemplate)
                .WithMany(tt => tt.TimeTableSlots)
                .HasForeignKey(tts => tts.TimeTableTemplateId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<TimeTableSlot>()
                .HasOne(tts => tts.Subject)
                .WithMany()
                .HasForeignKey(tts => tts.SubjectId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.Entity<TimeTableSlot>()
                .HasOne(tts => tts.Teacher)
                .WithMany()
                .HasForeignKey(tts => tts.TeacherId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.Entity<TimeTableTemplate>()
                .HasOne(tt => tt.AcademicYear)
                .WithMany()
                .HasForeignKey(tt => tt.AcademicYearId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<TimeTableTemplate>()
                .HasOne(tt => tt.Grade)
                .WithMany()
                .HasForeignKey(tt => tt.GradeId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.Entity<TimeTableTemplate>()
                .HasOne(tt => tt.Class)
                .WithMany()
                .HasForeignKey(tt => tt.ClassId)
                .OnDelete(DeleteBehavior.SetNull);

            // Configure decimal properties for extended entities
            builder.Entity<BorrowRecord>()
                .Property(br => br.FineAmount)
                .HasColumnType("decimal(10,2)");

            builder.Entity<ActivityResource>()
                .Property(ar => ar.Cost)
                .HasColumnType("decimal(10,2)");

            builder.Entity<Schools.Shared.Models.Event>()
                .Property(e => e.TicketPrice)
                .HasColumnType("decimal(10,2)");

            builder.Entity<EventRegistration>()
                .Property(er => er.AmountPaid)
                .HasColumnType("decimal(10,2)");

            // Indexes for better performance
            builder.Entity<BookReservation>()
                .HasIndex(br => new { br.StudentId, br.ReservationDate });

            builder.Entity<DigitalResourceAccess>()
                .HasIndex(dra => new { dra.UserId, dra.AccessDate });

            builder.Entity<ActivityFeedback>()
                .HasIndex(af => new { af.ActivityId, af.ParticipantId })
                .IsUnique();

            builder.Entity<EventRegistration>()
                .HasIndex(er => new { er.EventId, er.UserId })
                .IsUnique();

            // Schedule Extended Entities
            builder.Entity<Schedule>()
                .HasOne(s => s.AcademicYear)
                .WithMany()
                .HasForeignKey(s => s.AcademicYearId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<Schedule>()
                .HasOne(s => s.SubstituteTeacher)
                .WithMany()
                .HasForeignKey(s => s.SubstituteTeacherId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.Entity<ScheduleChange>()
                .HasOne(sc => sc.Schedule)
                .WithMany(s => s.ScheduleChanges)
                .HasForeignKey(sc => sc.ScheduleId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<ScheduleChange>()
                .HasOne(sc => sc.RequestedByUser)
                .WithMany()
                .HasForeignKey(sc => sc.RequestedBy)
                .OnDelete(DeleteBehavior.SetNull);

            builder.Entity<ScheduleChange>()
                .HasOne(sc => sc.ApprovedByUser)
                .WithMany()
                .HasForeignKey(sc => sc.ApprovedBy)
                .OnDelete(DeleteBehavior.SetNull);

            builder.Entity<ScheduleChange>()
                .HasOne(sc => sc.OriginalTeacher)
                .WithMany()
                .HasForeignKey(sc => sc.OriginalTeacherId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.Entity<ScheduleChange>()
                .HasOne(sc => sc.NewTeacher)
                .WithMany()
                .HasForeignKey(sc => sc.NewTeacherId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.Entity<TeacherAssignment>()
                .HasOne(ta => ta.Teacher)
                .WithMany()
                .HasForeignKey(ta => ta.TeacherId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<TeacherAssignment>()
                .HasOne(ta => ta.Subject)
                .WithMany(s => s.TeacherAssignments)
                .HasForeignKey(ta => ta.SubjectId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<TeacherAssignment>()
                .HasOne(ta => ta.Class)
                .WithMany()
                .HasForeignKey(ta => ta.ClassId)
                .OnDelete(DeleteBehavior.Restrict);

            // Remove these as TeacherAssignment doesn't have Section or AcademicYear properties

            builder.Entity<StudentEnrollment>()
                .HasOne(se => se.Student)
                .WithMany()
                .HasForeignKey(se => se.StudentId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<StudentEnrollment>()
                .HasOne(se => se.Class)
                .WithMany(c => c.StudentEnrollments)
                .HasForeignKey(se => se.ClassId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<StudentEnrollment>()
                .HasOne(se => se.Section)
                .WithMany(s => s.StudentEnrollments)
                .HasForeignKey(se => se.SectionId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.Entity<StudentEnrollment>()
                .HasOne(se => se.AcademicYear)
                .WithMany(ay => ay.StudentEnrollments)
                .HasForeignKey(se => se.AcademicYearId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<StudentBehavior>()
                .HasOne(sb => sb.Student)
                .WithMany()
                .HasForeignKey(sb => sb.StudentId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<EmployeeAttendance>()
                .HasOne(ea => ea.Employee)
                .WithMany()
                .HasForeignKey(ea => ea.EmployeeId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indexes for Schedule Extended Entities
            builder.Entity<Schedule>()
                .HasIndex(s => new { s.Date, s.StartTime, s.EndTime });

            builder.Entity<Schedule>()
                .HasIndex(s => new { s.TeacherId, s.Date });

            builder.Entity<Schedule>()
                .HasIndex(s => new { s.ClassId, s.Date });

            builder.Entity<TeacherAssignment>()
                .HasIndex(ta => new { ta.TeacherId, ta.SubjectId, ta.ClassId })
                .IsUnique();

            builder.Entity<StudentEnrollment>()
                .HasIndex(se => new { se.StudentId, se.AcademicYearId })
                .IsUnique();

            builder.Entity<ClassPeriod>()
                .HasIndex(cp => cp.PeriodNumber)
                .IsUnique();

            builder.Entity<EmployeeAttendance>()
                .HasIndex(ea => new { ea.EmployeeId, ea.Date })
                .IsUnique();
        }
    }
}
