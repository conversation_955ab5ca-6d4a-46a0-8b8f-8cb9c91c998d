using Microsoft.AspNetCore.Components.Authorization;
using Schools.Shared.DTOs;
using System.Net.Http.Json;
using System.Text.Json;

namespace Schools.Client.Services
{
    public class AuthService : IAuthService
    {
        private readonly HttpClient _httpClient;
        private readonly AuthenticationStateProvider _authStateProvider;

        public AuthService(HttpClient httpClient, AuthenticationStateProvider authStateProvider)
        {
            _httpClient = httpClient;
            _authStateProvider = authStateProvider;
        }

        public async Task<AuthResponseDto> LoginAsync(LoginDto loginDto)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync("api/auth/login", loginDto);
                var result = await response.Content.ReadFromJsonAsync<AuthResponseDto>();

                if (result?.IsSuccess == true && !string.IsNullOrEmpty(result.Token))
                {
                    // For demo purposes, just return success
                    // Real implementation would handle token storage
                }

                return result ?? new AuthResponseDto { IsSuccess = false, Message = "خطأ في الاستجابة" };
            }
            catch (Exception ex)
            {
                return new AuthResponseDto { IsSuccess = false, Message = "خطأ في الاتصال بالخادم" };
            }
        }

        public async Task<AuthResponseDto> RegisterAsync(RegisterDto registerDto)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync("api/auth/register", registerDto);
                var result = await response.Content.ReadFromJsonAsync<AuthResponseDto>();

                return result ?? new AuthResponseDto { IsSuccess = false, Message = "خطأ في الاستجابة" };
            }
            catch (Exception ex)
            {
                return new AuthResponseDto { IsSuccess = false, Message = "خطأ في الاتصال بالخادم" };
            }
        }

        public async Task LogoutAsync()
        {
            try
            {
                await _httpClient.PostAsync("api/auth/logout", null);
            }
            catch { }
            finally
            {
                // For demo purposes, just clear authentication
                if (_authStateProvider is CustomAuthenticationStateProvider customProvider)
                {
                    await customProvider.NotifyUserLogout();
                }
            }
        }

        public async Task<bool> IsAuthenticatedAsync()
        {
            var authState = await _authStateProvider.GetAuthenticationStateAsync();
            return authState.User.Identity?.IsAuthenticated == true;
        }

        public async Task<UserDto?> GetCurrentUserAsync()
        {
            try
            {
                var token = await GetTokenAsync();
                if (string.IsNullOrEmpty(token))
                    return null;

                _httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

                var response = await _httpClient.GetAsync("api/auth/profile");
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<UserDto>();
                }
            }
            catch { }

            return null;
        }

        public async Task<string?> GetTokenAsync()
        {
            // For demo purposes, return null
            return await Task.FromResult<string?>(null);
        }

        public async Task<bool> ChangePasswordAsync(ChangePasswordDto changePasswordDto)
        {
            try
            {
                var token = await GetTokenAsync();
                if (string.IsNullOrEmpty(token))
                    return false;

                _httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

                var response = await _httpClient.PostAsJsonAsync("api/auth/change-password", changePasswordDto);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateProfileAsync(UpdateProfileDto updateProfileDto)
        {
            try
            {
                var token = await GetTokenAsync();
                if (string.IsNullOrEmpty(token))
                    return false;

                _httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

                var response = await _httpClient.PutAsJsonAsync("api/auth/profile", updateProfileDto);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<string?> GetCurrentUserIdAsync()
        {
            try
            {
                var user = await GetCurrentUserAsync();
                return user?.Id;
            }
            catch
            {
                return null;
            }
        }
    }
}
