﻿@using Microsoft.AspNetCore.Components.Authorization

<div class="top-row ps-3 navbar navbar-dark bg-primary">
    <div class="container-fluid">
        <a class="navbar-brand" href="">
            <i class="fas fa-graduation-cap me-2"></i>
            نظام المدارس
        </a>
        <button title="قائمة التنقل" class="navbar-toggler" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass nav-scrollable" @onclick="ToggleNavMenu">
    <nav class="nav flex-column">
        <!-- الصفحة الرئيسية -->
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <i class="fas fa-home me-2"></i> الرئيسية
            </NavLink>
        </div>

        <!-- قوائم الإدارة -->
        <AuthorizeView Roles="Admin">
            <div class="nav-item px-3">
                <div class="nav-section-header">
                    <i class="fas fa-user-shield me-2"></i> إدارة النظام
                </div>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/admin/academic-years">
                    <i class="fas fa-calendar-alt me-2"></i> الأعوام الدراسية
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/admin/grades">
                    <i class="fas fa-layer-group me-2"></i> المراحل الدراسية
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/admin/classes">
                    <i class="fas fa-school me-2"></i> الصفوف الدراسية
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/admin/subjects">
                    <i class="fas fa-book me-2"></i> المواد الدراسية
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/admin/schedule">
                    <i class="fas fa-calendar-alt me-2"></i> إدارة الجداول
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/admin/sections">
                    <i class="fas fa-chalkboard me-2"></i> الأقسام الدراسية
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/admin/users">
                    <i class="fas fa-user-check me-2"></i> إدارة المستخدمين
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/admin/parents">
                    <i class="fas fa-users me-2"></i> إدارة أولياء الأمور
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/admin/system-settings">
                    <i class="fas fa-cogs me-2"></i> إعدادات النظام
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/admin/system-statistics">
                    <i class="fas fa-chart-bar me-2"></i> إحصائيات النظام
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/admin/notifications">
                    <i class="fas fa-bell me-2"></i> مركز الإشعارات
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/admin/executive-dashboard">
                    <i class="fas fa-chart-line me-2"></i> اللوحة التنفيذية
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/admin/documents">
                    <i class="fas fa-folder-open me-2"></i> إدارة المستندات
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/admin/data-analytics">
                    <i class="fas fa-brain me-2"></i> تحليل البيانات المتقدم
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/admin/events">
                    <i class="fas fa-calendar-alt me-2"></i> إدارة الفعاليات
                </NavLink>
            </div>
        </AuthorizeView>

        <!-- قوائم المعلمين -->
        <AuthorizeView Roles="Teacher">
            <div class="nav-item px-3">
                <div class="nav-section-header">
                    <i class="fas fa-chalkboard-teacher me-2"></i> أدوات المعلم
                </div>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/teacher/classes">
                    <i class="fas fa-users me-2"></i> صفوفي
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/teacher/attendance">
                    <i class="fas fa-clipboard-check me-2"></i> الحضور والغياب
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/teacher/exams">
                    <i class="fas fa-file-alt me-2"></i> الامتحانات
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/teacher/assignments">
                    <i class="fas fa-tasks me-2"></i> الواجبات
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/teacher/exams">
                    <i class="fas fa-laptop-code me-2"></i> الامتحانات الإلكترونية
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/teacher/bulk-grades">
                    <i class="fas fa-table me-2"></i> إدخال درجات متعددة
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/teacher/attendance-tracker">
                    <i class="fas fa-user-check me-2"></i> متتبع الحضور
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/teacher/student-performance">
                    <i class="fas fa-chart-line me-2"></i> تحليل أداء الطلاب
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/teacher/exam-settings">
                    <i class="fas fa-cogs me-2"></i> إعدادات الامتحانات
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/teacher/question-bank">
                    <i class="fas fa-database me-2"></i> بنك الأسئلة
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/teacher/exam-reports">
                    <i class="fas fa-chart-bar me-2"></i> تقارير الامتحانات
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/reports/attendance">
                    <i class="fas fa-calendar-check me-2"></i> تقارير الحضور
                </NavLink>
            </div>
        </AuthorizeView>

        <!-- قوائم الطلاب -->
        <AuthorizeView Roles="Student">
            <div class="nav-item px-3">
                <div class="nav-section-header">
                    <i class="fas fa-user-graduate me-2"></i> أدوات الطالب
                </div>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/student/schedule">
                    <i class="fas fa-calendar me-2"></i> جدولي الدراسي
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/student/exams">
                    <i class="fas fa-laptop-code me-2"></i> الامتحانات الإلكترونية
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/student/assignments">
                    <i class="fas fa-tasks me-2"></i> واجباتي
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/student/grades">
                    <i class="fas fa-star me-2"></i> درجاتي
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/student/my-grades">
                    <i class="fas fa-graduation-cap me-2"></i> درجاتي المفصلة
                </NavLink>
            </div>
        </AuthorizeView>

        <!-- قوائم أولياء الأمور -->
        <AuthorizeView Roles="Parent">
            <div class="nav-item px-3">
                <div class="nav-section-header">
                    <i class="fas fa-users me-2"></i> متابعة الأبناء
                </div>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/parent/dashboard">
                    <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/parent/children">
                    <i class="fas fa-child me-2"></i> أبنائي
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/parent/grades">
                    <i class="fas fa-star me-2"></i> الدرجات
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/parent/fees">
                    <i class="fas fa-dollar-sign me-2"></i> الرسوم المالية
                </NavLink>
            </div>
        </AuthorizeView>

        <!-- قوائم المحاسبين -->
        <AuthorizeView Roles="Accountant,Admin">
            <div class="nav-item px-3">
                <div class="nav-section-header">
                    <i class="fas fa-calculator me-2"></i> النظام المحاسبي
                </div>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/accounting/dashboard">
                    <i class="fas fa-tachometer-alt me-2"></i> لوحة تحكم المحاسبة
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/accounting/receipt-vouchers">
                    <i class="fas fa-receipt me-2"></i> سندات القبض
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/accounting/payment-vouchers">
                    <i class="fas fa-money-bill-wave me-2"></i> سندات الصرف
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/accounting/journal-entries">
                    <i class="fas fa-book me-2"></i> القيود اليومية
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/accounting/accounts">
                    <i class="fas fa-list-alt me-2"></i> دليل الحسابات
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/accounting/reports">
                    <i class="fas fa-chart-line me-2"></i> التقارير المحاسبية
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/accounting/fees">
                    <i class="fas fa-dollar-sign me-2"></i> إدارة الرسوم
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/accounting/payments">
                    <i class="fas fa-credit-card me-2"></i> المدفوعات
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/accounting/student-reports">
                    <i class="fas fa-chart-bar me-2"></i> التقارير المالية للطلاب
                </NavLink>
            </div>
        </AuthorizeView>

        <!-- إعدادات النظام المحاسبي -->
        <AuthorizeView Roles="Admin">
            <div class="nav-item px-3">
                <div class="nav-section-header">
                    <i class="fas fa-cogs me-2"></i> إعدادات النظام
                </div>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/accounting/settings">
                    <i class="fas fa-cog me-2"></i> إعدادات المحاسبة
                </NavLink>
            </div>
        </AuthorizeView>

        <!-- قوائم الموظفين -->
        <AuthorizeView Roles="Employee">
            <div class="nav-item px-3">
                <div class="nav-section-header">
                    <i class="fas fa-user-tie me-2"></i> شؤون الموظفين
                </div>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/employee/attendance">
                    <i class="fas fa-calendar-check me-2"></i> الحضور والغياب
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="/employee/requests">
                    <i class="fas fa-paper-plane me-2"></i> طلباتي
                </NavLink>
            </div>
        </AuthorizeView>
    </nav>
</div>

<style>
    .nav-section-header {
        font-weight: bold;
        color: #6c757d;
        font-size: 0.875rem;
        padding: 0.5rem 0;
        margin-top: 1rem;
        border-bottom: 1px solid #dee2e6;
    }

    .nav-section-header:first-child {
        margin-top: 0;
    }
</style>

@code {
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }
}
