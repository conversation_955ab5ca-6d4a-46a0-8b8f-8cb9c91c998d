@page "/admin/teachers"
@using Schools.Shared.Models
@using Schools.Shared.DTOs
@using Schools.Client.Services
@using Microsoft.AspNetCore.Authorization
@inject IApiService ApiService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@attribute [Authorize(Roles = "Admin")]

<PageTitle>إدارة المعلمين</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-chalkboard-teacher me-2"></i>
                        إدارة المعلمين
                    </h4>
                    <div class="d-flex gap-2">
                        <button class="btn btn-light" @onclick="ShowAddTeacherModal">
                            <i class="fas fa-plus me-2"></i>
                            إضافة معلم جديد
                        </button>
                        <div class="btn-group">
                            <button class="btn btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-filter me-2"></i>
                                فلترة: @GetSpecializationDisplayName(selectedSpecialization)
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" @onclick="@(() => FilterBySpecialization(""))">جميع التخصصات</a></li>
                                <li><a class="dropdown-item" href="#" @onclick="@(() => FilterBySpecialization("Mathematics"))">الرياضيات</a></li>
                                <li><a class="dropdown-item" href="#" @onclick="@(() => FilterBySpecialization("Science"))">العلوم</a></li>
                                <li><a class="dropdown-item" href="#" @onclick="@(() => FilterBySpecialization("Arabic"))">اللغة العربية</a></li>
                                <li><a class="dropdown-item" href="#" @onclick="@(() => FilterBySpecialization("English"))">اللغة الإنجليزية</a></li>
                                <li><a class="dropdown-item" href="#" @onclick="@(() => FilterBySpecialization("Islamic"))">التربية الإسلامية</a></li>
                                <li><a class="dropdown-item" href="#" @onclick="@(() => FilterBySpecialization("Social"))">الاجتماعيات</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if (isLoading)
                    {
                        <div class="text-center py-4">
                            <div class="spinner-border text-success" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    }
                    else
                    {
                        <!-- Statistics Cards -->
                        <div class="row mb-4">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>@totalTeachers</h4>
                                                <p class="mb-0">إجمالي المعلمين</p>
                                            </div>
                                            <i class="fas fa-chalkboard-teacher fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>@activeTeachers</h4>
                                                <p class="mb-0">معلمين نشطين</p>
                                            </div>
                                            <i class="fas fa-user-check fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>@maleTeachers</h4>
                                                <p class="mb-0">معلمين ذكور</p>
                                            </div>
                                            <i class="fas fa-male fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4>@femaleTeachers</h4>
                                                <p class="mb-0">معلمات إناث</p>
                                            </div>
                                            <i class="fas fa-female fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Search Bar -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" placeholder="البحث عن معلم..." @bind="searchTerm" @oninput="OnSearchChanged">
                                </div>
                            </div>
                        </div>

                        <!-- Teachers Grid -->
                        @if (filteredTeachers?.Any() == true)
                        {
                            <div class="row">
                                @foreach (var teacher in filteredTeachers.Take(12))
                                {
                                    <div class="col-lg-4 col-md-6 mb-4">
                                        <div class="card h-100 teacher-card">
                                            <div class="card-body">
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="avatar-lg bg-success rounded-circle d-flex align-items-center justify-content-center me-3">
                                                        <i class="fas fa-chalkboard-teacher fa-2x text-white"></i>
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <h5 class="card-title mb-1">@teacher.FirstName @teacher.LastName</h5>
                                                        <p class="text-muted mb-0">@teacher.TeacherNumber</p>
                                                    </div>
                                                    <div class="dropdown">
                                                        <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                                            <i class="fas fa-ellipsis-v"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <li><a class="dropdown-item" href="#" @onclick="() => ViewTeacherDetails(teacher.Id)">
                                                                <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                                            </a></li>
                                                            <li><a class="dropdown-item" href="#" @onclick="() => EditTeacher(teacher.Id)">
                                                                <i class="fas fa-edit me-2"></i>تعديل
                                                            </a></li>
                                                            <li><hr class="dropdown-divider"></li>
                                                            @if (teacher.IsActive)
                                                            {
                                                                <li><a class="dropdown-item text-warning" href="#" @onclick="() => DeactivateTeacher(teacher.Id)">
                                                                    <i class="fas fa-pause me-2"></i>إلغاء تفعيل
                                                                </a></li>
                                                            }
                                                            else
                                                            {
                                                                <li><a class="dropdown-item text-success" href="#" @onclick="() => ActivateTeacher(teacher.Id)">
                                                                    <i class="fas fa-play me-2"></i>تفعيل
                                                                </a></li>
                                                            }
                                                            <li><a class="dropdown-item text-danger" href="#" @onclick="() => DeleteTeacher(teacher.Id)">
                                                                <i class="fas fa-trash me-2"></i>حذف
                                                            </a></li>
                                                        </ul>
                                                    </div>
                                                </div>

                                                <div class="mb-3">
                                                    <span class="badge bg-@GetSpecializationColor(teacher.Specialization) me-2">
                                                        @GetSpecializationName(teacher.Specialization)
                                                    </span>
                                                    <span class="badge bg-@(teacher.IsActive ? "success" : "danger")">
                                                        @(teacher.IsActive ? "نشط" : "غير نشط")
                                                    </span>
                                                </div>

                                                <div class="row text-center">
                                                    <div class="col-4">
                                                        <div class="border-end">
                                                            <h6 class="mb-0">@teacher.ClassesCount</h6>
                                                            <small class="text-muted">الصفوف</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="border-end">
                                                            <h6 class="mb-0">@teacher.StudentsCount</h6>
                                                            <small class="text-muted">الطلاب</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <h6 class="mb-0">@teacher.ExperienceYears</h6>
                                                        <small class="text-muted">سنوات الخبرة</small>
                                                    </div>
                                                </div>

                                                <hr>

                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        <i class="fas @(teacher.Gender == "Male" ? "fa-male" : "fa-female") me-1"></i>
                                                        @(teacher.Gender == "Male" ? "ذكر" : "أنثى")
                                                    </small>
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        @teacher.HireDate?.ToString("yyyy")
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>

                            @if (filteredTeachers.Count() > 12)
                            {
                                <div class="text-center mt-3">
                                    <p class="text-muted">عرض 12 من أصل @filteredTeachers.Count() معلم</p>
                                    <button class="btn btn-outline-success" @onclick="LoadMoreTeachers">
                                        تحميل المزيد
                                    </button>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-chalkboard-teacher fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد معلمين</h5>
                                <p class="text-muted">لم يتم العثور على معلمين بالفلتر المحدد</p>
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .avatar-lg {
        width: 60px;
        height: 60px;
    }

    .teacher-card {
        transition: transform 0.2s;
    }

    .teacher-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
</style>

@code {
    private List<TeacherDto> teachers = new();
    private List<TeacherDto> filteredTeachers = new();
    private bool isLoading = true;
    private string selectedSpecialization = string.Empty;
    private string searchTerm = string.Empty;

    private int totalTeachers => teachers.Count;
    private int activeTeachers => teachers.Count(t => t.IsActive);
    private int maleTeachers => teachers.Count(t => t.Gender == "Male");
    private int femaleTeachers => teachers.Count(t => t.Gender == "Female");

    protected override async Task OnInitializedAsync()
    {
        await LoadTeachers();
    }

    private async Task LoadTeachers()
    {
        try
        {
            isLoading = true;
            // Mock data for demonstration
            teachers = GenerateMockTeachers();
            FilterTeachers();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تحميل البيانات: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private List<TeacherDto> GenerateMockTeachers()
    {
        var mockTeachers = new List<TeacherDto>();
        var random = new Random();
        var firstNames = new[] { "أحمد", "محمد", "فاطمة", "عائشة", "علي", "خديجة", "عبدالله", "مريم", "يوسف", "زينب" };
        var lastNames = new[] { "الأحمد", "المحمد", "العلي", "الحسن", "الزهراني", "القحطاني", "الغامدي", "العتيبي" };
        var specializations = new[] { "Mathematics", "Science", "Arabic", "English", "Islamic", "Social" };

        for (int i = 1; i <= 45; i++)
        {
            var gender = random.Next(2) == 0 ? "Male" : "Female";
            var specialization = specializations[random.Next(specializations.Length)];

            mockTeachers.Add(new TeacherDto
            {
                Id = i.ToString(),
                FirstName = firstNames[random.Next(firstNames.Length)],
                LastName = lastNames[random.Next(lastNames.Length)],
                TeacherNumber = $"TCH{i:D3}",
                Specialization = specialization,
                Gender = gender,
                HireDate = DateTime.Now.AddYears(-random.Next(1, 15)),
                ExperienceYears = random.Next(1, 20),
                ClassesCount = random.Next(3, 8),
                StudentsCount = random.Next(80, 200),
                IsActive = random.Next(10) > 0 // 90% active
            });
        }

        return mockTeachers;
    }

    private void FilterBySpecialization(string specialization)
    {
        selectedSpecialization = specialization;
        FilterTeachers();
    }

    private void FilterTeachers()
    {
        var query = teachers.AsEnumerable();

        if (!string.IsNullOrEmpty(selectedSpecialization))
        {
            query = query.Where(t => t.Specialization == selectedSpecialization);
        }

        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(t =>
                t.FirstName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                t.LastName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                t.TeacherNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
        }

        filteredTeachers = query.ToList();
        StateHasChanged();
    }

    private async Task OnSearchChanged(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? string.Empty;
        FilterTeachers();
    }

    private void ShowAddTeacherModal()
    {
        Navigation.NavigateTo("/admin/teachers/add");
    }

    private void ViewTeacherDetails(string teacherId)
    {
        Navigation.NavigateTo($"/admin/teachers/{teacherId}");
    }

    private void EditTeacher(string teacherId)
    {
        Navigation.NavigateTo($"/admin/teachers/{teacherId}/edit");
    }

    private async Task ActivateTeacher(string teacherId)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("alert", "تم تفعيل المعلم بنجاح");
            await LoadTeachers();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في تفعيل المعلم: {ex.Message}");
        }
    }

    private async Task DeactivateTeacher(string teacherId)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("alert", "تم إلغاء تفعيل المعلم بنجاح");
            await LoadTeachers();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"خطأ في إلغاء تفعيل المعلم: {ex.Message}");
        }
    }

    private async Task DeleteTeacher(string teacherId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "هل أنت متأكد من حذف هذا المعلم؟"))
        {
            try
            {
                await JSRuntime.InvokeVoidAsync("alert", "تم حذف المعلم بنجاح");
                await LoadTeachers();
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"خطأ في حذف المعلم: {ex.Message}");
            }
        }
    }

    private async Task LoadMoreTeachers()
    {
        await JSRuntime.InvokeVoidAsync("alert", "تم تحميل المزيد من المعلمين");
    }

    private string GetSpecializationDisplayName(string specialization)
    {
        return specialization switch
        {
            "Mathematics" => "الرياضيات",
            "Science" => "العلوم",
            "Arabic" => "اللغة العربية",
            "English" => "اللغة الإنجليزية",
            "Islamic" => "التربية الإسلامية",
            "Social" => "الاجتماعيات",
            _ => "جميع التخصصات"
        };
    }

    private string GetSpecializationName(string specialization)
    {
        return specialization switch
        {
            "Mathematics" => "رياضيات",
            "Science" => "علوم",
            "Arabic" => "عربي",
            "English" => "إنجليزي",
            "Islamic" => "إسلامية",
            "Social" => "اجتماعيات",
            _ => "غير محدد"
        };
    }

    private string GetSpecializationColor(string specialization)
    {
        return specialization switch
        {
            "Mathematics" => "primary",
            "Science" => "success",
            "Arabic" => "warning",
            "English" => "info",
            "Islamic" => "secondary",
            "Social" => "danger",
            _ => "dark"
        };
    }

    public class TeacherDto
    {
        public string Id { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string TeacherNumber { get; set; } = string.Empty;
        public string Specialization { get; set; } = string.Empty;
        public string Gender { get; set; } = string.Empty;
        public DateTime? HireDate { get; set; }
        public int ExperienceYears { get; set; }
        public int ClassesCount { get; set; }
        public int StudentsCount { get; set; }
        public bool IsActive { get; set; }
    }
}
