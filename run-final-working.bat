@echo off
echo ========================================
echo    نظام إدارة المدارس - النسخة النهائية العاملة
echo    School Management System - Final Working Version
echo ========================================
echo.

echo 🎯 تشغيل النظام بالبيانات الوهمية
echo Running system with mock data
echo.

echo 🧹 تنظيف المشروع...
echo Cleaning project...
dotnet clean Schools.Client > nul 2>&1

echo 🔧 بناء العميل...
echo Building Client...
dotnet build Schools.Client --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء العميل
    echo Client Build failed
    echo.
    echo 🔍 محاولة إصلاح المشاكل...
    echo Trying to fix issues...
    dotnet restore Schools.Client > nul 2>&1
    dotnet build Schools.Client --verbosity quiet
    if %errorlevel% neq 0 (
        echo ❌ فشل في الإصلاح
        echo Fix failed
        pause
        exit /b 1
    )
)

echo ✅ تم بناء العميل بنجاح
echo Client build successful
echo.

echo 🌐 تشغيل Blazor Client...
echo Starting Blazor Client...
echo.
echo 📱 سيتم فتح المتصفح تلقائياً على:
echo Browser will open automatically at:
echo http://localhost:5131
echo.

start "Schools Client - Final" cmd /k "echo === نظام إدارة المدارس === && echo تشغيل العميل على المنفذ 5131 && echo Client running on port 5131 && echo. && cd Schools.Client && dotnet run --urls=http://localhost:5131"

echo ⏳ انتظار تشغيل العميل...
echo Waiting for Client to start...
timeout /t 12 /nobreak > nul

echo 🌐 فتح المتصفح...
echo Opening browser...
start http://localhost:5131

echo.
echo ========================================
echo ✅ تم تشغيل النظام بنجاح!
echo System started successfully!
echo ========================================
echo.
echo 🌐 الرابط:
echo URL:
echo.
echo 📱 http://localhost:5131
echo.
echo ========================================
echo 🔐 بيانات تسجيل الدخول:
echo Login Credentials:
echo.
echo 📧 البريد: <EMAIL>
echo 🔑 كلمة المرور: Admin123!
echo.
echo ========================================
echo 🎊 مميزات النظام:
echo System Features:
echo.
echo ✅ 22+ صفحة متكاملة
echo ✅ واجهات عربية جميلة
echo ✅ تصميم متجاوب
echo ✅ نظام مصادقة آمن
echo ✅ إحصائيات تفاعلية
echo ✅ بيانات وهمية واقعية
echo.
echo ========================================
echo 📱 الصفحات المتاحة:
echo Available Pages:
echo.
echo 🏠 لوحة تحكم الإدارة
echo 📊 إدارة الأعوام الدراسية
echo 🎓 إدارة المراحل الدراسية
echo 🏫 إدارة الصفوف الدراسية
echo 📚 إدارة المواد الدراسية
echo 👥 إدارة المستخدمين
echo 🎯 إدارة الأنشطة والفعاليات
echo 📖 إدارة المكتبة الرقمية
echo ✅ إدارة الحضور والغياب
echo 📅 إدارة الجداول الدراسية
echo 📝 إدارة الدرجات والتقييم
echo 👨‍🏫 لوحة تحكم المعلم
echo 👨‍🎓 لوحة تحكم الطالب
echo 👨‍👩‍👧‍👦 لوحة تحكم ولي الأمر
echo.
echo ========================================
echo 💡 ملاحظات:
echo Notes:
echo.
echo 🔄 النظام يعمل بالبيانات الوهمية
echo 📊 جميع الوظائف تعمل بشكل كامل
echo 🎨 التصميم والواجهات متكاملة
echo 💾 البيانات لا تُحفظ (وضع العرض)
echo 🚀 أداء سريع ومستقر
echo.
echo ========================================
echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause > nul
