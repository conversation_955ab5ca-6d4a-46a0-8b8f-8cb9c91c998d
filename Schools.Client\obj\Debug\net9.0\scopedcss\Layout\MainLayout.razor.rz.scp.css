.page[b-2ijgs15dv0] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-2ijgs15dv0] {
    flex: 1;
}

.sidebar[b-2ijgs15dv0] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-2ijgs15dv0] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-2ijgs15dv0]  a, .top-row[b-2ijgs15dv0]  .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row[b-2ijgs15dv0]  a:hover, .top-row[b-2ijgs15dv0]  .btn-link:hover {
        text-decoration: underline;
    }

    .top-row[b-2ijgs15dv0]  a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row[b-2ijgs15dv0] {
        justify-content: space-between;
    }

    .top-row[b-2ijgs15dv0]  a, .top-row[b-2ijgs15dv0]  .btn-link {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page[b-2ijgs15dv0] {
        flex-direction: row;
    }

    .sidebar[b-2ijgs15dv0] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-2ijgs15dv0] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row.auth[b-2ijgs15dv0]  a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row[b-2ijgs15dv0], article[b-2ijgs15dv0] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
