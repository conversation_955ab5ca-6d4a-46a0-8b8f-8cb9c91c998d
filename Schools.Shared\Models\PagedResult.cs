using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;

namespace Schools.Shared.Models
{
    /// <summary>
    /// Generic paged result class for API responses
    /// </summary>
    /// <typeparam name="T">Type of items in the result</typeparam>
    public class PagedResult<T>
    {
        /// <summary>
        /// List of items for the current page
        /// </summary>
        public List<T> Items { get; set; } = new();

        /// <summary>
        /// Total number of items across all pages
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Current page number (1-based)
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// Number of items per page
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// Total number of pages
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// Whether there is a previous page
        /// </summary>
        public bool HasPrevious => Page > 1;

        /// <summary>
        /// Whether there is a next page
        /// </summary>
        public bool HasNext => Page < TotalPages;

        /// <summary>
        /// Previous page number (null if no previous page)
        /// </summary>
        public int? PreviousPage => HasPrevious ? Page - 1 : null;

        /// <summary>
        /// Next page number (null if no next page)
        /// </summary>
        public int? NextPage => HasNext ? Page + 1 : null;

        /// <summary>
        /// Starting item number for current page
        /// </summary>
        public int StartItem => TotalCount == 0 ? 0 : ((Page - 1) * PageSize) + 1;

        /// <summary>
        /// Ending item number for current page
        /// </summary>
        public int EndItem => Math.Min(Page * PageSize, TotalCount);

        /// <summary>
        /// Creates a new PagedResult with calculated TotalPages
        /// </summary>
        /// <param name="items">Items for current page</param>
        /// <param name="totalCount">Total count of all items</param>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>PagedResult instance</returns>
        public static PagedResult<T> Create(List<T> items, int totalCount, int page, int pageSize)
        {
            return new PagedResult<T>
            {
                Items = items,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };
        }

        /// <summary>
        /// Creates an empty PagedResult
        /// </summary>
        /// <param name="page">Current page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Empty PagedResult instance</returns>
        public static PagedResult<T> Empty(int page = 1, int pageSize = 20)
        {
            return new PagedResult<T>
            {
                Items = new List<T>(),
                TotalCount = 0,
                Page = page,
                PageSize = pageSize,
                TotalPages = 0
            };
        }

        /// <summary>
        /// Maps the current PagedResult to a new type
        /// </summary>
        /// <typeparam name="TResult">Target type</typeparam>
        /// <param name="mapper">Mapping function</param>
        /// <returns>PagedResult of target type</returns>
        public PagedResult<TResult> Map<TResult>(Func<T, TResult> mapper)
        {
            return new PagedResult<TResult>
            {
                Items = Items.Select(mapper).ToList(),
                TotalCount = TotalCount,
                Page = Page,
                PageSize = PageSize,
                TotalPages = TotalPages
            };
        }
    }

    /// <summary>
    /// Extension methods for IQueryable to support pagination
    /// </summary>
    public static class QueryableExtensions
    {
        /// <summary>
        /// Converts IQueryable to PagedResult
        /// </summary>
        /// <typeparam name="T">Entity type</typeparam>
        /// <param name="query">Source query</param>
        /// <param name="page">Page number (1-based)</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>PagedResult</returns>
        public static async Task<PagedResult<T>> ToPagedResultAsync<T>(
            this IQueryable<T> query,
            int page,
            int pageSize)
        {
            var totalCount = await query.CountAsync();
            var items = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return PagedResult<T>.Create(items, totalCount, page, pageSize);
        }

        /// <summary>
        /// Converts IQueryable to PagedResult with projection
        /// </summary>
        /// <typeparam name="TSource">Source entity type</typeparam>
        /// <typeparam name="TResult">Result type</typeparam>
        /// <param name="query">Source query</param>
        /// <param name="page">Page number (1-based)</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="selector">Projection selector</param>
        /// <returns>PagedResult</returns>
        public static async Task<PagedResult<TResult>> ToPagedResultAsync<TSource, TResult>(
            this IQueryable<TSource> query,
            int page,
            int pageSize,
            Expression<Func<TSource, TResult>> selector)
        {
            var totalCount = await query.CountAsync();
            var items = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(selector)
                .ToListAsync();

            return PagedResult<TResult>.Create(items, totalCount, page, pageSize);
        }
    }
}
